<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailRejectInterViewSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $submitCvBook;
    protected $submitCv;

    public function __construct($employer, $submitCvBook, $submitCv)
    {
        $this->employer = $employer;
        $this->submitCvBook = $submitCvBook;
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position = '';
        $type = '';

        $companyName = $this->employer->name;
        $employerName = $this->employer->name;
        $timeInterview = $this->submitCvBook->date_time_book_format;
        $address = $this->submitCvBook->address;

        if ($this->submitCv) {
            $type = $this->submitCv->bonus_type;
        }

        if (!empty($this->submitCv->warehouseCv)) {
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            if ($type == 'cv') {
                $position = $this->submitCv->warehouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
                $position = $this->submitCv->job->name;
            }
        }
        $link = route('employer-submitcv', ['discuss' => $this->submitCv->id]);

        return (new MailMessage)
            ->view('email.emailRejectInterView', [
                'employerName'  => $employerName,
                'companyName'   => $companyName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'type'          => $type,
                'timeInterview' => $timeInterview,
                'address'       => $address,
                'link'          => $link,
            ])
            ->subject('[RECLAND] Đặt lại lịch phỏng vấn ứng viên ' . $candidateName . ' vị trí ' . $position);
    }
}
