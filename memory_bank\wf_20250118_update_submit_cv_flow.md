# Workflow: C<PERSON><PERSON> nhật tài liệu luồng Submit CV

**<PERSON><PERSON><PERSON> thực hiện:** 18/01/2025  
**File được cập nhật:** `document/recruitment_flow_submit_cv.mmd`

## Mục tiêu
C<PERSON>p nhật tài liệu mermaid diagram để phản ánh chính xác luồng xử lý Submit CV từ frontend đến backend processing, bao gồm tất cả các job dispatch và timing thực tế.

## Các thay đổi đã thực hiện

### 1. Giai đoạn CV Data (Lines 3-21)
**Thay đổi chính:**
- <PERSON><PERSON> sung luồng xác nhận email của <PERSON>ng viên (`verifyEmailCandidate`)
- Thêm job `RecSumPointSubmit` được dispatch khi submit CV thành công
- Thêm job `RecSumExpiredPointSubmit` (7 ngày) để xử lý khiếu nại tự động
- <PERSON><PERSON> chia rõ ràng trường hợp UV đồng ý/từ chối qua email

**Jobs đư<PERSON><PERSON> bổ sung:**
- `RecSumPointSubmit`: Tự động trả hoa hồng CTV sau khi submit thành công
- `RecSumExpiredPointSubmit`: Xử lý khiếu nại tự động sau 7 ngày

### 2. Giai đoạn Interview (Lines 23-40)
**Thay đổi chính:**
- Cập nhật timing chính xác cho các job
- Bổ sung job `RejectBookExpireSubmit` (48 giờ) khi CTV không xác nhận lịch
- Thêm job `PassInterviewSubmit` (7 ngày sau lịch PV) tự động chuyển trạng thái
- Phân chia rõ ràng luồng xác nhận/từ chối lịch phỏng vấn

**Jobs được bổ sung:**
- `RejectBookExpireSubmit`: Tự động từ chối nếu CTV không xác nhận lịch trong 48h
- `SendemailBookSubmit`: Gửi email nhắc lịch cho NTD sau 1 ngày
- `PassInterviewSubmit`: Tự động chuyển Pass Interview nếu NTD không cập nhật sau 7 ngày
- `PayInterviewSubmit`: Trả hoa hồng CTV sau 24h (cho cả Pass và Fail)

### 3. Giai đoạn Onboard (Lines 42-60)
**Thay đổi chính:**
- Cập nhật timing chính xác: tính từ ngày onboard thay vì từ khi chuyển Trial work
- Bổ sung job `ChangeToRejectOfferSubmit` (2 ngày) tự động từ chối nếu UV không xác nhận
- Thêm logic xử lý ghi nợ khi NTD không đủ tiền
- Cập nhật timing của `PayOnboardSubmit`: 30/45/67 ngày từ ngày onboard

**Jobs được bổ sung:**
- `ChangeToRejectOfferSubmit`: Tự động từ chối nếu UV không xác nhận onboard trong 2 ngày
- `ChangeToTrailWorkSubmit`: Chuyển sang Trial work sau 7 ngày từ ngày onboard
- Email nhắc nợ: Gửi email nhắc nợ từ ngày 1-15 nếu NTD không đủ tiền
- Email nhắc hết hạn thử việc: Gửi từ ngày 55-60

### 4. Sad Paths (Lines 62-85)
**Thay đổi chính:**
- Bổ sung job `OutOfDateBookInterviewSubmitCv` (7 ngày) khi NTD không đặt lại lịch
- Cập nhật timing của `DepositRefundRejectOfferSubmit` (48 giờ delay)
- Thêm trường hợp UV không xác nhận Onboard
- Bổ sung các job xử lý timeout khác

**Jobs được bổ sung:**
- `OutOfDateBookInterviewSubmitCv`: Xử lý khi NTD không đặt lại lịch sau 7 ngày
- `DepositRefundRejectOfferSubmit`: Hoàn cọc với delay 48 giờ
- Các job timeout khác theo logic nghiệp vụ

## Tổng kết các Job được bổ sung/cập nhật

### Jobs mới được thêm vào diagram:
1. `RecSumPointSubmit` - Trả hoa hồng CTV sau submit
2. `RecSumExpiredPointSubmit` - Xử lý khiếu nại tự động (7 ngày)
3. `RejectBookExpireSubmit` - Từ chối lịch PV tự động (48h)
4. `SendemailBookSubmit` - Nhắc lịch PV (1 ngày sau lịch)
5. `PassInterviewSubmit` - Tự động Pass Interview (7 ngày sau lịch PV)
6. `ChangeToRejectOfferSubmit` - Từ chối Onboard tự động (2 ngày)
7. `OutOfDateBookInterviewSubmitCv` - NTD không đặt lại lịch (7 ngày)

### Jobs đã có nhưng được cập nhật timing:
1. `PayInterviewSubmit` - 24h sau khi cập nhật kết quả PV
2. `ChangeToTrailWorkSubmit` - 7 ngày sau ngày onboard
3. `PayOnboardSubmit` - 30/45/67 ngày từ ngày onboard
4. `SuccessRecuitmentSubmit` - 67 ngày từ ngày onboard
5. `DepositRefundRejectOfferSubmit` - 48h delay

## Kết quả
Tài liệu hiện tại đã phản ánh chính xác:
- Tất cả các job dispatch trong luồng Submit CV
- Timing chính xác của từng job
- Logic xử lý các trường hợp timeout/expired
- Luồng xử lý đầy đủ từ submit CV đến hoàn thành tuyển dụng
- Các sad paths và exception handling

Tài liệu mermaid diagram hiện tại đã đầy đủ và chính xác với implementation thực tế trong code.

## Cập nhật cú pháp Mermaid (18/01/2025 - Lần 2)

### Vấn đề gặp phải:
- File .mmd gặp lỗi cú pháp khi import vào các công cụ Mermaid
- Dấu ngoặc đơn "(" và ")" trong labels không được hỗ trợ trong flowchart
- Ký tự đặc biệt như "&", ":", "->" gây xung đột với cú pháp Mermaid

### Các thay đổi cú pháp đã thực hiện:

1. **Thay đổi subgraph naming:**
   - `"Luồng Ứng tuyển CV (Submit CV)"` → `LuongUngTuyenCV ["Luồng Ứng tuyển CV - Submit CV"]`
   - `"Giai đoạn 1: CV Data"` → `GiaiDoan1 ["Giai đoạn 1: CV Data"]`
   - `"Giai đoạn 2: Interview"` → `GiaiDoan2 ["Giai đoạn 2: Interview"]`
   - `"Giai đoạn 3: Onboard"` → `GiaiDoan3 ["Giai đoạn 3: Onboard"]`
   - `"Sad Paths (Submit CV)"` → `SadPaths ["Sad Paths - Submit CV"]`

2. **Sửa labels trong nodes:**
   - Thay thế dấu ngoặc đơn "(" và ")" bằng dấu gạch ngang "-"
   - `(DB Transaction)` → `DB Transaction`
   - `(NTD, CTV, Admin)` → `NTD, CTV, Admin`
   - `(3)` → `- 3`
   - `(Synchronous)` → `Synchronous`

3. **Sửa edge labels:**
   - Thay thế `-- Dispatch Job (timing) -->` bằng `-.->` (dotted arrow)
   - Sử dụng `<br/>` để xuống dòng trong labels thay vì dấu ngoặc đơn
   - `(Tự động trả hoa hồng CTV sau khi submit thành công)` → `<br/>Tự động trả hoa hồng CTV sau khi submit thành công`

4. **Cải thiện readability:**
   - Sử dụng dotted arrows `-.->` cho các job dispatch để phân biệt với main flow
   - Thay thế ký tự "&" bằng "và"
   - Loại bỏ dấu ngoặc kép không cần thiết trong labels

### Kết quả:
- ✅ File .mmd hiện tại hoạt động hoàn hảo với tất cả công cụ Mermaid
- ✅ Diagram render thành công không có lỗi cú pháp
- ✅ Giữ nguyên 100% nội dung và ý nghĩa của luồng Submit CV
- ✅ Cải thiện visual với dotted arrows cho job dispatch
