<?php

namespace App\Services\Frontend;

use App\Repositories\DepositRepository;
use App\Repositories\SeoRepository;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\SEOMeta;

class DepositService
{

    protected $depositRepository;

    public function __construct(DepositRepository $depositRepository)
    {
        $this->depositRepository = $depositRepository;
    }

    public function insert($params)
    {
        $params['created_at'] = date('Y-m-d H:i:s');
        $params['updated_at'] = date('Y-m-d H:i:s');
        return $this->depositRepository->insert($params);
    }
}
