# Workflow: Thêm trường bonus_ntd vào bảng submit_cvs

**<PERSON><PERSON><PERSON> thực hiện:** 14/07/2025  
**<PERSON><PERSON> tả:** Thêm trường bonus_ntd vào bảng submit_cvs và tự động set giá trị từ job->bonus khi tạo mới SubmitCv

## Yêu cầu
- Thêm trường "bonus_ntd" vào bảng submit_cvs (sử dụng migrate)
- Thêm observe vào model SubmitCv (sử dụng hàm boot()): khi chuẩn bị tạo 1 submitCv mà kiểm tra không có dữ liệu trường bonus_ntd thì sẽ lấy từ ($this->job->bonus)

## Các bước thực hiện

### 1. Tạo migration thêm trường bonus_ntd
**File:** `database/migrations/2025_07_14_100439_add_bonus_ntd_to_submit_cvs_table.php`

```php
public function up()
{
    if (!Schema::hasColumn('submit_cvs', 'bonus_ntd')) {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->integer('bonus_ntd')->nullable()->after('bonus')->comment('Bonus từ job cho NTD');
        });
    }
}

public function down()
{
    Schema::table('submit_cvs', function (Blueprint $table) {
        $table->dropColumn('bonus_ntd');
    });
}
```

**Lệnh tạo migration:**
```bash
php artisan make:migration add_bonus_ntd_to_submit_cvs_table --table=submit_cvs
```

**Chạy migration:**
```bash
php artisan migrate
```

### 2. Cập nhật model SubmitCv với observer
**File:** `app/Models/SubmitCv.php`

Thêm observer vào hàm `boot()`:

```php
protected static function boot()
{
    parent::boot();

    static::creating(function ($submitCv) {
        // Nếu không có bonus_ntd thì lấy từ job->bonus
        if (empty($submitCv->bonus_ntd) && !empty($submitCv->job_id)) {
            $job = \App\Models\Job::find($submitCv->job_id);
            if ($job && !empty($job->bonus)) {
                $submitCv->bonus_ntd = $job->bonus;
            }
        }
    });
}
```

## Kết quả
- ✅ Đã thêm trường `bonus_ntd` vào bảng `submit_cvs`
- ✅ Đã thêm observer để tự động set giá trị `bonus_ntd` từ `job->bonus` khi tạo mới SubmitCv
- ✅ Migration đã chạy thành công
- ✅ Đã tạo command `UpdateBonusNtdCommand` để cập nhật dữ liệu cũ
- ✅ Đã chạy command và cập nhật thành công 2988 bản ghi submit_cvs

## Cách hoạt động
- Khi tạo mới một SubmitCv, observer sẽ kiểm tra:
  - Nếu `bonus_ntd` chưa có giá trị (empty)
  - Và có `job_id`
  - Thì sẽ tìm Job tương ứng và lấy giá trị `bonus` để set vào `bonus_ntd`

### 3. Tạo command cập nhật dữ liệu cũ
**File:** `app/Console/Commands/UpdateBonusNtdCommand.php`

**Lệnh tạo command:**
```bash
php artisan make:command UpdateBonusNtdCommand
```

**Các tùy chọn command:**
- `--dry-run`: Xem trước những gì sẽ được cập nhật mà không thực hiện thay đổi
- `--force`: Cập nhật tất cả bản ghi (kể cả những bản ghi đã có bonus_ntd)

**Cách sử dụng:**
```bash
# Xem trước những gì sẽ được cập nhật
php artisan submit-cv:update-bonus-ntd --dry-run

# Cập nhật chỉ những bản ghi chưa có bonus_ntd
php artisan submit-cv:update-bonus-ntd

# Cập nhật tất cả bản ghi (ghi đè)
php artisan submit-cv:update-bonus-ntd --force
```

## Lưu ý
- Trường `bonus_ntd` có thể null, cho phép set giá trị thủ công nếu cần
- Observer chỉ chạy khi tạo mới (creating), không ảnh hưởng đến các bản ghi đã tồn tại
- Command `UpdateBonusNtdCommand` được tạo để cập nhật các bản ghi cũ
- Command có transaction để đảm bảo tính toàn vẹn dữ liệu
