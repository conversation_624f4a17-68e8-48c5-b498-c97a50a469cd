<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }


    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        // $employerName = $this->wareHouseCvSellingBuy->employer->name;
        // $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        // $companyName = $this->wareHouseCvSellingBuy->employer->name;
        
        $position = $this->submitCv->job->name;
        $employerName = $this->submitCv->employer->name;  //name ntd
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        // $type = $this->submitCv->bonus_type;

        $link = route('employer-submitcv',['view-detail' => $this->submitCv->id]);
        // $linkMarket = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusRecruiterCancelInterviewToEmployerSubmitCv', [
                'employerName' => $employerName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'link' => $link,
                // 'linkMarket' => $linkMarket,
            ])
            ->subject('[Recland] Thông báo hết hạn đặt lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);

    }


}

