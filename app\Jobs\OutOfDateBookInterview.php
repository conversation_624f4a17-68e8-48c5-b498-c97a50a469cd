<?php

namespace App\Jobs;


use App\Notifications\CandidateRejectRecruitmentAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployer;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRec;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class OutOfDateBookInterview implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuy;
    public $user;
    public function __construct($wareHouseCvSellingBuy,$user)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->user = $user;
    }


    public function handle()
    {
        //  5 => 'Reject Interview schedule',
        if ($this->wareHouseCvSellingBuy->status_recruitment == 5){
            $this->wareHouseCvSellingBuy->update([
                'status_recruitment' => 6,
            ]);
            $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($this->wareHouseCvSellingBuy,$this->user);
            $this->wareHouseCvSellingBuy->employer->notify(new ChangeStatusRecruiterCancelInterviewToEmployer($this->wareHouseCvSellingBuy));
            if ($this->wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusRecruiterCancelInterviewToAdmin($this->wareHouseCvSellingBuy));
            }else{
                $this->wareHouseCvSellingBuy->rec->notify(new ChangeStatusRecruiterCancelInterviewToRec($this->wareHouseCvSellingBuy));
            }

        }
    }


}
