<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\TopCollaboratorsDatatable;
use App\DataTables\Admin\TopRecDatatable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TopCollaboratorsRequest;
use App\Repositories\TopCollaboratorsRepository;
use App\Services\Frontend\TopRecService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TopCollaboratorsController extends Controller
{
    protected $topRecService;

    public function __construct(TopRecService $topRecService)
    {
        $this->topRecService = $topRecService;
    }
    public function index(TopRecDatatable $dataTable)
    {
        return $dataTable->render('admin.pages.top-collaborators.list');
    }

    public function store(TopCollaboratorsRequest $request)
    {
//        dd($request->all());
        try {
            DB::beginTransaction();
            $data = $request->only([
                'rec_name',
                'amount',
                'increase_percent',
                'is_hot',
            ]);
            $this->topRecService->insert($data);
            DB::commit();
            return response()->json(['success' => 'Thành công'], 200);
        }catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['error' => $exception->getMessage()], $exception->getCode());
        }
    }

    public function update($id)
    {
        try {
            $this->topRecService->update($id,[],\request()->all());
            return response()->json(['success' => 'Thành công'], 200);
        }catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $this->topRecService->delete($id,[]);
            return response()->json(['success' => 'Xóa dữ liệu thành công']);
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

}
