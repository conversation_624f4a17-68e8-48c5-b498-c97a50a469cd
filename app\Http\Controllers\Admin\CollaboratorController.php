<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ChagePasswordRequest;
use App\Http\Requests\Admin\CollaboratorRequest;
use App\Services\Admin\BonusService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CollaboratorController extends Controller
{

    protected $userService;
    protected $submitCvService;
    protected $bonusService;

    public function __construct(UserService $userService,SubmitCvService $submitCvService, BonusService $bonusService)
    {
        $this->userService = $userService;
        $this->submitCvService = $submitCvService;
        $this->bonusService = $bonusService;
    }

    public function index(){
        $datatable = $this->userService->buildDatatableCollaborator();
        $total = $this->userService->totalCollaborator();
        $totalCurrentMonth = $this->userService->totalCollaboratorCurrentMonth();
        return view('admin.pages.collaborator.index', compact('datatable','total','totalCurrentMonth'));
    }

    public function datatable(Request $request)
    {
        $data = $this->userService->datatableCollaborator($request->all());
        return response($data);
    }

    public function edit($id)
    {
        $data = $this->userService->detailService($id);
        $datatable = $this->submitCvService->buildDatatabler($data->id);
        return view('admin.pages.collaborator.edit',compact('data','datatable'));
    }

    public function update(CollaboratorRequest $request,$id)
    {
        $this->userService->updateCollaborator($request->all(), $id);
        Toast::success(__('message.edit_success'));
        return redirect()->route('collaborator.edit',['collaborator'=>$id]);
    }

    public function changePassword(ChagePasswordRequest $request,$id)
    {
        $this->userService->changePasswordById($id,$request->password);
        Toast::success(__('message.change_password_success'));
        return redirect()->route('collaborator.edit',['collaborator'=>$id]);
    }

    public function submitCsvDatatable(Request $request,$userId)
    {
        $params = $request->all();
        $params['user_id'] = $userId;
        $data = $this->submitCvService->datatable($params);
        return response($data);
    }
    /*
     * Lich su doanh thu CTV
     */
    public function historyBonus($userId){
        $userDetail = $this->userService->detailService($userId);
        $datatable = $this->userService->buildDatatableCollaboratorBonus($userId);
        return view('admin.pages.collaborator.bonus', compact('datatable', 'userDetail'));
    }

    public function changeStatus($id){
        try {
            DB::beginTransaction();
            $this->bonusService->updateStatus($id);
            DB::commit();
            Toast::success(__('message.edit_success'));
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log update change status: ', [
                'content: ' => $e->getMessage()
            ]);

            return false;
        }
    }

    public function datatableBonus($userId, Request $request)
    {
        $data = $this->userService->datatableCollaboratorBonus($userId, $request->all());
        return response($data);
    }

}
