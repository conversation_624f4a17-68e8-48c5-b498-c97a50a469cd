<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_history_payment', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->comment('NTD');
            $table->integer('submit_cv_id');
            $table->tinyInteger('type')->default(0)->comment('0 trừ tiền, 1 hoàn tiền khi khiếu nại....');
            $table->string('bonus_type');
            $table->integer('percent')->default(0)->comment('10,90% type_of_sale onboard, 100%: type_of_sale interview, cv');
            $table->text('comment')->nullable();
            $table->integer('amount')->comment('Số tiền mỗi lần thanh toán');
            $table->integer('balance')->nullable()->comment('So du sau khi thanh toan, hoac <PERSON>an tien');
            $table->tinyInteger('status')->default(0)->comment('chỉ dùng: (type_of_sale = onboard && type = 0) => status = 0 trừ tiền và chưa hoàn, status = 1 trừ tiền và đã hoàn');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_history_payment');
    }
};
