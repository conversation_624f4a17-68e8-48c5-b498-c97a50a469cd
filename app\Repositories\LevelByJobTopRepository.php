<?php

namespace App\Repositories;

use App\Models\LevelByJobTop;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LevelByJobTopRepository extends BaseRepository
{
    const MODEL = LevelByJobTop::class;

    public function getByGroup($params)
    {
        $query = $this->query();

        if (isset($params['group'])) {
            $query->where('group', $params['group']);
        }

        return $query->get();
    }
}
