<?php

namespace App\Services\Admin;

use App\Repositories\InformationContactRepository;
use App\Services\FileServiceS3;

class InformationContactService
{

    protected $informationContactRepository;

    public function __construct(InformationContactRepository $informationContactRepository)
    {
        $this->informationContactRepository = $informationContactRepository;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'city_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Thành phố vi'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'city_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Thành phố en'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'address_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Địa chỉ vi'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'address_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Địa chỉ en'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'phone',
                    'data-orderable' => 'false'
                ),
                'value' => 'Số điện thoại'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('information-contacts-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->informationContactRepository->getListInformationContact($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function createService(array $request)
    {
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->informationContactRepository->create($request);
    }

    public function detailService($id)
    {
        return $this->informationContactRepository->find($id);
    }

    public function updateService(array $request, $id)
    {
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->informationContactRepository->update($id, [], $request);
    }

    public function getAllContact(){
        return $this->informationContactRepository->getAllContact();
    }

}
