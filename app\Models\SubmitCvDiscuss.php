<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubmitCvDiscuss extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'submit_cvs_discuss';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['date_hour'];
    protected static function booted()
    {
        static::created(function ($model) {
            $model->createDiscuss();
        });
    }
    public function submitCv()
    {
        return $this->belongsTo(SubmitCv::class, 'submit_cvs_id', 'id');
    }

    public function employer()
    {
        return $this->hasOne(User::class, 'id', 'ntd_id');
    }


    public function rec(){
        return $this->hasOne(User::class,'id','ctv_id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function createDiscuss() {
        $submitCv = $this->submitCv;
        if (!empty($this->ntd_id)) {
            $receiver = $submitCv->rec;
            $senderType = 'employer';
        } else {
            $receiver = $submitCv->employer;
            $senderType = 'rec';
        }
        
        $discuss = new Discuss();
        $discuss->object()->associate($submitCv);
        $discuss->sub_object()->associate($this);
        $discuss->sender_id   = !empty($this->ntd_id) ? $this->ntd_id : $this->ctv_id;
        $discuss->sender_type = $senderType;
        $discuss->receiver_id = $receiver ? $receiver->id : null;
        $discuss->message     = $this->comment;
        $discuss->is_read     = $this->unread ? 0 : 1;
        $discuss->created_at  = $this->created_at;
        $discuss->updated_at  = $this->updated_at;
        $discuss->save();
        return $discuss;
    }

}
