<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingBuy;
use App\Models\WareHouseCvSellingBuyHistoryStatus;

class WareHouseCvSellingBuyHistoryStatusRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingBuyHistoryStatus::class;

    public function logStatus(WareHouseCvSellingBuy $wareHouseCvSellingBuy, $user = null, $comment = null)
    {
        //$user null trong truong hop Ung vien Click vao xac nhan, tu choi, TUPA sẽ dùng
        $candidate_name = '';
        if (!empty($wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv)) {
            $candidate_name = $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        }
        //status_recruitment = 2 (từ chối), = 3 xác nhận => là trường hợp Ứng viên click link trong email
        return $this->create([
            'warehouse_cv_selling_buy_id'   => $wareHouseCvSellingBuy->id,
            'user_id'                       => !empty($user) ? $user->id : '',
            'candidate_name'                => $candidate_name,
            'type'                          => !empty($user) ? $user->type : '',
            'status_recruitment'            => !empty($wareHouseCvSellingBuy->status_recruitment) ? $wareHouseCvSellingBuy->status_recruitment : 0,
            'comment'                       => $comment,
            'authority'                     => $wareHouseCvSellingBuy->wareHouseCvSelling->authority,
        ]);
    }

    public function getLogStatusWithCvBuy($idCvBuy)
    {
        return $this->query()->where('warehouse_cv_selling_buy_id', $idCvBuy)->orderBy('id', 'asc')->get();
    }

    public function getLastHistoryByStatus($warehouseCvSellingBuyId,$statusRecruitment){
        return $this->query()->where('warehouse_cv_selling_buy_id', $warehouseCvSellingBuyId)->where('status_recruitment',$statusRecruitment)->first();
    }



}
