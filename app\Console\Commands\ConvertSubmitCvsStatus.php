<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ConvertSubmitCvsStatus extends Command
{
    protected $signature = 'convert:submit-cvs-status';
    protected $description = 'Convert old submit_cvs status to new status IDs';

    public function handle()
    {
        $oldStatusMap = [
            0 => 'pending-review',
            1 => 'accepted',
            2 => 'rejected',
            3 => 'pass-interview',
            4 => 'fail-interview',
            5 => 'onboarded',
            6 => 'cancel',
            7 => 'draft',
            8 => 'offering',
            9 => 'pending-confirm',
            10 => 'admin-review',
            11 => 'admin-rejected',
            12 => 'candidate-rejected',
        ];

        $newStatusMap = [
            'pending-review'     => 3,    // Waiting setup interview
            'accepted'           => 7,    // Waiting Interview
            'rejected'           => 2,    // Candidate Cancel Apply (closest match)
            'pass-interview'     => 8,    // Pass Interview
            'fail-interview'     => 10,   // Fail Interview
            'onboarded'          => 16,   // Success Recruitment
            'cancel'             => 2,    // Candidate Cancel Apply
            'draft'              => 1,    // Waiting candidate confirm (closest match)
            'offering'           => 11,   // Offering
            'pending-confirm'    => 1,    // Waiting candidate confirm
            'admin-review'       => 3,    // Waiting setup interview (closest match)
            'admin-rejected'     => 2,    // Candidate Cancel Apply (closest match)
            'candidate-rejected' => 2,    // Candidate Cancel Apply
        ];

        $this->info('Starting status conversion...');

        $count = 0;
        DB::table('submit_cvs')->orderBy('id')->chunk(100, function ($submitCvs) use ($oldStatusMap, $newStatusMap, &$count) {
            foreach ($submitCvs as $submitCv) {
                $oldStatus = $oldStatusMap[$submitCv->status] ?? null;
                if ($oldStatus && isset($newStatusMap[$oldStatus])) {
                    $newStatus = $newStatusMap[$oldStatus];
                    DB::table('submit_cvs')
                        ->where('id', $submitCv->id)
                        ->update(['status' => $newStatus]);
                    $this->info("Updated submit_cv ID {$submitCv->id} from {$oldStatus} to {$newStatus}");
                    $count++;
                }
            }
        });

        $this->info("Conversion completed. {$count} records updated.");
    }
}
