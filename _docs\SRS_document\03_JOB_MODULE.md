# Module <PERSON> dụ<PERSON> (<PERSON>le)

## 1. Tổ<PERSON> quan

Module Tuyển dụng là module quản lý toàn bộ quy trình đăng tin tuyển dụng, tìm kiếm việc làm và kết nối giữa nhà tuyển dụng với cộng tác viên/ứng viên.

## 2. <PERSON><PERSON><PERSON> năng <PERSON>h

### 2.1. <PERSON><PERSON><PERSON>n lý Tin Tuyển dụng

#### 2.1.1. Đă<PERSON> Tin Tuyển dụng (Employer)
- **Input Required**:
  - Tiêu đề công việc (*)
  - Vị trí tuyển dụng (*)
  - <PERSON><PERSON><PERSON> ngh<PERSON> (*) - chọn từ danh mục
  - C<PERSON><PERSON> bậc (*) - Junior/Middle/Senior/Manager
  - <PERSON><PERSON><PERSON> thứ<PERSON> làm việc (*) - Fulltime/Parttime/Remote
  - Đ<PERSON><PERSON> điể<PERSON> làm việc (*)
  - <PERSON><PERSON><PERSON> lương:
    - <PERSON><PERSON> định
    - <PERSON> (min-max)
    - Thỏa thuận
  - <PERSON><PERSON> lượng tuyển (*)
  - <PERSON><PERSON><PERSON> nộ<PERSON> hồ sơ (*)

- **Thông tin Chi tiết**:
  - Mô tả công việc
  - Yêu cầu ứng viên
  - Quyền lợi được hưởng
  - Thông tin liên hệ
  - Kỹ năng yêu cầu (tags)

- **Cấu hình Hoa hồng**:
  - Loại hoa hồng:
    - Cố định (VNĐ)
    - Phần trăm lương (%)
  - Mức hoa hồng cho CTV
  - Điều kiện nhận hoa hồng:
    - Ứng viên đi làm
    - Qua thử việc
    - Làm việc X tháng

- **Process**:
  1. Validate thông tin đầu vào
  2. Kiểm tra số dư ví (nếu có phí đăng tin)
  3. Tạo job với status = draft
  4. Admin duyệt tin
  5. Publish tin tuyển dụng
  6. Gửi notification cho CTV phù hợp

#### 2.1.2. Sửa/Xóa Tin Tuyển dụng
- **Điều kiện sửa**:
  - Chỉ sửa được khi tin chưa có người apply
  - Hoặc cần admin approve nếu đã có người apply

- **Điều kiện xóa**:
  - Soft delete
  - Giữ lại data cho báo cáo
  - Không thể xóa nếu có CV đang xử lý

#### 2.1.3. Gia hạn Tin Tuyển dụng
- **Options**:
  - Gia hạn 15 ngày
  - Gia hạn 30 ngày
  - Gia hạn 60 ngày
- **Phí gia hạn**: Tính theo gói

### 2.2. Tìm kiếm và Lọc Việc làm

#### 2.2.1. Search Engine
- **Tìm kiếm theo**:
  - Từ khóa (title, description)
  - Ngành nghề
  - Địa điểm
  - Mức lương
  - Kinh nghiệm
  - Hình thức làm việc

- **Full-text Search**:
  - Sử dụng MySQL FULLTEXT
  - Support tiếng Việt có dấu
  - Ranking by relevance

#### 2.2.2. Filter Options
- **Ngành nghề**: Multi-select từ categories
- **Địa điểm**: Tỉnh/thành phố
- **Mức lương**:
  - Dưới 10 triệu
  - 10-15 triệu
  - 15-25 triệu
  - 25-50 triệu
  - Trên 50 triệu
- **Kinh nghiệm**:
  - Không yêu cầu
  - Dưới 1 năm
  - 1-3 năm
  - 3-5 năm
  - Trên 5 năm
- **Cấp bậc**: Nhân viên/Trưởng nhóm/Quản lý/Giám đốc

#### 2.2.3. Sorting
- Mới nhất (default)
- Lương cao nhất
- Hoa hồng cao nhất
- Deadline gần nhất

### 2.3. Chi tiết Công việc

#### 2.3.1. Thông tin Hiển thị
- **Header**:
  - Logo công ty
  - Tên công việc
  - Tên công ty
  - Địa điểm
  - Mức lương
  - Hạn nộp

- **Body**:
  - Mô tả công việc
  - Yêu cầu ứng viên
  - Quyền lợi
  - Địa điểm làm việc
  - Thời gian làm việc

- **Sidebar**:
  - Thông tin công ty
  - Việc làm liên quan
  - Chia sẻ social

#### 2.3.2. Actions cho CTV
- **Giới thiệu ứng viên**: Button prominent
- **Lưu việc làm**: Để xem sau
- **Chia sẻ**: Social sharing
- **Báo cáo**: Nếu tin không phù hợp

### 2.4. Quản lý Tin Tuyển dụng (Employer)

#### 2.4.1. Dashboard
- **Metrics**:
  - Tổng tin đang đăng
  - Tổng lượt xem
  - Tổng CV nhận được
  - Tỷ lệ phù hợp

- **Charts**:
  - Lượt xem theo ngày
  - CV nhận theo nguồn
  - Hiệu quả từng tin

#### 2.4.2. Danh sách Tin
- **Columns**:
  - Tiêu đề
  - Trạng thái (Active/Expired/Draft)
  - Lượt xem
  - CV nhận
  - Ngày đăng
  - Hạn nộp
  - Actions

- **Bulk Actions**:
  - Gia hạn nhiều tin
  - Tạm dừng/Kích hoạt
  - Xuất báo cáo

#### 2.4.3. Duplicate Job
- Copy toàn bộ thông tin
- Cho phép chỉnh sửa trước khi đăng
- Useful cho vị trí tương tự

### 2.5. Job Matching và Recommendation

#### 2.5.1. Cho CTV
- **Gợi ý việc phù hợp**:
  - Dựa trên lĩnh vực quan tâm
  - Lịch sử giới thiệu
  - Kỹ năng của CV trong kho

- **Notification**:
  - Email việc mới phù hợp
  - Push notification (future)

#### 2.5.2. Cho Employer
- **Gợi ý CTV phù hợp**:
  - CTV có CV matching
  - CTV active trong ngành
  - Success rate cao

## 3. Trạng thái Tin Tuyển dụng

### 3.1. Job Status Flow
```
Draft -> Pending Review -> Active -> Expired/Closed
         |                    |
         v                    v
      Rejected            Paused
```

### 3.2. Status Details
- **Draft**: Đang soạn thảo
- **Pending Review**: Chờ admin duyệt
- **Active**: Đang tuyển dụng
- **Paused**: Tạm dừng tuyển
- **Expired**: Hết hạn
- **Closed**: Đã tuyển đủ
- **Rejected**: Admin từ chối

## 4. Database Schema

### 4.1. Bảng jobs
```sql
- id
- company_id
- employer_id (người đăng)
- title
- slug (for SEO)
- position
- category_id
- level (junior/middle/senior)
- job_type (fulltime/parttime/remote)
- location (json - multiple locations)
- salary_type (fixed/range/negotiable)
- salary_min
- salary_max
- quantity
- deadline
- description
- requirements
- benefits
- working_time
- skills (json array)
- commission_type (fixed/percentage)
- commission_value
- commission_condition
- status
- views_count
- submit_count
- is_hot
- is_urgent
- published_at
- expired_at
- created_at
- updated_at
```

### 4.2. Bảng job_meta
```sql
- id
- job_id
- meta_key
- meta_value
- created_at
- updated_at
```

### 4.3. Bảng job_logs
```sql
- id
- job_id
- user_id
- action (create/update/delete/view)
- data (json - changes)
- ip_address
- user_agent
- created_at
```

### 4.4. Bảng user_job_cares
```sql
- id
- user_id
- job_id
- created_at
```

### 4.5. Bảng job_views
```sql
- id
- job_id
- user_id (nullable)
- ip_address
- created_at
```

## 5. API Endpoints

### 5.1. Public APIs
- `GET /api/jobs` - Danh sách việc làm
- `GET /api/jobs/{slug}` - Chi tiết việc làm
- `GET /api/jobs/search` - Tìm kiếm việc làm
- `GET /api/jobs/categories` - Danh mục ngành nghề
- `GET /api/jobs/locations` - Danh sách địa điểm

### 5.2. Authenticated APIs (Employer)
- `POST /api/employer/jobs` - Tạo tin tuyển dụng
- `PUT /api/employer/jobs/{id}` - Cập nhật tin
- `DELETE /api/employer/jobs/{id}` - Xóa tin
- `POST /api/employer/jobs/{id}/extend` - Gia hạn tin
- `POST /api/employer/jobs/{id}/pause` - Tạm dừng tin
- `POST /api/employer/jobs/{id}/activate` - Kích hoạt lại
- `GET /api/employer/jobs/{id}/statistics` - Thống kê tin

### 5.3. Authenticated APIs (CTV)
- `POST /api/jobs/{id}/save` - Lưu việc làm
- `DELETE /api/jobs/{id}/save` - Bỏ lưu
- `GET /api/jobs/saved` - Danh sách đã lưu
- `GET /api/jobs/recommended` - Việc gợi ý

## 6. Business Rules

### 6.1. Posting Rules
- Mỗi công ty tối đa 50 tin active
- Tin tuyển dụng active tối đa 60 ngày
- Phải có ít nhất 1 skill tag
- Salary_max >= Salary_min * 1.2

### 6.2. Commission Rules
- Commission tối thiểu: 500,000 VNĐ
- Commission tối đa: 50% lương tháng đầu
- Có thể set điều kiện nhận commission

### 6.3. SEO Rules
- Auto generate slug từ title
- Meta description từ job description (first 160 chars)
- Structured data for Google Jobs

## 7. Performance Optimization

### 7.1. Caching
- Cache job list: 5 phút
- Cache job detail: 15 phút
- Cache categories: 1 giờ
- Clear cache khi update

### 7.2. Database Optimization
- Index on: company_id, category_id, status, deadline
- Fulltext index on: title, description
- Partition by created_at (monthly)

### 7.3. Query Optimization
- Eager loading relationships
- Pagination với cursor
- Limit fields trong API response

## 8. Integration Points

### 8.1. Notification Service
- Notify CTV khi có job mới phù hợp
- Notify employer khi có CV mới
- Reminder trước deadline

### 8.2. Analytics
- Track view source (direct/search/social)
- Conversion rate (view -> apply)
- Popular search keywords

### 8.3. External Job Boards
- Auto post to partner sites
- Import jobs từ client API
- Sync status changes

## 9. Mobile Considerations

### 9.1. Responsive Design
- Mobile-first approach
- Touch-friendly buttons
- Simplified forms for mobile

### 9.2. Mobile Features
- Swipe to save/skip jobs
- Location-based search
- Quick apply với saved CV

## 10. Future Enhancements

### 10.1. AI Features
- Job recommendation engine
- Auto-matching với CV
- Salary prediction

### 10.2. Advanced Features
- Video job descriptions
- Virtual job fairs
- Live chat với employer
- Job alert subscriptions
