<?php

namespace App\Helpers;

use App\Services\Frontend\UserJobCareService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Str;

class Common
{
    // Tạo hàm cắt bớt tên ví dụ "<PERSON><PERSON><PERSON>" sẽ đổi thành "B.T Nga"
    public static function  cutName($fullName, $addSpaceBeforeLastName = true)
    {
        $nameParts = array_filter(explode(' ', trim($fullName)));

        if (empty($nameParts)) {
            return '';
        }

        $result = '';
        $totalParts = count($nameParts);

        for ($i = 0; $i < $totalParts; $i++) {
            if ($i == $totalParts - 1) {
                // Thêm khoảng trắng trước tên nếu cần
                if ($addSpaceBeforeLastName && $i > 0) {
                    $result .= ' ';
                }
                $result .= $nameParts[$i];
            } else {
                $result .= mb_strtoupper(mb_substr($nameParts[$i], 0, 1, 'UTF-8')) . '.';
            }
        }

        return $result;
    }

    public static function cleanPhone($value, $country_code = '')
    {
        $tel = preg_replace('/[^0-9]/', '', $value);
        $tel = ltrim($tel, '0');
        if (trim($country_code) != '') {
            $len_country = strlen($country_code);
            if (substr($tel, 0, $len_country) == $country_code && strlen($tel) > 10) {
                $tel = substr($tel, $len_country);
            }
            $tel = $country_code . $tel;
            return $tel;
        } else {
            if (substr($tel, 0, 2) == '84' && strlen($tel) > 10) {
                $tel = substr($tel, 2);
            }
            return '0' . $tel;
        }
    }
    public static function getTenNumberPhone($phone)
    {
        $change_array = array(
            array('84', '0'),
            array('+84', '0'),
            array('016', '03'),
            array('0120', '070'),
            array('0121', '079'),
            array('0122', '077'),
            array('0126', '076'),
            array('0128', '078'),
            array('0123', '083'),
            array('0124', '084'),
            array('0125', '085'),
            array('0127', '081'),
            array('0129', '082'),
            array('0188', '058'),
            array('0186', '056'),
            array('0199', '059')
        ); //

        foreach ($change_array as $key => $value) {
            if (Str::startsWith($phone, $value[0])) {
                $phone = Str::replaceFirst($value[0], $value[1], $phone);
                break;
            }
        }
        return $phone;
    }
    public static function checkFormatDate($strDate, $format = 'Y-m-d H:i:s')
    {
        $d = \DateTime::createFromFormat($format, $strDate);
        return $d && $d->format($format) === $strDate;
    }

    public static function renderAttribute(array $array)
    {
        $attributeStrings = [];

        foreach ($array as $attribute => $value) {
            if ($value === '') {
                $attributeStrings[] = $attribute;

                continue;
            }
            $value = htmlentities($value, ENT_QUOTES, 'UTF-8', false);

            $attributeStrings[] = "{$attribute}={$value}";
        }
        return implode(' ', $attributeStrings);
    }

    public static function buildSlug($string)
    {
        return Str::slug($string) . '-' . Str::random(8);
    }

    public static function getCities()
    {
        $cities = [];
        foreach (config('constant.cities') as $city) {
            $cities[Str::slug($city)] = $city;
        }
        return $cities;
    }

    public static function checkUserJobCare($userId, $jobId, $type)
    {
        $userJobCareService = app(UserJobCareService::class);
        return $userJobCareService->checkUserJobCare($userId, $jobId, $type);
    }

    public static function getJobType($key)
    {
        $lang = Lang::locale();
        $str = 'type.' . $lang . '.' . $key;
        return config('job.' . $str);
    }

    public static function formatNumber($number)
    {
        $number = number_format($number);
        return str_replace(',', '.', $number);
    }

    public static function formatDate($date)
    {
        $lang = Lang::locale();
        $dateNew = Carbon::parse($date)->locale($lang);
        return $dateNew->isoFormat('Do MMMM YYYY');
    }

    public static function transLang($key, $replace = [])
    {
        if (!$replace) {
            return $key;
        }

        $str = $newStr = $key;

        if (is_array($str)) {
            foreach ($str as $k => $item) {
                $kNew = str_replace('.textarea', '', $item);
                $newStr = $kNew;
            }
        }

        foreach ($replace as $k => $v) {
            $newStr = str_replace(':' . $k, $v, $newStr);
        }

        return $newStr;
    }

    public static function fullTextWildcards($term)
    {
        // removing symbols used by MySQL
        $reservedSymbols = ['-', '+', '<', '>', '@', '(', ')', '~'];
        $term = str_replace($reservedSymbols, '', $term);

        $words = explode(' ', $term);

        foreach ($words as $key => $word) {
            /*
             * applying + operator (required word) only big words
             * because smaller ones are not indexed by mysql
             */
            if (strlen($word) >= 1) {
                $words[$key] = '+' . $word . '*';
            }
        }

        return implode(' ', $words);
    }

    public static function convertNumber($number = 0, $rate = 'VND')
    {
        if ($rate == 'VND') {
            $number = $number / 1000000;
        } elseif ($rate == 'USD') {
            $number = $number / 1000;
        }
        return $number;
    }

    public static function subStringContent($content, $limit)
    {
        $content = strip_tags($content);
        $excerpt = explode(' ', $content, $limit);
        if (count($excerpt) >= $limit) {
            array_pop($excerpt);
            $excerpt = implode(" ", $excerpt) . '[...]';
        } else {
            $excerpt = implode(" ", $excerpt);
        }
        $excerpt = preg_replace('`\[[^\]]*\]`', '', $excerpt);
        return $excerpt . '...';
    }

    public static function convertArray(array $array = [])
    {
        $arrayNew = [];
        foreach ($array as $key => $value) {
            $arrayNew[] = [
                'id'   => $key,
                'text' => $value,
            ];
        }
        return $arrayNew;
    }
}
