---
description:
globs:
alwaysApply: false
---
# Warehouse CV Guide

Warehouse CV là tính năng chính của RecLand, cho phép lưu trữ và quản lý CV của ứng viên.

## Cấu trúc dữ liệu

- [ResumeWareHouse](mdc:app/Models/ResumeWareHouse.php) - Model chính quản lý warehouse CV
- [ResumeVersion](mdc:app/Models/ResumeVersion.php) - Quản lý các phiên bản của CV
- [ResumeSection](mdc:app/Models/ResumeSection.php) - <PERSON><PERSON><PERSON> phần trong CV (education, experience, etc.)

## Controllers

- [ResumeWareHouseController](mdc:app/Http/Controllers/ResumeWareHouseController.php) - Quản lý warehouse CV
- [ResumeVersionController](mdc:app/Http/Controllers/ResumeVersionController.php) - Quản lý phiên bản CV
- [Resume/EducationController](mdc:app/Http/Controllers/Resume/EducationController.php) - Quản lý phần học vấn
- [Resume/ExperienceController](mdc:app/Http/Controllers/Resume/ExperienceController.php) - Quản lý phần kinh nghiệm

## Luồng xử lý

1. Ứng viên tạo CV mới hoặc upload CV có sẵn
2. Hệ thống phân tích CV (nếu upload) và tạo các sections
3. Ứng viên chỉnh sửa các sections của CV
4. Hệ thống tự động lưu phiên bản mỗi khi có thay đổi lớn
5. Ứng viên có thể xuất CV theo nhiều định dạng (PDF, DOCX)
6. Ứng viên có thể chia sẻ CV với nhà tuyển dụng

## Tính năng chính

- Quản lý nhiều CV cho một ứng viên
- Theo dõi lịch sử thay đổi CV
- Phân tích và đề xuất cải thiện CV
- Matching CV với công việc phù hợp
- Xuất CV theo nhiều template khác nhau
