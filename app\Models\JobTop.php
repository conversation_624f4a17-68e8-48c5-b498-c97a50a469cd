<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class JobTop extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'job_tops';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = [
        'is_it',
    ];

    public function getIsItAttribute()
    {
        return false;
    }
}
