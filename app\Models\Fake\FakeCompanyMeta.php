<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeCompanyMeta extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'company_metas';

    protected $fillable = [
        'company_id',
        'company_type',
        'is_top_brand',
        'is_top_company',
        'website',
        'youtube_id',
        'avg_rating',
    ];

    public function company()
    {
        return $this->belongsTo(FakeCompany::class, 'company_id', 'id');
    }
}
