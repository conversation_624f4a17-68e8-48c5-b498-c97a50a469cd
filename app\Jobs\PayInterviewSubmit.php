<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\SubmitCvService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PayInterviewSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        //sau 24h thi Thanh toan tien tra CTV -> interview
        //pass, fail thi hoan tien
        if ($submitCv->status == 8 || $submitCv->status == 10  &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5) ) {

            $submitCvService = resolve(SubmitCvService::class);
            $submitCvService->payCtv($submitCv);
        }
    }




}
