<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\Frontend\BannerService;
use App\Services\Frontend\CategoryService;
use App\Services\Frontend\PostService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PostController extends Controller
{
    protected $postService;
    protected $categoryService;
    protected $bannerService;
    protected $settingService;
    protected $seoService;
    protected $routeName;

    public function __construct(
        PostService     $postService,
        CategoryService $categoryService,
        BannerService   $bannerService,
        SettingService  $settingService,
        SeoService      $seoService
    )
    {
        $this->postService = $postService;
        $this->categoryService = $categoryService;
        $this->bannerService = $bannerService;
        $this->settingService = $settingService;
        $this->seoService = $seoService;
        $this->routeName = \Route::currentRouteName();
    }

    public function index(Request $request)
    {
        $lang = app()->getLocale();
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        $this->seoService->getConfig($this->routeName, $lang);
        //get post hot
        $postHots = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-left')), config('constant.type_post.post'), $arrSettingGlobal['setting.global.limit_slider_post_left_most_recent']);

        //get post center
        $banners = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-center')), config('constant.type_post.post'), 1);

        $postRight = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-right')), config('constant.type_post.post'), 2);

        //get 2 category hien thi trang chu Blog
        $categories = $this->categoryService->getListHome(5);
        if (count($categories) > 0) {
            foreach ($categories as $key => $value) {
                $postTmp = $this->postService->getPostByPositonCategory($value['id'], null, config('constant.type_post.post'), 4);
                $categories[$key]['posts'] = $postTmp;
            }
        }
        $postRecentNew = $this->postService->getPostNew($arrSettingGlobal['setting.global.limit_recent_news']);
        return view('frontend.pages.blog.home', compact(
            'postHots',
            'banners',
            'categories',
            'postRight',
            'postRecentNew'
        ));
    }

    public function subCategory(Request $request)
    {
        $categories = $this->categoryService->getListHome(5);
        if (count($categories) > 0) {
            foreach ($categories as $key => $value) {
                $postTmp = $this->postService->getPostByPositonCategory($value['id'], strtolower(config('constant.position_meta.home-center-by-category')), config('constant.type_post.post'), 4);
                $categories[$key]['posts'] = $postTmp;
            }
        }
//        $category = $this->categoryService->findBySlug($slug);
        return view('frontend.inc_layouts.v2.home_header');
    }

    public function detail(Request $request, $slug)
    {
        $lang = \Lang::locale();
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        $data = $this->postService->detailPost(['lang' => $lang, 'slug' => $slug]);

        //get post right
        $postRight = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-right')), config('constant.type_post.post'), 2);

        //Recent new
        $postRecentNew = $this->postService->getRandom($request->all(), $arrSettingGlobal['setting.global.limit_recent_news']);

        //get config seo
        $postSeo = $data->postSeo;
        if ($postSeo) {
            $this->seoService->getConfig($postSeo, $lang, true);
        }

        $dataPrev = $this->postService->detailPost([], ['search' => 'max', 'id' => $data->id]);

        $dataNext = $this->postService->detailPost([], ['search' => 'min', 'id' => $data->id]);

        //Most poplular
        $postMostPoplular = $this->postService->getPostByView($arrSettingGlobal['setting.global.limit_most_poplular']);
        $postMostRandom = $this->postService->getPostRandom($arrSettingGlobal['setting.global.limit_most_recent']);

        //get banner Advertisement
        $bannerAdvertisement = $this->bannerService->getListByType('', strtolower(config('constant.position_banner.home-post-advertisement')), 10);
        $arrBannerAdvertisement = [];
        foreach ($bannerAdvertisement as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBannerAdvertisement, $item->image_url_vn);
            } else {
                array_push($arrBannerAdvertisement, $item->image_url_en);
            }
        }

        //get banner right
        $bannerRight = $this->bannerService->getListByType('', strtolower(config('constant.position_banner.home-post-right')), 2);
        $arrBannerRight = [];
        foreach ($bannerRight as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBannerRight, $item->image_url_vn);
            } else {
                array_push($arrBannerRight, $item->image_url_en);
            }
        }

//dd($dataNext);
        return view('frontend.pages.blog.detail', compact(
            'data',
            'arrBannerAdvertisement',
            'postMostPoplular',
            'arrBannerRight',
            'postMostRandom',
            'dataPrev',
            'dataNext',
            'postRight',
            'postRecentNew',
        ));
    }

    public function listPosts(Request $request,$slug = null){
        $data = $request->all();
        $lang = app()->getLocale();
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        $this->seoService->getConfig($this->routeName, $lang);
        //get post hot
        $postHots = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-left')), config('constant.type_post.post'), $arrSettingGlobal['setting.global.limit_slider_post_left_most_recent']);

        $category = $this->categoryService->findBySlug($slug);
        if ($category){
            $data['category_id'] = $category->id;
            $data['size'] = 10;
        }
        //get post center
        $banners = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-center')), config('constant.type_post.post'), 1);

        $postRight = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-right')), config('constant.type_post.post'), 2);

        $posts =  $this->postService->getPostPaginate($data);
        $postRecentNew = $this->postService->getPostNew($arrSettingGlobal['setting.global.limit_recent_news']);
        return view('frontend.pages.blog.list', compact(
            'postHots',
            'banners',
            'posts',
            'postRight',
            'postRecentNew'
        ));
    }

    public function searchBlog(Request $request)
    {
        $keyword = $request->input('keyword');

        $results = $this->postService->getKeySearchPost($keyword);

        if ($results){
            return redirect()->route('blog-list-search', ['keyword' => $keyword])
                ->with(['results' => $results, 'status' => 'Tìm kiếm thành công!']);
        }else{
            return redirect()->back()->with('error', 'Không tìm thấy kết quả nào.');
        }
    }

    public function blogListSearch($keyword)
    {
        $banners = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-center')), config('constant.type_post.post'), 1);
        $postRight = $this->postService->getPostByPositon(strtolower(config('constant.position_meta.home-right')), config('constant.type_post.post'), 2);
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        $postRecentNew = $this->postService->getPostNew($arrSettingGlobal['setting.global.limit_recent_news']);


        $results = session('results');

        return view('frontend.pages.blog.list-search', compact('keyword', 'results', 'banners', 'postRight', 'postRecentNew'));
    }



}
