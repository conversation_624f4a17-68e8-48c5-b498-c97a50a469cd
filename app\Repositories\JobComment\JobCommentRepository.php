<?php

namespace App\Repositories\JobComment;

use App\Models\JobComment;
use App\Models\Job;
use App\Models\User;
use App\Mail\NewJobComment;
use App\Mail\NewJobCommentReply;
use Illuminate\Support\Facades\Mail;

class JobCommentRepository implements JobCommentRepositoryInterface
{
    public function create(array $data)
    {
        $comment = JobComment::create($data);
        
        // Lấy thông tin job và người comment
        $job = Job::findOrFail($data['job_id']);
        $commenter = User::findOrFail($data['user_id']);
        
        if (isset($data['parent_id'])) {
            // <PERSON><PERSON><PERSON> là một reply
            $parentComment = JobComment::findOrFail($data['parent_id']);
            
            // Gửi mail cho người comment gốc nếu người reply không phải chính họ
            if ($parentComment->user_id !== $data['user_id']) {
                Mail::to($parentComment->user->email)
                    ->queue(new NewJobCommentReply($comment, $parentComment, $job, $commenter));
            }
        } else {
            // Đ<PERSON>y là comment gốc - giữ nguyên logic cũ
            if ($job->user_id !== $data['user_id']) {
                Mail::to($job->user->email)
                    ->queue(new NewJobComment($comment, $job, $commenter));
            }
        }
        
        return $comment;
    }

    public function getCommentsByJobId($jobId)
    {
        return JobComment::with(['user:id,type,name,referral_define,company_id', 'replies.user:id,type,name,referral_define,company_id'])
            ->where('job_id', $jobId)
            ->whereNull('parent_id')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($comment) {
                $user = $comment->user;
                if ($user->type == config('constant.role.employer')) {
                    $company = $user->company;
                    $user->name = $company->name;
                } elseif ($user->type == config('constant.role.rec')) {
                    $user->name = 'CTV ' . $user->referral_define;
                } elseif ($user->type == config('constant.role.admin')) {
                    $user->name = 'Admin';
                }
                return [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'user_name' => $user->name,
                    'created_at' => $comment->created_at,
                    'replies' => $comment->replies->map(function ($reply) {
                        $user = $reply->user;
                        if ($user->type == config('constant.role.employer')) {
                            $company = $user->company;
                            $user->name = $company->name;
                        } elseif ($user->type == config('constant.role.rec')) {
                            $user->name = 'CTV ' . $user->referral_define;
                        } elseif ($user->type == config('constant.role.admin')) {
                            $user->name = 'Admin';
                        }
                        return [
                            'id' => $reply->id,
                            'content' => $reply->content,
                            'user_name' => $user->name,
                            'created_at' => $reply->created_at,
                            'parent_id' => $reply->parent_id
                        ];
                    })
                ];
            });
    }

    public function delete($id)
    {
        return JobComment::findOrFail($id)->delete();
    }
}
