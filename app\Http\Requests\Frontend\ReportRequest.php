<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class ReportRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type_issue' => 'required',
            'description' => 'required',
//            'file' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
        ];
    }

}
