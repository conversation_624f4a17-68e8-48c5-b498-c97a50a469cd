<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('top_recs', function (Blueprint $table) {
            $table->id();
            $table->string('rec_name', 255)->comment('ten ctv');
            $table->integer('amount')->default(0)->comment('Doanh thu thang');
            $table->integer('increase_percent')->nullable()->default(0)->comment('Phan tram tang so voi thang truoc');
            $table->tinyInteger('is_hot')->default(0)->comment('Bieu tuong hot');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('top_recs');
    }
};
