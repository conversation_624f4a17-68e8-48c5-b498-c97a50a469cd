---
description:
globs:
alwaysApply: false
---
# Service & Repository Guide

RecLand sử dụng Service và Repository pattern để tách biệt business logic và data access.

## Services

Services chứa business logic của ứng dụng:

- [JobService](mdc:app/Services/JobService.php) - Xử lý logic liên quan đến công việc
- [ResumeService](mdc:app/Services/ResumeService.php) - Xử lý logic liên quan đến CV
- [UserService](mdc:app/Services/UserService.php) - Xử lý logic liên quan đến người dùng
- [PaymentService](mdc:app/Services/PaymentService.php) - Xử lý logic thanh toán
- [MatchingService](mdc:app/Services/MatchingService.php) - Xử lý matching giữa CV và công việc

## Repositories

Repositories xử lý tương tác với database:

- [JobRepository](mdc:app/Repositories/JobRepository.php) - Tương tác với bảng jobs
- [ResumeRepository](mdc:app/Repositories/ResumeRepository.php) - Tương tác với bảng resumes
- [UserRepository](mdc:app/Repositories/UserRepository.php) - Tương tác với bảng users
- [CompanyRepository](mdc:app/Repositories/CompanyRepository.php) - Tương tác với bảng companies

## Interfaces

Mỗi repository có một interface tương ứng:

- [JobRepositoryInterface](mdc:app/Repositories/Interfaces/JobRepositoryInterface.php)
- [ResumeRepositoryInterface](mdc:app/Repositories/Interfaces/ResumeRepositoryInterface.php)
- [UserRepositoryInterface](mdc:app/Repositories/Interfaces/UserRepositoryInterface.php)

## Service Provider

Các service và repository được đăng ký trong [AppServiceProvider](mdc:app/Providers/AppServiceProvider.php):

```php
public function register()
{
    $this->app->bind(
        \App\Repositories\Interfaces\UserRepositoryInterface::class,
        \App\Repositories\UserRepository::class
    );
    
    $this->app->bind(
        \App\Repositories\Interfaces\JobRepositoryInterface::class,
        \App\Repositories\JobRepository::class
    );
    
    // ... other bindings
}
```

## Workflow

Luồng xử lý tiêu chuẩn:

1. Controller nhận request
2. Controller gọi đến Service tương ứng
3. Service sử dụng các Repository để tương tác với database
4. Service xử lý business logic
5. Service trả kết quả về Controller
6. Controller trả response về cho user
