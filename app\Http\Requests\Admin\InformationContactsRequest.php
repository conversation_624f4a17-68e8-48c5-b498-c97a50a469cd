<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class InformationContactsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'city_vi' => 'required',
            'city_en' => 'required',
            'address_vi' => 'required',
            'address_en' => 'required',
            'phone' => 'required',
            'google_map' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'website.regex' => __('message.website_regex'),
        ];
    }
}
