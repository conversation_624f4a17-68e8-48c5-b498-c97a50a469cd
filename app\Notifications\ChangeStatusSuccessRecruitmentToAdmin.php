<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusSuccessRecruitmentToAdmin extends Mailable
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function content(): Content
    {
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $url = route('luot-ban.show',['luot_ban' => $this->wareHouseCvSellingBuy->id]);
        return new Content(
            view: 'email.changeStatusSuccessRecruitmentToAdmin',
            with: [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'url' => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $message->subject('[Recland]Thông báo thử việc thành công Ứng viên'.$candidateName.' vị trí '.$position.' tại Công ty  '.$companyName);
        return $this;
    }


}

