<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToEmployer extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $point;

    public function __construct($wareHouseCvSellingBuy,$point)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->point = $point;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $url = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);
        $employerWallet = route('employer-wallet');
        $linkMarket = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusCancelOnboardToEmployer', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'url' => $url,
                'employerWallet' => $employerWallet,
                'linkMarket' => $linkMarket,
                'point' => $this->point,
            ])
            ->subject('[Recland] Thông báo ứng viên '.$candidateName.' đã “Cancel Onboard” vị trí '.$position);
    }

}
