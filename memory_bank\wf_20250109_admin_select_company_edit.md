# Workflow: Thê<PERSON> chức năng chọn admin trong trang edit company

## Ng<PERSON><PERSON> thực hiện: 2025-01-09

## M<PERSON><PERSON> tiêu

Thêm chức năng cho phép chọn admin (có quyền company.rotation) trong trang edit company để gán admin vào company đó.

## Các tác vụ đã thực hiện

### 1. <PERSON><PERSON><PERSON> nhật CompanyController

-   **File**: `app/Http/Controllers/Admin/CompanyController.php`
-   **Thay đổi**:
    -   Thêm import `use App\Models\User;`
    -   Thêm method `getAdminsWithCompanyRotationPermission()` để lấy danh sách admins có quyền company.rotation
    -   Thêm method `hasCompanyRotationPermission()` để kiểm tra quyền
    -   Cập nhật method `edit()` để truyền biến `$admins` vào view

### 2. C<PERSON><PERSON> nhật View Edit Company

-   **File**: `resources/views/admin/pages/company/edit.blade.php`
-   **Thay đổi**:
    -   Thêm field select `admin_id` với danh sách admins
    -   Thêm validation display cho field admin_id
    -   Thêm helper text cho field

### 3. Cập nhật CompanyRequest

-   **File**: `app/Http/Requests/Admin/CompanyRequest.php`
-   **Thay đổi**:
    -   Thêm validation rule `'admin_id' => 'nullable|exists:users,id'` cho cả POST và PUT methods

### 4. Cập nhật CompanyService

-   **File**: `app/Services/Admin/CompanyService.php`
-   **Thay đổi**:
    -   Thêm xử lý `admin_id` trong method `create()` - set null nếu empty
    -   Thêm xử lý `admin_id` trong method `update()` - set null nếu empty
    -   Thêm column `admin_name` vào datatable để hiển thị

### 5. Cập nhật CompanyRepository

-   **File**: `app/Repositories/CompanyRepository.php`
-   **Thay đổi**:
    -   Thêm `->with('admin')` vào method `getListCompany()` để load relationship admin

### 6. Cập nhật JavaScript Datatable

-   **File**: `public/backend/assets/js/datatable-custom.js`
-   **Thay đổi**:
    -   Thêm function `renderAdminName()` để render tên admin trong datatable
    -   Hiển thị badge info nếu có admin, text muted nếu chưa có

### 7. Cập nhật Model Company

-   **File**: `app/Models/Company.php`
-   **Thay đổi**:
    -   Thêm accessor `getAdminNameAttribute()` để lấy tên admin

## Chức năng mới

-   **Trang edit company**: Có field select để chọn admin từ danh sách admins có quyền company.rotation
-   **Danh sách company**: Hiển thị cột "Admin quản lý" với tên admin được gán
-   **Validation**: Đảm bảo admin_id phải tồn tại trong bảng users
-   **Xử lý null**: Cho phép admin_id = null (không bắt buộc phải có admin)

## Cách sử dụng

1. Vào trang edit company từ danh sách companies
2. Chọn admin từ dropdown "Admin quản lý"
3. Lưu thay đổi
4. Admin được gán sẽ hiển thị trong danh sách companies

## Test

-   Kiểm tra dropdown chỉ hiển thị admins có quyền company.rotation
-   Kiểm tra có thể chọn admin và lưu thành công
-   Kiểm tra có thể bỏ chọn admin (set null)
-   Kiểm tra hiển thị tên admin trong datatable

## Ghi chú

-   Chức năng này bổ sung cho command assign admin tự động đã tạo trước đó
-   Admin có thể được gán thủ công qua giao diện hoặc tự động qua command
-   Quyền company.rotation được kiểm tra để đảm bảo chỉ admin có thẩm quyền mới được gán
