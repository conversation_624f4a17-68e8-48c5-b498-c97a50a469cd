<?php

namespace App\Rules\Admin;

use Carbon\Carbon;
use Illuminate\Contracts\Validation\Rule;

class CheckDateBeforeNowRule implements Rule
{
    protected $id;
    protected $type;
    protected $isExists;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        try {
            $now = Carbon::now();
            $date = Carbon::createFromFormat('d/m/Y',$value)->startOfDay();
            return $date->timestamp < $now->timestamp;
        }catch (\Exception $exception){
            return false;
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('frontend/validation.before_now');
    }
}
