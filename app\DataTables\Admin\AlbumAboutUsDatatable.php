<?php

namespace App\DataTables\Admin;

use App\Services\Admin\AboutUsAlbumService;
use App\Traits\DataTableTrait;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class AlbumAboutUsDatatable extends DataTable
{
    use DataTableTrait;

    protected $aboutUsAlbumService;

    public function __construct(AboutUsAlbumService $aboutUsAlbumService)
    {
        $this->aboutUsAlbumService = $aboutUsAlbumService;
    }
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('action', function ($item) {

                return view('admin.pages.album-aboutUs.action', compact('item'))->render();
            });
    }

//    /**
//     * Get query source of dataTable.
//     *
//     * @param \App\Models\Admin\AlbumAboutUsDatatable $model
//     * @return \Illuminate\Database\Eloquent\Builder
//     */
    public function query()
    {
        $param = request()->only(['name']);
        $param['authorized'] = true;
        return $this->aboutUsAlbumService->indexService($param);
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
                    ->setTableId('albumaboutus-datatable')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->dom('Bfrtip')
                    ->orderBy(1);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            'id' =>['title' => 'ID'],
            'name' => ['title' => 'Tên Album'],
            'action' => ['title' => 'Hành động'],
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Admin\AlbumAboutUs_' . date('YmdHis');
    }
}
