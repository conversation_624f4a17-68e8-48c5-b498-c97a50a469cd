<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentOpenTurnCvSubmit extends Notification implements ShouldQueue
{
    use Queueable;


    protected $submitCv;
    protected $point;

    public function __construct($submitCv,$point)
    {
        $this->submitCv = $submitCv;
        $this->point = $point;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->submitCv->user->name;
        $recName = $this->submitCv->employer->name;
        $companyName = $this->submitCv->user->company_name;
        $statusRecruitment = $this->submitCv->status_recruitment_value;
        $url = route('rec-cv-sold',['cv_sold' =>  $this->submitCv->id]);
        $recTurnovers = route('rec-turnovers');
        $buyDate = $this->submitCv->created_at->format('d/m/Y');
        return (new MailMessage)
            ->view('email.paymentOpenTurnCvSubmit', [
                'candidateName' => $candidateName,
                'recName' => $recName,
                'companyName' => $companyName,
                'url' => $url,
                'point' => $this->point,
                'recTurnovers' => $recTurnovers,
                'statusRecruitment' => $statusRecruitment,
                'buyDate' => $buyDate,
            ])
            ->subject('[RECLAND] Thanh toán thành công submit CV');

    }

}

