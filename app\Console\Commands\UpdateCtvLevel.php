<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\SubmitCv;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateCtvLevel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:update-ctv-level';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật level cho user dựa trên số lần giới thiệu ứng viên từ tháng 6/2025';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Batch UpdateCtvLevel Start');
        $this->info('Bắt đầu cập nhật level...');

        try {
            // Lấy thời gian từ tháng 6/2025
            $startDate = Carbon::create(2025, 5, 1)->startOfMonth();
            $currentDate = Carbon::now()->subMonth()->endOfMonth();

            $this->info("Xử lý dữ liệu từ: {$startDate->format('d/m/Y')} đến: {$currentDate->format('d/m/Y')}");

            // Lấy tất cả user có submit_cvs từ tháng 6/2025
            $submitCvData = SubmitCv::selectRaw('
                    user_id,
                    YEAR(created_at) as year,
                    MONTH(created_at) as month,
                    COUNT(*) as total_submit
                ')
                ->where('created_at', '>=', $startDate)
                ->where('user_id', '>', 0)
                ->groupBy('user_id', 'year', 'month')
                ->orderBy('user_id')
                ->orderBy('year')
                ->orderBy('month')
                ->get();

            // Nhóm dữ liệu theo user_id
            $userSubmitData = [];
            foreach ($submitCvData as $data) {
                $userId = $data->user_id;
                $monthKey = $data->year . '-' . str_pad($data->month, 2, '0', STR_PAD_LEFT);

                if (!isset($userSubmitData[$userId])) {
                    $userSubmitData[$userId] = [];
                }

                $userSubmitData[$userId][$monthKey] = $data->total_submit;
            }

            $this->info("Tìm thấy " . count($userSubmitData) . " user có submit CV từ tháng 6/2025");

            $qualifiedUsers = 0;
            $disqualifiedUsers = 0;
            $totalUsersLevel0 = 0;

            // Kiểm tra từng user
            foreach ($userSubmitData as $userId => $monthlyData) {
                $user = User::find($userId);
                if (!$user) {
                    continue;
                }

                $isQualified = $this->checkUserQualification($monthlyData, $startDate, $currentDate);

                if ($isQualified) {
                    // Set level = 1
                    $user->level = 1;
                    $user->save();
                    $qualifiedUsers++;
                    $this->line("✓ User ID {$userId} ({$user->name}) đạt điều kiện level = 1");
                } else {
                    // Set level = 0
                    if ($user->level != 0) {
                        $user->level = 0;
                        $user->save();
                        $disqualifiedUsers++;
                        $this->line("✗ User ID {$userId} ({$user->name}) không đạt điều kiện, đã set level = 0");
                    }
                    $totalUsersLevel0++;
                }
            }

            $this->info("Hoàn thành!");
            $this->info("- Số user đạt điều kiện (level = 1): {$qualifiedUsers}");
            $this->info("- Số user không đạt điều kiện (level = 0): {$disqualifiedUsers}");
            $this->info("- Số user có level = 0: {$totalUsersLevel0}");
            Log::info("Batch UpdateCtvLevel End - Qualified: {$qualifiedUsers}, Disqualified: {$disqualifiedUsers}");
        } catch (\Exception $e) {
            $this->error('Lỗi xảy ra: ' . $e->getMessage());
            Log::error('Batch UpdateCtvLevel Error: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return 1;
        }

        return 0;
    }

    /**
     * Kiểm tra user có đạt điều kiện level không
     *
     * @param array $monthlyData Dữ liệu submit theo tháng của user
     * @param Carbon $startDate Ngày bắt đầu (tháng 6/2025)
     * @param Carbon $currentDate Ngày hiện tại
     * @return bool
     */
    private function checkUserQualification(array $monthlyData, Carbon $startDate, Carbon $currentDate): bool
    {
        // Tìm tháng đầu tiên có >= 20 lần submit
        $qualifyingMonth = null;
        $tempDate = $startDate->copy();

        while ($tempDate->lte($currentDate)) {
            $monthKey = $tempDate->format('Y-m');
            $submitCount = $monthlyData[$monthKey] ?? 0;

            if ($submitCount >= 20) {
                $qualifyingMonth = $tempDate->copy();
                break;
            }

            $tempDate->addMonth();
        }

        // Nếu không có tháng nào >= 20 lần submit
        if (!$qualifyingMonth) {
            return false;
        }

        // Kiểm tra từ tháng tiếp theo sau qualifying month đến tháng hiện tại
        // Tất cả các tháng phải có >= 10 lần submit
        $checkDate = $qualifyingMonth->copy()->addMonth();

        while ($checkDate->lte($currentDate)) {
            $monthKey = $checkDate->format('Y-m');
            $submitCount = $monthlyData[$monthKey] ?? 0;

            if ($submitCount < 10) {
                return false;
            }

            $checkDate->addMonth();
        }

        return true;
    }
}
