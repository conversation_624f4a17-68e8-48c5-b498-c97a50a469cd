<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailIInterviewToAdmin extends Mailable
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function content(): Content
    {
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $companyName = $this->wareHouseCvSellingBuy->employer->company->name;
        $url = route('luot-ban.show',['luot_ban' => $this->wareHouseCvSellingBuy->id]);
        return new Content(
            view: 'email.changeStatusFailIInterviewToAdmin',
            with: [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'url' => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->company->name;
        $message->subject('[Recland] Thông báo Ứng viên '.$candidateName.' đã Fail phỏng vấn của công ty '.$companyName);
        return $this;
    }


}

