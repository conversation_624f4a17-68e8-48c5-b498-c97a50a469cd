<?php

namespace App\Http\Requests\Admin;

use App\Rules\Admin\CheckEmailTypeRule;
use Illuminate\Foundation\Http\FormRequest;

class DepositRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_id'       => 'required',
            'deposit_time'      => 'required',
            'amount'     => 'required',
            'point'        => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required'     => __('message.required'),
        ];
    }


}
