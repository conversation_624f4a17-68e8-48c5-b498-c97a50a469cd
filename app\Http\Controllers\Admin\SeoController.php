<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SeoRequest;
use App\Services\Admin\SeoService;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;

class SeoController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    public function index(Request $request)
    {
        //data side-bar
        $dataSideBar = config('seos');
        //save data config to table seos
        $this->seoService->createSeo($dataSideBar);
        //data vi
        $dataVi = $this->seoService->index([
            'lang'    => 'vi',
            'keyword' => array_key_first($dataSideBar),
        ]);

        //data en
        $dataEn = $this->seoService->index([
            'lang'    => 'en',
            'keyword' => array_key_first($dataSideBar),
        ]);

        return view('admin.pages.seo.index', compact('dataVi', 'dataEn', 'dataSideBar'));
    }

    public function getKey(Request $request)
    {
        $data = $this->seoService->getKey($request->all());
        return $data;
    }

    public function create(Request $request)
    {
        $data = $this->seoService->create($request->all());
        Toast::success(__('message.edit_success'));
        return back();
    }
}
