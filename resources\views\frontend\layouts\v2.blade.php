<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=yes">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {!! SEOMeta::generate() !!}
    {!! OpenGraph::generate() !!}
    @if (isset($page_image_url))
    <meta property="og:image" content="{{ $page_image_url }}" />
    @else
    <meta property="og:image" content="{{ config('settings.global.page_image_url') }}" />
    @endif
    <link type="image/x-icon" href="{{ gen_url_file_s3(config('settings.global.img_favicon')) }}" rel="shortcut icon" />
    <link rel="stylesheet" href="{{ asset2('frontend/assets_v2/css/all.css') }}" media="all" />
    <!-- build:head2 -->
    <!-- build:head -->
    @stack('style')
    @stack('scripts_head')

    @include('frontend.inc_layouts.v2.header_script')
</head>

<body class="">

    @include('frontend.inc_layouts.v2.home_header', [
    'topnav_class' =>
    Route::current()->getName() == 'home'
    ? 'home'
    : (in_array(Route::current()->getName(), [
    'employer-dashboard',
    'employer-register',
    'employer.send-email-forgot-password',
    'employer-form-reset-password',
    'rec-show-forgot-password',
    'rec-form-reset-password',
    ])
    ? 'home sticky'
    : ''),
    ])
    <div id="app">
        @yield('content')
    </div>

    <!--Footer-->
    @include('frontend.inc_layouts.v2.hh_footer')
    <!--End Footer-->

    <!-- Bug Report Modal -->
    @auth('client')
    @include('frontend.partials.bug_report_modal')
    @endauth

    <div class="overlay"></div>
    <!--Jquery lib-->
    @stack('scripts')
    <script>
        window.default_locale = "{{ app()->getLocale() }}"; //current language set by default
        window.fallback_locale = "{{ config('constant.language.vi') }}";
    </script>
    <script type="text/javascript" src="{{ asset2('frontend/assets_v2/js/all.js') }}"></script>
    @if (use_vue_in_route())
    <script src="{{ mix('js/app.js') }}"></script>
    @endif
    {{-- <script type="text/javascript" src="{{ asset2('frontend/assets_v2/js/popper.min.js') }}"></script> --}}
    <!-- build:script -->

    <script>
        // $('.topnav').addClass('home');
    </script>
    @stack('after_styles')
    @yield('after_styles')
    @stack('after_scripts')
    @yield('after_scripts')
    <div style="position:fixed;top:0px;left:0px;width:0;height:0;" id='scrollzipPoint'></div>
    @if (Session::has('message'))
    <script>
        $(document).ready(function() {
                toastr.success('{{ Session('message') }}');
            });
    </script>
    @endif
    @if (Session::has('error'))
    <script>
        $(document).ready(function() {
                toastr.error('{{ Session('error') }}');
            });
    </script>
    @endif
    {{-- @if (Session::has('error')) --}}
    <script>
        $(document).ready(function() {
            @if (request()->get('msg') == 'register_success')
                swal({
                    title: 'Đăng ký thành công',
                    text: 'Bạn đã đăng ký thành công, vui lòng kiểm tra email để xác thực tài khoản',
                    icon: 'success',
                    button: 'Đóng'
                });
                @endif
            });
    </script>
    {{-- @endif --}}
    {!! App\Services\Admin\Toast\Facades\Toast::message() !!}
    @include('frontend.inc_layouts.v2.contactus')
</body>

</html>