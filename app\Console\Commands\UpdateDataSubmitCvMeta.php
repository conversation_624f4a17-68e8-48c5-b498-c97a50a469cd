<?php

namespace App\Console\Commands;

use App\Services\Admin\SubmitCvService;
use Illuminate\Console\Command;

class UpdateDataSubmitCvMeta extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:submitcvmeta';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        parent::__construct();
        $this->submitCvService = $submitCvService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->submitCvService->checkSubmitCvMeta();
            \Log::info('Batch UpdateDataSubmitCvMeta DONE');
        } catch (\Exception $e) {
            \Log::error('Batch UpdateDataSubmitCvMeta Error: ' . $e);
        }
    }
}
