<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\ReportRequest;
use App\Services\Admin\InformationContactService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\UserService;
use Illuminate\Http\Request;

class OtherController extends Controller
{
    protected $settingService;

    protected $informationContactService;

    protected $seoService;
    protected $userService;

    public function __construct(
        SettingService $settingService,
        InformationContactService $informationContactService,
        SeoService $seoService,
        UserService $userService,
    )
    {
        $this->settingService = $settingService;
        $this->informationContactService = $informationContactService;
        $this->seoService = $seoService;
        $this->userService = $userService;
    }

    public function aboutUs()
    {
        $lang = app()->getLocale();
        $str = 'about-us';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        return view('frontend.pages.other.about-us', compact('arrLang'));
    }


    public function contactUs(){
        $lang = app()->getLocale();
        $contacts = $this->informationContactService->getAllContact();
        $str = 'contact-us';
        $this->seoService->getConfig($str, $lang);
        return view('frontend.pages.other.contact_us',compact('contacts'));
    }

    public function approveCandidate()
    {
        return view('frontend.pages.other.approve');
    }

    public function rejectCandidate()
    {
        return view('frontend.pages.other.reject');
    }

    public function report(ReportRequest $request)
    {
        $data = $this->userService->report($request->all());

        if ($data) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

}
