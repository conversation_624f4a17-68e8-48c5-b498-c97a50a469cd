<?php

namespace App\Http\Middleware;

use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Frontend\PermissionEmployerService;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CheckEmployer
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::guard('client')->check()
            && Auth::guard('client')->user()->email_verified_at
            && Auth::guard('client')->user()->type == config('constant.role.employer')
        ) {
            $user = Auth::guard('client')->user();
            $user->load('userEmployerType.employeeRole');
            $excludeRoute = [
                'employer-submitcv-detail',
                'employer-submitcv-status-detail',
                'employer-manager-dashboard',
                //test
                /*'employer-users',
                'employer-users-create',
                'employer-users-role',*/
            ];
            if ($user->userEmployerType->type == 'manager' ||
                in_array($request->route()->getName(), $excludeRoute)  ||
                PermissionEmployerService::checkEmployerPermission($user, $request->route()->getName())
            ){
                return $next($request);
            }else{
                Toast::error(__('message.role_fail'));
                return redirect()->route('employer-manager-dashboard');
//            abort(401, 'Access denied');
            }
        }

        $params = [];
        $request->session()->flash('type', 'LoginRequest');
        
        if (url()->previous()){
            $params['redirect'] = base64_encode(url()->full());
        }

        $params = http_build_query($params);
        Session::flash('error', __('frontend/login/message.please_check_email'));

        return redirect()->route('employer-dashboard', $params);
    }
}
