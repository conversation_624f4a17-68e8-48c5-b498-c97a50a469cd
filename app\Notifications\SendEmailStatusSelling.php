<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;

class SendEmailStatusSelling extends Mailable
{
    use Queueable, SerializesModels;

    protected $warehouseCvSelling;

    public function __construct($warehouseCvSelling)
    {
        $this->warehouseCvSelling = $warehouseCvSelling;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.SendEmailStatusSelling',
            with: [
                'name' => $this->warehouseCvSelling->user->name,
                'candidateName' => $this->warehouseCvSelling->wareHouseCv->candidate_name,
                'url' => route('rec-cv-selling',['selling' => $this->warehouseCvSelling->id]),
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->warehouseCvSelling->wareHouseCv->candidate_name;
        $message->subject('[<PERSON><PERSON><PERSON>] Thông báo đồng ý uỷ quyền Ứng viên '.$candidateName);
        return $this;
    }

}
