<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DepositRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $arrPrices = array_column(config('constant.deposit_price_and_point'), 'price');
        $arrMethod = config('constant.deposit_method');
        return [
            'amount' => ['required', Rule::in($arrPrices)],
            'payment_method'    => ['required', Rule::in($arrMethod)],
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
        ];
    }

}
