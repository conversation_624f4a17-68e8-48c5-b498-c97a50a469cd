# Workflow: <PERSON><PERSON><PERSON> trang thống kê tổng quan trong Admin

**<PERSON><PERSON><PERSON> tạo**: 14/01/2025  
**<PERSON><PERSON> tả**: <PERSON><PERSON><PERSON> trang thống kê tổng quan hiển thị các chỉ số Companies, Collaborators, Users, Total CV, Job is running với các bộ lọc theo thời gian, loại dữ liệu và nguồn CV.

## Các file đã tạo/chỉnh sửa

### 1. Controller
**File**: `app/Http/Controllers/Admin/OverviewStatisticsController.php`
- **Mô tả**: Controller xử lý logic hiển thị trang thống kê và lọc dữ liệu
- **Chức năng**:
  - `index()`: Hiển thị trang thống kê với bộ lọc
  - `filterData()`: Xử lý AJAX lọc dữ liệu

### 2. Service
**File**: `app/Services/Admin/OverviewStatisticsService.php`
- **<PERSON><PERSON> tả**: Service xử lý logic nghiệp vụ thống kê
- **<PERSON><PERSON><PERSON> năng**:
  - `getOverviewStatistics()`: <PERSON><PERSON><PERSON> dữ liệu thống kê theo bộ lọc
  - `generateQuarters()`: Tạo danh sách quý dựa trên khoảng thời gian
  - `getCompaniesCount()`: Đếm số lượng công ty
  - `getCollaboratorsCount()`: Đếm số lượng cộng tác viên
  - `getUsersCount()`: Đếm tổng số users
  - `getTotalCvCount()`: Đếm tổng số CV với bộ lọc nguồn
  - `getRunningJobsCount()`: Đếm số job đang chạy

### 3. Views
**File**: `resources/views/admin/pages/overview-statistics/index.blade.php`
- **Mô tả**: View chính hiển thị trang thống kê
- **Tính năng**:
  - Form bộ lọc với 4 trường: từ ngày, đến ngày, loại dữ liệu, nguồn CV
  - AJAX submit form
  - Responsive design

**File**: `resources/views/admin/pages/overview-statistics/filter-statistics.blade.php`
- **Mô tả**: View hiển thị bảng kết quả thống kê
- **Tính năng**:
  - Bảng thống kê với các cột theo yêu cầu
  - Dòng tổng cộng
  - Biểu đồ ECharts hiển thị xu hướng
  - Responsive table

### 4. Routes
**File**: `routes/admin.php`
- **Thay đổi**:
  - Thêm import `OverviewStatisticsController`
  - Thêm route group `overview-statistics` với 2 routes:
    - GET `/overview-statistics/` → `index`
    - POST `/overview-statistics/filter-data` → `filterData`

### 5. Sidebar Menu
**File**: `resources/views/admin/inc_layouts/side_bar.blade.php`
- **Thay đổi**:
  - Thêm menu "Thống kê tổng quan" vào submenu "Thống kê"
  - Cập nhật điều kiện active/expanded cho menu

## Bộ lọc được implement

1. **Khoảng thời gian tạo**: 
   - Từ ngày - Đến ngày
   - Mặc định hiển thị 4 quý gần nhất

2. **Dữ liệu thật hay ảo**:
   - Tất cả / Dữ liệu thật (is_real = 1) / Dữ liệu ảo (is_real = 0)

3. **Nguồn CV** (chỉ áp dụng cho cột Total CV):
   - Tất cả / Nguồn nội bộ (source NOT NULL) / Nguồn cộng tác viên (source IS NULL)

## Các cột thống kê

1. **Companies**: Số lượng công ty được tạo trong khoảng thời gian
2. **Collaborators**: Số lượng cộng tác viên (users có type = collaborator)
3. **Users**: Tổng số users được tạo
4. **Total CV**: Số lượng CV trong warehouse_cvs (có áp dụng bộ lọc nguồn)
5. **Job is running**: Số job có status = 1 và is_active = 1

## Tính năng bổ sung

- Biểu đồ line chart hiển thị xu hướng các chỉ số theo thời gian
- Dòng tổng cộng ở cuối bảng
- Responsive design cho mobile
- AJAX loading không reload trang
- Toast notification khi lọc thành công

## Cách sử dụng

1. Truy cập menu "Thống kê" → "Thống kê tổng quan"
2. Sử dụng các bộ lọc để xem dữ liệu theo nhu cầu
3. Nhấn "Lọc dữ liệu" để áp dụng bộ lọc
4. Nhấn "Đặt lại" để xóa tất cả bộ lọc

## Ghi chú kỹ thuật

- Sử dụng ECharts cho biểu đồ
- Bootstrap cho responsive design
- jQuery AJAX cho tương tác không reload trang
- Laravel Eloquent ORM cho truy vấn database
- Service pattern để tách biệt logic nghiệp vụ

## Kiểm tra và Test

### Routes đã được tạo thành công:
```
GET|HEAD   admin/overview-statistics ....................................... overview-statistics.index
POST       admin/overview-statistics/filter-data ................ overview-statistics.filter-data
```

### Truy cập trang:
- URL: `/admin/overview-statistics`
- Menu: Thống kê → Thống kê tổng quan

### Các test case cần kiểm tra:
1. **Hiển thị mặc định**: Trang load với 4 quý gần nhất
2. **Bộ lọc thời gian**: Chọn từ ngày - đến ngày
3. **Bộ lọc dữ liệu thật/ảo**: Kiểm tra filter is_real
4. **Bộ lọc nguồn CV**: Kiểm tra filter nguồn nội bộ/cộng tác viên
5. **Biểu đồ**: Kiểm tra hiển thị chart ECharts
6. **Responsive**: Kiểm tra trên mobile/tablet
7. **AJAX**: Kiểm tra lọc không reload trang

## Hoàn thành

✅ Tất cả các file đã được tạo và cấu hình thành công
✅ Routes đã được đăng ký
✅ Menu sidebar đã được cập nhật
✅ Tính năng sẵn sàng để sử dụng
