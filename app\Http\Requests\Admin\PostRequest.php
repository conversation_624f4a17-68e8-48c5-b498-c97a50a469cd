<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class PostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
            'title_vi'          => 'required',
            'title_en'          => 'required',
            'category_id'       => 'required',
            'date'              => 'required',
            'description_vi'    => 'required',
            'description_en'    => 'required',
            'status'            => 'required',
            'tags'              => 'required',
            'banner_img'        => 'mimes:jpg,png,jpeg',
            'banner_detail_img' => 'mimes:jpg,png,jpeg',
        ];
    }

    public function messages()
    {
        return [
            'required'                => __('message.required'),
            'banner_img.mimes'        => __('message.format_image'),
            'banner_detail_img.mimes' => __('message.format_image'),
        ];
    }
}
