<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintsAcceptedToRecSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $content       = $this->submitCv->txt_complain;
        $image         = $this->submitCv->img_complain;
        $companyName   = $this->submitCv->employer->name;
        $employerName  = $this->submitCv->employer->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->bonus_type;
        $recName       = $this->submitCv->rec->name;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        // $link = route('rec-cv-sold',['cv_sold' =>  $this->submitCv->id,'open_complain' => 1]);
        $link    = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id, 'open_complain' => 1]);
        return (new MailMessage)
            ->view('email.complaintsAcceptedToRecSubmit', [
                'companyName'   => $companyName,
                'candidateName' => $candidateName,
                'employerName'  => $employerName,
                'recName'       => $recName,
                'position'      => $position,
                'type'          => $type,
                'content'       => $content,
                'image'         => gen_url_file_s3($image),
                'link'          => $link,
                'submitCvId'    => $this->submitCv->id
            ])
            ->subject('[Recland][Case #'.$this->submitCv->id.']  Kết quả khiếu nại');
    }

}
