<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_survey_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('survey_field_id')->constrained('survey_fields')->onDelete('cascade');
            $table->timestamp('surveyed_at'); // Thời điểm khảo sát
            $table->timestamps();

            // <PERSON><PERSON><PERSON> bảo mỗi user chỉ chọn một lĩnh vực một lần
            $table->unique(['user_id', 'survey_field_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_survey_results');
    }
}; 