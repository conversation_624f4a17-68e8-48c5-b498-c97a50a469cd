<?php

namespace App\Console\Commands;

use App\Models\Skill;
use App\Services\Admin\SkillService;
use Illuminate\Console\Command;
use File;

class CrawlSkill extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'CrawlSkill';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'CrawlSkill';

    protected $skillService;

    public function __construct(SkillService $skillService)
    {
        parent::__construct();
        $this->skillService = $skillService;
    }

    protected $dataString = [];

    /**
     * Execute the console command.
     *
     * @return int
     */

    protected function getData($str){
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://employer.vietnamworks.com/v2/suggest/skills?q='.$str,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Accept: application/json, text/javascript, */*; q=0.01',
                'Accept-Language: en-US,en;q=0.9',
                'Connection: keep-alive',
                'Cookie: cookiesession1=678A3E0E01234ABCDEFGHJKLMNOP4305; lang=2; __zlcmid=1D5lOjiyIhnLo21; __stdf=0; _ga=GA1.2.913482.1669209873; __stp={"visit":"returning","uuid":"6fb3a00c-23e4-4d07-ad9c-0e34c0f25f7b"}; VNWWS128450527232960E=ZWQxYjdmZjZhODZjNzk3YmUxZDRlYTc3ZGQxMDgyYTE4OWIxMjdiOGJlMDJmNGZlYTY1YWRhOWYyZGFkNzhiYw%7CNmVhMjJjMmQyNGM4MGYzOWQ5NTI3ZDAxODBkNTcxNDQ0OTU1OTUxOTI3N2MwNjIzM2RhNDE4ZmEwMDgzMGE5Yw; ONB_JWT=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************.ZrK28ZrMbG16hMTK3yGO1C2AhMI77dAo_v-ke0x-2J4; PHPSESSID=r35jj1388t8jorj2tr7o360ot6; VNW128450527232960E=eaf0da8542e1f3d6f752b33803ae41a37065034; _clck=o80j5b|1|f6z|0; __stgeo="0"; __stbpnenable=0; __sts={"sid":1669727794563,"tx":1669727831044,"url":"https%3A%2F%2Femployer.vietnamworks.com%2Fv2%2Fcandidate%2Fsearch","pet":1669727831044,"set":1669727794563,"pUrl":"https%3A%2F%2Femployer.vietnamworks.com%2Fv2%2F","pPet":1669727794563,"pTx":1669727794563}; isShowGuideLine=1; FIREBASE_JWT=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N5YmiGbKorbTPg3tkHb7Yqnpu6HbDlAMAfTLiFBHu4sOq8dl4rRJx6eITB4MZVMS16_3wOj43yNV7DPgNitptqypcrobR3JVi9f1pvJSA-fU5LCa_FSnML4C2aRVeMapbQdjSZk3xZ0hm5k2cbiCHUUxXPuDxrTPAq7MipcHXpwCuIV2GVi7_QSUCii7OnNK-_AqE-Hs4ZjY63D4i2oW-EKodPh1_UzePKAB9D4vZGnW4_lbDWdmeDsdH8aV19vhxG5OxtfPPTaW1gFj1SAiQIlwCdq4Cn_RhSLJYSLcy3GZBHSw8DAyht9n747Us42lTTeE4BTHEbPlr0GZhIosgg; _gid=GA1.2.1060102149.1669727836; __insp_wid=1521427198; __insp_nv=true; __insp_targlpu=aHR0cHM6Ly9lbXBsb3llci52aWV0bmFtd29ya3MuY29tL3YyL2pvYi1wb3N0aW5nL25ldy1qb2I%3D; __insp_targlpt=UG9zdCBBIEpvYiAtIFZpZXRuYW1Xb3JrcyAtIFRvcCBlbXBsb3ltZW50IGFuZCByZWNydWl0bWVudCB3ZWJzaXRlIGluIFZpZXRuYW0u; __insp_norec_sess=true; __insp_slim=1669727875398; _clsk=qzquzx|1669727875815|5|1|k.clarity.ms/collect; _gat=1; _hjSessionUser_1732111=********************************************************************************************************************; _hjIncludedInSessionSample=0; _hjSession_1732111=eyJpZCI6IjZiY2Q3NWY1LWZjNGQtNDdkNS1hM2M2LTQ2ZjdmNjBhYTZhMCIsImNyZWF0ZWQiOjE2Njk3Mjc5MjU2NzksImluU2FtcGxlIjpmYWxzZX0=; _hjIncludedInPageviewSample=1; _hjAbsoluteSessionInProgress=1; cookiesession1=678A3E0EXYZABCDEFGHJKLMNOPQRFD5F',
                'Referer: https://employer.vietnamworks.com/v2/job-posting/new-job',
                'Sec-Fetch-Dest: empty',
                'Sec-Fetch-Mode: cors',
                'Sec-Fetch-Site: same-origin',
                'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36',
                'X-Requested-With: XMLHttpRequest',
                'sec-ch-ua: "Google Chrome";v="107", "Chromium";v="107", "Not=A?Brand";v="24"',
                'sec-ch-ua-mobile: ?0',
                'sec-ch-ua-platform: "macOS"'
            ),
        ));

        $response = curl_exec($curl);
        return json_decode($response,true);
    }

    public function getNameFromNumber($num) {
        $numeric = $num % 26;
        $letter = chr(65 + $numeric);
        $num2 = intval($num / 26);
        if ($num2 > 0) {
            return $this->getNameFromNumber($num2 - 1) . $letter;
        } else {
            return $letter;
        }
    }
    //21484
    public function handle()
    {


        $i = 701;
        for ($x = 0;$x<=$i;$x++){
            $str = $this->getNameFromNumber($x);
            $data = $this->getData($str);
            if (!empty($data['data'])){
                foreach ($data['data'] as $datum){
                    $skill = $this->skillService->getByName($datum['text']);
                    if (empty($skill)){
                        $this->skillService->createService(
                            [
                                'name' => $datum['text'],
                                'is_active' => 1,
                            ]
                        );
                    }
                }
            }
        }

        Skill::chunk(500, function($skills) {
                foreach ($skills as $skill) {
                    $strings = $skill->name;
                    $strings = explode(" ",$strings);
                    if (count($strings) > 1){
                        foreach ($strings as $string){
                            if (!in_array($string,$this->dataString)){
                                $this->dataString[] = $string;
                                $data = $this->getData($string);
                                if (!empty($data['data'])){
                                    foreach ($data['data'] as $datum){
                                        $skill = $this->skillService->getByName($datum['text']);
                                        if (empty($skill)){
                                            $this->skillService->createService(
                                                [
                                                    'name' => $datum['text'],
                                                    'is_active' => 1,
                                                ]
                                            );
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            });


    }
}
