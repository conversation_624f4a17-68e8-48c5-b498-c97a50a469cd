<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CandidateRejectRecruitmentRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $cvBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($cvBuy)
    {
        $this->cvBuy = $cvBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        //ten ctv
        $name = $this->cvBuy->wareHouseCvSelling->user->name;  //name ctv
        $candidateName = $this->cvBuy->wareHouseCvSelling->warehouseCv->candidate_name;
        $companyName = $this->cvBuy->employer->name;
        $type = $this->cvBuy->wareHouseCvSelling->type_of_sale;
        $candidateJobTitle = '';
        if ($type == 'cv'){
            $candidateJobTitle = $this->cvBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->cvBuy->job)){
            $candidateJobTitle = $this->cvBuy->job->name;
        }
        $link = route('rec-cv-sold') . '?cv_sold=' . $this->cvBuy->wareHouseCvSelling->id;
        $linkOther = route('rec-cv-selling', ['sell_cv' => 1]);

       return (new MailMessage)
            ->view('email.ungvien_tuchoi_ungtuyen_ctv', [
                'name' => $name,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'candidateJobTitle' => $candidateJobTitle,
                'type'  => $type,
                'link' => $link,
                'linkOther' => $linkOther,
            ])
            ->subject('[Recland] Thông báo Ứng viên '. $candidateName .' đã từ chối lời mời ứng tuyển công ty '. $companyName);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [

        ];
    }
}
