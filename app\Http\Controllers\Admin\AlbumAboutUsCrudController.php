<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\AlbumAboutUsRequest;
use App\Models\Company;
use App\Models\Lead;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;

/**
 * Class CompanyCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class AlbumAboutUsCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;



    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\AboutUsAlbum::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/album-about-us');
        CRUD::setEntityNameStrings('Album', 'Albums');
        // CRUD::setEditView('admins.company.edit');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::setFromDb(); // set columns from db columns.
        $this->crud->removeButton('show');

        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(AlbumAboutUsRequest::class);
        CRUD::addFields($this->fieldData());

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    public function fieldData()
    {
        // $validateIgnoreMst =  !empty(request('id')) ? ',mst,' . request('id') : '';
        // $validateIgnoreEmail =  !empty(request('id')) ? ',email,' . request('id') : '';
        // $validateIgnoreMobile =  !empty(request('id')) ? ',mobile,' . request('id') : '';
        return [
            [
                'name' => 'name',
                'label' => 'Tên Album',
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-group col-md-6',
                ],
            ],
            [
                'name' => 'photos',
                'label' => 'Ảnh album (vui lòng upload 2 ảnh dài và 4 ảnh vuông)',
                'type' => "relationship",
                'subfields' => [
                    [
                        'label' => "Image",
                        'name' => "photo_url",
                        'type' => 'image',
                        // 'crop' => true, // set to true to allow cropping, false to disable
                        // 'aspect_ratio' => 1, // omit or set to 0 to allow any aspect ratio
                        'disk'      => 's3', // in case you need to show images from a different disk
                        'prefix'    => env('AWS_CLOUDFRONT_URL') . '/', // in case your db value is only the file name (no path), you can use this to prepend your path to the image src (in HTML), before it's shown to the user;
                        'wrapper' => [
                            'class' => 'form-group col-md-10',
                        ],
                    ],
                    [   // Switch
                        'name'  => 'type',
                        'type'  => 'switch',
                        'label'    => 'Ảnh dài',
                        // optional
                        'color'    => 'primary', // May be any bootstrap color class or an hex color
                        'onLabel' => '✓',
                        'offLabel' => '✕',
                        'wrapper' => [
                            'class' => 'form-group col-md-2',
                        ],
                    ],
                ]
            ],
            //         [
            //             'name' => 'position',
            //             'label' => 'Vị trí',
            //             'type' => 'text',
            //             'wrapper' => [
            //                 'class' => 'form-group col-md-3',
            //             ],
            //         ],
            //         [
            //             'name' => 'email',
            //             'label' => 'email',
            //             'type' => 'text',
            //             'wrapper' => [
            //                 'class' => 'form-group col-md-5',
            //             ],
            //         ],
            //         [
            //             'name' => 'phone',
            //             'label' => 'Số điện thoại',
            //             'type' => 'text',
            //             'wrapper' => [
            //                 'class' => 'form-group col-md-5',
            //             ],
            //         ],
            //         [
            //             'name' => 'address',
            //             'label' => 'Địa chỉ',
            //             'type' => 'text',
            //             'wrapper' => [
            //                 'class' => 'form-group col-md-7',
            //             ],
            //         ],
            //     ],
            // ]

        ];
    }
    protected function setupInlineCreateOperation()
    {

        $this->setupCreateOperation();
    }
}
