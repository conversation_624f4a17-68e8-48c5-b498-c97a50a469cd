# Tài liệu Đặc tả Yêu cầu <PERSON>ầ<PERSON> mềm (SRS) - <PERSON><PERSON> thống Quản lý Tuyển dụng RecLand

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**Tá<PERSON> g<PERSON>:** AI Assistant  
**M<PERSON><PERSON> đích:** Tài liệu đặc tả chi tiết để tái xây dựng hệ thống

---

## 1. Tổng quan hệ thống

### 1.1. <PERSON><PERSON><PERSON> đích và tầm nhìn

**RecLand** là một nền tảng SaaS (Software as a Service) chuyên về quản lý tuyển dụng và kết nối nhân sự, hoạt động theo mô hình **cost-per-action** thay vì chi phí đăng tin truyền thống. Hệ thống giải quyết bài toán tìm kiếm và tuyển dụng nhân sự chất lượng cho các doanh nghiệp thông qua hai luồng nghiệp vụ chính:

1. **MarketCV (Mua bán CV):** Nhà tuyển dụng chủ động tìm kiếm và mua thông tin ứng viên từ kho CV có sẵn
2. **Submit CV (Ứng tuyển):** Ứng viên/Cộng tác viên nộp hồ sơ vào các tin tuyển dụng cụ thể

### 1.2. Phạm vi hệ thống

Hệ thống bao gồm:
- **Website Frontend:** Giao diện người dùng (Vue.js + Laravel Blade)
- **Admin Panel:** Quản trị hệ thống (Backpack for Laravel)
- **API Services:** Tích hợp với các hệ thống bên ngoài
- **Background Jobs:** Xử lý các tác vụ bất đồng bộ
- **Payment Gateway:** Tích hợp ZaloPay và chuyển khoản ngân hàng
- **File Storage:** Amazon S3 cho lưu trữ CV và tài liệu
- **Email System:** Gửi thông báo và email marketing

### 1.3. Đối tượng người dùng

| Vai trò | Mô tả | Quyền hạn chính |
|---------|-------|-----------------|
| **Admin** | Quản trị viên hệ thống | CRUD tất cả dữ liệu, quản lý cấu hình, duyệt giao dịch, xem báo cáo tổng thể |
| **Employer (NTD)** | Nhà tuyển dụng/Doanh nghiệp | Đăng tin tuyển dụng, mua CV, quản lý ví điện tử, theo dõi quy trình tuyển dụng |
| **Candidate (UV)** | Ứng viên tìm việc | Tạo/cập nhật hồ sơ CV, tìm kiếm và ứng tuyển công việc, theo dõi trạng thái |
| **Collaborator (CTV)** | Cộng tác viên/Headhunter | Tìm kiếm ứng viên phù hợp, submit CV để hưởng hoa hồng theo các mốc |

### 1.4. Kiến trúc tổng thể

#### 1.4.1. Technology Stack

**Backend:**
- **Framework:** Laravel 9.x (PHP 8.1+)
- **Database:** MySQL 8.0
- **Queue:** Database driver với Redis (optional)
- **Cache:** Redis/File cache
- **Storage:** Amazon S3
- **Search:** MySQL Full-text search

**Frontend:**
- **Framework:** Vue.js 3.x
- **Build Tool:** Laravel Mix với Webpack
- **UI Components:** Bootstrap Vue Next
- **CSS Framework:** Bootstrap 5 + Custom SCSS

**Infrastructure:**
- **Web Server:** Nginx/Apache
- **Application Server:** PHP-FPM
- **Database:** MySQL 8.0
- **Cache:** Redis
- **File Storage:** Amazon S3
- **Email:** SMTP/Mailgun

#### 1.4.2. Kiến trúc ứng dụng

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Vue.js)  │  Admin Panel (Backpack)  │  API       │
├─────────────────────────────────────────────────────────────┤
│                    APPLICATION LAYER                        │
├─────────────────────────────────────────────────────────────┤
│  Controllers  │  Services  │  Jobs  │  Notifications        │
├─────────────────────────────────────────────────────────────┤
│                    DOMAIN LAYER                             │
├─────────────────────────────────────────────────────────────┤
│  Models  │  Repositories  │  Business Logic                 │
├─────────────────────────────────────────────────────────────┤
│                    INFRASTRUCTURE LAYER                     │
├─────────────────────────────────────────────────────────────┘
│  Database  │  File Storage  │  Email  │  Payment Gateway    │
└─────────────────────────────────────────────────────────────┘
```

### 1.5. Các module chính

#### 1.5.1. User Management Module
- Quản lý người dùng (Admin, Employer, Candidate, Collaborator)
- Authentication & Authorization
- Profile management
- Role-based permissions

#### 1.5.2. Job Management Module  
- Đăng tin tuyển dụng
- Quản lý danh sách công việc
- Tìm kiếm và lọc công việc
- Job expiration handling

#### 1.5.3. CV Warehouse Module
- Kho lưu trữ CV
- CV parsing và processing
- CV search và filtering
- CV privacy management

#### 1.5.4. Marketplace Module (MarketCV)
- Tìm kiếm và mua CV từ kho
- 3 hình thức dịch vụ: CV Data, Interview, Onboard
- Payment processing
- Commission calculation

#### 1.5.5. Submit CV Module
- Ứng tuyển vào tin tuyển dụng
- CV submission workflow
- Interview scheduling
- Onboarding process

#### 1.5.6. Wallet & Payment Module
- Ví điện tử cho Employer
- Top-up và withdrawal
- Transaction history
- Payment gateway integration

#### 1.5.7. Notification Module
- Email notifications
- In-app notifications  
- SMS notifications (future)
- Notification templates

#### 1.5.8. Reporting & Analytics Module
- Dashboard analytics
- Revenue reports
- User activity reports
- Commission reports

### 1.6. Luồng nghiệp vụ chính

#### 1.6.1. Luồng MarketCV (Mua bán CV)

```
NTD tìm kiếm CV → Xem preview → Chọn mua → Thanh toán → Nhận thông tin liên hệ
                                    ↓
                            Các giai đoạn tiếp theo:
                            - CV Data (xem thông tin)
                            - Interview (phỏng vấn)  
                            - Onboard (tuyển dụng thành công)
```

#### 1.6.2. Luồng Submit CV (Ứng tuyển)

```
NTD đăng tin → UV/CTV submit CV → NTD xem danh sách → Chọn ứng viên → Thanh toán
                                                           ↓
                                                   Quy trình tuyển dụng:
                                                   - CV Data → Interview → Onboard
```

### 1.7. Tích hợp bên ngoài

#### 1.7.1. Payment Gateways
- **ZaloPay:** Thanh toán online
- **Bank Transfer:** Chuyển khoản ngân hàng (manual confirmation)

#### 1.7.2. File Storage
- **Amazon S3:** Lưu trữ CV, documents, images
- **Local Storage:** Temporary files, cache

#### 1.7.3. Email Services
- **SMTP:** Email notifications
- **Mailgun:** Bulk email sending

#### 1.7.4. External APIs
- **CV Parser API:** Phân tích nội dung CV (n8n webhook)
- **Hide CV API:** Ẩn thông tin nhạy cảm trong CV
- **ITNavi API:** Đồng bộ dữ liệu ứng viên

### 1.8. Bảo mật và tuân thủ

#### 1.8.1. Authentication & Authorization
- **Multi-role authentication:** Admin, Employer, Candidate, Collaborator
- **Session management:** Laravel session với Redis
- **Password security:** Bcrypt hashing
- **API authentication:** Sanctum tokens

#### 1.8.2. Data Security
- **HTTPS:** Bắt buộc cho tất cả connections
- **Data encryption:** Sensitive data encryption
- **File security:** S3 private buckets với signed URLs
- **SQL Injection prevention:** Eloquent ORM protection

#### 1.8.3. Privacy Protection
- **CV anonymization:** Tự động ẩn thông tin cá nhân
- **Data access control:** Role-based access
- **Audit logging:** Track all data changes
- **GDPR compliance:** Data deletion và export

### 1.9. Performance và Scalability

#### 1.9.1. Performance Requirements
- **Page load time:** < 3 seconds
- **API response time:** < 500ms
- **Concurrent users:** 100+ simultaneous users
- **Database queries:** Optimized với indexing

#### 1.9.2. Scalability Design
- **Horizontal scaling:** Load balancer ready
- **Database optimization:** Query optimization, indexing
- **Caching strategy:** Redis caching cho frequent data
- **CDN integration:** Static assets delivery

#### 1.9.3. Monitoring & Logging
- **Application logs:** Laravel logging
- **Error tracking:** Exception handling
- **Performance monitoring:** Query performance tracking
- **Job monitoring:** Queue job status tracking

---

## 2. Định nghĩa thuật ngữ

| Thuật ngữ | Định nghĩa |
|-----------|------------|
| **NTD** | Nhà Tuyển Dụng (Employer) |
| **UV** | Ứng Viên (Candidate) |
| **CTV** | Cộng Tác Viên (Collaborator/Headhunter) |
| **CV Data** | Dịch vụ mua thông tin liên hệ cơ bản của ứng viên |
| **Interview** | Dịch vụ thanh toán khi ứng viên đồng ý phỏng vấn |
| **Onboard** | Dịch vụ thanh toán khi ứng viên được tuyển dụng thành công |
| **Wallet** | Ví điện tử của NTD để thực hiện giao dịch |
| **Warehouse CV** | Kho CV của hệ thống, thu thập từ nhiều nguồn |
| **Authority** | Chế độ ủy quyền cho Admin quản lý thay CTV |
| **Bonus** | Hoa hồng thanh toán cho CTV theo từng giai đoạn |
| **Job** | Tin tuyển dụng do NTD đăng |
| **Submit CV** | Hồ sơ ứng tuyển vào tin tuyển dụng cụ thể |

---

*Tài liệu này sẽ được bổ sung chi tiết trong các phần tiếp theo.*
