<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeEmployer extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'employers';

    protected $fillable = [
        'company_id',
        'name',
        'email',
        'password',
        'mobile',
        'admin_id',
        'total_point',
        'total_job',
        'type',
        'is_active',
        'telegram_name',
        'telegram_id',
        'slug',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function company()
    {
        return $this->belongsTo(FakeCompany::class, 'company_id', 'id');
    }

    public function jobs()
    {
        return $this->hasMany(FakeJob::class, 'employer_id', 'id');
    }

    public function metas()
    {
        return $this->hasOne(FakeEmployerMeta::class, 'employer_id', 'id');
    }
}
