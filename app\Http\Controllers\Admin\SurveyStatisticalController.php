<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SurveyField;
use App\Models\UserSurveyResult;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SurveyStatisticalController extends Controller
{
    public function index(Request $request)
    {
        // Thống kê tổng quan
        $totalSurveyFields = SurveyField::count();
        $totalSurveyResponses = UserSurveyResult::count();
        $totalRespondents = UserSurveyResult::distinct('user_id')->count();

        // Thống kê theo lĩnh vực (top 10)
        $fieldStatistics = SurveyField::withCount('userResults')
            ->orderBy('user_results_count', 'desc')
            ->take(20)
            ->get();

        // Thống kê theo thời gian (7 ngày gần nhất)
        $recentDays = UserSurveyResult::select(
            DB::raw('DATE(surveyed_at) as date'),
            DB::raw('COUNT(DISTINCT user_id) as user_count'),
            DB::raw('COUNT(*) as response_count')
        )
        ->where('surveyed_at', '>=', now()->subDays(7))
        ->groupBy('date')
        ->orderBy('date', 'desc')
        ->get();

        // Thống kê chi tiết theo lĩnh vực với pagination
        $detailedStatistics = $this->getDetailedStatistics($request);

        return view('admin.pages.survey-statistical.index', compact(
            'totalSurveyFields',
            'totalSurveyResponses',
            'totalRespondents',
            'fieldStatistics',
            'recentDays',
            'detailedStatistics'
        ));
    }

    public function filterData(Request $request)
    {
        $detailedStatistics = $this->getDetailedStatistics($request);

        $view = view('admin.pages.survey-statistical.filter-statistics', compact('detailedStatistics'))->render();

        return response()->json([
            'status' => 'success',
            'html' => $view,
        ]);
    }

    private function getDetailedStatistics(Request $request)
    {
        $query = SurveyField::withCount('userResults')
            ->with(['userResults' => function ($query) {
                $query->with('user')->latest();
            }]);

        // Lọc theo tên lĩnh vực
        if ($request->filled('field_name')) {
            $query->where('name', 'like', '%' . $request->field_name . '%');
        }

        // Lọc theo thời gian
        if ($request->filled('from_date') && $request->filled('to_date')) {
            $query->whereHas('userResults', function ($q) use ($request) {
                $q->whereBetween('surveyed_at', [
                    $request->from_date . ' 00:00:00',
                    $request->to_date . ' 23:59:59'
                ]);
            });
        }

        return $query->paginate(30);
    }

    public function fieldDetail($fieldId)
    {
        $field = SurveyField::with(['userResults' => function ($query) {
            $query->with('user')->latest();
        }])->findOrFail($fieldId);

        $users = UserSurveyResult::where('survey_field_id', $fieldId)
            ->with('user')
            ->latest()
            ->paginate(20);

        return view('admin.pages.survey-statistical.field-detail', compact('field', 'users'));
    }

    public function export(Request $request)
    {
        $query = SurveyField::withCount('userResults');

        // Áp dụng các filter tương tự như trong getDetailedStatistics
        if ($request->filled('field_name')) {
            $query->where('name', 'like', '%' . $request->field_name . '%');
        }

        if ($request->filled('from_date') && $request->filled('to_date')) {
            $query->whereHas('userResults', function ($q) use ($request) {
                $q->whereBetween('surveyed_at', [
                    $request->from_date . ' 00:00:00',
                    $request->to_date . ' 23:59:59'
                ]);
            });
        }

        $statistics = $query->get();

        $filename = 'survey_statistics_' . date('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($statistics) {
            $file = fopen('php://output', 'w');
            
            // Thêm BOM cho UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            // Header
            fputcsv($file, ['STT', 'Tên lĩnh vực', 'Số lượng phản hồi', 'Mô tả']);

            // Data
            foreach ($statistics as $index => $stat) {
                fputcsv($file, [
                    $index + 1,
                    $stat->name,
                    $stat->user_results_count,
                    $stat->description ?? ''
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
} 