<?php

namespace App\Jobs;

use App\Models\SubmitCvHistoryStatus;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdminSubmit;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Notifications\ChangeStatusSuccessRecruitmentToRecSubmit;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SuccessRecuitmentSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {
        $submitCvRepository = app(SubmitCvRepository::class);
        $submitCvHistoryStatus = app(SubmitCvHistoryStatus::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        //14 => 'Trial work',
        //Trạng thái khiếu nại: NTD khiếu nại = 1, CTV từ chối = 2, CTV xác nhận = 3, Admin xác nhân = 4, Admin từ chối = 5
        if ($submitCv->status == 14 &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {
            //16 => 'Success Recruitment',
            $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
            $submitCv->update(['status' => 16, 'status_payment' => 4]);

            $submitCvHistoryStatus->logStatus($submitCv);
            if ($submitCv->authorize > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusSuccessRecruitmentToAdminSubmit($submitCv));
            }else{
                $submitCv->rec->notify(new ChangeStatusSuccessRecruitmentToRecSubmit($submitCv));
            }
        }
    }
}
