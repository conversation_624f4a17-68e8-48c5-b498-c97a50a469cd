# Workflow: T<PERSON><PERSON> trang danh sách giao dịch

## <PERSON><PERSON><PERSON> thực hiện: 2025-01-09

## <PERSON><PERSON><PERSON> tiêu

Tạo trang hiển thị toàn bộ danh sách giao dịch với các bộ lọc theo thời gian, admin, lo<PERSON><PERSON> tà<PERSON>, công ty và widget tổng hợp.

## <PERSON><PERSON><PERSON> c<PERSON>u

-   <PERSON><PERSON> lọc theo thời gian (từ ngày - đến ngày)
-   <PERSON><PERSON> lọc theo <PERSON>min (chỉ admin có quyền "company.rotation")
-   B<PERSON> lọc theo loại tài khoản (employer/rec)
-   <PERSON><PERSON> lọc theo công ty (dạng autocomplete)
-   **[CẬP NHẬT]** B<PERSON> lọc theo loại giao dịch (tiền vào/tiền ra)
-   Sắp xếp các giao dịch mới nhất lên đầu
-   Widget tổng hợp: tổng số tiền đã <PERSON> (bị trừ), tổng số tiền đã nhận
-   **[CẬP NHẬT]** Thêm thông tin ứng tuyển giống trang EmployerLookup
-   **[CẬP NHẬT]** Filter company đổi thành select2 ajax autocomplete

## Các tác vụ đã thực hiện

### 1. Tạo Controller TransactionListController

**File**: `app/Http/Controllers/Admin/TransactionListController.php`

**Chức năng**:

-   `index()`: Hiển thị trang danh sách giao dịch với bộ lọc
-   `getTransactions()`: Lấy danh sách giao dịch với các bộ lọc
-   `getSummaryData()`: Lấy widget tổng hợp (tổng thu/chi)
-   `getAdminsWithCompanyRotationPermission()`: Lấy admins có quyền company.rotation
-   `hasCompanyRotationPermission()`: Kiểm tra quyền admin
-   `searchCompanies()`: API tìm kiếm công ty cho autocomplete
-   **[THÊM MỚI]** `getJobInfo()`: Lấy thông tin job và ứng tuyển liên quan đến giao dịch

**Các bộ lọc đã implement**:

-   Thời gian: `date_from`, `date_to`
-   Admin: `admin_id` (filter qua companies.admin_id)
-   Loại tài khoản: `user_type` (employer/rec)
-   Công ty: `company_id` (với select2 ajax autocomplete)
-   **[MỚI]** Loại giao dịch: `transaction_type` (income/expense)

### 2. Tạo View hiển thị

**File**: `resources/views/admin/pages/transaction-list/index.blade.php`

**Thành phần**:

-   **Widget tổng hợp**: 2 card hiển thị tổng thu nhập và tổng chi tiêu
-   **Form bộ lọc**:
    -   Input date cho thời gian
    -   Select admin (chỉ admin có quyền company.rotation)
    -   Select loại tài khoản
    -   **[CẬP NHẬT]** Select2 công ty với tìm kiếm Ajax (minimumInputLength: 1)
    -   **[MỚI]** Select loại giao dịch (Tiền vào/Tiền ra)
-   **Bảng giao dịch**: Hiển thị danh sách với pagination
-   **Columns**: STT, Thời gian, Người dùng, Admin quản lý, **[THÊM MỚI]** Thông tin ứng tuyển, Số tiền, Số dư sau GD, Ghi chú

### 3. Thêm Routes

**File**: `routes/admin.php`

**Routes đã thêm**:

-   `GET /transaction-list/`: Hiển thị trang danh sách
-   `GET /transaction-list/search-companies`: API tìm kiếm công ty

### 4. Tính năng Chi tiết

#### **[MỚI]** Bộ lọc Loại giao dịch

-   **Tiền vào**: Lọc giao dịch có `amount > 0`
-   **Tiền ra**: Lọc giao dịch có `amount < 0`
-   **Tất cả**: Hiển thị tất cả giao dịch
-   **Widget tích hợp**: Áp dụng filter vào widget tổng hợp

#### **[MỚI]** Thông tin Ứng tuyển

-   Lấy thông tin từ `object` relationship của WalletTransaction
-   Kiểm tra nếu object là instance của SubmitCv
-   Hiển thị:
    -   Tên job
    -   Tên ứng viên
    -   Email ứng viên
    -   Trạng thái ứng tuyển (badge)
-   Nếu không có thông tin ứng tuyển: hiển thị "Không có thông tin ứng tuyển"

#### **[CẬP NHẬT]** Bộ lọc Công ty

-   Sử dụng Select2 với Ajax search
-   `minimumInputLength: 1` (bắt buộc nhập ít nhất 1 ký tự)
-   Tìm kiếm theo tên công ty hoặc MST
-   Hiển thị format: "Tên công ty - MST"
-   Preload công ty đã chọn khi load trang

#### Bộ lọc Admin

-   Chỉ lấy admin có `type = 'admin'` và `is_active = 1`
-   Kiểm tra quyền `company.rotation` trong JSON permissions của roles
-   Hiển thị trong dropdown với option "-- Tất cả --"

#### Widget Tổng hợp

-   **Tổng thu nhập**: SUM(amount) WHERE amount > 0
-   **Tổng chi tiêu**: SUM(ABS(amount)) WHERE amount < 0
-   Áp dụng cùng bộ lọc với danh sách giao dịch
-   **[MỚI]** Bao gồm cả filter loại giao dịch

#### Hiển thị Dữ liệu

-   Sắp xếp theo thời gian tạo mới nhất
-   Phân trang 20 items/page
-   Hiển thị badge màu cho loại tài khoản
-   Format số tiền với dấu + cho thu nhập, - cho chi tiêu
-   **[MỚI]** Thông tin ứng tuyển trong cột riêng biệt

### 5. Database Relations

**Quan hệ sử dụng**:

-   `WalletTransaction` -> `Wallet` -> `User`
-   `User` -> `Company` -> `Admin` (User)
-   `User` -> `Roles` (để check permissions)
-   **[THÊM MỚI]** `WalletTransaction` -> `object` (polymorphic) -> `SubmitCv` -> `Job`
-   **[THÊM MỚI]** `SubmitCv` -> `submitCvMeta` (thông tin ứng viên)
-   **[THÊM MỚI]** `SubmitCv` -> `user` (backup thông tin ứng viên)

### 6. **[MỚI]** Logic Lọc Loại giao dịch

```php
// Bộ lọc theo loại giao dịch (tiền vào/tiền ra)
if ($request->filled('transaction_type')) {
    switch ($request->transaction_type) {
        case 'income':
            $query->where('wallet_transactions.amount', '>', 0);
            break;
        case 'expense':
            $query->where('wallet_transactions.amount', '<', 0);
            break;
    }
}
```

### 7. **[MỚI]** Logic Lấy Thông tin Ứng tuyển

```php
private function getJobInfo($transaction)
{
    $info = [
        'job' => null,
        'submit_cv' => null,
        'candidate_name' => null,
        'candidate_email' => null,
        'job_name' => null,
        'status' => null
    ];

    // Kiểm tra object liên quan đến giao dịch
    if ($transaction->object && $transaction->object instanceof SubmitCv) {
        $submitCv = $transaction->object;
        $job = $submitCv->job;

        if ($job) {
            $info['job'] = $job;
            $info['job_name'] = $job->name;
            $info['submit_cv'] = $submitCv;
            $info['status'] = $submitCv->status_value ?? '';

            // Lấy thông tin ứng viên
            if ($submitCv->submitCvMeta) {
                $info['candidate_name'] = $submitCv->submitCvMeta->candidate_name;
                $info['candidate_email'] = $submitCv->submitCvMeta->candidate_email;
            } elseif ($submitCv->user) {
                $info['candidate_name'] = $submitCv->user->name;
                $info['candidate_email'] = $submitCv->user->email;
            }
        }
    }

    return $info;
}
```

## Cách sử dụng

1. Truy cập `/admin/transaction-list/`
2. Sử dụng các bộ lọc:
    - Chọn khoảng thời gian
    - Chọn admin quản lý
    - Chọn loại tài khoản
    - **[CẬP NHẬT]** Tìm kiếm công ty (nhập ít nhất 1 ký tự)
    - **[MỚI]** Chọn loại giao dịch (Tiền vào/Tiền ra/Tất cả)
3. Xem widget tổng hợp ở đầu trang
4. Xem danh sách giao dịch với pagination
5. **[MỚI]** Xem thông tin ứng tuyển liên quan đến giao dịch

## Test Cases

### Bộ lọc

-   [x] Lọc theo thời gian hoạt động
-   [x] Lọc theo admin (chỉ admin có quyền company.rotation)
-   [x] Lọc theo loại tài khoản
-   [x] **[CẬP NHẬT]** Lọc theo công ty (select2 ajax autocomplete)
-   [x] **[MỚI]** Lọc theo loại giao dịch (tiền vào/tiền ra)
-   [x] Kết hợp nhiều bộ lọc cùng lúc

### **[MỚI]** Thông tin ứng tuyển

-   [x] Hiển thị thông tin job nếu có
-   [x] Hiển thị thông tin ứng viên nếu có
-   [x] Hiển thị trạng thái ứng tuyển
-   [x] Hiển thị "Không có thông tin ứng tuyển" nếu không có

### Widget tổng hợp

-   [x] Hiển thị đúng tổng thu nhập
-   [x] Hiển thị đúng tổng chi tiêu
-   [x] Áp dụng bộ lọc vào widget
-   [x] **[MỚI]** Áp dụng filter loại giao dịch vào widget

### Hiển thị

-   [x] Sắp xếp theo thời gian mới nhất
-   [x] Pagination hoạt động
-   [x] Hiển thị đúng thông tin giao dịch
-   [x] **[MỚI]** Hiển thị thông tin ứng tuyển

## Ghi chú

-   Trang này dựa trên logic của `EmployerLookupController`
-   Sử dụng cùng cách check quyền `company.rotation` như trong `CompanyController`
-   Widget tổng hợp tính theo bộ lọc hiện tại
-   **[CẬP NHẬT]** Select2 cho công ty với Ajax tìm kiếm để tối ưu performance
-   **[MỚI]** Thông tin ứng tuyển lấy từ polymorphic relationship `object`
-   **[MỚI]** Logic lấy thông tin ứng viên ưu tiên `submitCvMeta` trước `user`
-   **[MỚI]** Filter loại giao dịch giúp dễ dàng phân tích thu chi

## **[CẬP NHẬT]** Thay đổi so với phiên bản trước

### Thêm mới:

1. **Thông tin ứng tuyển**: Cột mới hiển thị job, ứng viên, trạng thái
2. **Select2 Ajax**: Filter công ty sử dụng select2 với tìm kiếm Ajax
3. **Database relationships**: Thêm relationship với SubmitCv và Job
4. **Method getJobInfo()**: Logic lấy thông tin ứng tuyển từ giao dịch
5. **[MỚI]** **Filter loại giao dịch**: Lọc theo tiền vào/tiền ra

### Cải tiến:

1. **Performance**: Filter công ty chỉ tải khi cần thiết
2. **UX**: Bắt buộc nhập ít nhất 1 ký tự để tìm kiếm công ty
3. **UI**: Cột thông tin ứng tuyển với format đẹp hơn
4. **[MỚI]** **Phân tích**: Dễ dàng phân tích thu/chi với filter loại giao dịch

## Files đã tạo/sửa

1. **Tạo mới**:

    - `app/Http/Controllers/Admin/TransactionListController.php`
    - `resources/views/admin/pages/transaction-list/index.blade.php`
    - `memory_bank/wf_20250109_transaction_list_page.md`

2. **Chỉnh sửa**:
    - `routes/admin.php`: Thêm import và routes cho TransactionListController
    - **[CẬP NHẬT]** `app/Http/Controllers/Admin/TransactionListController.php`: Thêm thông tin ứng tuyển, filter loại giao dịch
    - **[CẬP NHẬT]** `resources/views/admin/pages/transaction-list/index.blade.php`: Thêm cột ứng tuyển, cập nhật select2, thêm filter loại giao dịch

## Changelog

### Version 1.0 (2025-01-09 - Ban đầu)

-   Tạo trang danh sách giao dịch cơ bản
-   Các bộ lọc theo thời gian, admin, loại tài khoản, công ty
-   Widget tổng hợp

### Version 1.1 (2025-01-09 - Cập nhật)

-   **[THÊM]** Thông tin ứng tuyển
-   **[CẬP NHẬT]** Filter company thành select2 ajax autocomplete
-   **[CẬP NHẬT]** Cải thiện performance và UX

### Version 1.2 (2025-01-09 - Thêm filter loại giao dịch)

-   **[THÊM]** Bộ lọc loại giao dịch (tiền vào/tiền ra)
-   **[CẬP NHẬT]** Widget tổng hợp áp dụng filter loại giao dịch
-   **[CẬP NHẬT]** Cải thiện layout form filter (col-md-4)

## Tính năng nổi bật

### 🎯 **Bộ lọc toàn diện**

-   ⏰ Thời gian: Từ ngày - đến ngày
-   👨‍💼 Admin: Chỉ admin có quyền company.rotation
-   👤 Loại tài khoản: Employer/Rec
-   🏢 Công ty: Select2 Ajax autocomplete
-   **[MỚI]** 💰 Loại giao dịch: Tiền vào/Tiền ra

### 📊 **Widget thông minh**

-   📈 Tổng thu nhập (có thể lọc riêng)
-   📉 Tổng chi tiêu (có thể lọc riêng)
-   🎛️ Áp dụng tất cả bộ lọc

### 📋 **Thông tin chi tiết**

-   💼 Thông tin job liên quan
-   🧑‍💼 Thông tin ứng viên
-   ✅ Trạng thái ứng tuyển
-   🏷️ Badge màu phân loại

### ⚡ **Performance tối ưu**

-   🔍 Ajax search cho công ty
-   📄 Pagination 20 items
-   🚀 Query tối ưu với joins
-   💾 Cache Select2 results
