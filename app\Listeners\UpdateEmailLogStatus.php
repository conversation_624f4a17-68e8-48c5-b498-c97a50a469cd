<?php

namespace App\Listeners;

use App\Jobs\UpdateEmailLogStatus as JobsUpdateEmailLogStatus;
use Illuminate\Mail\Events\MessageSent;
use Request;
use App\Models\AccessLog;
use App\Models\EmailLog;

class UpdateEmailLogStatus
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MessageSent  $event
     * @return void
     */
    public function handle(MessageSent $event)
    {
        $message = $event->message;
        dispatch(new JobsUpdateEmailLogStatus($message));
        // $hash = md5(implode(',', $message->getTo()) . $message->getSubject() . ',' . $message->getHtmlBody());
        // $email_log = EmailLog::where('hash', $hash)->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-5 minute')))->first();
        // if ($email_log) {
        //     $email_log->status = 1;
        //     $email_log->save();
        // }
    }
}