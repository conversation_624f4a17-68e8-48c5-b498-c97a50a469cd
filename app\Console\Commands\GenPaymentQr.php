<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class GenPaymentQr extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:GenPaymentQr';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'GenPaymentQr';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //63046679
        $str = $this->crcChecksum($text);
        dd($str);

        printf("%u\n", $str);

        return Command::SUCCESS;
    }

    function genPaymentQrSt($sotien, $noidung){
        $text = "00020101021238560010A0000007270126000697041501121146240366660208QRIBFTTA5303704540710000005802VN62250821Nap tien cho NTD 12346304";
    }


    function crcChecksum($str)
    {
        // The PHP version of the JS str.charCodeAt(i)
        function charCodeAt($str, $i)
        {
            return ord(substr($str, $i, 1));
        }

        $crc = 0xFFFF;
        $strlen = strlen($str);
        for ($c = 0; $c < $strlen; $c++) {
            $crc ^= charCodeAt($str, $c) << 8;
            for ($i = 0; $i < 8; $i++) {
                if ($crc & 0x8000) {
                    $crc = ($crc << 1) ^ 0x1021;
                } else {
                    $crc = $crc << 1;
                }
            }
        }
        $hex = $crc & 0xFFFF;
        $hex = dechex($hex);
        $hex = strtoupper($hex);

        return $hex;
    }

}
