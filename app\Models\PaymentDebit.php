<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class PaymentDebit extends BaseModel
{

    protected $fillable = [
        'warehouse_cv_selling_buy_id',
        'user_id',
        'point',
        'paid_point',
        'status',
    ];

    public function getMissingPointAttribute(){
        return $this->point - $this->paid_point > 0 ? $this->point - $this->paid_point : 0;
    }

}
