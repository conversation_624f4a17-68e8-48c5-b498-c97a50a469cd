<?php

namespace App\Jobs;

use App\Notifications\ComplaintsAcceptedToEmployer;
use App\Notifications\ComplaintsAcceptedToEmployerSubmit;
use App\Notifications\ComplaintsAcceptedToRec;
use App\Notifications\ComplaintsAcceptedToRecSubmit;
use App\Notifications\RecAgreeComplain;
use App\Notifications\RecAgreeComplainEmployer;
use App\Notifications\RecAgreeComplainEmployerSubmit;
use App\Notifications\RecAgreeComplainSubmit;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
/*
 * sau 7 ngày khieu nai mà CTV hoac Admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
 */

class RecSumExpiredPointSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $SubmitCvId;
    public $statusComplain;

    public function __construct($SubmitCvId, $statusComplain)
    {
        $this->SubmitCvId = $SubmitCvId;
        $this->statusComplain = $statusComplain;
    }

    /**
     * @return void
     * HT NTD
     */
    public function handle()
    {
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
        $submitCvOnboardRepository = app(SubmitCvOnboardRepository::class);
        $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
        $submitCv = $submitCvRepository->find($this->SubmitCvId);
        if (!empty($submitCv)) {
            //Trạng thái khiếu nại: NTD 1 khiếu nại, CTV 2 từ chối, CTV 3 xác nhận, Admin 4 xác nhân, Admin 5 từ chối
            //1. sau 7 ngày CTV ko cập nhật thì tự động chuyển thành xác nhận khiếu nại => hoan tien NTD
            //2. sau 7 ngày admin ko cập nhật thì tự động chuyển thành xác nhận khiếu nại => hoan tien NTD
            if (
                ($submitCv->status_complain == 1 && $this->statusComplain == 1) ||
                ($submitCv->status_complain == 2 && $this->statusComplain == 2)
            ) {
                try {
                    $submitCvData = [
                        //1 Neu CTV ko xác nhận, thì tự động udpate CTV xác nhận
                        //2 CTV từ chối, admin chưa xác nhận thì tự động update thành admin đã xác nhận
                        'status_complain' => $submitCv->status_complain == 1 ? 3 : 4,
                    ];
                    //CTV xac nhan
                    if ($submitCv->bonus_type == 'cv') {
                        $submitCvData['status'] = config('constant.status_recruitment_revert.CancelBuyCVdata');
                    }

                    if ($submitCv->bonus_type == 'cv' ||  $submitCv->bonus_type == 'interview') {
                        $submitCvData['status_payment'] = 3; //Hoan tien
                    }

                    //                    if ($submitCv->wareHouseCvSelling->type_of_sale == 'onboard'){
                    //                        $cvSellingBuyData['status_payment'] = 2;//Hoàn cọc
                    //                    }
                    $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);

                    $submitCv->update($submitCvData);
                    $rec = $submitCv->rec;
                    $employer = $submitCv->employer;
                    $point = 0;
                    //Hoan tien tra NTD
                    //CV data , interview
                    if ($submitCv->bonus_type == 'cv' || $submitCv->bonus_type == 'interview') {
                        //ghi log mua
                        $submitCvHistoryPaymentRepository->create([
                            'user_id'      => $submitCv->employer->id,
                            'submit_cv_id' => $submitCv->id,
                            'type'         => 1,                                                       //0 trừ tiền, 1 hoàn tiền
                            'percent'      => 100,                                                     //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale' => $submitCv->bonus_type,
                            'amount'       => $submitCv->bonus_point,
                            'balance'      => $submitCv->employer->wallet->amount + $submitCv->bonus_point,
                            'status'       => 0
                        ]);

                        if ($submitCv->bonus_type == 'cv') {
                            //interview ko thay đổi trạng thái
                            //Log thay đổi trang thái
                            $submitCvHistoryStatusRepository->create([
                                'user_id'            => $submitCv->employer->id,
                                'submit_cvs_id'      => $submitCv->id,
                                'status_recruitment' => config('constant.status_recruitment_revert.CancelBuyCVdata'),
                                'type'               => 'employer',
                                'authority'          => $submitCv->authorize
                            ]);
                        }

                        //Cộng point của NTD
                        $point = $submitCv->bonus_point;
                        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->bonus_point;
                        $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                        $submitCv->employer->wallet->addAmount($submitCv->bonus_point, $submitCv, 'Cộng tiền vào ví', 'rec_sum_expired_point_submit');
                        // $submitCv->employer->wallet->save();
                    }
                    //cv onboard
                    if ($submitCv->bonus_type == 'onboard') {

                        if ($submitCv->status == 14) {
                            $onboard = $submitCvOnboardRepository->getFirstBySubmitCvId($submitCv->id);
                            $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                            $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                            $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                            $dateComplain = \Illuminate\Support\Carbon::createFromFormat('Y-m-d H:i:s', $submitCv->date_complain);
                            $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                            $dateComplain =  strtotime($dateComplain);
                            $percent = $point = 0;
                            //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                            if ($createDateTrailWork30 >= $dateComplain) {
                                $percent = 100;
                                $point = $submitCv->bonus_point;
                            }
                            //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                            if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                                $percent = 70;
                                $point = (int) round(($submitCv->bonus_point * $percent) / 100);
                            }
                            //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                            if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                                $percent = 50;
                                $point = (int) round(($submitCv->bonus_point * $percent) / 100);
                            }
                            //ghi log hoan tien
                            $submitCvHistoryPaymentRepository->create([
                                'user_id'      => $submitCv->employer->id,
                                'submit_cv_id' => $submitCv->id,
                                'type'         => 1,                                              //0 trừ tiền, 1 hoàn tiền
                                'percent'      => $percent,
                                'type_of_sale' => $submitCv->bonus_type,
                                'amount'       => $point,
                                'balance'      => $submitCv->employer->wallet->amount + $point,
                                'status'       => 0
                            ]);

                            //Cộng point của NTD
                            $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                            $submitCv->employer->wallet->addAmount($point, $submitCv, 'Cộng tiền vào ví', 'rec_sum_expired_point_submit');
                            // $submitCv->employer->wallet->save();
                            $submitCvData['status_payment'] = 3; //Hoàn cọc
                            $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                            $submitCv->update($submitCvData);
                        } else {
                            $submitCvPaymentHistoryData = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                            //hoàn bao nhiêu point
                            $point = 0;
                            if ($submitCvPaymentHistoryData) {
                                foreach ($submitCvPaymentHistoryData as $key => $value) {
                                    //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                    $value->status = 1;
                                    $value->save();
                                    //ghi log hoan tien
                                    $submitCvHistoryPaymentRepository->create([
                                        'user_id'      => $submitCv->employer->id,
                                        'submit_cv_id' => $submitCv->id,
                                        'type'         => 1,                                                      //0 trừ tiền, 1 hoàn tiền
                                        'percent'      => $value->percent,                                        //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                        'type_of_sale' => $submitCv->bonus_type,
                                        'amount'       => $value->amount,
                                        'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                                        'status'       => 0
                                    ]);
                                    $point += $value->amount;
                                }
                            }
                            //Cộng point của NTD
                            // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
                            $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                            $submitCv->employer->wallet->addAmount($point, $submitCv, 'Cộng tiền vào ví', 'rec_sum_expired_point_submit');
                            // $submitCv->employer->wallet->save();
                            $submitCvData['status_payment'] = 2; //Hoàn cọc
                            $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                            $submitCv->update($submitCvData);
                        }
                    }

                    //gửi email cho CTV
                    if ($submitCv->status_complain == 4) {
                        $submitCv->rec->notify(new ComplaintsAcceptedToRecSubmit($submitCv));
                        $submitCv->employer->notify(new ComplaintsAcceptedToEmployerSubmit($submitCv, $point));
                    }
                    if ($submitCv->status_complain == 3) {
                        $submitCv->rec->notify(new RecAgreeComplainSubmit($rec, $employer, $submitCv));
                        $submitCv->employer->notify(new RecAgreeComplainEmployerSubmit($rec, $employer, $submitCv, $point));
                    }
                } catch (\Exception $e) {
                    Log::info('RecSumPoint error log: ', [
                        'RecSumPoint: ' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}
