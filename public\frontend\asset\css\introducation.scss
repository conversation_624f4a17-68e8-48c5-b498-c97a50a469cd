@import "config";

#modal-introduction-v2 {
    .modal-dialog {
        width: 33.33333vw;
        min-width: 480px;
    }

    .modal-header {
        padding: 0 23px;
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        height: 83px;
        color: $color-palette1;
        border-bottom: 1px solid $color-palette3;
        display: flex;
        width: 100%;
        justify-content: space-between;

        .group-checkbox {
            display: none;
            text-align: right;

            .wapper-checkbox {
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
                margin-bottom: 6px;
            }

            .checkbox-header-modal {
                input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;
                }

                .checkmark {
                    display: inline-block;
                    height: 18px;
                    width: 18px;
                    border-radius: 3px;
                    border: 1px solid $color-st30;
                    margin-right: 9px;
                    position: relative;
                    top: 2px;
                }

                input:checked~.checkmark {
                    background-image: url("../images/member-manager/icon-checkbox-st2.svg");
                    background-repeat: no-repeat;
                    background-position: center;
                }

                .checkmark:after {
                    content: "";
                    position: absolute;
                    display: none;
                }

                input:checked~.checkmark:after {
                    display: block;
                }

                .checkmark:after {}
            }

            .link-checkbox {
                a {
                    display: block;
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 16px;
                    color: $color-primary1;
                }
            }
        }

        .icon-file {
            display: none;
        }
    }

    .btn-confirm {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-primary1;
        margin: 0 12px;
    }


    .btn-cancel {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-primary1;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-palette4;
        margin: 0 12px;
    }

    .step1 {
        padding-top: 24px;
    }

    .step {
        display: none;
    }

    .modal-body {
        padding: 24px 24px;

        .btn-option {
            display: block;
            width: 208px;
            margin: 0 auto;
            margin-bottom: 24px;

            input {
                display: none;
            }

            label {
                display: block;
                width: 100%;
            }

            .label-option {
                display: block;
                width: 100%;
                font-weight: 400;
                height: 46px;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand1;
                background-color: $color-palette4;
                border: 1px solid $color-brand1;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                padding: 10px 10px 10px 58px;

                background-image: url(../images/introduction/icon-option-type1.svg);
                background-repeat: no-repeat;
                background-position: left 24px center;
            }

            input:checked~.label-option {
                background: url(../images/introduction/icon-option-type1-hover.svg) no-repeat left 24px center, linear-gradient(90deg, #056777 0%, #079DB2 100%);
                color: $color-palette4;
            }
        }

        .btn-option-2 {
            .label-option {
                color: $color-brand;
                border: 1px solid $color-brand;
                background-image: url(../images/introduction/icon-option-type2.svg);
                background-repeat: no-repeat;
                background-position: left 24px center;
            }

            input:checked~.label-option {
                background: url(../images/introduction/icon-option-type2-hover.svg) no-repeat left 24px center, linear-gradient(90deg, #FBA943 0%, #FFC680 100%);
                color: $color-palette4;
            }
        }

        .group-input-border {
            padding: 24px;
            border: 1px solid $color-palette3;
            border-radius: 8px;

            label {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-brand1;
                margin-bottom: 16px;
                display: block;
            }

            .select2-container {
                display: block;

                .select2-selection--single {
                    background: $color-palette4;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                    height: 44px;
                    width: 100%;
                }
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 44px;
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: 12px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                top: 0px;
            }
        }

        .group-toggle {
            .header-toggle {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-brand1;
                position: relative;
                margin-bottom: 16px;

                .arrow-toggle {
                    width: 24px;
                    height: 24px;
                    background-image: url(../images/introduction/icon-action-toggle.svg);
                    background-repeat: no-repeat;
                    background-position: center;
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }

            .body-toggle {
                display: none;
            }

            .item-form {
                margin-bottom: 24px;

                label {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    display: block;
                    margin-bottom: 8px;
                    color: $color-palette1;

                    span {
                        color: $color-st9;
                    }
                }

                .group-field {
                    input {
                        display: block;
                        background: $color-palette4;
                        width: 100%;
                        height: 44px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        border: 2px solid #C2EAF4;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                    }

                    input:read-only {
                        background: $color-palette3;
                    }

                    select {
                        background: $color-palette4;
                        width: 100%;
                        height: 44px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        border: none;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                    }

                    textarea {
                        background: $color-palette4;
                        width: 100%;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        border: 2px solid #C2EAF4;
                        color: $color-palette1;
                        padding: 10px 12px;
                        outline: none;
                    }
                }

                .input-unit {
                    display: flex;
                    flex-wrap: wrap;
                    box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
                    border-radius: 8px;
                    border: 2px solid #C2EAF4;

                    input {
                        background: $color-palette4;
                        display: block;
                        width: calc(100% - 82px);
                        box-shadow: none;
                        border-radius: 8px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        border: none;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                        height: 44px;
                    }

                    .select2-unit {
                        width: 82px;
                    }

                    .select2-container .select2-selection--single {
                        box-shadow: none;
                    }

                    .select2-container--default .select2-selection--single .select2-selection__rendered,
                    .select2-container .select2-selection--single,
                    .select2-container--default .select2-selection--single .select2-selection__arrow {
                        border: none;
                        line-height: 44px;
                        //height: 44px;
                    }

                    .select2-container {
                        line-height: 44px;
                        height: 44px;
                    }

                    .group-input-unit {
                        border-right: 1px solid $color-palette3;
                        justify-content: space-between;
                        background: $color-palette4;
                        display: flex;
                        width: calc(100% - 120px);

                        .input-unit-item {
                            width: 45%;
                            position: relative;
                        }

                        .input-unit-item:after {
                            content: '';
                            width: 12px;
                            height: 2px;
                            background: $color-brand1;
                            position: absolute;
                            top: 21px;
                            right: -25px;
                            border-radius: 2px;
                        }

                        .input-unit-item:last-child:after {
                            display: none;
                        }

                        input {
                            border-right: none;
                            width: 100%;
                        }
                    }


                }

                .select2-container {
                    display: block;
                    width: 100%;

                    .select2-selection--single {
                        background: $color-palette4;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        height: 44px;
                        border: 2px solid #C2EAF4;
                        width: 100%;
                    }
                }

                .select2-container--default .select2-selection--single .select2-selection__rendered {
                    line-height: 44px;
                }

                .select2-container .select2-selection--single .select2-selection__rendered {
                    padding-left: 12px;
                    font-size: 14px;
                }

                .select2-container--default .select2-selection--single .select2-selection__arrow {
                    top: 0px;
                }

            }
        }

        .group-toggle.active {
            .body-toggle {
                display: block;
            }

            .arrow-toggle {
                transform: rotate(180deg);
            }
        }

        .drop-upload-file {
            cursor: pointer;

            .image-loading {
                display: none;
            }
        }

        .drop-upload-loading {
            .image-upload {
                display: none;
            }

            .image-loading {
                display: block;
            }
        }

        .image-upload {
            width: 100%;
        }

        .image-loading {
            width: 100%;
        }

        .group-skill {
            margin-bottom: 24px;

            .btn-add-skill {
                display: inline-block;
                width: 20px;
                height: 20px;
                background-image: url(../images/introduction/icon-add-skill.svg);
                background-repeat: no-repeat;
                background-position: center;
                position: relative;
                left: 15px;
                top: 3px;
            }

            label {
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                display: block;
                margin-bottom: 8px;
                color: $color-palette1;
            }

            input {
                background: $color-palette4;
                width: 100%;
                height: 44px;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                border: 2px solid #C2EAF4;
                color: $color-palette1;
                padding: 0 12px;
                outline: none;
                display: block;
            }

            .item-group-skill {
                margin-bottom: 12px;
                padding-right: 48px;
                position: relative;

                .remove {
                    position: absolute;
                    top: 13px;
                    right: 0;
                    width: 20px;
                    height: 20px;
                    background-image: url(../images/introduction/icon-remove-skill.svg);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            }

            .item-group-skill:last-child {
                margin-bottom: 0;
            }

            .select2-container {
                display: block;
                width: 100% !important;
            }

            .select2-container .select2-selection--single {
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                height: 44px;
                width: 100%;
                border: 2px solid #C2EAF4;
            }

            .select2-selection__rendered {
                line-height: 44px;
                font-size: 14px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                top: 0px;
            }
        }

    }

    .select2-search__field {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-palette1;
    }

    .col-iframe {
        display: none;
    }

    .wapper-iframe {
        padding: 24px;
        margin-bottom: 24px;

        iframe {
            width: 100%;
            height: 544px;
        }
    }

    .description-iframe {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        padding-right: 24px;
        padding-left: 54px;
        color: $color-brand;
        background-image: url(../images/introduction/icon-des.svg);
        background-repeat: no-repeat;
        background-position: left 24px top;

        b {
            font-weight: bold;
        }
    }

    .col-iframe {
        border-right: 1px solid $color-palette3;
        box-shadow: 2px 0px 10px rgba(52, 60, 107, 40%);
        position: relative;
        z-index: 99;
    }

    .row-modal {
        height: 100%;

    }

    form {
        height: 100%;
    }

    .col-main {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .text-nextstep {
        display: block;
    }

    .text-finish {
        display: none;
    }

    .modal-step3 {
        width: 66.66666vw;
        min-width: 960px;

        .row-modal {
            display: flex;
            height: 100%;

            .col-modal {
                width: 50%;
            }

            .col-main {
                display: flex;
                flex-direction: column;
            }
        }

        .col-iframe {
            display: block;
        }

        .group-checkbox {
            display: block;
        }

        .text-nextstep {
            display: none;
        }

        .text-finish {
            display: block;
        }
    }

    .modal-step1 {
        .step1 {
            display: block;
        }
    }

    .modal-step2.type-chontukho {
        .step2.step2-chontukho {
            display: block;
        }
    }

    .modal-step2.type-themmoi_cv {
        .step2.step2-themmoi_cv {
            display: block;
        }

        .modal-footer-fixed {
            display: none;
        }

    }

    .modal-step3 {
        .step3 {
            display: block;
        }
    }

    .modal-step3.type-themmoi_cv {
        #cv-download-file {
            display: unset;
        }

        #cv-upload-file {
            display: unset;
        }
    }

    .modal-step3.type-chontukho {
        #cv-download-file {
            display: unset;
        }

    }

    .error {
        color: $color-primary1v2 !important;
        font-weight: 400 !important;
        font-size: 10px !important;
        line-height: 12px !important;
        margin-top: 14px;
        padding-left: 20px;
        position: relative;
    }

    #select2-ware_house_cv_v2-results {
        .select2-results__option {
            padding: 0;
        }

        .item-dropdow-ware_house_cv_v2 {
            padding: 12px 24px;
            border-bottom: 1px solid $color-palette3;
            font-style: normal;
            font-size: 12px;
            line-height: 16px;
            color: $color-st26;

            .line1 {
                margin-bottom: 6px;

                b {
                    font-weight: bold;
                }
            }

            .line2 {
                .dropdown-phone {
                    background-image: url(../images/introduction/icon-phone-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }

                .dropdown-email {
                    background-image: url(../images/introduction/icon-email-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }
            }
        }
    }

    .multiple-select2 {
        .select2-selection--multiple {
            width: 100%;
            height: 46px;
            background-color: $color-palette4;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
            border-radius: 8px;
            border: none;
            padding-bottom: 0;
            background-repeat: no-repeat;
            background-position: center right 15px;
            padding-left: 12px;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg width='32' height='32' viewBox='0 0 32 32' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M9.33337 12.6666L16 19.3333L22.6667 12.6666H9.33337Z' fill='%23F9AC4E'/%3e%3c/svg%3e");
            border: 2px solid #C2EAF4;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border: none;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__clear {
            display: none;
        }

        .select2-container .select2-selection--multiple .select2-selection__rendered {
            position: absolute;
            height: 100%;
            display: flex;
            align-items: center;
            flex-flow: row wrap;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            padding-left: 10px;
            padding-right: 20px;
            border-radius: 15px;
            height: 21px;
            background: $color-white;
            border-color: $color-palette3;
            margin-top: 0;
            margin-left: 0;
            margin-right: 5px;
            display: flex;
            align-items: center;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            left: auto;
            right: 9px;
            border-right: 0;
            padding: 0;
            color: $color-brand;
            font-size: 18px;
            top: -2px;
        }

        .select2-search__field {
            display: none;
        }

        .select2-selection__choice__display {
            color: $color-palette1;
            font-size: 14px;
        }
    }

    .select2-selection__arrow {
        //background: url(../images/list-new/arrow-select.svg) no-repeat;
        background-position: center;
        top: 14px;
        right: 12px;

        b {
            display: none;
        }
    }

    .select2-dropdown {
        margin-top: 6px;
    }

    .select2-container--open .select2-dropdown--below {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-right: 2px;
    }

    .select2-container--default .select2-results>.select2-results__options {
        overflow-y: scroll;
        font-size: 14px;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
        width: 4px;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
        background: $color-white;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
        background: $color-brand1;
        border-radius: 2px;
    }

    .select2-results:before {
        content: '';
        width: 10px;
        height: 10px;
        position: absolute;
        top: -3px;
        right: 17px;
        background: $color-white;
        transform: rotate(45deg);
    }

    .select2-container--open .select2-dropdown--above {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-right: 2px;
    }

    .select2-dropdown--above {
        .select2-results:before {
            bottom: -3px;
            top: auto;
        }
    }

    .select2-dropdown--above.select2-dropdown {
        margin-top: -6px;
    }

}

#modal-add-new-submitcv {
    .modal-dialog {
        width: 33.33333vw;
        min-width: 480px;
    }

    .modal-header {
        padding: 0 23px;
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        height: 83px;
        color: $color-palette1;
        border-bottom: 1px solid $color-palette3;
        display: flex;
        width: 100%;
        justify-content: space-between;

        .group-checkbox {
            display: none;
            text-align: right;

            .wapper-checkbox {
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
                margin-bottom: 6px;
            }

            .checkbox-header-modal {
                input {
                    position: absolute;
                    opacity: 0;
                    cursor: pointer;
                    height: 0;
                    width: 0;
                }

                .checkmark {
                    display: inline-block;
                    height: 18px;
                    width: 18px;
                    border-radius: 3px;
                    border: 1px solid $color-st30;
                    margin-right: 9px;
                    position: relative;
                    top: 2px;
                }

                input:checked~.checkmark {
                    background-image: url("../images/member-manager/icon-checkbox-st2.svg");
                    background-repeat: no-repeat;
                    background-position: center;
                }

                .checkmark:after {
                    content: "";
                    position: absolute;
                    display: none;
                }

                input:checked~.checkmark:after {
                    display: block;
                }

                .checkmark:after {}
            }

            .link-checkbox {
                a {
                    display: block;
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 16px;
                    color: $color-primary1;
                }
            }
        }

        .icon-file {
            display: none;
        }
    }

    .btn-confirm {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-primary1;
        margin: 0 12px;
    }

    .btn-primary {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-primary1;
        margin: 0 12px;
    }

    .btn-sm {
        padding: 5px 20px !important;
        width: auto;
    }

    .btn-cancel {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-primary1;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-palette4;
        margin: 0 12px;
    }

    .step1 {
        padding-top: 24px;
    }

    .step {
        display: none;
    }

    .modal-body {
        padding: 24px 24px;

        .btn-option {
            display: block;
            width: 208px;
            margin: 0 auto;
            margin-bottom: 24px;

            input {
                display: none;
            }

            label {
                display: block;
                width: 100%;
            }

            .label-option {
                display: block;
                width: 100%;
                font-weight: 400;
                height: 46px;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand1;
                background-color: $color-palette4;
                border: 1px solid $color-brand1;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                padding: 10px 10px 10px 58px;

                background-image: url(../images/introduction/icon-option-type1.svg);
                background-repeat: no-repeat;
                background-position: left 24px center;
            }

            input:checked~.label-option {
                background: url(../images/introduction/icon-option-type1-hover.svg) no-repeat left 24px center, linear-gradient(90deg, #056777 0%, #079DB2 100%);
                color: $color-palette4;
            }
        }

        .btn-option-2 {
            .label-option {
                color: $color-brand;
                border: 1px solid $color-brand;
                background-image: url(../images/introduction/icon-option-type2.svg);
                background-repeat: no-repeat;
                background-position: left 24px center;
            }

            input:checked~.label-option {
                background: url(../images/introduction/icon-option-type2-hover.svg) no-repeat left 24px center, linear-gradient(90deg, #FBA943 0%, #FFC680 100%);
                color: $color-palette4;
            }
        }

        .group-input-border {
            padding: 24px;
            border: 1px solid $color-palette3;
            border-radius: 8px;

            label {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-brand1;
                margin-bottom: 16px;
                display: block;
            }

            .select2-container {
                display: block;

                .select2-selection--single {
                    background: $color-palette4;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                    height: 44px;
                    width: 100%;
                }
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 44px;
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: 12px;
                font-size: 14px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                top: 0px;
            }
        }

        .group-toggle {
            .header-toggle {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-brand1;
                position: relative;
                margin-bottom: 16px;

                .arrow-toggle {
                    width: 24px;
                    height: 24px;
                    background-image: url(../images/introduction/icon-action-toggle.svg);
                    background-repeat: no-repeat;
                    background-position: center;
                    position: absolute;
                    top: 0;
                    right: 0;
                }
            }

            .body-toggle {
                display: none;
            }

            .item-form {
                margin-bottom: 24px;

                label {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    display: block;
                    margin-bottom: 8px;
                    color: $color-palette1;

                    span {
                        color: $color-st9;
                    }
                }

                .group-field {
                    input {
                        border: 2px solid #C2EAF4;
                        display: block;
                        background: $color-palette4;
                        width: 100%;
                        height: 44px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                    }

                    select {
                        background: $color-palette4;
                        width: 100%;
                        height: 44px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        border: none;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                    }

                    textarea {
                        border: 2px solid #C2EAF4;
                        background: $color-palette4;
                        width: 100%;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        color: $color-palette1;
                        padding: 10px 12px;
                        outline: none;
                    }
                }

                .input-unit {
                    display: flex;
                    flex-wrap: wrap;
                    box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
                    border-radius: 8px;
                    border: 2px solid #C2EAF4;

                    input {
                        background: $color-palette4;
                        display: block;
                        width: calc(100% - 120px);
                        box-shadow: none;
                        border-radius: 8px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        border: none;
                        color: $color-palette1;
                        padding: 0 12px;
                        outline: none;
                        height: 44px;
                    }

                    .select2-unit {
                        width: 82px;
                    }

                    .select2-container .select2-selection--single {
                        box-shadow: none;
                    }

                    .select2-container--default .select2-selection--single .select2-selection__rendered,
                    .select2-container .select2-selection--single,
                    .select2-container--default .select2-selection--single .select2-selection__arrow {
                        border: none;
                        line-height: 44px;
                        height: 44px;
                    }

                    .select2-container {
                        line-height: 44px;
                        height: 44px;
                    }

                    .group-input-unit {
                        border-right: 1px solid $color-palette3;
                        justify-content: space-between;
                        background: $color-palette4;
                        display: flex;
                        width: calc(100% - 120px);
                        border: 2px solid #C2EAF4;

                        .input-unit-item {
                            width: 45%;
                            position: relative;
                        }

                        .input-unit-item:after {
                            content: '';
                            width: 12px;
                            height: 2px;
                            background: $color-brand1;
                            position: absolute;
                            top: 21px;
                            right: -25px;
                            border-radius: 2px;
                        }

                        .input-unit-item:last-child:after {
                            display: none;
                        }

                        input {
                            border-right: none;
                            width: 100%;
                        }
                    }


                }

                .select2-container {
                    display: block;

                    .select2-selection--single {
                        background: $color-palette4;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        height: 44px;
                        width: 100%;
                    }
                }

                .select2-container--default .select2-selection--single .select2-selection__rendered {
                    line-height: 44px;
                }

                .select2-container .select2-selection--single .select2-selection__rendered {
                    padding-left: 12px;
                    font-size: 14px;
                }

                .select2-container--default .select2-selection--single .select2-selection__arrow {
                    top: 0px;
                }

                .select2-selection__arrow {
                    display: none;
                }

            }
        }

        .group-toggle.active {
            .body-toggle {
                display: block;
            }

            .arrow-toggle {
                transform: rotate(180deg);
            }
        }

        .drop-upload-file {
            cursor: pointer;

            .image-loading {
                display: none;
            }
        }

        .drop-upload-loading {
            .image-upload {
                display: none;
            }

            .image-loading {
                display: block;
            }
        }

        .image-upload {
            width: 100%;
        }

        .image-loading {
            width: 100%;
        }

        .group-skill {
            margin-bottom: 24px;

            .btn-add-skill {
                display: inline-block;
                width: 20px;
                height: 20px;
                background-image: url(../images/introduction/icon-add-skill.svg);
                background-repeat: no-repeat;
                background-position: center;
                position: relative;
                left: 15px;
                top: 3px;
            }

            label {
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                display: block;
                margin-bottom: 8px;
                color: $color-palette1;
            }

            input {
                background: $color-palette4;
                width: 100%;
                height: 44px;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                border: none;
                color: $color-palette1;
                padding: 0 12px;
                outline: none;
                display: block;
            }

            .item-group-skill {
                margin-bottom: 12px;
                padding-right: 48px;
                position: relative;

                .remove {
                    position: absolute;
                    top: 13px;
                    right: 0;
                    width: 20px;
                    height: 20px;
                    background-image: url(../images/introduction/icon-remove-skill.svg);
                    background-repeat: no-repeat;
                    background-position: center;
                }
            }

            .item-group-skill:last-child {
                margin-bottom: 0;
            }

            .select2-container {
                display: block;
                width: 100% !important;
            }

            .select2-container .select2-selection--single {
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                height: 44px;
                width: 100%;
                border: none;
            }

            .select2-selection__rendered {
                line-height: 44px;
                font-size: 14px;
            }

            .select2-container--default .select2-selection--single .select2-selection__arrow {
                display: none;
            }
        }

    }

    .select2-search__field {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-palette1;
    }

    .col-iframe {
        display: none;
    }

    .wapper-iframe {
        padding: 24px;
        margin-bottom: 24px;

        iframe {
            width: 100%;
            height: 544px;
        }
    }

    .description-iframe {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        padding-right: 24px;
        padding-left: 54px;
        color: $color-brand;
        background-image: url(../images/introduction/icon-des.svg);
        background-repeat: no-repeat;
        background-position: left 24px top;

        b {
            font-weight: bold;
        }
    }

    .col-iframe {
        border-right: 1px solid $color-palette3;
        box-shadow: 2px 0px 10px rga(52, 60, 107, 40%);
        position: relative;
        z-index: 99;
    }

    .row-modal {
        height: 100%;

    }

    form {
        height: 100%;
    }

    .col-main {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .text-nextstep {
        display: block;
    }

    .text-finish {
        display: none;
    }

    .modal-step3 {
        width: 66.66666vw;
        min-width: 960px;

        .row-modal {
            display: flex;
            height: 100%;

            .col-modal {
                width: 50%;
            }

            .col-main {
                display: flex;
                flex-direction: column;
            }
        }

        .col-iframe {
            display: block;
        }

        .group-checkbox {
            display: block;
        }

        .text-nextstep {
            display: none;
        }

        .text-finish {
            display: block;
        }
    }

    .modal-step1 {
        .step1 {
            display: block;
        }
    }

    .modal-step2.type-chontukho {
        .step2.step2-chontukho {
            display: block;
        }
    }

    .modal-step2.type-themmoi_cv {
        .step2.step2-themmoi_cv {
            display: block;
        }

        .modal-footer-fixed {
            display: none;
        }

    }

    .modal-step3 {
        .step3 {
            display: block;
        }
    }

    .modal-step3.type-themmoi_cv {
        #cv-download-file {
            display: unset;
        }

        #cv-upload-file {
            display: unset;
        }
    }

    .modal-step3.type-chontukho {
        #cv-download-file {
            display: unset;
        }

    }

    .error {
        color: $color-primary1v2 !important;
        font-weight: 400 !important;
        font-size: 10px !important;
        line-height: 12px !important;
        margin-top: 14px;
        padding-left: 20px;
        position: relative;
    }

    #select2-ware_house_cv_v2-results {
        .select2-results__option {
            padding: 0;
        }

        .item-dropdow-ware_house_cv_v2 {
            padding: 12px 24px;
            border-bottom: 1px solid $color-palette3;
            font-style: normal;
            font-size: 12px;
            line-height: 16px;
            color: $color-st26;

            .line1 {
                margin-bottom: 6px;

                b {
                    font-weight: bold;
                }
            }

            .line2 {
                .dropdown-phone {
                    background-image: url(../images/introduction/icon-phone-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }

                .dropdown-email {
                    background-image: url(../images/introduction/icon-email-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }
            }
        }
    }

    .multiple-select2 {
        .select2-selection--multiple {
            width: 100%;
            height: 46px;
            background-color: $color-palette4;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
            border-radius: 8px;
            border: none;
            padding-bottom: 0;
            background-image: url(../images/list-new/arrow-select.svg);
            background-repeat: no-repeat;
            background-position: center right 15px;
            padding-left: 12px;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border: none;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__clear {
            display: none;
        }

        .select2-container .select2-selection--multiple .select2-selection__rendered {
            position: absolute;
            height: 100%;
            display: flex;
            align-items: center;
            flex-flow: row wrap;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            padding-left: 10px;
            padding-right: 20px;
            border-radius: 15px;
            height: 21px;
            background: $color-white;
            border-color: $color-palette3;
            margin-top: 0;
            margin-left: 0;
            margin-right: 5px;
            display: flex;
            align-items: center;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            left: auto;
            right: 9px;
            border-right: 0;
            padding: 0;
            color: $color-brand;
            font-size: 18px;
            top: -2px;
        }

        .select2-search__field {
            display: none;
        }

        .select2-selection__choice__display {
            color: $color-palette1;
        }
    }

    .select2-results__options {
        max-width: 100%;
    }

    .select2-selection__arrow {
        //background: url(../images/list-new/arrow-select.svg) no-repeat;
        background-position: center;
        top: 14px;
        right: 12px;

        b {
            display: none;
        }
    }

    .select2-dropdown {
        margin-top: 6px;
    }

    .select2-container--open .select2-dropdown--below {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-right: 2px;
    }

    .select2-container--default .select2-results>.select2-results__options {
        overflow-y: scroll;
        font-size: 14px;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar {
        width: 4px;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-track {
        background: $color-white;
    }

    .select2-container--default .select2-results>.select2-results__options::-webkit-scrollbar-thumb {
        background: $color-brand1;
        border-radius: 2px;
    }

    .select2-results:before {
        content: '';
        width: 10px;
        height: 10px;
        position: absolute;
        top: -3px;
        right: 17px;
        background: $color-white;
        transform: rotate(45deg);
    }

    .select2-container--open .select2-dropdown--above {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        padding-top: 2px;
        padding-bottom: 2px;
        padding-right: 2px;
    }

    .select2-dropdown--above {
        .select2-results:before {
            bottom: -3px;
            top: auto;
        }
    }

    .select2-dropdown--above.select2-dropdown {
        margin-top: -6px;
    }


}


#modal-introduction-success {
    background: rgba(0, 0, 0, 0.5);

    .modal-footer {
        border-top: none;
    }

    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-content {
        padding-top: 48px;
        padding-bottom: 56px;
        background-image: url(../images/member-manager/bg-modal.svg);
        background-repeat: no-repeat;
        background-position: right 22px top -12px;
    }

    .modal-header {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: $color-palette1;
        text-align: center;
        padding: 0;
        margin-bottom: 48px;
    }

    .modal-body {
        margin-bottom: 48px;
        text-align: center;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 64px;
        padding-right: 64px;
        color: $color-palette1;

        p {
            margin-bottom: 12px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;

            span {
                color: $color-brand;
            }

            a {
                color: $color-primary1;
            }
        }

        p:last-child {
            margin-bottom: 0;
        }
    }

    .modal-footer {
        padding: 0;
        justify-content: center;
        gap: 48px;

        .btn {
            width: 160px;
            height: 46px;
            margin: 0;
            border-radius: 30px;
            border: 1px solid $color-brand1;
            color: $color-brand1;
        }

        .btn:focus {
            box-shadow: none;
        }
    }
}

#modal-authority-confirm {
    background: rgba(0, 0, 0, 0.5);

    .modal-footer {
        border-top: none;
    }

    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-content {
        padding-top: 48px;
        padding-bottom: 56px;
        background-image: url(../images/member-manager/bg-modal.svg);
        background-repeat: no-repeat;
        background-position: right 22px top -12px;
    }

    .modal-header {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: $color-palette1;
        text-align: center;
        padding: 0;
        margin-bottom: 48px;
    }

    .modal-body {
        margin-bottom: 48px;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 64px;
        padding-right: 64px;
        color: $color-palette1;

        p {
            margin-bottom: 12px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;

        }

        .st1 {
            color: $color-brand;
        }

        .st2 {
            color: $color-primary1;
        }

        .wapper-checkbox {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: $color-palette1;
            margin-bottom: 6px;
        }

        .checkbox-modal {
            display: block;

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .checkmark {
                display: inline-block;
                height: 18px;
                width: 18px;
                border-radius: 3px;
                border: 1px solid $color-st30;
                margin-right: 9px;
                position: relative;
                top: 4px;
            }

            input:checked~.checkmark {
                background-image: url("../images/member-manager/icon-checkbox-st2.svg");
                background-repeat: no-repeat;
                background-position: center;
            }

            .checkmark:after {
                content: "";
                position: absolute;
                display: none;
            }

            input:checked~.checkmark:after {
                display: block;
            }

            .checkmark:after {}
        }

        .link-checkbox {
            a {
                display: block;
                font-weight: 600;
                font-size: 12px;
                line-height: 16px;
                color: $color-primary1;
            }
        }

        .error {
            color: $color-primary1v2 !important;
            font-weight: 400 !important;
            font-size: 10px !important;
            line-height: 12px !important;
            margin-top: 14px;
            padding-left: 20px;
            position: relative;
        }
    }

    .modal-footer {
        padding: 0;
        justify-content: center;
        gap: 48px;

        .btn {
            width: 160px;
            height: 46px;
            margin: 0;
            border-radius: 30px;
            border: 1px solid $color-brand1;
            color: $color-brand1;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        }

        .btn-cancel {
            border: 1px solid $color-primary1v2;
            color: $color-primary1v2;
        }

        .btn-confirm {
            border: 1px solid $color-brand1;
            background: $color-brand1;
            color: $color-palette4;
        }

        .btn:focus {
            box-shadow: none;
        }
    }
}

#modal-sell-cv.right {
    font-size: inherit;
}

#modal-sell-cv {
    .modal-dialog {
        width: 33.33333vw;
        min-width: 480px;
    }

    .modal-header {
        padding: 0 23px;
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        height: 83px;
        color: $color-palette1;
        border-bottom: 1px solid $color-palette3;
        width: 100%;
    }

    .modal-title {
        display: block;
        font-size: 20px;
    }

    .modal-body {
        padding: 24px 24px;
    }

    .step {
        display: none;
    }

    .modal-step1 {
        .step1 {
            display: block;
        }

        .modal-footer-fixed {
            display: none;
        }
    }

    .modal-footer-fixed {
        display: block;
    }

    .modal-step2 {
        .step2 {
            display: block;
        }
    }

    .group-input-border {
        padding: 24px;
        border: 1px solid $color-palette3;
        border-radius: 8px;

        label {
            color: $color-brand1;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 16px;
            display: block;
        }

        .select2-container {
            display: block;

            .select2-selection--single {
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                height: 44px;
                width: 100%;
                border: 1px solid #079DB2;
            }

        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            padding-left: 12px;
            font-size: 14px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            top: 0px;
        }

    }

    .select2-container--default .select2-results>.select2-results__options {
        max-width: 100%;
    }

    #select2-ware_house_cv_v2-results {
        .select2-results__option {
            padding: 0;
        }

        .item-dropdow-ware_house_cv_v2 {
            padding: 12px 24px;
            border-bottom: 1px solid $color-palette3;
            font-style: normal;
            font-size: 12px;
            line-height: 16px;
            color: $color-st26;

            .line1 {
                margin-bottom: 6px;

                b {
                    font-weight: bold;
                }
            }

            .line2 {
                .dropdown-phone {
                    background-image: url(../images/introduction/icon-phone-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }

                .dropdown-email {
                    background-image: url(../images/introduction/icon-email-dropdown.svg);
                    background-repeat: no-repeat;
                    background-position: left center;
                    margin-right: 25px;
                    padding-left: 22px;
                }
            }
        }
    }

    .select2-dropdown {
        margin-top: 6px;
        //border: 1px solid $color-palette3;
    }

    .select2-dropdown--below {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    .select2-dropdown--below {
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
    }

    .select2-selection__arrow {
        //background: url(../images/list-new/arrow-select.svg) no-repeat;
        background-position: center;
        top: 14px;
        right: 12px;
    }

    .select2-selection__arrow b {
        display: none;
    }

    .select2-selection__clear {
        display: none;
    }

    .customer-dropdown-select2 {
        border: 1px solid $color-palette3;

        .select2-results__option {
            color: $color-st26;
            border-bottom: 1px solid $color-palette3;
            height: auto;
            min-height: 40px;
        }

        .select2-dropdown {}
    }

    .select2-results__option {
        height: auto;
        width: 100%;
    }

    .line-1-step2 {
        display: flex;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 22px;

        .title-line-1-step-2 {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            color: $color-brand1;
            line-height: 24px;
        }

        .view-detail-cv {
            a {
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: $color-st6;
            }

            img {
                position: relative;
                top: -2px;
            }
        }
    }

    .line-2-step2 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 16px;
        margin-bottom: 38px;

        .name-line2-step2 {
            margin-bottom: 6px;
            font-style: normal;
            font-size: 14px;
            line-height: 20px;
            color: $color-st26;

            span {
                font-weight: 600;
            }
        }

        .info-line-2-step2 {
            ul {
                li {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 16px;
                    display: inline-block;
                    color: $color-palette1;
                    margin-right: 24px;

                    img {
                        position: relative;
                        top: -1px;
                    }
                }
            }
        }

        .btn-back-to-step1 {
            a {
                padding: 6px 12px;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                display: block;
                color: $color-primary1;
                border: 1px solid #079DB2;
                border-radius: 4px;
            }

        }
    }


    .form-step2 {
        .title-form-step2 {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 16px;
        }

        .item-form {
            margin-bottom: 24px;

            label {
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                display: block;
                margin-bottom: 8px;
                color: $color-palette1;

                span {
                    color: $color-st9;
                }
            }
        }

        input {
            display: block;
            background: #FCFCFE;
            width: 100%;
            height: 44px;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            border: none;
            color: #3C4455;
            padding: 0 12px;
            outline: none;
        }

        .select2-container {
            display: block;

            .select2-selection--single {
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                height: 44px;
                width: 100%;
            }
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 44px;
            color: $color-st26;
        }

        .select2-container .select2-selection--single .select2-selection__rendered {
            padding-left: 12px;
            font-size: 14px;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            top: 0px;
        }


        //.range{
        //    margin-top: 40px;
        //    margin-bottom: 28px;
        //    padding: 0 10px;
        //}
        //
        //.range-slider {
        //
        //    height: 5px;
        //    position: relative;
        //    background-color: #e1e9f6;
        //    border-radius: 2px;
        //}
        //.range-selected {
        //    height: 100%;
        //    left: 0;
        //    right: 0;
        //    width: 100%;
        //    position: absolute;
        //    border-radius: 5px;
        //    background-color: $color-st6;
        //}
        //
        //.range-input {
        //    position: relative;
        //}
        //.range-input input {
        //    position: absolute;
        //    width: 100%;
        //    height: 5px;
        //    top: -7px;
        //    background: none;
        //    pointer-events: none;
        //    -webkit-appearance: none;
        //    -moz-appearance: none;
        //}
        //.range-input input::-webkit-slider-thumb {
        //    height: 20px;
        //    width: 20px;
        //    border-radius: 50%;
        //    background: radial-gradient(50% 50% at 50% 50%, #FBA943 0%, rgba(251, 169, 67, 0.2) 100%);
        //    pointer-events: auto;
        //    -webkit-appearance: none;
        //}
        //.range-input input::-moz-range-thumb {
        //    height: 15px;
        //    width: 15px;
        //    border-radius: 50%;
        //    background: radial-gradient(50% 50% at 50% 50%, #FBA943 0%, rgba(251, 169, 67, 0.2) 100%);
        //    pointer-events: auto;
        //    -moz-appearance: none;
        //}
        //
        //.range-price {
        //    margin: 30px 0;
        //    width: 100%;
        //    display: flex;
        //    justify-content: center;
        //    align-items: center;
        //}
        //.range-price label {
        //    margin-right: 5px;
        //}
        //.range-price input {
        //    width: 40px;
        //    padding: 5px;
        //}
        //.range-price input:first-of-type {
        //    margin-right: 15px;
        //}
        //
        //.range-value{
        //    position: absolute;
        //    top: -39px;
        //}
        //.range-value span{
        //    font-size: 14px;
        //    line-height: 20px;
        //    width: 30px;
        //    height: 24px;
        //    text-align: center;
        //    display: block;
        //    position: absolute;
        //    left: 50%;
        //    transform: translate(-50%, 0);
        //    border-radius: 6px;
        //    color: $color-palette1;
        //}

        .label-header-item-form {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-secondary1;
            display: flex;
            justify-content: space-between;
        }

        .confirm-step2 {
            label {
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                color: $color-palette1;
                line-height: 20px;
                padding-top: 10px;

                span {
                    color: $color-primary1;
                }
            }

            label.error {
                padding-top: 0;
            }

            input {
                display: none;
            }

            input:checked~.checkmark {
                background-image: url(../images/cv-on-sale/tick.svg);
                background-repeat: no-repeat;
                background-position: center;
            }

            input:checked~.checkmark:after {
                display: block;
            }

            .checkmark {
                display: inline-block;
                height: 18px;
                width: 18px;
                border-radius: 3px;
                border: 2px solid $color-brand1;
                margin-right: 9px;
                position: relative;
                top: 3px;
            }
        }

        .form-item-price {
            margin-bottom: 34px;

            .input-unit {
                margin-bottom: 8px
            }
        }

        .after-price-cv {
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            color: #7984A5;
            padding-left: 12px;
            margin-bottom: 8px;

            span {
                color: #FD8F3B;
            }
        }

        .input-unit {
            input {
                box-shadow: none;
            }

            .group-input-unit {
                width: calc(100% - 120px);
            }

            display: flex;
            flex-wrap: wrap;
            box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
            border-radius: 8px;

            .label-unit {
                width: 82px;
                line-height: 44px;
                height: 44px;
                text-align: center;
                color: #3C4455;
                border-left: 1px solid #DEE4F5;
            }
        }

        .title-cv-company {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-brand1;
            margin-bottom: 16px;
        }

        .input-search-company {
            background: $color-palette4 url(../images/search-job/search-job.svg) no-repeat;
            background-position: right 16px center;
        }

        .company-selected-field {
            position: relative;
            margin-bottom: 6px;

            .remove {
                width: 24px;
                height: 24px;
                background: url(../images/cv-on-sale/icon-remove.svg) no-repeat;
                background-position: center;
                position: absolute;
                right: 12px;
                top: 10px;
            }
        }
    }

    .row-modal {
        display: flex;
        height: 100%;
    }

    .col-right-header {
        display: none;
    }

    .action-group-checkbox {
        display: none;
    }

    .col-iframe {
        display: none;
    }

    .modal-step2 {
        .action-group-checkbox {
            display: block;
        }

        .wapper-col-left-header {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-between;
        }

        .group-checkbox {
            .wapper-checkbox {
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
                margin-bottom: 6px;
                text-align: right;

                .checkbox-header-modal {
                    input {
                        position: absolute;
                        opacity: 0;
                        cursor: pointer;
                        height: 0;
                        width: 0;
                    }

                    .checkmark {
                        display: inline-block;
                        height: 18px;
                        width: 18px;
                        border-radius: 3px;
                        border: 1px solid #231F20;
                        margin-right: 9px;
                        position: relative;
                        top: 2px;
                    }

                    input:checked~.checkmark:after {
                        display: block;
                        background-color: inherit;
                    }

                    input:checked~.checkmark {
                        background-image: url(../images/member-manager/icon-checkbox-st2.svg);
                        background-repeat: no-repeat;
                        background-position: center;
                    }

                    .checkmark {
                        display: inline-block;
                        height: 18px;
                        width: 18px;
                        border-radius: 3px;
                        border: 1px solid #231F20;
                        margin-right: 9px;
                        position: relative;
                        top: 2px;
                    }

                    .checkmark:after {
                        content: "";
                        position: absolute;
                        display: none;
                    }

                }

            }

            .link-checkbox {
                a {
                    display: block;
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 16px;
                    color: #079DB2;
                }
            }
        }

        .wapper-col-right-header {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-between;

            //padding: 0 23px;
            .modal-title {
                img {
                    margin-right: 0;
                }
            }
        }

        .button-image {
            padding-right: 24px;

            a {
                margin-left: 10px
            }
        }

        .col-left-header {
            width: 100%;
        }

        .row-header {
            width: 100%;
        }
    }

    .view-more-cv {
        width: 66.66666vw;
        min-width: 960px;

        .col-right-header {
            display: block;
        }

        .modal-body {
            padding: 0;
            overflow-y: hidden;
        }

        .col-modal {
            overflow-y: scroll;
        }

        .col-modal {
            height: 100%;
        }

        .step {
            height: 100%;
        }

        .col-modal {
            padding: 24px;
            width: 50%;
        }

        .col-main {
            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
            position: relative;
            z-index: 99;
        }

        .modal-header {
            display: block;
            padding: 0;
        }

        .row-header {
            height: 100%;
            display: flex;
        }

        .col-left-header {
            width: 50%;
            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
        }

        .col-right-header {
            width: 50%;
        }

        .wapper-col-left-header {
            padding: 0 23px;
        }

        .step2 {
            display: flex;
        }

        .wapper-iframe {
            iframe {
                width: 100%;
                height: 544px;
            }
        }

        .description-iframe {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            padding-right: 24px;
            padding-left: 54px;
            color: #FD8F3B;
            background-image: url(../images/introduction/icon-des.svg);
            background-repeat: no-repeat;
            background-position: left 24px top;
        }

        .col-iframe {
            display: block;
        }
    }


    .btn-confirm {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-primary1;
        margin: 0 12px;
    }

    .btn-cancel {
        padding: 13px 15px;
        width: 160px;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-primary1;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: 1px solid $color-primary1;
        background: $color-palette4;
        margin: 0 12px;
    }

    .error-message,
    label.error {
        background-position: top -1px left;
    }

}

#modal-sell-success {
    background: rgba(0, 0, 0, 0.5);

    .modal-footer {
        border-top: none;
    }

    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-content {
        padding-top: 48px;
        padding-bottom: 56px;
        background-image: url(../images/member-manager/bg-modal.svg);
        background-repeat: no-repeat;
        background-position: right 22px top -12px;
    }

    .modal-header {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: $color-palette1;
        text-align: center;
        padding: 0;
        margin-bottom: 48px;
    }

    .modal-body {
        margin-bottom: 48px;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 64px;
        padding-right: 64px;
        color: $color-palette1;
        text-align: center;

        p {
            margin-bottom: 10px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;

        }

        .st1 {
            color: $color-brand;
        }

        .name {
            font-size: 18px;
        }

        .st2 {
            color: $color-primary1;
        }
    }

    .modal-footer {
        padding: 0;
        justify-content: center;
        gap: 48px;

        .btn {
            width: 160px;
            height: 46px;
            margin: 0;
            border-radius: 30px;
            border: 1px solid $color-brand1;
            color: $color-brand1;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        }

        .btn-cancel {
            border: 1px solid $color-brand1;
            color: $color-brand1;
        }

        .btn-confirm {
            border: 1px solid $color-brand1;
            background: $color-brand1;
            color: $color-palette4;
            line-height: 32px;
        }

        .btn:focus {
            box-shadow: none;
        }
    }
}

#modal-sell-error {
    background: rgba(0, 0, 0, 0.5);

    .modal-footer {
        border-top: none;
    }

    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-content {
        padding-top: 48px;
        padding-bottom: 56px;
        background-image: url(../images/member-manager/bg-modal.svg);
        background-repeat: no-repeat;
        background-position: right 22px top -12px;
    }

    .modal-header {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: $color-palette1;
        text-align: center;
        padding: 0;
        margin-bottom: 48px;
    }

    .modal-body {
        margin-bottom: 48px;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 64px;
        padding-right: 64px;
        color: $color-palette1;
        text-align: center;

        p {
            margin-bottom: 10px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;

        }

        .st1 {
            color: $color-brand;
        }

        .name {
            font-size: 18px;
        }

        .st2 {
            color: $color-primary1;
        }
    }

    .modal-footer {
        padding: 0;
        justify-content: center;
        gap: 48px;

        .btn {
            width: 160px;
            height: 46px;
            margin: 0;
            border-radius: 30px;
            border: 1px solid $color-brand1;
            color: $color-brand1;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        }

        .btn-cancel {
            border: 1px solid $color-brand1;
            color: $color-brand1;
        }

        .btn-confirm {
            border: 1px solid $color-brand1;
            background: $color-brand1;
            color: $color-palette4;
        }

        .btn:focus {
            box-shadow: none;
        }
    }
}

.modal-confirm-authority {
    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-content {
        padding-top: 48px;
        padding-bottom: 56px;
        background-image: url(../images/member-manager/bg-modal.svg);
        background-repeat: no-repeat;
        background-position: right 22px top -12px;
    }

    .modal-header {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: #3C4455;
        text-align: center;
        padding: 0;
        margin-bottom: 48px;
    }

    .modal-body {
        margin-bottom: 48px;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 64px;
        padding-right: 64px;
        color: #3C4455;

        .content {
            color: #3C4455;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #DEE4F5;
            margin-bottom: 20px;

            b {
                font-weight: 700;
            }
        }

        .line-info {
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
            color: #3C4455;
            margin-bottom: 20px;

            .bonus {
                color: #FD8F3B;
            }

            .onboard {
                color: #079DB2;
            }

        }

        .wapper-checkbox {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: $color-palette1;
            margin-bottom: 6px;
        }

        .checkbox-modal {
            display: block;

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 0;
                width: 0;
            }

            .checkmark {
                display: inline-block;
                height: 18px;
                width: 18px;
                border-radius: 3px;
                border: 1px solid $color-st30;
                margin-right: 9px;
                position: relative;
                top: 4px;
            }

            input:checked~.checkmark {
                background-image: url("../images/member-manager/icon-checkbox-st2.svg");
                background-repeat: no-repeat;
                background-position: center;
            }

            .checkmark:after {
                content: "";
                position: absolute;
                display: none;
                background-color: initial;
            }

            input:checked~.checkmark:after {
                display: block;
            }

            .st2 {
                color: #079DB2;
            }
        }

    }

    .error {
        color: $color-primary1v2 !important;
        font-weight: 400 !important;
        font-size: 10px !important;
        line-height: 12px !important;
        margin-top: 14px;
        padding-left: 20px;
        position: relative;
    }

    .modal-footer {
        padding: 0;
        justify-content: center;
        gap: 48px;
        border-top: none;

        .btn {
            width: 160px;
            height: 46px;
            margin: 0;
            border-radius: 30px;
            border: 1px solid $color-brand1;
            color: $color-brand1;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        }

        .btn-cancel-authority {
            border: 1px solid $color-primary1v2;
            color: $color-primary1v2;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .btn-confirm {
            border: 1px solid $color-brand1;
            background: $color-brand1;
            color: $color-palette4;
        }

        .btn:focus {
            box-shadow: none;
        }
    }

}

#modal-confirm-interview {
    background: rgba(0, 0, 0, 0.5);
}

#modal-confirm-onboard {
    background: rgba(0, 0, 0, 0.5);
}

#candidate-introduction-manager {
    padding-bottom: 48px;

    .header {
        padding: 24px 44px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background: #F2F2F2;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);

        .search {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 18px;

            .input-search input {
                width: 600px;
                background: $color-palette4 url(../images/search-job/search-job.svg) no-repeat;
                background-position: left 16px center;
                box-shadow: 0px 4px 4px rgba(51, 51, 51, 0.04), 0px 4px 16px rgba(51, 51, 51, 0.08);
                border-radius: 8px;
                height: 56px;
                border: none;
                padding-left: 44px;
                padding-right: 14px;
                outline: none;
            }

            .button-search {
                border: none;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette4;
                padding: 10px 24px 10px 56px;
                background: $color-brand1 url(../images/cv-on-sale/icon-search.svg) no-repeat;
                position: relative;
                background-position: left 22px center;
            }
        }

        .button-header {
            a {
                display: block;
                width: 100%;
                min-width: 172px;
                font-style: normal;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: #17677B;
                background: #FCFCFE;
                border: 1px solid #17677B;
                padding: 10px;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                text-align: center;
            }
        }
    }

    .filter {
        padding: 0 44px;

        .group-checkbox {
            padding: 12px 0;

            input[type=radio] {
                display: none;
            }

            .item-filter-checkbox {
                margin-right: 16px;
            }

            .box {
                padding: 0 17px;
                height: 32px;
                text-align: center;
                cursor: pointer;
                display: flex;
                justify-content: center;
                align-items: center;
                background: #FFFFFF;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
            }

            input[type=radio]:checked+.box {
                color: #FCFCFE;
                background: #17677B;
            }
        }

        .open-filter {
            display: flex;
            align-items: center;

            a {
                color: #3C4455;
                font-style: normal;
                font-weight: 800;
                font-size: 16px;
                line-height: 24px;
            }
        }

        .wrap-filter {
            display: flex;
            justify-content: space-between;
        }


    }

    .group-item {
        padding: 0 44px;

        .item {
            padding: 24px;
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 32px;
            background: #FCFCFE;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 4px;
            position: relative;
            transition: 0.2s;

            .ava {
                width: 90px;
                height: 90px;
                border-radius: 50%;
                overflow: hidden;
            }

            .content {
                padding-top: 5px;
                max-width: calc(100% - 106px);
                width: 100%;

                .name {
                    font-style: normal;
                    font-weight: 800;
                    font-size: 18px;
                    line-height: 26px;
                    color: #17677B;
                    width: 200px;
                }

                .header-content-item {
                    display: flex;
                    align-items: center;
                    gap: 30px;
                    margin-bottom: 12px;
                }

                .list-info {
                    ul {
                        li {
                            width: 162px;
                            display: inline-flex;
                            margin-right: 15px;
                            color: #3C4455;
                            position: relative;
                            padding-left: 24px;
                            word-break: break-word;
                            line-height: 1.5;
                            font-weight: 400;
                            font-size: 14px;

                            .icon {
                                width: 20px;
                                height: 20px;
                                display: inline-block;
                                margin-right: 6px;
                                position: absolute;
                                left: 0;
                                top: -1px;
                            }

                            span {
                                display: -webkit-box;
                                -webkit-line-clamp: 1;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }
                    }
                }

                .buyer {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 10px;
                    line-height: 12px;
                    color: #7984A5;
                    margin-bottom: 12px;

                    .label {
                        position: relative;
                        top: -1px;
                    }

                    .name {
                        color: #3C4455;
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 20px;
                    }

                    .date {
                        font-style: normal;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        color: #3C4455;
                    }
                }

                .main-content-item {
                    display: flex;
                }

                .object-main-content-item {
                    width: 146px;

                    .span-st1 {
                        color: #1DB9AA;
                    }

                    .span-st2 {
                        color: #FF4A59;
                    }
                }

                .label-object {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 10px;
                    color: #7984A5;
                    margin-bottom: 6px;
                }

                .main-object {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    color: #3C4455;
                }

                .object-price {
                    .main-object {
                        span {
                            color: #FBA943;
                        }

                        span.currency {
                            color: #7984A5;
                            font-size: 12px;
                        }
                    }
                }

            }

            .status-group {
                text-align: center;
                position: absolute;
                right: 24px;
                top: 24px;
                width: 146px;
                height: 70%;

                .button {
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    justify-content: space-evenly;

                    a {
                        display: block;
                        padding: 8px 8px;
                        background: #17677B;
                        font-style: normal;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 16px;
                        color: #FCFCFE;
                        border: 1px solid #17677B;
                        border-radius: 30px;
                    }
                }

            }
        }

        .item:hover {
            box-shadow: 0px 10px 26px rgba(112, 131, 187, 0.45);
        }
    }
}

#modal-cv-detail {
    .modal-dialog {
        width: 33.33333vw;
        min-width: 480px;
    }

    .modal-header {
        padding: 0 23px;
        font-style: normal;
        font-weight: 800;
        line-height: 28px;
        height: 83px;
        color: #3C4455;
        border-bottom: 1px solid #DEE4F5;
        display: flex;
        width: 100%;
        justify-content: space-between;
        font-size: 20px;
    }

    .modal-title {
        font-size: 20px;
    }

    .row-modal {
        display: flex;
        height: 100%;
    }

    .col-modal {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-body {
            padding: 24px 24px;

            .line-1-step2 {
                display: flex;
                width: 100%;
                justify-content: space-between;
                margin-bottom: 22px;

                .title-line-1-step-2 {
                    font-style: normal;
                    font-weight: 600;
                    font-size: 16px;
                    color: #17677B;
                    line-height: 24px;
                }

                .view-detail-cv {
                    font-size: 14px;

                    a {
                        font-style: normal;
                        font-weight: 400;
                        line-height: 20px;
                        color: #FBA943;
                    }

                    img {
                        position: relative;
                        top: -2px;
                    }
                }

            }

            .line-2-step2 {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-left: 16px;
                margin-bottom: 38px;

                .name-line2-step2 {
                    margin-bottom: 6px;
                    font-style: normal;
                    font-size: 14px;
                    line-height: 20px;
                    color: $color-st26;

                    span {
                        font-weight: 600;
                    }
                }

                .info-line-2-step2 {
                    ul {
                        li {
                            font-style: normal;
                            font-weight: 400;
                            line-height: 16px;
                            display: inline-block;
                            color: $color-palette1;
                            margin-right: 24px;
                            font-size: 12px;

                            img {
                                position: relative;
                                top: -1px;
                            }
                        }
                    }
                }

                .btn-back-to-step1 {
                    a {
                        padding: 6px 12px;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 16px;
                        display: block;
                        color: $color-primary1;
                        border: 1px solid #079DB2;
                        border-radius: 4px;
                    }

                }
            }

            .title-form-step2 {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 16px;
                color: #17677B;
            }

            .item-form {
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                display: block;
                margin-bottom: 24px;
                color: #3C4455;

                .header-item-form {
                    display: flex;
                    justify-content: space-between;
                }

                .view-more {
                    a {
                        color: #DEE4F5;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                    }

                    img {
                        position: relative;
                        top: -2px;

                    }
                }

                .view-more-st1 {
                    a {
                        color: #FBA943;
                    }

                    a.disabled {
                        color: #DEE4F5;
                    }
                }

                label {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    display: block;
                    margin-bottom: 8px;
                    color: #3C4455;
                }

                .group-field {
                    input {
                        display: block;
                        background: #FCFCFE;
                        width: 100%;
                        height: 44px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        border: none;
                        color: #3C4455;
                        padding: 0 12px;
                        outline: none;
                    }

                    .item-status-st1 {
                        color: #FBA943;
                    }
                }
            }

            .title-cv-company {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: #17677B;
                margin-bottom: 16px;
            }
        }

        .btn-popup {
            background: #FCFCFE;
            border: 2px solid #079DB2;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            align-items: center;
            text-align: center;
            color: #17677B;
            font-weight: 400;
            font-size: 18px;
            line-height: 26px;
            height: 56px;
            margin: 0 12px;
            width: 210px;
        }


    }

    .wrapper-iframe {
        padding: 24px;
        margin-bottom: 24px;

        iframe {
            width: 100%;
            height: 544px;
        }
    }

    .description-iframe {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        padding-right: 24px;
        padding-left: 54px;
        color: #FD8F3B;
        background-image: url(../images/introduction/icon-des.svg);
        background-repeat: no-repeat;
        background-position: left 24px top;
    }

    .comment-st1 {
        margin-bottom: 32px;

        .header-comment {
            color: #3C4455;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 8px;

            .date {
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                margin-bottom: 8px;
            }
        }

        .field-comment {
            margin-bottom: 16px;

            input {
                background: #FCFCFE;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
                height: 44px;
                width: 100%;
                border: none;
                padding: 0 12px;
                outline: none;
            }

            textarea {
                background: #FCFCFE;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
                width: 100%;
                border: none;
                padding: 10px 12px;
                outline: none;
                line-height: 20px;
            }
        }

    }

    .comment-st2 {
        margin-bottom: 32px;

        .header-comment {
            text-align: right;
            color: #3C4455;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 8px;

            .date {
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                margin-bottom: 8px;
            }
        }

        .field-comment {
            margin-bottom: 16px;

            input {
                background: #E9F7FD;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
                height: 44px;
                width: 100%;
                border: none;
                padding: 0 12px;
                outline: none;
                text-align: right;
            }
        }
    }

    .footer-comment {
        margin-top: 16px;
        text-align: right;

        button {
            margin-left: 16px;
        }

        .btn-st1 {
            height: 28px;
            color: #079DB2;
            padding: 6px 12px;
            font-size: 12px;
            line-height: 16px;
            border: 1px solid #079DB2;
            border-radius: 4px;
            background: #ffffff;
        }

        .btn-st2 {
            background: #17677B;
            border: 1px solid #17677B;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 16px;
            height: 28px;
            color: #FCFCFE;
        }

        .btn-st3 {
            height: 28px;
            color: #FD8F3B;
            padding: 6px 12px;
            font-size: 12px;
            line-height: 16px;
            border: 1px solid #FD8F3B;
            border-radius: 4px;
            background: #ffffff;
        }
    }

    .col-comment {
        height: 100%;
        display: flex;
        flex-direction: column;

        .modal-footer-fixed {
            padding: 12px 24px;
        }

        .wrap-input-comment {
            height: 80px;
        }

        .group-comment {
            height: 100%;
            padding: 10px 50px 10px 12px;
            background: #FCFCFE;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            position: relative;

            textarea {
                width: 100%;
                height: 100%;
                border: none;
                outline: none;
                background: #FCFCFE;
            }

            button {
                position: absolute;
                background: #ffffff url(../images/cv-on-sale/btn-send-comment.svg) no-repeat;
                width: 32px;
                height: 32px;
                border: none;
                right: 12px;
                top: 24px;
            }
        }
    }

    .col-complain {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .complain-st1 {
        margin-bottom: 32px;

        .header-complain {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #3C4455;
            margin-bottom: 8px;

            span {
                font-weight: 400;
                font-size: 12px;
                line-height: 24px;
                color: #FD8F3B;
            }
        }

        .field-complain {
            margin-bottom: 16px;

            input {
                background: #FCFCFE;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
                height: 44px;
                width: 100%;
                border: none;
                padding: 0 12px;
                outline: none;
            }

            textarea {
                background: #FCFCFE;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                color: #3C4455;
                font-weight: 400;
                font-size: 14px;
                width: 100%;
                border: none;
                padding: 10px 12px;
                outline: none;
                line-height: 20px;
            }
        }

        .field-complain-text {
            margin-bottom: 16px;
            background: #FCFCFE;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            color: #3C4455;
            font-weight: 400;
            font-size: 14px;
            width: 100%;
            padding: 10px 12px;
            outline: none;
            line-height: 20px;
        }

        .image-complain {
            text-align: center;

            img {
                max-width: 100%;
            }
        }

        .description {
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: #7984A5;
            margin-bottom: 16px;

            span {
                color: #FF4A59;
            }
        }

        .footer-complain {
            text-align: right;

            button {
                margin-left: 16px;
            }

            .btn-complain-btn {
                background: #FFFFFF;
                color: #079DB2;
                border: 1px solid #079DB2;
                border-radius: 4px;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                padding: 6px 12px;
            }

            .btn-complain-btn-2 {
                background: #FD8F3B;
                color: #FCFCFE;
                border: 1px solid #FD8F3B;
                border-radius: 4px;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                padding: 6px 12px;
            }
        }

    }


    .col-discus {
        .list-discuss {
            .item-discuss {
                margin-bottom: 32px;

                .header {
                    color: #3C4455;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 16px;
                    margin-bottom: 8px;

                    span {
                        color: #3C4455;
                        font-weight: 600;
                        font-size: 16px;
                    }
                }

                .content {
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    padding: 12px;
                    color: #3C4455;

                    b {
                        font-weight: bold;
                    }
                }
            }

            .item-discuss-ntd {
                .header {
                    text-align: right;
                }

                .content {
                    background: #E9F7FD;
                    text-align: right;

                    b {
                        font-weight: bold;
                    }
                }
            }
        }

        .modal-footer {
            padding: 12px 24px;
            display: block;

            .wrap-input {
                padding: 16px 12px;
                background: #FCFCFE;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                text-align: left;
                height: 72px;
                position: relative;

                input {
                    border: none;
                    background: #FCFCFE;
                    outline: none;
                    width: calc(100% - 38px);
                    height: 32px;
                }

                .bnt-comment {
                    border: none;
                    background-image: url(../images/candidate/icon-send-comment.svg);
                    background-repeat: no-repeat;
                    background-position: center;
                    width: 32px;
                    height: 32px;
                    position: absolute;
                    right: 12px;
                    top: 24px;
                }
            }
        }

    }


    .col-site {
        display: none;
    }

    .col-discus {
        display: none;
    }

    .col-info {
        display: none;
    }

    .open-col-discus {
        width: 66.66666vw;
        min-width: 960px;

        .col-main {
            width: 50%;

            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
            position: relative;
            z-index: 99;
        }

        .col-discus {
            display: flex;
            width: 50%;

            .modal-header {
                padding-left: 0;

                img {
                    margin-right: 0;
                }
            }

        }
    }

    .open-col-site {
        width: 66.66666vw;
        min-width: 960px;

        .col-main {
            width: 50%;

            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
            position: relative;
            z-index: 99;
        }

        .col-site {
            display: block;
            width: 50%;

            .modal-header {
                padding-left: 0;

                img {
                    margin-right: 0;
                }
            }

        }
    }

    .open-col-info {
        width: 66.66666vw;
        min-width: 960px;

        .col-main {
            width: 50%;
            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
            position: relative;
            z-index: 99;
        }

        .col-info {
            display: block;
            width: 50%;

            .modal-header {
                padding-left: 0;

                img {
                    margin-right: 0;
                }
            }

        }
    }

    .view-more-cv {
        width: 66.66666vw;
        min-width: 960px;

        .col-right-header {
            display: block;
        }

        .modal-body {
            padding: 0;
            overflow-y: hidden;
        }

        .col-modal {
            overflow-y: scroll;
        }

        .col-modal {
            height: 100%;
        }

        .step {
            height: 100%;
        }

        .col-modal {
            padding: 24px;
            width: 50%;
        }

        .col-main {
            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
            position: relative;
            z-index: 99;
        }

        .modal-header {
            display: block;
            padding: 0;
        }

        .row-header {
            height: 100%;
            display: flex;
        }

        .col-left-header {
            width: 50%;
            border-right: 1px solid #DEE4F5;
            box-shadow: 2px 0px 10px rgba(52, 60, 107, 0.4);
        }

        .col-right-header {
            width: 50%;
        }

        .wapper-col-left-header {
            padding: 0 23px;
        }

        .step2 {
            display: flex;
        }

        .wapper-iframe {
            iframe {
                width: 100%;
                height: 544px;
            }
        }

        .description-iframe {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            padding-right: 24px;
            padding-left: 54px;
            color: #FD8F3B;
            background-image: url(../images/introduction/icon-des.svg);
            background-repeat: no-repeat;
            background-position: left 24px top;
        }

        .col-iframe {
            display: block;
        }
    }

    .col-info {
        .text-show-public-cv {
            display: block;
        }

        .text-show-private-cv {
            display: none;
        }

        #iframe-private-cv {
            display: block;
        }

        #iframe-public-cv {
            display: none;
        }

    }

    .show-public-cv {
        .text-show-public-cv {
            display: none;
        }

        .text-show-private-cv {
            display: block;
        }

        #iframe-private-cv {
            display: none;
        }

        #iframe-public-cv {
            display: block;
        }
    }
}

.text-download {
    font-weight: normal;
    font-size: 14px;
    // line-height: 20px;
    // color: #3C4455;
}

.text-orange {
    color: #fba943;
}

.text-orange:hover {
    color: #fba943;
    text-decoration: underline;
}