# Tài liệu Đặc tả Hệ thống Authentication/Authorization - RecLand

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> gi<PERSON>:** AI Assistant

---

## 1. Tổ<PERSON> quan Hệ thống Authentication

### 1.1. <PERSON><PERSON>n trúc Authentication

RecLand sử dụng Laravel Authentication system với multiple guards:

- **Web Guard:** Session-based authentication cho web interface
- **Admin Guard:** Session-based authentication cho admin panel
- **Client Guard:** Session-based authentication cho client (employer/rec)
- **API Guard:** Token-based authentication cho API endpoints

### 1.2. User Types và Roles

| User Type | Mô tả | Guard | Quyền hạn chính |
|-----------|-------|-------|-----------------|
| **admin** | Quản trị viên hệ thống | admin | Full access, quản lý toàn bộ hệ thống |
| **employer** | <PERSON><PERSON><PERSON> tuyển dụng/<PERSON><PERSON>h nghiệ<PERSON> | client | <PERSON><PERSON><PERSON> tin, mua CV, quản lý tuyển dụng |
| **rec** | Cộng tác viên/Headhunter | client | Submit CV, bán CV, nhận hoa hồng |

---

## 2. Authentication Configuration

### 2.1. Guards Configuration

**File:** `config/auth.php`

```php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'admin' => [
        'driver' => 'session',
        'provider' => 'admin',
    ],
    'client' => [
        'driver' => 'session',
        'provider' => 'client',
    ],
],

'providers' => [
    'users' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
    'admin' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
    'client' => [
        'driver' => 'eloquent',
        'model' => App\Models\User::class,
    ],
],
```

### 2.2. Password Reset Configuration

```php
'passwords' => [
    'users' => [
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 60, // 60 minutes
        'throttle' => 60, // 60 seconds between requests
    ],
],

'password_timeout' => 10800, // 3 hours
```

---

## 3. Middleware System

### 3.1. Core Authentication Middleware

#### 3.1.1. Authenticate Middleware

**File:** `app/Http/Middleware/Authenticate.php`

```php
class Authenticate extends Middleware {
    protected function redirectTo($request) {
        if (!$request->expectsJson()) {
            return route('login');
        }
    }
}
```

**Chức năng:** Kiểm tra user đã đăng nhập chưa
**Redirect:** Chuyển hướng đến trang login nếu chưa đăng nhập

#### 3.1.2. RedirectIfAuthenticated Middleware

**File:** `app/Http/Middleware/RedirectIfAuthenticated.php`

```php
class RedirectIfAuthenticated {
    public function handle(Request $request, Closure $next, ...$guards) {
        $guards = empty($guards) ? [null] : $guards;
        
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                return redirect(RouteServiceProvider::HOME);
            }
        }
        
        return $next($request);
    }
}
```

**Chức năng:** Chuyển hướng user đã đăng nhập khỏi trang login/register

### 3.2. Role-Based Authorization Middleware

#### 3.2.1. CheckRole Middleware (Admin)

**File:** `app/Http/Middleware/CheckRole.php`

```php
class CheckRole {
    public function handle($request, Closure $next) {
        $user = Auth::guard('admin')->user();
        
        // Kiểm tra user type
        if ($user->type != config('constant.role.admin')) {
            return redirect()->route('login');
        }
        
        // Kiểm tra permission
        if (PermissionService::checkPermission($request->route()->getName())) {
            return $next($request);
        } else {
            Toast::error('Bạn chưa có quyền để thực hiện chức năng này!');
            abort(401, 'Access denied');
        }
    }
}
```

**Chức năng:** 
- Kiểm tra user có phải admin không
- Kiểm tra permission cho route cụ thể
- Sử dụng PermissionService để validate quyền

#### 3.2.2. CheckEmployer Middleware

**File:** `app/Http/Middleware/CheckEmployer.php`

```php
class CheckEmployer {
    public function handle($request, Closure $next) {
        if (Auth::guard('client')->check() 
            && Auth::guard('client')->user()->email_verified_at
            && Auth::guard('client')->user()->type == config('constant.role.employer')) {
            
            $user = Auth::guard('client')->user();
            $user->load('userEmployerType.employeeRole');
            
            $excludeRoute = [
                'employer-submitcv-detail',
                'employer-submitcv-status-detail',
                'employer-manager-dashboard',
            ];
            
            // Kiểm tra quyền
            if ($user->userEmployerType->type == 'manager' ||
                in_array($request->route()->getName(), $excludeRoute) ||
                PermissionEmployerService::checkEmployerPermission($user, $request->route()->getName())) {
                return $next($request);
            } else {
                Toast::error(__('message.role_fail'));
                return redirect()->route('employer-manager-dashboard');
            }
        }
        
        // Redirect nếu chưa đăng nhập hoặc chưa verify email
        $params = [];
        $request->session()->flash('type', 'LoginRequest');
        
        if (url()->previous()) {
            $params['redirect'] = base64_encode(url()->full());
        }
        
        Session::flash('error', __('frontend/login/message.please_check_email'));
        return redirect()->route('employer-dashboard', http_build_query($params));
    }
}
```

**Chức năng:**
- Kiểm tra user là employer và đã verify email
- Kiểm tra sub-role (manager/employee) và permissions
- Redirect với thông báo lỗi nếu không có quyền

#### 3.2.3. CheckRec Middleware

**File:** `app/Http/Middleware/CheckRec.php`

```php
class CheckRec {
    public function handle($request, Closure $next) {
        if (Auth::guard('client')->check()
            && Auth::guard('client')->user()->email_verified_at
            && Auth::guard('client')->user()->type == config('constant.role.rec')) {
            return $next($request);
        }
        
        $params = [];
        if (url()->previous()) {
            $params['redirect'] = base64_encode(url()->full());
        }
        
        Session::flash('error', __('frontend/login/message.please_check_email'));
        return redirect()->route('rec-login', http_build_query($params));
    }
}
```

**Chức năng:**
- Kiểm tra user là rec (CTV) và đã verify email
- Redirect về trang login rec nếu không đủ điều kiện

### 3.3. API Authentication Middleware

#### 3.3.1. CheckApiKey Middleware

**File:** `app/Http/Middleware/CheckApiKey.php`

```php
class CheckApiKey {
    public function handle(Request $request, Closure $next) {
        $apiKey = $request->header('X-API-Key');
        
        // Kiểm tra API Key có tồn tại không
        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API Key không được cung cấp'
            ], 401);
        }
        
        // Kiểm tra API Key có hợp lệ không
        $validApiKeys = config('api.valid_keys', []);
        if (!in_array($apiKey, $validApiKeys)) {
            return response()->json([
                'success' => false,
                'message' => 'API Key không hợp lệ'
            ], 401);
        }
        
        return $next($request);
    }
}
```

**Chức năng:**
- Kiểm tra API Key trong header X-API-Key
- Validate API Key với danh sách keys hợp lệ
- Trả về JSON error nếu không hợp lệ

---

## 4. Permission System

### 4.1. Admin Permission Service

**File:** `app/Services/Admin/PermissionService.php`

```php
class PermissionService {
    public static function checkPermission($routeName) {
        $user = Auth::guard('admin')->user();
        
        if (!$user) {
            return false;
        }
        
        // Super admin có tất cả quyền
        if ($user->type === 'super_admin') {
            return true;
        }
        
        // Lấy permissions của user
        $userPermissions = $user->permissions()->pluck('route_name')->toArray();
        
        // Kiểm tra route có trong permissions không
        return in_array($routeName, $userPermissions);
    }
    
    public static function getPermissionsByModule($module) {
        return Permission::where('module', $module)
                        ->orderBy('sort_order')
                        ->get();
    }
}
```

### 4.2. Employer Permission Service

**File:** `app/Services/Admin/PermissionEmployerService.php`

```php
class PermissionEmployerService {
    public static function checkEmployerPermission($user, $routeName) {
        // Manager có tất cả quyền
        if ($user->userEmployerType->type === 'manager') {
            return true;
        }
        
        // Employee kiểm tra theo role
        $employeeRole = $user->userEmployerType->employeeRole;
        if (!$employeeRole) {
            return false;
        }
        
        // Lấy permissions của role
        $rolePermissions = $employeeRole->permissions()->pluck('route_name')->toArray();
        
        return in_array($routeName, $rolePermissions);
    }
    
    public static function getEmployerModules($user) {
        if ($user->userEmployerType->type === 'manager') {
            return EmployerModule::all();
        }
        
        $roleId = $user->userEmployerType->employeeRole->id;
        return EmployerModule::whereHas('permissions.roles', function($query) use ($roleId) {
            $query->where('employee_role_id', $roleId);
        })->get();
    }
}
```

---

## 5. User Model và Authentication

### 5.1. User Model Authentication Methods

```php
class User extends Authenticatable implements MustVerifyEmail {
    use HasApiTokens, HasFactory, Notifiable;
    
    protected $fillable = [
        'name', 'email', 'password', 'type', 'company_id', 
        'is_active', 'email_verified_at'
    ];
    
    protected $hidden = ['password', 'remember_token'];
    
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
    ];
    
    // Authentication methods
    public function isAdmin() {
        return $this->type === config('constant.role.admin');
    }
    
    public function isEmployer() {
        return $this->type === config('constant.role.employer');
    }
    
    public function isRec() {
        return $this->type === config('constant.role.rec');
    }
    
    public function isActive() {
        return $this->is_active == 1;
    }
    
    public function hasVerifiedEmail() {
        return !is_null($this->email_verified_at);
    }
    
    // Permission methods
    public function hasPermission($permission) {
        if ($this->isAdmin()) {
            return PermissionService::checkPermission($permission);
        }
        
        if ($this->isEmployer()) {
            return PermissionEmployerService::checkEmployerPermission($this, $permission);
        }
        
        return false;
    }
}
```

### 5.2. User Relationships

```php
// User relationships for authorization
public function userEmployerType() {
    return $this->hasOne(UserEmployerType::class);
}

public function employeeRole() {
    return $this->hasOneThrough(
        EmployeeRole::class,
        UserEmployerType::class,
        'user_id',
        'id',
        'id',
        'employee_role_id'
    );
}

public function permissions() {
    return $this->belongsToMany(Permission::class, 'user_permissions');
}

public function company() {
    return $this->belongsTo(Company::class);
}
```

---

## 6. Session Management

### 6.1. Session Configuration

```php
// config/session.php
'driver' => env('SESSION_DRIVER', 'database'),
'lifetime' => env('SESSION_LIFETIME', 120), // 2 hours
'expire_on_close' => false,
'encrypt' => false,
'files' => storage_path('framework/sessions'),
'connection' => env('SESSION_CONNECTION', null),
'table' => 'sessions',
'store' => env('SESSION_STORE', null),
'lottery' => [2, 100], // 2% chance to run garbage collection
'cookie' => env('SESSION_COOKIE', 'recland_session'),
'path' => '/',
'domain' => env('SESSION_DOMAIN', null),
'secure' => env('SESSION_SECURE_COOKIE', false),
'http_only' => true,
'same_site' => 'lax',
```

### 6.2. Session Security

```php
// Middleware để track login activity
class TrackLoginActivity {
    public function handle($request, Closure $next) {
        if (Auth::check()) {
            $user = Auth::user();
            
            // Update last login time
            $user->update(['last_login_at' => now()]);
            
            // Log login activity
            LoginActivity::create([
                'user_id' => $user->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'login_at' => now()
            ]);
        }
        
        return $next($request);
    }
}
```

---

## 7. Security Measures

### 7.1. CSRF Protection

**File:** `app/Http/Middleware/VerifyCsrfToken.php`

```php
class VerifyCsrfToken extends Middleware {
    protected $except = [
        'api/*',
        'webhook/*',
        'payment/callback/*',
        'zalopay/callback',
    ];

    protected function tokensMatch($request) {
        $token = $this->getTokenFromRequest($request);

        return is_string($request->session()->token()) &&
               is_string($token) &&
               hash_equals($request->session()->token(), $token);
    }
}
```

**Chức năng:**
- Bảo vệ tất cả POST/PUT/DELETE requests
- Exclude các API endpoints và webhooks
- Verify CSRF token trong form submissions

### 7.2. XSS Prevention

```php
// Helper function để sanitize input
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }

    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Middleware để sanitize request data
class SanitizeInput {
    public function handle($request, Closure $next) {
        $input = $request->all();

        array_walk_recursive($input, function(&$value) {
            if (is_string($value)) {
                $value = sanitizeInput($value);
            }
        });

        $request->merge($input);
        return $next($request);
    }
}
```

### 7.3. SQL Injection Prevention

```php
// Sử dụng Eloquent ORM và prepared statements
class UserRepository {
    public function findByEmail($email) {
        // Safe - sử dụng parameter binding
        return User::where('email', $email)->first();
    }

    public function searchUsers($keyword) {
        // Safe - sử dụng parameter binding
        return User::where('name', 'LIKE', "%{$keyword}%")
                   ->orWhere('email', 'LIKE', "%{$keyword}%")
                   ->get();
    }

    // Tránh raw queries, nếu cần thiết thì dùng parameter binding
    public function customQuery($status) {
        return DB::select('SELECT * FROM users WHERE status = ?', [$status]);
    }
}
```

---

## 8. Password Security

### 8.1. Password Policies

```php
// Password validation rules
class PasswordValidationService {
    public static function rules() {
        return [
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
                'confirmed'
            ]
        ];
    }

    public static function messages() {
        return [
            'password.regex' => 'Mật khẩu phải chứa ít nhất 1 chữ hoa, 1 chữ thường, 1 số và 1 ký tự đặc biệt',
            'password.min' => 'Mật khẩu phải có ít nhất 8 ký tự',
        ];
    }

    public static function validateStrength($password) {
        $score = 0;

        // Length check
        if (strlen($password) >= 8) $score++;
        if (strlen($password) >= 12) $score++;

        // Character variety
        if (preg_match('/[a-z]/', $password)) $score++;
        if (preg_match('/[A-Z]/', $password)) $score++;
        if (preg_match('/[0-9]/', $password)) $score++;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score++;

        return [
            'score' => $score,
            'strength' => $score < 3 ? 'weak' : ($score < 5 ? 'medium' : 'strong')
        ];
    }
}
```

### 8.2. Password Hashing

```php
// User model password hashing
class User extends Authenticatable {
    public function setPasswordAttribute($password) {
        $this->attributes['password'] = Hash::make($password);
    }

    public function verifyPassword($password) {
        return Hash::check($password, $this->password);
    }

    public function changePassword($oldPassword, $newPassword) {
        if (!$this->verifyPassword($oldPassword)) {
            throw new InvalidPasswordException('Mật khẩu cũ không đúng');
        }

        $this->password = $newPassword;
        $this->save();

        // Log password change
        PasswordChangeLog::create([
            'user_id' => $this->id,
            'changed_at' => now(),
            'ip_address' => request()->ip()
        ]);
    }
}
```

### 8.3. Password Reset Security

```php
class PasswordResetService {
    public function sendResetLink($email) {
        $user = User::where('email', $email)->first();

        if (!$user) {
            // Don't reveal if email exists
            return ['status' => 'sent'];
        }

        // Rate limiting
        $key = 'password_reset_' . $email;
        if (Cache::has($key)) {
            throw new TooManyRequestsException('Vui lòng đợi 60 giây trước khi gửi lại');
        }

        Cache::put($key, true, 60); // 60 seconds

        // Generate secure token
        $token = Str::random(64);

        // Store token with expiration
        PasswordReset::updateOrCreate(
            ['email' => $email],
            [
                'token' => Hash::make($token),
                'created_at' => now()
            ]
        );

        // Send email
        $user->notify(new ResetPassword($token, $user));

        return ['status' => 'sent'];
    }

    public function resetPassword($token, $email, $password) {
        $reset = PasswordReset::where('email', $email)
                             ->where('created_at', '>', now()->subHours(1))
                             ->first();

        if (!$reset || !Hash::check($token, $reset->token)) {
            throw new InvalidTokenException('Token không hợp lệ hoặc đã hết hạn');
        }

        $user = User::where('email', $email)->first();
        $user->password = $password;
        $user->save();

        // Delete used token
        $reset->delete();

        // Revoke all sessions
        $this->revokeAllSessions($user);

        return ['status' => 'reset'];
    }
}
```

---

## 9. Multi-Factor Authentication (Future Enhancement)

### 9.1. TOTP Implementation

```php
class TwoFactorAuthService {
    public function generateSecret($user) {
        $secret = Google2FA::generateSecretKey();

        $user->update([
            'two_factor_secret' => encrypt($secret),
            'two_factor_enabled' => false
        ]);

        return $secret;
    }

    public function getQRCodeUrl($user, $secret) {
        $companyName = config('app.name');
        $email = $user->email;

        return Google2FA::getQRCodeUrl(
            $companyName,
            $email,
            $secret
        );
    }

    public function verifyCode($user, $code) {
        $secret = decrypt($user->two_factor_secret);

        return Google2FA::verifyKey($secret, $code);
    }

    public function enableTwoFactor($user, $code) {
        if (!$this->verifyCode($user, $code)) {
            throw new InvalidCodeException('Mã xác thực không đúng');
        }

        $user->update(['two_factor_enabled' => true]);

        // Generate recovery codes
        $recoveryCodes = $this->generateRecoveryCodes($user);

        return $recoveryCodes;
    }
}
```

---

## 10. API Authentication

### 10.1. API Token Management

```php
class ApiTokenService {
    public function createToken($user, $name, $abilities = ['*']) {
        return $user->createToken($name, $abilities);
    }

    public function revokeToken($user, $tokenId) {
        return $user->tokens()->where('id', $tokenId)->delete();
    }

    public function revokeAllTokens($user) {
        return $user->tokens()->delete();
    }

    public function getActiveTokens($user) {
        return $user->tokens()->where('expires_at', '>', now())->get();
    }
}

// API Authentication middleware
class ApiAuthenticate {
    public function handle($request, Closure $next) {
        if (!$request->bearerToken()) {
            return response()->json(['error' => 'Token required'], 401);
        }

        $user = Auth::guard('sanctum')->user();

        if (!$user) {
            return response()->json(['error' => 'Invalid token'], 401);
        }

        if (!$user->is_active) {
            return response()->json(['error' => 'Account disabled'], 403);
        }

        return $next($request);
    }
}
```

---

## 11. Audit Logging

### 11.1. Authentication Audit

```php
class AuthenticationAudit {
    public static function logLogin($user, $request) {
        LoginLog::create([
            'user_id' => $user->id,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'success' => true
        ]);
    }

    public static function logFailedLogin($email, $request) {
        LoginLog::create([
            'email' => $email,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'success' => false
        ]);
    }

    public static function logLogout($user, $request) {
        LogoutLog::create([
            'user_id' => $user->id,
            'ip_address' => $request->ip(),
            'logout_at' => now()
        ]);
    }

    public static function logPermissionDenied($user, $route, $request) {
        PermissionDeniedLog::create([
            'user_id' => $user->id,
            'route_name' => $route,
            'ip_address' => $request->ip(),
            'attempted_at' => now()
        ]);
    }
}
```

---

## 12. Security Headers

### 12.1. Security Headers Middleware

```php
class SecurityHeaders {
    public function handle($request, Closure $next) {
        $response = $next($request);

        // Prevent clickjacking
        $response->headers->set('X-Frame-Options', 'DENY');

        // Prevent MIME type sniffing
        $response->headers->set('X-Content-Type-Options', 'nosniff');

        // XSS Protection
        $response->headers->set('X-XSS-Protection', '1; mode=block');

        // Strict Transport Security
        if ($request->secure()) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }

        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' 'unsafe-eval' *.googleapis.com *.gstatic.com; " .
               "style-src 'self' 'unsafe-inline' *.googleapis.com; " .
               "img-src 'self' data: *.amazonaws.com; " .
               "font-src 'self' *.googleapis.com *.gstatic.com;";

        $response->headers->set('Content-Security-Policy', $csp);

        return $response;
    }
}
```

---

## 13. Rate Limiting

### 13.1. Authentication Rate Limiting

```php
class AuthenticationRateLimit {
    public function handle($request, Closure $next) {
        $key = 'login_attempts_' . $request->ip();
        $maxAttempts = 5;
        $decayMinutes = 15;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);

            return response()->json([
                'error' => 'Too many login attempts. Please try again in ' . $seconds . ' seconds.'
            ], 429);
        }

        $response = $next($request);

        // If login failed, increment attempts
        if ($response->getStatusCode() === 401) {
            RateLimiter::hit($key, $decayMinutes * 60);
        } else {
            // Clear attempts on successful login
            RateLimiter::clear($key);
        }

        return $response;
    }
}
```

---

## 14. Environment Security

### 14.1. Environment Variables

```env
# Authentication
AUTH_GUARD_DEFAULT=web
AUTH_PASSWORD_TIMEOUT=10800

# Session Security
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# API Security
API_RATE_LIMIT=60
API_TOKEN_EXPIRY=525600

# Password Security
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_MIXED_CASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true
```

### 14.2. Production Security Checklist

```php
class SecurityChecker {
    public function checkSecurityConfiguration() {
        $issues = [];

        // Check HTTPS
        if (!request()->secure() && app()->environment('production')) {
            $issues[] = 'HTTPS not enabled in production';
        }

        // Check debug mode
        if (config('app.debug') && app()->environment('production')) {
            $issues[] = 'Debug mode enabled in production';
        }

        // Check session security
        if (!config('session.secure') && app()->environment('production')) {
            $issues[] = 'Secure session cookies not enabled';
        }

        // Check CSRF protection
        if (!in_array(\App\Http\Middleware\VerifyCsrfToken::class, app('router')->getMiddleware())) {
            $issues[] = 'CSRF protection not enabled';
        }

        return $issues;
    }
}
```

---

*Tài liệu hệ thống Authentication/Authorization hoàn tất với đầy đủ chi tiết về authentication, authorization, security measures và best practices.*
