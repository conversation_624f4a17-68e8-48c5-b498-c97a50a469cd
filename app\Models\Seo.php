<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class Seo extends Model implements Auditable
{
    use HasFactory;
    use \OwenIt\Auditing\Auditable;


    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['image_url_vi', 'image_url_en'];

    protected $audit_tags = [];
    public function addAuditTag($tag) {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }
    public function generateTags() : array {
        return $this->audit_tags;
    }
    public function getImageUrlViAttribute()
    {
        return gen_url_file_s3($this->img_thumb_vi, '', false);
    }

    public function getImageUrlEnAttribute()
    {
        return gen_url_file_s3($this->img_thumb_en, '', false);
    }
}
