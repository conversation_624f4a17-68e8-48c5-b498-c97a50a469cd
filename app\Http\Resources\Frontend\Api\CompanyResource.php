<?php

namespace App\Http\Resources\Frontend\Api;

use Carbon\Carbon;
use App\Helpers\Common;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        $cities = Common::getCities();
        $address = isset($this['address']) ? $this['address'] : $this->address;
        $career_value = isset($this['career_value']) ? $this['career_value'] : $this->career_value;
        $scale = isset($this['scale']) ? $this['scale'] : $this->scale;
        $address = json_decode($address, true);
        $address_full = [];
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a['area'])) {
                    $address_full[] = [
                        'province' => $cities[$a['area']],
                        'address'  => $a['address'],
                    ];
                }
            }
        }

        $area = [];
        $areaString = '';
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a['area']) && isset($cities[$a['area']])) {
                    $area[] = $cities[$a['area']];

                }
            }
        }
        if (!empty($career_value) && !is_array($career_value)) {
            $career_value = explode(',', $career_value);
        }
        foreach (array_unique($area) as $a) {
            $areaString .= $a . ', ';
        }
        // dd($this->jobsActive()->count());

        return [
            'name'             => isset($this['name']) ? $this['name'] : $this->name,
            'slug'             => isset($this['slug']) ? $this['slug'] : $this->slug,
            'address'          => isset($this['address']) ? $this['address'] : $this->address,
            'logo'             => isset($this['logo']) ? gen_url_file_s3($this['logo']) : gen_url_file_s3($this->logo),
            'banner'           => isset($this['banner']) ? gen_url_file_s3($this['banner']) : gen_url_file_s3($this->banner),
            'scale'            => isset(config('constant.scale')[$scale]) ? config('constant.scale')[$scale] : '',
            'website'          => isset($this['website']) ? $this['website'] : $this->website,
            'about'            => isset($this['about']) ? $this['about'] : $this->about,
            'area'             => array_unique($area),
            'area_string'      => rtrim($areaString, ', '),
            'career_value'     => $career_value,
            'address_full'     => $address_full,
            'submit_count'     => $this->submit_cvs()->count(),
            // 'count_job_active' => $this->count_job_active,
            'count_job_active' => $this->jobsActive()->count(),
            'url'              => route('detail-company', ['slug' => (isset($this['slug']) ? $this['slug'] : $this->slug)]),
            'job_count'        => $this->jobs()->count(),
        ];
    }

}
