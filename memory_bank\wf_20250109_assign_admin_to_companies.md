# Workflow: Assign Admin to Companies

## Ng<PERSON><PERSON> thực hiện: 2025-01-09

## <PERSON><PERSON><PERSON> tiêu

<PERSON>o migration và command để assign admin_id cho các companies dựa trên audit logs của jobs.

## C<PERSON><PERSON> tác vụ đã thực hiện

### 1. Tạo Migration thêm field admin_id vào bảng companies

-   **File**: `database/migrations/2025_07_09_103053_add_admin_id_to_companies_table.php`
-   **Mô tả**: Thêm field `admin_id` (foreign key) vào bảng `companies`
-   **Thông tin field**:
    -   Type: `unsignedBigInteger`
    -   Nullable: `true`
    -   Foreign key: references `id` on `users` table
    -   OnDelete: `set null`
    -   Comment: "Admin quản lý company"

### 2. Cập nhật Model Company

-   **File**: `app/Models/Company.php`
-   **Thay đổi**:
    -   Thêm `admin_id` vào mảng `fillable`
    -   Thêm relationship `admin()` để liên kết với model User

### 3. Tạo Command AssignAdminToCompanies

-   **File**: `app/Console/Commands/AssignAdminToCompanies.php`
-   **Signature**: `assign:admin-to-companies`
-   **Mô tả**: Assign admin_id cho companies dựa trên audit logs của jobs

#### Logic của Command:

1. Lấy danh sách companies có `is_real = 1` và `admin_id IS NULL`
2. Với mỗi company:
    - Lấy danh sách jobs của company
    - Với mỗi job, lấy audit logs từ bảng `audits`
    - Tìm user có `type = admin` và có quyền quản lý company
    - Assign admin_id đầu tiên tìm thấy

#### Permissions được kiểm tra:

-   `company.rotation` (chính)
-   `company.edit`
-   `company.index`
-   `company.create`

### 4. Chạy Migration và Command

-   Migration đã được chạy thành công
-   Command đã được test và hoạt động đúng

## Kết quả

-   Migration: ✅ Thành công
-   Command: ✅ Thành công
-   Documentation: ✅ Hoàn thành

## Lưu ý

-   Command sẽ tự động dừng khi tìm thấy admin đầu tiên phù hợp cho mỗi company
-   Sử dụng audit logs để xác định admin đã từng chỉnh sửa jobs của company
-   Nếu không tìm thấy admin phù hợp, company sẽ không được assign admin_id

## Cách sử dụng

```bash
php artisan assign:admin-to-companies
```

## Files liên quan

-   `database/migrations/2025_07_09_103053_add_admin_id_to_companies_table.php`
-   `app/Models/Company.php`
-   `app/Console/Commands/AssignAdminToCompanies.php`
-   `memory_bank/wf_20250109_assign_admin_to_companies.md`
