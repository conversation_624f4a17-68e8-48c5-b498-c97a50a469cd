
# SRS - F02.2: Submit CV - <PERSON><PERSON><PERSON> đo<PERSON> Interview

**<PERSON><PERSON><PERSON> bản:** 6.0
**Ngày:** 2025-07-08
**T<PERSON><PERSON> giả:** Gemini

---

### 1. <PERSON><PERSON> tả

Sau khi NTD chấp nhận hồ sơ, họ có thể mời ứng viên phỏng vấn. Hành động này sẽ trừ một khoản phí Interview (bằng **Point**) từ ví NTD ngay lập tức và sau đó trả hoa hồng (bằng **Price**) cho CTV (nếu có). Logic được điều phối bởi `SubmitCvService` và các Job bất đồng bộ (`PayInterviewSubmit`, `DepositRefundRejectOfferSubmit`).

### 2. Đối tượng tham gia

*   **Nhà tuyển dụng (NTD):** Người mời phỏng vấn và trả phí.
*   **Cộng tác viên (CTV):** <PERSON><PERSON><PERSON><PERSON> nhận hoa hồng.
*   **Hệ thống:** Tự động hóa quy trình.

### 3. Điều kiện tiên quyết

*   Bản ghi `submit_cvs` đang ở trạng thái **1 (Đã chấp nhận)**.
*   Ví của NTD có đủ số dư Point cho phí Interview.
*   `job->bonus_type` được cấu hình là 'onboard' (vì nếu là 'cv', phí đã trả ở giai đoạn trước).

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (HTTP Request - Mời phỏng vấn):** NTD nhấn nút "Mời phỏng vấn" trên giao diện. Request được gửi đến phương thức `submitCvChangeStatus` trong `SubmitCvService` với `status_recruitment` là `PassInterview` (8) hoặc `FailInterview` (10).

2.  **Bước 2 (Service xử lý logic):** Toàn bộ logic được bao bọc trong một **DB Transaction**.
    *   **a. Lấy dữ liệu & Kiểm tra:** Service lấy `SubmitCv` và `Wallet` của NTD. Kiểm tra trạng thái của `submit_cvs` phải là `WaitingPayment` (21).
    *   **b. Cập nhật trạng thái:** Service cập nhật `status` trong `submit_cvs` thành `PassInterview` (8) hoặc `FailInterview` (10).
    *   **c. Ghi lịch sử:** Ghi lại sự kiện thay đổi trạng thái vào `submit_cvs_history_status`.

3.  **Bước 3 (Commit & Phản hồi):** DB Transaction được commit. Controller trả về HTTP `200 OK`.

4.  **Bước 4 (Trả hoa hồng cho CTV):**
    *   Job **`PayInterviewSubmit`** được dispatch với độ trễ 24 giờ. Job này sẽ xử lý việc cộng tiền hoa hồng cho CTV nếu không có khiếu nại hoặc khiếu nại đã được giải quyết.

5.  **Bước 5 (Sắp xếp lịch):** NTD và UV/CTV sử dụng các công cụ khác (`submit_cvs_discuss`, `submit_cvs_books`) để chốt lịch phỏng vấn cụ thể. Khi chốt lịch, NTD cập nhật trạng thái trên hệ thống thành `WaitingInterview` (7).

6.  **Bước 6 (Gửi thông báo & Lên lịch trả hoa hồng):**
    *   Khi trạng thái cập nhật thành 7, hệ thống dispatch job **`SendemailBookSubmit`** để gửi email nhắc lịch cho các bên.
    *   Đồng thời, hệ thống dispatch job **`PassInterviewSubmit`** với một độ trễ (ví dụ: 7 ngày sau thời gian phỏng vấn dự kiến). Job này sẽ tự động chuyển trạng thái sang "Pass Interview" nếu NTD không cập nhật trạng thái sau thời gian này.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi không đủ Point:**
    *   **Nguyên nhân:** Tại **Bước 3b**, ví NTD không đủ Point.
    *   **Xử lý:** `WalletService` ném Exception. Transaction được rollback. Controller trả về HTTP `422` với thông báo lỗi.

*   **5.2. Lỗi trạng thái không hợp lệ:**
    *   **Nguyên nhân:** Tại **Bước 3a**, NTD cố gắng mời phỏng vấn một CV không ở trạng thái "Đã chấp nhận".
    *   **Xử lý:** Service ném `InvalidStatusException`. Controller trả về HTTP `409 Conflict` với thông báo "Hành động không thể thực hiện với trạng thái hiện tại của hồ sơ."

*   **5.3. NTD/Ứng viên hủy sau khi mời (Hoàn tiền):**
    *   **Nguyên nhân:** Sau khi phí Interview đã bị trừ, quy trình bị hủy vì một lý do nào đó (ví dụ: NTD đổi ý và cập nhật trạng thái thành **2 - Rejected**, hoặc UV từ chối và NTD cập nhật trạng thái **4 - Fail Interview** hoặc **9 - Candidate Cancel Interview**).
    *   **Xử lý:** Trong `SubmitCvService`, khi xử lý các thay đổi trạng thái này, hệ thống sẽ kiểm tra xem phí Interview đã được trả chưa. Nếu đã trả, nó sẽ dispatch job **`DepositRefundRejectOfferSubmit`**.

### 6. Yêu cầu phi chức năng

*   **Tính toàn vẹn:** Việc hoàn tiền phải được xử lý tin cậy qua các Job để đảm bảo NTD luôn nhận lại được tiền khi quy trình không diễn ra như mong đợi. Logic hoàn tiền phải kiểm tra kỹ để tránh hoàn tiền hai lần.
*   **Tức thì:** Hành động mời phỏng vấn trừ tiền ngay lập tức, mang lại trải nghiệm dứt khoát cho NTD.

### 7. Mô hình dữ liệu liên quan

*   **Models:** `SubmitCv`, `SubmitCvHistoryStatus`, `Job`, `Wallet`, `WalletTransaction`.
*   **Repositories:** `SubmitCvRepository`, `SubmitCvHistoryStatusRepository`.
*   **Services:** `SubmitCvService`, `WalletService`.
*   **Controller:** `WareHouseSubmitCvController`.

### 8. Các Job liên quan (Background Processes)

*   **`CandidateCancelInterviewSubmitCv`**
    *   **Mô tả:** Xử lý khi NTD hủy phỏng vấn hoặc lịch phỏng vấn hết hạn trong luồng Job Submit. Job này sẽ cập nhật trạng thái và gửi thông báo cho các bên liên quan.
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `submit_cvs` thành **9 (Candidate Cancel Interview)** hoặc **6 (Recruiter Cancel Interview)**.
    *   **Happy Path:** Nếu `submit_cv->status` là `config('constant.status_recruitment_revert.Waitingsetupinterview')` (tức là trạng thái chờ thiết lập phỏng vấn), job sẽ:
        *   Cập nhật `status` thành `config('constant.status_recruitment_revert.RecruiterCancelInterview')` (tức là 6 - NTD hủy phỏng vấn).
        *   Ghi log lịch sử trạng thái vào `submit_cvs_history_status`.
        *   Gửi thông báo cho NTD (`ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv`).
        *   Nếu `submit_cv->authorize > 0`, gửi thông báo cho Admin (`ChangeStatusRecruiterCancelInterviewToAdminSubmitCv`).
        *   Nếu `submit_cv->authorize == 0`, gửi thông báo cho CTV (`ChangeStatusRecruiterCancelInterviewToRecSubmit`).
    *   **Sad Path / Xử lý lỗi:** Job sẽ không thực hiện hành động nếu trạng thái của `submit_cv` không phải là `Waitingsetupinterview`.

*   **`PayInterviewSubmit`**
    *   **Mô tả:** Xử lý việc thanh toán hoa hồng phỏng vấn cho CTV trong luồng Job Submit.
    *   **Khi nào được dispatch:** Với một độ trễ sau khi `submit_cv->status` thay đổi thành 8 (Pass Interview) hoặc 10 (Fail Interview).
    *   **Happy Path:** Nếu `submit_cv->status` là 8 hoặc 10 VÀ `submit_cv->status_complain` là 0 (Không khiếu nại) hoặc 5 (Admin từ chối khiếu nại), job sẽ gọi `SubmitCvService->payCtv()` để cộng tiền vào ví CTV và ghi log giao dịch.
    *   **Sad Path / Xử lý lỗi:** Nếu `submit_cv->status` không hợp lệ hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thanh toán. Lỗi trong quá trình thanh toán sẽ khiến job thất bại và có thể được thử lại.

*   **`DepositRefundRejectOfferSubmit`**
    *   **Mô tả:** Hoàn lại Point cho NTD khi một giao dịch bị hủy hoặc từ chối trong luồng Job Submit.
    *   **Khi nào được dispatch:** Khi `submit_cv->status` thay đổi sang các trạng thái hủy/từ chối (ví dụ: 2, 4, 9, 12, 15).
    *   **Happy Path:** Job tìm các bản ghi thanh toán trước đó (`submitCvHistoryPaymentRepository->finBySubmitTypeStatus`), đánh dấu chúng là đã hoàn tiền, tạo các bản ghi log hoàn tiền mới, cập nhật `submit_cv->status_payment` thành 2 (Hoàn cọc), và cộng Point vào ví của NTD thông qua `WalletService`.
    *   **Sad Path / Xử lý lỗi:** Nếu không tìm thấy bản ghi thanh toán nào để hoàn tiền hoặc nếu đã được hoàn tiền trước đó, job sẽ không thực hiện hành động hoàn tiền. Lỗi trong quá trình hoàn tiền sẽ khiến job thất bại và có thể được thử lại.

### 9. Các thông báo liên quan

*   **`CandidateConfirmRecruitmentSubmitCv` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên đã đồng ý lời mời phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên công ty NTD, tên ứng viên, vị trí, link đến trang quản lý ứng viên của job.
    *   **Kênh:** Mail, Database.
*   **`CandidateRejectRecruitmentAdminSubmit` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên từ chối lời mời phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên ứng viên, tên công ty, vị trí.
    *   **Kênh:** Mail.
*   **`CandidateRejectRecruitmentEmployerSubmit` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên từ chối lời mời phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, số tiền được hoàn lại, link đến ví NTD.
    *   **Kênh:** Mail, Database.
*   **`CandidateRejectRecruitmentRecSubmit` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên do CTV giới thiệu đã từ chối lời mời phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, tên công ty, vị trí, link đến trang quản lý CV đã nộp của CTV.
    *   **Kênh:** Mail, Database.
*   **`ChangeStatusCancelInterviewToAdminSubmit` (gửi cho Admin):**
    *   **Mô tả:** Thông báo hủy phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên ứng viên, công ty, vị trí, loại hình, số Point được hoàn lại, link đến ví và marketplace.
    *   **Kênh:** Mail.
*   **`ChangeStatusCancelInterviewToEmployerSubmit` (gửi cho NTD):**
    *   **Mô tả:** Thông báo hủy phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên ứng viên, công ty, vị trí, loại hình, số bonus được hoàn lại, link đến ví và trang submit CV.
    *   **Kênh:** Mail.
*   **`ChangeStatusCancelInterviewToRecSubmit` (gửi cho CTV):**
    *   **Mô tả:** Thông báo hủy phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên ứng viên, loại hình, tên CTV, vị trí, công ty, số bonus được hoàn lại, link đến ví, marketplace, và trang submit CV.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailIInterviewSubmit` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên Fail phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, ghi chú.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailIInterviewToAdminSubmit` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên Fail phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, ghi chú.
    *   **Kênh:** Mail.
*   **`ChangeStatusPassInterviewAdminSubmit` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên Pass phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, loại hình, vị trí, công ty, link đến trang submit CV.
    *   **Kênh:** Mail.
*   **`ChangeStatusPassInterviewSubmit` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên Pass phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, loại hình, vị trí, công ty, link đến trang submit CV.
    *   **Kênh:** Mail.
*   **`PaymentInterviewToRecSubmit` (gửi cho CTV):**
    *   **Mô tả:** Thông báo thanh toán hoa hồng phỏng vấn thành công (cho luồng Job Submit).
    *   **Nội dung chính:** Tên ứng viên, tên CTV, công ty, số bonus được thanh toán, link đến trang submit CV và doanh thu.
    *   **Kênh:** Mail.
*   **`RemindScheduleInterviewSubmit` (gửi cho NTD):**
    *   **Mô tả:** Nhắc nhở NTD cập nhật kết quả phỏng vấn (cho luồng Job Submit).
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, loại hình, thời gian, địa điểm phỏng vấn, số điện thoại, link cập nhật trạng thái.
    *   **Kênh:** Mail.

