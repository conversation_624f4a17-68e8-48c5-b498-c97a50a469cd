<?php

namespace App\Services\Frontend;

class PermissionEmployerService
{
    public static function getEmployerPermission($user)
    {
        $roles = isset($user->userEmployerType->employeeRole->permission) ? json_decode($user->userEmployerType->employeeRole->permission, true) : [];

        return $roles;
    }

    public static function checkEmployerPermission($user, $route)
    {
        if (in_array($route,self::getEmployerPermission($user))){
            return true;
        }

        return false;
    }


}
