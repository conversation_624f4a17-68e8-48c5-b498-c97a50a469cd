<?php

namespace App\Jobs;

use App\Notifications\ChangeStatusTrailWorkToAdmin;
use App\Notifications\ChangeStatusTrailWorkToAuthorityRec;
use App\Notifications\ChangeStatusTrailWorkToRec;
use App\Notifications\RemindExpireTrailWork;
use App\Notifications\RemindPaymentDebit;
use App\Repositories\PaymentDebitRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class ChangeToTrailWork implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wareHouseCvSellingBuyId;
    protected $user;

    public function __construct($wareHouseCvSellingBuyId, $user)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->user = $user;
    }

    public function handle()
    {

        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $paymentDebitRepository = app(PaymentDebitRepository::class);
        $wareHouseCvSellingHistoryBuyRepository = app(WareHouseCvSellingHistoryBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuyOnboardRepository = app(WareHouseCvSellingBuyOnboardRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);

        if ($wareHouseCvSellingBuy->status_recruitment != 13) return false;

        $point = $wareHouseCvSellingBuy->point - (0.1 * $wareHouseCvSellingBuy->point);
        //check so point của NTD neu ko đủ thi dừng  + $wareHouseCvSellingBuy->point
        if ($point > $wareHouseCvSellingBuy->employer->wallet->amount) {
            //Luu vao 1 table Ghi nợ, user_id (ntd) status = 0 chưa trả nợ
            //sau khi NTD nạp tiền -> đủ tiền thì sẽ thanh toán -> status đã thanh toán = 1
            $paymentDebitRepository->create([
                'warehouse_cv_selling_buy_id' => $wareHouseCvSellingBuy->id,
                'user_id' => $wareHouseCvSellingBuy->user_id,
                'point' => $point,
            ]);

            $wareHouseCvSellingBuy->employer->notify((new RemindPaymentDebit($wareHouseCvSellingBuy))
                ->delay([
                    'mail' => now()->addMinutes(24 * 60),
                ]));
            $start = 10;
            while ($start <= 15) {
                $wareHouseCvSellingBuy->employer->notify((new RemindPaymentDebit($wareHouseCvSellingBuy))
                    ->delay([
                        'mail' => now()->addMinutes($start * 24 * 60),
                    ]));
                $start++;
            }
        } else {
            $percent = 90; //đặt cọc lần 1 10%, trailwork thanh toan nốt 90%
            //trừ point của NTD
            $balance  = $wareHouseCvSellingBuy->employer->wallet->amount - $point;
            // $wareHouseCvSellingBuy->employer->wallet->amount = $balance;
            $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
            $wareHouseCvSellingBuy->employer->wallet->subtractAmount($point, $wareHouseCvSellingBuy, 'Trừ tiền từ ví', 'payment_trailwork');
            // $wareHouseCvSellingBuy->employer->wallet->save();
            //ghi log mua
            $wareHouseCvSellingHistoryBuyRepository->create([
                'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                'type'                          =>  0, //0 trừ tiền, 1 hoàn tiền
                'percent'                       =>  $percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                'point'                         =>  $point,
                'balance'                       =>  $balance,
                'status'                        =>  0
            ]);
            $paymentStatus = 4;
        }
        $updateWareHouseCvSellingBuy = [
            'status_recruitment' => 14,
        ];

        if (!empty($paymentStatus)) {
            $updateWareHouseCvSellingBuy['status_payment'] = $paymentStatus;
        }

        //update Trial work
        $wareHouseCvSellingBuy->update($updateWareHouseCvSellingBuy);
        //update status history: Trial work
        $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $this->user);
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusTrailWorkToAdmin($wareHouseCvSellingBuy));
            $wareHouseCvSellingBuy->rec->notify(new ChangeStatusTrailWorkToAuthorityRec($wareHouseCvSellingBuy));
        } else {
            $wareHouseCvSellingBuy->rec->notify(new ChangeStatusTrailWorkToRec($wareHouseCvSellingBuy));
        }
        //set queue sau 55 ngày  Trail work gửi email
        $start = 55;
        $onboard = $wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($wareHouseCvSellingBuy->id);
        $carbonDate = $currentDateBook = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);
        while ($start < 60) {
            $addTime = clone $carbonDate;
            $wareHouseCvSellingBuy->employer->notify((new RemindExpireTrailWork($wareHouseCvSellingBuy, $start))
                ->delay([
                    'mail' => $addTime->addMinutes($start * 24 * 60),
                ]));
            $start++;
        }

        //sau 7 ngay het han trailwork (2 thang) = 67 ngay nếu NTD ko đổi trạng thái thì tự động đổi sang 16 => 'Success Recruitment',
        $addRecuitmentTime = clone $carbonDate;
        SuccessRecuitment::dispatch($wareHouseCvSellingBuy->id)->delay($addRecuitmentTime->addMinutes(67 * 24 * 60));
        //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
        $add30Time = clone $carbonDate;
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 15)->delay($add30Time->addMinutes(30 * 24 * 60));
        //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
        $add45Time = clone $carbonDate;
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 10)->delay($add45Time->addMinutes(45 * 24 * 60));
        // Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
        $add67Time = clone $carbonDate;
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 75)->delay($add67Time->addMinutes(67 * 24 * 60));
    }
}
