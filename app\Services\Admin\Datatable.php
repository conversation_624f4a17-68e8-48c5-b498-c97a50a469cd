<?php

namespace App\Services\Admin;

use Illuminate\View\View;

class Datatable
{

    protected array $header = [];

    protected array $data = [];

    protected array $renderValue = [];

    protected string $route;

    protected string $title;

    protected array $action = [];

    public function setTitle(string $title): Datatable
    {
        $this->title = $title;
        return $this;
    }

    public function setRenderValue(array $renderValue): Datatable
    {
        $this->renderValue = $renderValue;
        return $this;
    }

    public function setRoute(string $route): Datatable
    {
        $this->route = $route;
        return $this;
    }

    public function render(): View
    {
        return view('admin.inc_layouts.datatable.table',$this->data());
    }

    protected function data(): array
    {

        $data = [
            'header' => $this->header,
            'route' => $this->route,
            'renderValue' => $this->renderValue,
            'action' => $this->action,
        ];

        if (isset($this->title)){
            $data['title'] = $this->title;
        }

        return $data;
    }

    public function setAction(array $action): Datatable
    {
        $this->action = $action;
        return $this;
    }

    public static function convertRequest(array $params): array
    {
        $request = [];
        $orders = [];
        if (isset($params['length'])){
            $request['size'] = $params['length'];
        }
        if (isset($params['start']) && isset($params['length']) && (int)$params['length'] > 0){
            $request['page'] = ($params['start']/(int)$params['length']) + 1;
        }

        if (isset($params['order']) && isset($params['columns'])){
            $columns = $params['columns'];
            $dataOrders = $params['order'];
            foreach ($dataOrders as $dataOrder){
                if (!empty($columns[(int)$dataOrder['column']])){
                    $thisOrder = $columns[(int)$dataOrder['column']];
                    $orders[$thisOrder['data']] = $dataOrder['dir'];
                }
            }
        }

        if (isset($params['search'])){
            $request['search'] = $params['search']['value'];
        }
        unset($params['draw']);
        unset($params['columns']);
        unset($params['start']);
        unset($params['length']);
        unset($params['search']);
        unset($params['_']);
        $request = array_merge($request,$params);
        return [
            'request' => $request,
            'orders' => $orders,
        ];
    }


}
