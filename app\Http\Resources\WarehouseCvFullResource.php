<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseCvFullResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $result = [
            'id'                         => $this->id,
            'user_id'                    => $this->user_id,
            'candidate_name'             => $this->candidate_name,
            'candidate_mobile'           => $this->candidate_mobile,
            'candidate_email'            => $this->candidate_email,
            'candidate_job_title'          => $this->candidate_job_title,
            'candidate_salary_expect'      => $this->candidate_salary_expect,
            'candidate_salary_expect_to'   => $this->candidate_salary_expect_to,
            'career'                       => $this->career,
            'candidate_portfolio'          => $this->candidate_portfolio,
            'candidate_currency'           => $this->candidate_currency,
            'candidate_address'            => $this->candidate_address,
            'candidate_location'           => $this->candidate_location,
            'candidate_est_timetowork'     => $this->candidate_est_timetowork,
            'candidate_est_timetowork_str' => config('constant.thoigiandilamdukien')[$this->candidate_est_timetowork],
            'candidate_formwork'           => $this->candidate_formwork,
            // 'cv_public'                  => $this->cv_public,
            'cv_private'                 => $this->cv_private,
            'assessment'                 => $this->assessment,
            'main_skill'                 => $this->main_skill,
            'year_experience'            => $this->year_experience,
            'is_active'                  => $this->is_active,
            'source'                     => $this->source,
            'created_at'                 => $this->created_at,
            'updated_at'                 => $this->updated_at,
            'deleted_at'                 => $this->deleted_at,
            'rank'                       => $this->rank,
            'url_cv_private'             => $this->url_cv_private,
            // 'url_cv_public'              => $this->url_cv_public,
            'name_cv_private'            => $this->name_cv_private,
            // 'name_cv_public'             => $this->name_cv_public,
            'created_at_value'           => $this->created_at_value,
            'updated_at_value'           => $this->updated_at_value,
            'arr_main_skill'             => $this->arr_main_skill,
        ];
        return $result;
    }
}