<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    protected $commands = [
        Commands\RecreateDiscussModel::class,
    ];
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('command:bonus')->dailyAt('00:30')->withoutOverlapping()->onOneServer()->runInBackground();
        $schedule->command('cv:expired')->daily()->withoutOverlapping()->onOneServer()->runInBackground();
        foreach (['07:30', '12:30', '16:30'] as $time) {
            // $schedule->command('cv:pending')->dailyAt($time)->withoutOverlapping()->onOneServer()->runInBackground();
        }
        // $schedule->command('command:expired-status-job')->daily()->withoutOverlapping()->onOneServer()->runInBackground();
        $schedule->command('command:clean-data')->dailyAt('03:00')->withoutOverlapping()->onOneServer()->runInBackground();
        $schedule->command('candidate:reject-job')->everyMinute()->withoutOverlapping()->onOneServer()->runInBackground();
        $schedule->command('transactions:update')->everyTwoHours();
        $schedule->command('user:update-ctv-level')->monthlyOn(1, '05:00')->withoutOverlapping()->onOneServer()->runInBackground();
        $schedule->command('job:extend-expiration')->dailyAt('23:00')->withoutOverlapping()->onOneServer()->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
