@extends('admin.layouts.app')
@section('css_custom')
<link href="{{asset2('/backend/assets/plugins/fileupload/css/fileupload.css')}}" rel="stylesheet" type="text/css" />
<!-- INTERNAL Fancy File Upload css -->
<link href="{{asset2('/backend/assets/plugins/fancyuploder/fancy_fileupload.css')}}" rel="stylesheet" />
@endsection
@section('content')
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Cập nhật công ty</h4>
    </div>
</div>
<!--End Page header-->
<div class="row">
    <div class="col-xl-12 col-md-12 col-lg-12">
        <form action="{{route('company.update',['company'=>$company->id])}}" enctype="multipart/form-data" method="post"
            class="sbm_form_s">
            @csrf
            {{method_field('put')}}
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Tên công ty <span class="text-red">*</span></label>
                                <input class="form-control @if($errors->has('name')) is-invalid @endif" name="name"
                                    value="{{old('name',$company->name)}}">
                                @if($errors->has('name'))
                                <span class="text-danger"> {{$errors->first('name')}} </span>
                                @endif

                            </div>
                            <div class="form-group">
                                <label class="form-label">Website</label>
                                <input type="url" class="form-control @if($errors->has('website')) is-invalid @endif"
                                    name="website" value="{{old('website',$company->website)}}">
                                @if($errors->has('website'))
                                <span class="text-danger"> {{$errors->first('website')}} </span>
                                @endif
                            </div>
                            <div class="form-group">
                                <label class="form-label">Quy mô <span class="text-red">*</span></label>
                                <select class="form-control @if($errors->has('scale')) is-invalid @endif" name="scale">
                                    <option value="">Chọn quy mô</option>
                                    @foreach(config('constant.scale') as $key => $value)
                                    <option @if(old('scale',$company->scale) == $key) selected @endif
                                        value="{{$key}}">{{$value}}</option>
                                    @endforeach
                                </select>
                                @if($errors->has('scale'))
                                <span class="text-danger"> {{$errors->first('scale')}} </span>
                                @endif
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Logo <span class="text-red">*</span></label>
                                        <div class="max-with-upload">
                                            <input id="upload_logo" type="file" class="dropify" name="logo"
                                                data-default-file="{{isset($company->path_logo) && old('upload_logo_flg') != 1 ? $company->path_logo : ''}}"
                                                data-height="180" />
                                            <input type="hidden" id="upload_logo_flg" name="upload_logo_flg">
                                        </div>
                                        @if($errors->has('logo'))
                                        <div class="text-danger"> {{$errors->first('logo')}} </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Banner <span class="text-red">*</span></label>
                                        <div class="max-with-upload">
                                            <input id="upload_banner" type="file" class="dropify" name="banner"
                                                data-default-file="{{isset($company->path_banner) && old('upload_banner_flg') != 1 ? $company->path_banner : ''}}"
                                                data-height="180" />
                                            <input type="hidden" id="upload_banner_flg" name="upload_banner_flg">
                                        </div>
                                        @if($errors->has('banner'))
                                        <div class="text-danger"> {{$errors->first('banner')}} </div>
                                        @endif
                                    </div>
                                </div>


                            </div>

                        </div>
                        <div class="col-md-6">
                            @for($i = 0;$i < 3;$i++) <div class="form-group">
                                <label class="form-label">@if($i == 0) Địa chỉ <span class="text-red">*</span> @else
                                    &nbsp; @endif </label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div
                                            class="@if($errors->has('address.'.$i.'.area')) is-invalid-select-2 @endif">
                                            <select name="address[{{$i}}][area]"
                                                class="form-control select2-show-search custom-select is-invalid"
                                                data-placeholder="Khu vực">
                                                <option value="">Khu vực</option>
                                                @foreach($cities as $key => $city)
                                                <option @if(!empty($company->address_value[$i]->area) &&
                                                    old('address.'.$i.'.area',$company->address_value[$i]->area) ==
                                                    $key) selected @endif value="{{$key}}">{{$city}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @if($errors->has('address.'.$i.'.area'))
                                        <div class="text-danger"> {{$errors->first('address.'.$i.'.area')}} </div>
                                        @endif
                                    </div>
                                    <div class="col-md-9">
                                        <input name="address[{{$i}}][address]"
                                            class="form-control @if($errors->has('address.'.$i.'.address')) is-invalid @endif"
                                            value="{{!empty($company->address_value[$i]->address) ? old('address.'.$i.'.address',$company->address_value[$i]->address):''}}">
                                        @if($errors->has('address.'.$i.'.address'))
                                        <div class="text-danger"> {{$errors->first('address.'.$i.'.address')}} </div>
                                        @endif
                                    </div>
                                </div>
                        </div>
                        @endfor
                        <div class="form-group">
                            <label class="form-label">Trạng thái hoạt động <span class="text-red">*</span></label>
                            <label class="custom-switch">
                                @if(old('flg_status') == 1)
                                <input type="checkbox" @if(old('is_active')==1) checked @endif value="1"
                                    name="is_active" class="custom-switch-input">
                                @else
                                <input type="checkbox" @if($company->is_active == 1) checked @endif value="1"
                                name="is_active" class="custom-switch-input">
                                @endif
                                <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                <span class="custom-switch-description mr-2" id="status_active">Active</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Hiển thị trang chủ</label>
                            <label class="custom-switch">
                                @if(old('flg_status') == 1)
                                <input type="checkbox" @if(old('home')==1) checked @endif value="1" name="home"
                                    class="custom-switch-input">
                                @else
                                <input type="checkbox" @if($company->home == 1) checked @endif value="1" name="home"
                                class="custom-switch-input">
                                @endif
                                <span class="custom-switch-indicator custom-switch-indicator-xl"></span>
                                <span class="custom-switch-description mr-2" id="home_active">Có</span>
                            </label>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-group">
                                    <label class="form-label">Độ ưu tiên <span class="text-red">*</span></label>
                                    <select class="form-control @if($errors->has('priority')) is-invalid @endif"
                                        name="priority" data-placeholder="--- Chọn ---">
                                        <option label="--- Chọn ---"></option>
                                        @foreach($priority as $k => $v)
                                        <option value="{{$k}}" {{(old('priority', $company->priority)) == $k ?
                                            'selected' : ''}}>{{$v}}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('priority'))
                                    <span class="text-danger"> {{$errors->first('priority')}} </span>
                                    @endif
                                </div>
                            </div>
                            <div class="col-6">
                                <?php
                                        $arrCareer = [];
                                        if (old('career')) {
                                            $arrCareer = array_flip(old('career'));
                                        } else {
                                            $arrCareer = explode(',', $company->career);
                                            $arrCareer = array_flip($arrCareer);
                                        }
                                        ?>
                                <div class="form-group">
                                    <label class="form-label">Lĩnh vực <span class="text-red">*</span></label>
                                    <div class="@if($errors->has('career')) is-invalid-select-2 @endif">
                                        <select id="tags"
                                            class="form-control select2-show-search custom-select select-max-item @if($errors->has('career')) is-invalid @endif"
                                            name="career[]" multiple="multiple" data-placeholder="--- Chọn ---">
                                            <option label="--- Chọn ---"></option>
                                            @foreach($career as $k => $item)
                                            <option value="{{$k}}" {{isset($arrCareer[$k]) ? 'selected' : '' }}>
                                                {{$item}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if($errors->has('career'))
                                    <span class="text-danger"> {{$errors->first('career')}} </span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Mã số thuế <span class="text-red">*</span></label>
                            <input type="text" class="form-control @if($errors->has('mst')) is-invalid @endif"
                                name="mst" value="{{old('mst',$company->mst)}}">
                            @if($errors->has('mst'))
                            <span class="text-danger"> {{$errors->first('mst')}} </span>
                            @endif
                        </div>

                        <div class="form-group">
                            <label class="form-label">Admin quản lý</label>
                            <select class="form-control @if($errors->has('admin_id')) is-invalid @endif" name="admin_id"
                                data-placeholder="-- Chọn admin --">
                                @foreach($admins as $adminId => $adminName)
                                <option value="{{$adminId}}" {{(old('admin_id', $company->admin_id)) == $adminId ?
                                    'selected' : ''}}>{{$adminName}}</option>
                                @endforeach
                            </select>
                            @if($errors->has('admin_id'))
                            <span class="text-danger"> {{$errors->first('admin_id')}} </span>
                            @endif
                            <small class="form-text text-muted">Chọn admin có quyền quản lý công ty này</small>
                        </div>

                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label">Giới thiệu</label>
                    <textarea name="about" id="editor" rows="10" cols="80">{{old('about',$company->about)}}</textarea>
                </div>
            </div>
            <div class="card-footer text-right">
                <input type="hidden" class="flg_status" name="flg_status" value="{{old('flg_status')}}" />
                <a href="{{route('company.index')}}" class="btn btn-danger btn-lg">Close</a>
                <button class="btn btn-success btn-lg sbm_form" type="button">Submit</button>
            </div>
    </div>
    </form>

</div>
</div>

@endsection


@section('scripts')
<script src="{{asset2('backend/assets/plugins/fileupload/js/dropify.js')}}"></script>
<script src="{{asset2('backend/assets/js/filupload.js')}}"></script>
<script>
    $(document).ready(function () {
            CKEDITOR.replace( 'editor',  {filebrowserImageBrowseUrl: '/file-manager/ckeditor'});
            $('[name="is_active"]').prop("checked") ? $('#status_active').html('Active') : $('#status_active').html('Inactive');
            $('[name="home"]').prop("checked") ? $('#home_active').html('Có') : $('#home_active').html('Không');
            $('[name="is_active"]').change(function (){
                if($(this).prop("checked")){
                    $('#status_active').html('Active');
                }else{
                    $('#status_active').html('Inactive');
                }
            });
            $('[name="home"]').change(function (){
                if($(this).prop("checked")){
                    $('#home_active').html('Có');
                }else{
                    $('#home_active').html('Không');
                }
            });

            $(document).on('click', '.sbm_form', function(){
                $('.flg_status').val('1');
                $('.sbm_form_s').get(0).submit();
            });
            var dropifyElements = {};
            $('.dropify').each(function() {
                dropifyElements[this.id] = true;
            });
            var drEvent = $('.dropify').dropify();

            drEvent.on('dropify.beforeClear', function(event, element) {
                var id = event.target.id;
                if(dropifyElements[id]) {
                    $('#'+id+'_flg').val(1);

                }
            });

            $("#tags").select2({
                maximumSelectionLength: 3
            });
        })
        
    // Function to render admin name in datatable
    function renderAdminName(data, type, row) {
        if (row.admin && row.admin.name) {
            return '<span class="badge badge-info">' + row.admin.name + '</span>';
        }
        return '<span class="text-muted">Chưa có</span>';
    }
</script>
@endsection