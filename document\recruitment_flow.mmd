graph TD
    A[Bắt đầu] --> B{CTV Gửi CV};
    B --> B1[Loại: Dữ liệu CV];
    B --> B2[Loại: Phỏng vấn];
    B --> B3[Loại: Onboard];

    subgraph "<PERSON><PERSON><PERSON> nhận & <PERSON><PERSON> toán ban đầu"
        B1 --> C[<PERSON><PERSON> thống gửi email xác nhận cho Ứng viên];
        B2 --> C;
        B3 --> C;
        C --> D{Ứng viên xác nhận?};
        D -- Không --> E["<div style='text-align:left'><b>Hủy ứng tuyển</b><br>[Status: CandidateCancelApply]<br><b>Tự động:</b> Sau 7 ngày nếu UV không xác nhận.</div>"];
        D -- Có --> F["<div style='text-align:left'><b>Chờ thanh toán</b><br>[Status: WaitingPayment]</div>"];
        F --> G{NTD thanh toán phí};
        G -- <PERSON><PERSON> toán --> H["<div style='text-align:left'><b>Ch<PERSON> xếp lịch phỏng vấn</b><br>[Status: Waiting setup interview]<br><b>Job:</b> `CandidateCancelInterviewSubmitCv` được tạo, delay 7 ngày.<br>Nếu NTD không xếp lịch, job sẽ chạy, chuyển status sang 'RecruiterCancelInterview' và hoàn tiền.</div>"];
    end

    E --> End[Kết thúc];

    subgraph "Quy trình Tuyển dụng & Phỏng vấn"
        H --> I{NTD Xếp lịch / Review};
        I -- Từ chối --> J["<div style='text-align:left'><b>NTD từ chối CV</b><br>[Status: RecruiterRejectCV]<br>Gửi email thông báo cho CTV.</div>"];
        J --> End;
        I -- Chấp nhận / Mua --> K{Kiểm tra loại hình};

        K -- "Dữ liệu CV" --> L["<div style='text-align:left'><b>Mua CV thành công</b><br>[Status: BuyCVdatasuccessfull]<br><b>Khiếu nại:</b> NTD có thể khiếu nại trong vòng 7 ngày.<br><b>Job:</b> `RecSumExpiredPointSubmit` được tạo, delay 7 ngày. Nếu CTV không xác nhận khiếu nại, job sẽ chạy và tự động đồng ý khiếu nại.</div>"];
        L --> L1[Thanh toán cho CTV];
        L1 --> End;

        K -- "Phỏng vấn" --> M["<div style='text-align:left'><b>Chờ phỏng vấn</b><br>[Status: Waiting Interview]</div>"];
        K -- "Onboard" --> M;

        M --> N{NTD cập nhật kết quả PV};
        N -- Trượt --> O["<div style='text-align:left'><b>Phỏng vấn thất bại</b><br>[Status: FailInterview]<br><b>Job:</b> `PayInterviewSubmit` được tạo, delay 24 giờ. Job sẽ thanh toán cho CTV.</div>"];
        O --> End;
        N -- Đậu --> P["<div style='text-align:left'><b>Phỏng vấn thành công</b><br>[Status: PassInterview]</div>"];
    end

    subgraph "Quy trình Onboard & Hoàn tất"
        P --> Q{Kiểm tra loại hình};
        Q -- "Phỏng vấn" --> R["<div style='text-align:left'><b>Thanh toán cho CTV (Tự động)</b><br><b>Job:</b> `PayInterviewSubmit` được tạo, delay 24 giờ.</div>"];
        R --> End;

        Q -- "Onboard" --> S["<div style='text-align:left'><b>Chờ Onboard</b><br>[Status: Waitingonboard]</div>"];
        S --> T[NTD đặt lịch Onboard];
        T --> U["<div style='text-align:left'><b>Thử việc</b><br>[Status: Trialwork]<br><b>Khiếu nại:</b> NTD có thể khiếu nại trong suốt thời gian thử việc (tối đa 67 ngày).</div>"];
        U --> V{NTD cập nhật kết quả thử việc};
        V -- Thất bại --> W["<div style='text-align:left'><b>Thử việc thất bại</b><br>[Status: Failtrailwork]<br><b>Chính sách hoàn tiền cho NTD:</b><br>- Báo cáo trong 0-30 ngày: Hoàn 100%<br>- Báo cáo trong 31-60 ngày: Hoàn 70%<br>- Báo cáo trong 61-67 ngày: Hoàn 50%<br><b>Job:</b> `DepositRefundRejectOfferSubmit` được tạo, delay 48 giờ. Job sẽ hoàn tiền cho NTD.</div>"];
        W --> End;
        V -- Thành công --> X["<div style='text-align:left'><b>Tuyển dụng thành công</b><br>[Status: SuccessRecruitment]</div>"];
        U -.->|"<div style='text-align:center'><b>Tự động sau 67 ngày</b><br>Nếu NTD không cập nhật<br><b>Job:</b> `SuccessRecuitmentSubmit`</div>"| X;
    end

    subgraph "Thanh toán Onboard"
        X --> Y["<div style='text-align:left'><b>Hệ thống thanh toán cho CTV (Tự động)</b><br><b>Jobs:</b><br>- `PayOnboardSubmit` (15%): Delay 30 ngày<br>- `PayOnboardSubmit` (10%): Delay 45 ngày<br>- `PayOnboardSubmit` (75%): Delay 67 ngày</div>"];
        Y --> End;
    end

    style End fill:#f9f,stroke:#333,stroke-width:2px