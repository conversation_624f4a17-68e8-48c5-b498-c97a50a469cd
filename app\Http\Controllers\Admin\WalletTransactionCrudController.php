<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Class WalletTransactionCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class WalletTransactionCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(WalletTransaction::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/wallet-transaction');
        CRUD::setEntityNameStrings('giao dịch ví', 'danh sách giao dịch ví');

        // Disable all buttons except show
        CRUD::denyAccess(['create', 'update', 'delete']);
        CRUD::denyAccess(['show']);
        // Thiết lập sắp xếp mặc định theo thời gian tạo giảm dần
        CRUD::orderBy('created_at', 'desc');
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Thêm các cột hiển thị
        CRUD::addColumn([
            'name' => 'wallet_id',
            'label' => 'Ví',
            'type' => 'select',
            'entity' => 'wallet',
            'attribute' => 'id',
            'model' => Wallet::class,
        ]);

        CRUD::addColumn([
            'name' => 'user_type',
            'label' => 'Loại người dùng',
            'type' => 'text',
        ]);

        CRUD::addColumn([
            'name' => 'user_name',
            'label' => 'Tên người dùng',
            'type' => 'closure',
            'escaped' => false,
            'function' => function ($entry) {
                $user = $entry->user;
                if ($user->type == 'employer') {
                    return '<strong>' . $user->name . '</strong> <br>(' . $user->email . ')';
                } else {
                    return '<strong>' . $user->name . '</strong> <br>(' . $user->referral_define . ')';
                }
            },
        ]);
        CRUD::addColumn([
            'name' => 'description',
            'label' => 'Thông tin',
            'type' => 'closure',
            'escaped' => false,
            'function' => function ($entry) {
                $object = $entry->object;
                $output = 'N/A';

                if ($object) {
                    if ($object instanceof \App\Models\SubmitCv) {
                        $output = $object->warehouseCv->candidate_name;
                        $output .= ' <br> <a target="_blank" href="/admin/job/' . $object->job_id . '/edit">' . $object->job->name . '</a>';
                    } else {
                        $output = get_class($object) . ' #' . $object->id;
                    }
                }

                return $output;
            },
        ]);

        CRUD::addColumn([
            'name' => 'formatted_amount',
            'label' => 'Số tiền giao dịch',
            'type' => 'text',
            'wrapper' => [
                'element' => 'span',
                'class' => function ($crud, $column, $entry, $related_key) {
                    return $entry->amount >= 0 ? 'text-success' : 'text-danger';
                },
            ],
        ]);

        CRUD::addColumn([
            'name' => 'formatted_balance_after',
            'label' => 'Số dư sau giao dịch',
            'type' => 'text',
        ]);

        CRUD::addColumn([
            'name' => 'note',
            'label' => 'Tên giao dịch',
            'type' => 'text',
            'limit' => 50,
        ]);

        // CRUD::addColumn([
        //     'name' => 'type',
        //     'label' => 'Loại giao dịch',
        //     'type' => 'text',
        //     'wrapper' => [
        //         'element' => 'span',
        //         'class' => function ($crud, $column, $entry, $related_key) {
        //             $classes = [
        //                 'deposit' => 'badge badge-success',
        //                 'withdraw' => 'badge badge-danger',
        //                 'payment' => 'badge badge-warning',
        //                 'refund' => 'badge badge-info',
        //             ];
        //             return $classes[$entry->type] ?? 'badge badge-secondary';
        //         },
        //     ],
        // ]);

        CRUD::addColumn([
            'name' => 'created_at',
            'label' => 'Thời gian giao dịch',
            'type' => 'datetime',
            'format' => 'DD/MM/YYYY HH:mm:ss',
        ]);

        // Thêm bộ lọc
        CRUD::addFilter(
            [
                'name' => 'user_type',
                'type' => 'dropdown',
                'label' => 'Loại người dùng'
            ],
            [
                'employer' => 'Nhà tuyển dụng',
                'rec' => 'Cộng tác viên',
            ],
            function ($value) {
                $this->crud->query->whereHas('wallet.user', function (Builder $query) use ($value) {
                    $query->where('type', $value);
                });
            }
        );
        // Thêm bộ lọc theo email người dùng
        CRUD::addFilter(
            [
                'name' => 'user_name',
                'type' => 'text',
                'label' => 'Email người dùng'
            ],
            false,
            function ($value) {
                $this->crud->query->whereHas('wallet.user', function (Builder $query) use ($value) {
                    $query->where('email', 'like', '%' . $value . '%');
                });
            }
        );

        CRUD::addFilter(
            [
                'name' => 'created_at',
                'type' => 'date_range',
                'label' => 'Thời gian giao dịch'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from) {
                    $this->crud->addClause('where', 'created_at', '>=', Carbon::parse($dates->from));
                }
                if ($dates->to) {
                    $this->crud->addClause('where', 'created_at', '<=', Carbon::parse($dates->to)->endOfDay());
                }
            }
        );

        // Thêm bộ lọc theo số tiền
        CRUD::addFilter(
            [
                'name' => 'amount_range',
                'type' => 'range',
                'label' => 'Số tiền giao dịch',
                'label_from' => 'Từ',
                'label_to' => 'Đến'
            ],
            false,
            function ($value) {
                $range = json_decode($value);
                if ($range->from) {
                    $this->crud->addClause('where', 'amount', '>=', (float) $range->from);
                }
                if ($range->to) {
                    $this->crud->addClause('where', 'amount', '<=', (float) $range->to);
                }
            }
        );


        // Mặc định sắp xếp theo thời gian giao dịch mới nhất
        $this->crud->orderBy('created_at', 'desc');
    }

    /**
     * Define what happens when the Show operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-show
     * @return void
     */
    protected function setupShowOperation()
    {
        $this->setupListOperation();

        // Thêm thông tin chi tiết về đối tượng liên quan
        CRUD::addColumn([
            'name' => 'object_type',
            'label' => 'Loại đối tượng liên quan',
            'type' => 'text',
        ]);

        CRUD::addColumn([
            'name' => 'object_id',
            'label' => 'ID đối tượng liên quan',
            'type' => 'text',
        ]);

        // Thêm thông tin chi tiết về ví
        CRUD::addColumn([
            'name' => 'wallet.amount',
            'label' => 'Số dư hiện tại của ví',
            'type' => 'number',
            'thousands_sep' => '.',
            'dec_point' => ',',
            'decimals' => 0,
            'prefix' => '',
            'suffix' => ' đ',
        ]);

        CRUD::addColumn([
            'name' => 'wallet.price',
            'label' => 'Số dư price hiện tại của ví',
            'type' => 'number',
            'thousands_sep' => '.',
            'dec_point' => ',',
            'decimals' => 0,
            'prefix' => '',
            'suffix' => ' đ',
        ]);
    }
}