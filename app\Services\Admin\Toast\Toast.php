<?php

namespace App\Services\Admin\Toast;

use Illuminate\Session\SessionManager as Session;
use Illuminate\Config\Repository as Config;

class Toast
{

    protected $session;

    protected $config;

    protected $messages = [];

    function __construct(Session $session, Config $config)
    {
        $this->session = $session;
        $this->config  = $config;
    }

    public function message()
    {
        $messages = $this->session->get('toastr::messages');
        if (! $messages) $messages = [];
        return view('admin.inc_layouts.toast.message',['messages' => $messages])->render();
    }

    public function add($type, $message, $title = null, $options = [])
    {
        $types = ['error', 'info', 'success', 'warning'];

        if (! in_array($type, $types)) {
            throw new \Exception("The $type remind message is not valid.");
        }

        $this->messages[] = [
            'type'    => $type,
            'title'   => $title,
            'message' => $message,
            'options' => $options,
        ];

        $this->session->flash('toastr::messages', $this->messages);
    }

    public function info($message, $title = null, $options = [])
    {
        $this->add('info', $message, $title, $options);
    }

    public function success($message, $title = 'Success', $options = [])
    {
        $this->add('success', $message, $title, $options);
    }

    public function warning($message, $title = 'Warning', $options = [])
    {
        $this->add('warning', $message, $title, $options);
    }

    public function error($message, $title = 'error', $options = [])
    {
        $this->add('error', $message, $title, $options);
    }

    public function clear()
    {
        $this->messages = [];
    }
}
