<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CompanyRequest;
use App\Services\Admin\CompanyService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CompanyController extends Controller
{
    protected $companyService;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    public function index(Request $request)
    {
        $datatable = $this->companyService->buildDatatable();
        $total = $this->companyService->total();
        $totalCurrentMonth = $this->companyService->totalCurrentMonth();
        return view(
            'admin.pages.company.index',
            compact('datatable', 'total', 'totalCurrentMonth')
        );
    }

    public function datatable(Request $request)
    {
        $data = $this->companyService->datatable($request->all(), true);
        return response($data);
    }

    public function create()
    {
        $cities = Common::getCities();
        return view('admin.pages.company.create', compact('cities'));
    }

    public function store(CompanyRequest $request)
    {
        $company = $this->companyService->create($request->except('_token'));
        Toast::success(__('message.edit_success'));
        return redirect()->route('company.edit', ['company' => $company->id]);
    }

    public function show($id)
    {
        $data = $this->companyService->detail($id);
        return $data;
    }

    public function edit($id)
    {
        $cities = Common::getCities();
        $company = $this->companyService->detailOnlyCompany($id);
        $career = config('job.career.vi');
        $priority = config('constant.priority');
        $admins = $this->getAdminsWithCompanyRotationPermission();
        return view('admin.pages.company.edit', compact('cities', 'company', 'career', 'priority', 'admins'));
    }

    public function update(CompanyRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $this->companyService->update($request->except(['_token', '_method']), $id);
            DB::commit();
            Toast::success(__('message.edit_success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log update company: ', [
                'content: ' => $e->getMessage()
            ]);
        }
        return back();
    }

    public function destroy($id) {}

    /**
     * Lấy danh sách admins có quyền company.rotation
     */
    private function getAdminsWithCompanyRotationPermission()
    {
        $admins = User::where('type', 'admin')
            ->where('is_active', 1)
            ->with('roles')
            ->get()
            ->filter(function ($user) {
                return $this->hasCompanyRotationPermission($user);
            });

        return $admins->pluck('name', 'id')->prepend('-- Chọn admin --', '');
    }

    /**
     * Kiểm tra user có quyền company.rotation không
     */
    private function hasCompanyRotationPermission(User $user): bool
    {
        $roles = $user->roles;

        foreach ($roles as $role) {
            $permissions = json_decode($role->permission, true);

            if (is_array($permissions) && in_array('company.rotation', $permissions)) {
                return true;
            }
        }

        return false;
    }
}
