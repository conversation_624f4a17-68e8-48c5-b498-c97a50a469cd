<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuyBook;
use App\Models\WareHouseCvSellingBuyHistoryStatus;
use App\Notifications\CandidateRejectRecruitmentAdmin;
use App\Notifications\CandidateRejectRecruitmentEmployer;
use App\Notifications\CandidateRejectRecruitmentRec;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class RejectRecruitment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    /**
     * @return void
     * Sau 48h mà Ứng viên ko xác nhận, thì Hoàn tiền trả NTD
     * HT NTD
     */
    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
        $wareHouseCvSellingHistoryBuyRepository = resolve(WareHouseCvSellingHistoryBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = resolve(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        //sau 48h mà ứng viên ko xac nhan thi chuyển trạng thái từ chối
        //1 => 'Waiting candidate confirm',
        if (!empty($wareHouseCvSellingBuy)) {
            if ($wareHouseCvSellingBuy->status_recruitment == config('constant.status_recruitment_revert.Waitingcandidateconfirm')) {
                //interview
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                    Log::info('interview $wareHouseCvSellingBuy 222222: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                    //ghi log hoan tien
                    $wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $wareHouseCvSellingBuy->point,
                        'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                        'status'                        =>  0
                    ]);
                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $wareHouseCvSellingBuy->status_recruitment = config('constant.status_recruitment_revert.CandidateCancelApply');
                    $wareHouseCvSellingBuy->status_payment = 3; //3 => 'Hoàn tiền'
                    $wareHouseCvSellingBuy->save();
                    //Log thay đổi trang thái
                    $wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'status_recruitment'            =>  config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name'                =>  $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name,
                        'type'                          =>  'employer',
                        'authority'                     =>  $wareHouseCvSellingBuy->wareHouseCvSelling->authority
                    ]);
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'RejectRecruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                    Log::info('interview $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                }
                //onboard
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                    //2 => 'Candidate Cancel Apply',
                    $wareHouseCvSellingHistoryBuyData = $wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    Log::info('onboard $wareHouseCvSellingBuy 222222: ', [
                        'xxxx: ' => $wareHouseCvSellingHistoryBuyData
                    ]);
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($wareHouseCvSellingHistoryBuyData) {
                        foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $wareHouseCvSellingHistoryBuyRepository->create([
                                'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                                'point'                         =>  $value->point,
                                'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                                'status'                        =>  0
                            ]);
                            $point += $value->point;
                        }
                    }

                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $wareHouseCvSellingBuy->status_recruitment = config('constant.status_recruitment_revert.CandidateCancelApply');
                    $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                    $wareHouseCvSellingBuy->save();
                    //Log thay đổi trang thái
                    $wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'status_recruitment'            =>  config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name'                =>  $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name,
                        'type'                          =>  'employer',
                        'authority'                     =>  $wareHouseCvSellingBuy->wareHouseCvSelling->authority
                    ]);
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'RejectRecruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                    Log::info('onboard $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                }

                //gui mail cho ctv
                $wareHouseCvSellingBuy->wareHouseCvSelling->user->notify(new CandidateRejectRecruitmentRec($wareHouseCvSellingBuy));

                //gui mail cho ntd
                $wareHouseCvSellingBuy->employer->notify(new CandidateRejectRecruitmentEmployer($wareHouseCvSellingBuy));

                //gui mail cho admin
                Mail::to(config('settings.global.email_admin'))->send(new CandidateRejectRecruitmentAdmin($wareHouseCvSellingBuy));
            }
        }
    }
}
