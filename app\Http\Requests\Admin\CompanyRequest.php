<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $regex = '/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/';
        switch ($this->method()) {
            case 'POST': {
                    return [
                        'name' => 'required',
                        // 'website' => 'required|regex:'.$regex,
                        'scale' => 'required',
                        'address.0.area' => 'required',
                        'address.0.address' => 'required',
                        'mst' => 'required',
                        'logo' => 'required|mimes:jpeg,jpg,png|max:5120',
                        'banner' => 'required|mimes:jpeg,jpg,png|max:5120'
                    ];
                }
            case 'PUT': {
                    $rules = [
                        'name' => 'required',
                        // 'website' => 'required|regex:' . $regex,
                        'scale' => 'required',
                        'address.0.area' => 'required',
                        'address.0.address' => 'required',
                        'mst' => 'required',
                        'logo' => 'mimes:jpeg,jpg,png|max:5120',
                        'banner' => 'mimes:jpeg,jpg,png|max:5120',
                        'career' => 'required',
                        //                    'priority' => 'required',
                    ];
                    if ($this->upload_logo_flg) {
                        $rules['logo'] = 'required|mimes:jpeg,jpg,png|max:5120';
                    }
                    if ($this->upload_banner_flg) {
                        $rules['banner'] = 'required|mimes:jpeg,jpg,png|max:5120';
                    }
                    return $rules;
                }
            default:
                break;
        }
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'mimes' => __('message.mimes'),
            'max' => __('message.file_max'),
            'website.regex' => __('message.website_regex'),
        ];
    }
}
