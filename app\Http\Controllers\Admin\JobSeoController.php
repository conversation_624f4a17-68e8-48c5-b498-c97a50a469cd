<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\JobService;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;

class JobSeoController extends Controller
{

    protected $jobService;

    public function __construct(JobService $jobService)
    {
        $this->jobService = $jobService;
    }

    public function updateJobSeo(Request $request, $postId)
    {
        $this->jobService->updateJobSeo($request->all(), $postId);
        Toast::success(__('message.edit_success'));
        return back();
    }
}
