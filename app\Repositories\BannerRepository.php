<?php

namespace App\Repositories;

use App\Models\Banner;

class BannerRepository extends BaseRepository
{

    const MODEL = Banner::class;

    public function getListBanner($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

        $query->orderBy($order_by, $sort);

        if (isset($params['search'])) {
            $query->where('id', $params['search']);
        }

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    /**
     * @param $type
     *
     * @return void
     */
    function getListByType($type='', $position, $limit = 0){

        $query = $this->query()
            ->where('is_active', 1)
            ->where('position', $position);
        if($type != '') $query->where('type', $type);

        if($limit > 0) $query->take($limit);

        return $query->get();
    }
}
