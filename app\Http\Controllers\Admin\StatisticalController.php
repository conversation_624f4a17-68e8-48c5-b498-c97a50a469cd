<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\CompanyService;
use App\Services\Admin\JobService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use App\Services\Admin\WareHouseCvService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class StatisticalController extends Controller
{

    protected $userService;
    protected $jobService;
    protected $companyService;
    protected $wareHouseCvService;
    protected $submitCvService;

    public function __construct(UserService     $userService, JobService $jobService,
                                CompanyService  $companyService, WareHouseCvService $wareHouseCvService,
                                SubmitCvService $submitCvService)
    {
        $this->userService = $userService;
        $this->jobService = $jobService;
        $this->companyService = $companyService;
        $this->wareHouseCvService = $wareHouseCvService;
        $this->submitCvService = $submitCvService;
    }

    public function index(Request $request)
    {
        $key = $request->input('key');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $totalRecruitingCompany = $this->companyService->totalRecruitingCompany();
        $totalRecruitingJob = $this->jobService->totalRecruitingJob();
        $totalSubmitCv = $this->submitCvService->totalSubmitCv();
        $totalsubmitCvJob = $this->submitCvService->totalSubmitCvJob($fromDate, $toDate);
        $searchSubmitCvToJob = $this->submitCvService->searchSubmitCvToJob($key);
        return view('admin.pages.statistical.index', compact(
            'totalRecruitingCompany',
            'totalRecruitingJob',
            'totalSubmitCv',
            'totalsubmitCvJob',
            'searchSubmitCvToJob'
        ));
    }

    public function filterData(Request $request)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $key = $request->input('key');

        $totalRecruitingCompany = $this->companyService->totalRecruitingCompany($fromDate, $toDate);
        $totalRecruitingJob     = $this->jobService->totalRecruitingJob($fromDate, $toDate);
        $totalSubmitCv          = $this->submitCvService->totalSubmitCv($fromDate, $toDate);
        $totalsubmitCvJob       = $this->submitCvService->totalSubmitCvJob($fromDate, $toDate);
        // dd($totalSubmitCv);

        if ($key) {
            $searchSubmitCvToJob = $this->submitCvService->searchSubmitCvToJob($key);
        } else {
            $searchSubmitCvToJob = null;
        }

        if (!empty($searchSubmitCvToJob)) {
            Toast::success(__('message.search_success'));
            $message = __('message.search_success');
        } elseif (!empty($fromDate) && !empty($toDate)) {
            Toast::success(__('message.filter_success'));
            $message = __('message.filter_success');
        } else {
            $message = __('message.no_results');
        }

        $view = view('admin.pages.statistical.filter-statistical', compact(
            'totalRecruitingCompany',
            'totalRecruitingJob',
            'totalSubmitCv',
            'totalsubmitCvJob',
            'searchSubmitCvToJob'
        ))->render();

        return response()->json([
            'status' => 'success',
            'message' => $message,
            'html' => $view,
        ]);
    }



}
