<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Repositories\JobComment\JobCommentRepositoryInterface;
use App\Repositories\JobComment\JobCommentRepository;

class JobCommentController extends Controller
{
    protected $jobCommentRepository;

    public function __construct(JobCommentRepository $jobCommentRepository)
    {
        $this->jobCommentRepository = $jobCommentRepository;
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách comments của một job
     */
    public function index($jobId)
    {
        $comments = $this->jobCommentRepository->getCommentsByJobId($jobId);

        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    /**
     * Tạo comment mới
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'job_id' => 'required|exists:job,id',
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:job_comments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }

        // try {
            $comment = $this->jobCommentRepository->create([
                'job_id' => $request->job_id,
                'user_id' => Auth::guard('admin')->id(),
                'content' => $request->content,
                'parent_id' => $request->parent_id ?? null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Đã thêm bình luận thành công',
                'data' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'user_name' => Auth::guard('admin')->user()->name,
                    'created_at' => $comment->created_at,
                    'parent_id' => $comment->parent_id
                ]
            ]);
        // } catch (\Exception $e) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Có lỗi xảy ra, vui lòng thử lại sau'
        //     ], 500);
        // }
    }

    /**
     * Xóa comment
     */
    public function destroy($id)
    {
        $comment = JobComment::findOrFail($id);

        // Kiểm tra quyền xóa
        // if ($comment->user_id !== Auth::id()) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Bạn không có quyền xóa bình luận này'
        //     ], 403);
        // }

        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa bình luận thành công'
        ]);
    }

    /**
     * Toggle trạng thái hiển thị của comment
     */
    public function toggleStatus($id)
    {
        try {
            $comment = JobComment::findOrFail($id);
            $comment->is_approved = !$comment->is_approved;
            $comment->save();

            return response()->json([
                'success' => true,
                'message' => 'Đã cập nhật trạng thái bình luận thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật trạng thái'
            ], 500);
        }
    }
}
