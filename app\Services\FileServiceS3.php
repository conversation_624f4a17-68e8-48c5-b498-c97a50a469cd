<?php

namespace App\Services;


use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Laravel\Facades\Image;

class FileServiceS3
{
    private static $instance = null;

    private function __construct() {}

    public static function getInstance(): ?FileServiceS3
    {
        if (self::$instance == null) {
            self::$instance = new FileServiceS3();
        }

        return self::$instance;
    }

    public function uploadToS3($file, $pathFolder = 'others'): ?string
    {
        try {
            // dd($file);
            if (strpos($file, 'http://') !== false || strpos($file, 'https://') !== false || strpos($file, 'data:image') !== false) {
                $image = Image::make($file);
                list($type, $ext) = explode('/', $image->mime());
                $filePath = $pathFolder . '/' . uniqid() . '.' . $ext;
                $flag = Storage::disk('s3')->put($filePath, $image->encode('jpg', 95));
            } elseif ($file) {
                $name = md5(time() . Str::slug($file->getClientOriginalName())) . DOT . $file->getClientOriginalExtension();
                $filePath = $pathFolder . DS . $name;
                $flag = Storage::disk('s3')->put($filePath, file_get_contents($file));
                // return $flag ? $filePath : null;
            }
            return $flag ? $filePath : null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    /**
     * Upload file từ local storage lên S3
     * 
     * @param string $localFilePath Đường dẫn file local (có thể là storage/app/... hoặc public/...)
     * @param string $pathFolder Thư mục đích trên S3
     * @return string|null Đường dẫn file trên S3 nếu thành công, null nếu thất bại
     */
    public function uploadFromLocalToS3(string $localFilePath, string $pathFolder = 'others'): ?string
    {
        try {
            // Kiểm tra file có tồn tại không
            if (!file_exists($localFilePath)) {
                Log::warning(__CLASS__ . __FUNCTION__, ['message' => 'File không tồn tại', 'path' => $localFilePath]);
                return null;
            }

            // Lấy thông tin file
            $pathInfo = pathinfo($localFilePath);
            $fileName = $pathInfo['filename'] ?? 'file';
            $extension = $pathInfo['extension'] ?? '';

            // Tạo tên file mới để tránh trùng lặp
            $newFileName = md5(time() . Str::slug($fileName)) . DOT . $extension;
            $s3FilePath = $pathFolder . DS . $newFileName;

            // Đọc nội dung file và upload lên S3
            $fileContent = file_get_contents($localFilePath);
            $flag = Storage::disk('s3')->put($s3FilePath, $fileContent);

            return $flag ? $s3FilePath : null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'localPath' => $localFilePath, 'request' => request()]);
            return null;
        }
    }

    /**
     * Upload file từ Laravel storage disk lên S3
     * 
     * @param string $storagePath Đường dẫn file trong Laravel storage (ví dụ: 'temp/file.jpg')
     * @param string $disk Disk name của Laravel storage (mặc định là 'local')
     * @param string $pathFolder Thư mục đích trên S3
     * @return string|null Đường dẫn file trên S3 nếu thành công, null nếu thất bại
     */
    public function uploadFromStorageToS3(string $storagePath, string $disk = 'local', string $pathFolder = 'others'): ?string
    {
        try {
            // Kiểm tra file có tồn tại trong storage không
            if (!Storage::disk($disk)->exists($storagePath)) {
                Log::warning(__CLASS__ . __FUNCTION__, ['message' => 'File không tồn tại trong storage', 'path' => $storagePath, 'disk' => $disk]);
                return null;
            }

            // Lấy thông tin file
            $pathInfo = pathinfo($storagePath);
            $fileName = $pathInfo['filename'] ?? 'file';
            $extension = $pathInfo['extension'] ?? '';

            // Tạo tên file mới để tránh trùng lặp
            $newFileName = md5(time() . Str::slug($fileName)) . DOT . $extension;
            $s3FilePath = $pathFolder . DS . $newFileName;

            // Lấy nội dung file từ storage và upload lên S3
            $fileContent = Storage::disk($disk)->get($storagePath);
            $flag = Storage::disk('s3')->put($s3FilePath, $fileContent);

            return $flag ? $s3FilePath : null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'storagePath' => $storagePath, 'disk' => $disk, 'request' => request()]);
            return null;
        }
    }

    public function uploadToS3FromLink($link, $pathFolder = 'others'): ?string
    {
        try {
            if (!empty($link)) {
                $pathParts = pathinfo($link);

                $name = md5(time() . Str::slug($pathParts['filename'])) . DOT . $pathParts['extension'];

                $filePath = $pathFolder . DS . $name;

                $flag = Storage::disk('s3')->put($filePath, file_get_contents($link));

                return $flag ? $filePath : null;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function moveFileS3($pathFrom, $pathFolder): ?string
    {
        try {
            if (!empty($pathFrom) && Storage::disk('s3')->exists($pathFrom)) {
                $pathParts = pathinfo($pathFrom);

                $name = md5(time() . Str::slug($pathParts['filename'])) . DOT . $pathParts['extension'];

                $pathTo = $pathFolder . DS . $name;

                if (Storage::disk('s3')->exists($pathTo)) {
                    return $pathTo;
                }

                Storage::disk('s3')->move($pathFrom, $pathTo);

                return $pathTo;
            }

            return null;
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
            return null;
        }
    }

    public function deleteFileOnS3($paths)
    {
        if (is_array($paths)) {
            foreach ($paths as $path) {
                $this->deleteOnlyFile($path);
            }
        } else {
            $this->deleteOnlyFile($paths);
        }
    }

    private function deleteOnlyFile($path)
    {
        try {
            if (!empty($path) && Storage::disk('s3')->exists($path)) {
                Storage::disk('s3')->delete($path);
            }
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception, 'request' => request()]);
        }
    }
}
