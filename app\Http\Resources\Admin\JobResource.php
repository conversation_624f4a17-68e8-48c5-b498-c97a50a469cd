<?php

namespace App\Http\Resources\Admin;

use App\Helpers\Common;
use App\Http\Resources\Api\Frontend\CompanyResource;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);
        $data['short_note'] = Str::limit($data['note'], 50);
        $data['company']['name'] = '<strong>' . $data['company']['name'] . '</strong><br><em>' . $data['short_note'] .'</em>';
        $data['name'] = '<strong>' . $data['name'] . '</strong><br><em class="badge rounded-pill bg-warning text-dark">' . $data['job_type'] .'</em>';
        $statusLabels = [
            0 => ['text' => 'Dừng tuyển', 'class' => 'secondary'],
            1 => ['text' => 'Đang tuyển', 'class' => 'success'],
            2 => ['text' => 'Hết hạn tuyển', 'class' => 'warning']
        ];

        $activeStatus = $data['is_active'] ? 'Active' : 'Inactive';
        $activeClass = $data['is_active'] ? 'success' : 'danger';
        $is_actived_str = sprintf(
            '<div class="text-center"><span class="badge badge-%s-light">%s</span></div>',
            $activeClass,
            $activeStatus
        );

        if (isset($statusLabels[$data['status']])) {
            $status = $statusLabels[$data['status']];
            $is_actived_str .= sprintf(
                '<div class="text-center"><span class="mt-2 badge badge-%s">%s</span></div>',
                $status['class'],
                $status['text']
            );
        }
        $data['is_active'] = $is_actived_str;
        return $data;
    }
}
