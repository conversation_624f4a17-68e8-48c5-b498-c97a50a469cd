# SRS - F01.1: Mua CV Data từ Marketplace

**<PERSON><PERSON><PERSON> bản:** 6.0
**Ngày:** 2025-07-08
**Tác giả:** Gemini

---

### 1. <PERSON><PERSON> tả

Tính năng này cho phép Nhà tuyển dụng (NTD) dùng **Point** từ ví của mình để mua quyền truy cập thông tin liên hệ (CV Data) của một ứng viên. Các CV này được đăng bán trên Marketplace bởi các Cộng tác viên (CTV). Luồng xử lý được thực hiện thông qua `WareHouseSubmitCvController`, điều phối logic trong `WareHouseCvSellingBuyService` và tương tác với CSDL qua các Repository (`WareHouseCvSellingBuyRepository`, `WalletRepository`).

### 2. <PERSON><PERSON><PERSON> tượng tham gia

*   **<PERSON><PERSON><PERSON> tuyển dụng (NTD):** Người mua.
*   **<PERSON><PERSON><PERSON> tác viên (CTV):** <PERSON><PERSON>ời bán.
*   **Hệ thống:** Xử lý giao dịch và thông báo.

### 3. Điều kiện tiên quyết

*   NTD đã đăng nhập và có vai trò là "employer".
*   Ví NTD (`wallets` table, `type`='employer') có đủ số dư **Point**.
*   Một bản ghi trong `warehouse_cv_sellings` tồn tại và đang ở trạng thái "đang bán".
*   **CV đã được xử lý:** CV đã được Job `ProcessWarehouseCvJob` xử lý thành công, đảm bảo thông tin đầy đủ và file CV đã được tải lên S3.

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (HTTP Request):** NTD nhấn nút "Mua CV" trên giao diện. Frontend gửi một request (ví dụ: `POST /api/marketplace/buy-cv/{selling_id}`) đến máy chủ, kèm theo ID của `warehouse_cv_sellings`.

2.  **Bước 2 (Controller tiếp nhận):** Phương thức `buyCV` trong `WareHouseCvSellingBuyService` tiếp nhận request. Nó thực hiện các validation ban đầu (người dùng đã đăng nhập, có phải NTD không).

3.  **Bước 3 (Gọi Service):** Controller gọi đến phương thức `buyCV` trong `WareHouseCvSellingBuyService`, truyền vào đối tượng `User` (NTD) và `$params` (chứa `id` của CV bán và `job` nếu có).

4.  **Bước 4 (Service xử lý logic):** Toàn bộ các hành động trong bước này được bao bọc bởi một **Database Transaction** (`DB::beginTransaction()` và `DB::commit()`).
    *   **a. Lấy dữ liệu:** Service sử dụng các repository để lấy ra các đối tượng cần thiết: `WareHouseCvSelling` từ ID, `User` (NTD) và `Wallet` của NTD.
    *   **b. Kiểm tra điều kiện:** Service thực hiện một loạt các kiểm tra:
        *   Kiểm tra `WareHouseCvSelling` có tồn tại không.
        *   Kiểm tra người dùng đã đăng nhập chưa.
        *   Kiểm tra NTD đã mua CV này trước đó chưa (`checkwareHouseCvSellingBuy`).
        *   Kiểm tra ví NTD có đủ Point không (`point > $user->wallet->amount`).
    *   **c. Tạo bản ghi mua hàng:** `WareHouseCvSellingBuyService` sử dụng `WareHouseCvSellingBuyRepository` để tạo một bản ghi mới trong `warehouse_cv_selling_buys`, lưu lại `user_id` (NTD), `ctv_id`, `price`, `point`, `warehouse_cv_selling_id`, `status_recruitment` (đặt là `BuyCVdatasuccessfull` - 18 cho CV Data), `status_payment` (đặt là `4` - NTD Đã thanh toán), `job_id` (nếu có) và một `token` ngẫu nhiên.
    *   **d. Trừ Point & Ghi Log:** Service trừ `point` tương ứng từ ví của NTD (`$user->wallet->subtractAmount`). Một bản ghi trong `wallet_transactions` được tạo để ghi lại lịch sử giao dịch.
    *   **e. Ghi lịch sử chi tiết mua hàng:** Service sử dụng `WareHouseCvSellingHistoryBuyRepository` để tạo một bản ghi mới trong `ware_house_cv_selling_history_buys` với `type = 0` (trừ tiền) và `percent = 100`.
    *   **f. Ghi lịch sử trạng thái:** Service sử dụng `WareHouseCvSellingBuyHistoryStatusRepository` để tạo một bản ghi mới trong `ware_house_cv_selling_buy_history_statuses` với `status_recruitment` là `BuyCVdatasuccessfull`.

5.  **Bước 5 (Commit & Thông báo):** Database Transaction được commit thành công. Service dispatch các notification và job sau:
    *   **Notification `BuyCvSuccess` (gửi cho CTV):** Thông báo CV đã được mua. Nội dung bao gồm tên CTV, tên ứng viên, công ty NTD, vị trí, và số tiền thưởng.
    *   **Job `RecSumPoint`:** Kích hoạt job `RecSumPoint` với độ trễ 7 ngày. Job này sẽ xử lý việc cộng tiền hoa hồng cho CTV nếu không có khiếu nại hoặc khiếu nại đã được giải quyết.

6.  **Bước 6 (HTTP Response):** Controller nhận được kết quả thành công và trả về một JSON response với HTTP status `200 OK` và thông báo "Mua CV thành công!". Frontend nhận được response này và hiển thị thông tin đã được mở khóa của ứng viên.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi không đủ Point:**
    *   **Nguyên nhân:** Tại **Bước 4b**, `WalletService` phát hiện `point` > `user->wallet->amount`.
    *   **Xử lý:** Service trả về `false`. Controller bắt được và trả về HTTP status `422 Unprocessable Entity` với thông báo lỗi "Số dư Point trong ví không đủ để thực hiện giao dịch."

*   **5.2. Lỗi CV đã được mua:**
    *   **Nguyên nhân:** Tại **Bước 4b**, Service kiểm tra trong `warehouse_cv_selling_buys` đã tồn tại bản ghi với `user_id` và `warehouse_cv_selling_id` này.
    *   **Xử lý:** Service trả về `false`. Controller bắt được và trả về HTTP status `409 Conflict` với thông báo "Bạn đã mua CV này trước đó."

*   **5.3. Lỗi CV không còn được bán:**
    *   **Nguyên nhân:** Tại **Bước 4b**, `warehouse_cv_selling` không tồn tại.
    *   **Xử lý:** Service trả về `false`. Controller bắt được và trả về HTTP status `404 Not Found` với thông báo "CV này không còn được bán hoặc đã bị xóa."

*   **5.4. Lỗi NTD tự mua CV của mình:**
    *   **Nguyên nhân:** Tại **Bước 4b**, Service phát hiện `ntd->id` == `selling->user_id`.
    *   **Xử lý:** Service trả về `false`. Controller bắt được và trả về HTTP status `403 Forbidden` với thông báo "Bạn không thể mua CV do chính mình đăng bán."

*   **5.5. Lỗi hệ thống chung:**
    *   **Nguyên nhân:** Một lỗi không mong muốn xảy ra ở bất kỳ đâu trong khối `try` của Service (ví dụ: mất kết nối CSDL).
    *   **Xử lý:** Khối `catch (\Exception $e)` cuối cùng sẽ bắt được lỗi. `DB::transaction` sẽ tự động rollback. Hệ thống ghi log lỗi và Controller trả về HTTP status `500 Internal Server Error` với một thông báo chung chung.

### 6. Yêu cầu phi chức năng

*   **Toàn vẹn dữ liệu:** Việc sử dụng `DB::transaction` là bắt buộc để đảm bảo tính nhất quán. Hoặc là tất cả các bước ghi vào CSDL thành công, hoặc là không có gì thay đổi.
*   **Kiểm toán:** Mọi thay đổi về số dư ví phải được ghi lại chi tiết trong `wallet_transactions`.

### 7. Mô hình dữ liệu liên quan

*   **Models:** `WareHouseCvSelling`, `WareHouseCvSellingBuy`, `Wallet`, `WalletTransaction`, `User`, `WareHouseCvSellingHistoryBuy`, `WareHouseCvSellingBuyHistoryStatus`.
*   **Repositories:** `WareHouseCvSellingRepository`, `WareHouseCvSellingBuyRepository`, `WalletRepository`, `WalletTransactionRepository`, `WareHouseCvSellingHistoryBuyRepository`, `WareHouseCvSellingBuyHistoryStatusRepository`.
*   **Services:** `WareHouseCvSellingBuyService`, `WalletService`.
*   **Controller:** `WareHouseSubmitCvController`.

### 8. Các Job liên quan (Background Processes)

*   **`ProcessWarehouseCvJob`**
    *   **Mô tả:** Job này chịu trách nhiệm xử lý một file CV thô (ví dụ: PDF) được tải lên, trích xuất dữ liệu có cấu trúc từ CV bằng cách gọi một API bên ngoài (n8n webhook), cập nhật các trường thông tin trong bảng `warehouse_cvs` và tải các phiên bản công khai/riêng tư của CV lên S3. Job sẽ thử lại tối đa 3 lần nếu thất bại.
    *   **Khi nào được dispatch:** Job này được dispatch khi một bản ghi `WareHouseCv` mới được tạo (ví dụ: khi CTV tải lên CV hoặc Admin thêm CV vào kho).
    *   **Happy Path:**
        1.  Tìm `WareHouseCv` bằng ID.
        2.  Đọc file CV từ đường dẫn cục bộ (`cv_public` có tiền tố `pending_process::`).
        3.  Gửi file CV đến API phân tích CV bên ngoài (`config('app.n8n_webhook_url') . '/webhook/cv-parser-with-markdown'`).
        4.  Nếu API trả về thành công:
            *   Lưu dữ liệu đã phân tích vào trường `meta` của `WareHouseCv` dưới khóa `cv_parsed_data`.
            *   Cập nhật các trường như `candidate_name`, `candidate_mobile`, `candidate_email`, `candidate_address`, `candidate_job_title`, `year_experience`, `assessment`, `main_skill`, `candidate_location` trong `WareHouseCv` từ dữ liệu phân tích.
            *   Tải file CV gốc (`cv_public`) và file CV đã ẩn thông tin (`cv_private`) lên S3. Việc tải lên S3 được thực hiện từ `pdf_url` trả về bởi API hoặc từ file cục bộ nếu `pdf_url` trống.
            *   Xóa file CV cục bộ sau khi tải lên S3 thành công.
            *   Ghi log thành công.
            *   **Lưu ý:** Các trường `status` của `WareHouseCv` (processing, completed, failed) hiện đang bị comment trong code và không được cập nhật. Phương thức `saveAdditionalMetaData` cũng không được gọi, nghĩa là các thông tin chi tiết như học vấn, kinh nghiệm làm việc, dự án, ngôn ngữ, và thông tin bổ sung khác không được lưu vào meta data.
    *   **Sad Path / Xử lý lỗi:**
        *   **`WareHouseCv` không tìm thấy:** Ghi log lỗi và dừng xử lý.
        *   **Đường dẫn file CV không hợp lệ hoặc file không tồn tại cục bộ:** Job kiểm tra `cv_public` phải có tiền tố `pending_process::`. Nếu không có hoặc file không tồn tại tại đường dẫn cục bộ, job sẽ ghi log lỗi và dừng xử lý. Trong một số trường hợp, `cv_public` sẽ được cập nhật với tiền tố lỗi (`file_not_found::` hoặc `path_invalid::`).
        *   **API phân tích CV thất bại:** Ghi log lỗi với phản hồi từ API.
        *   **Lỗi chung trong quá trình xử lý:** Ghi log lỗi và ném lại exception để hệ thống queue xử lý (job sẽ được thử lại tối đa 3 lần).

*   **`RecSumPoint`**
    *   **Mô tả:** Job này chịu trách nhiệm xử lý việc thanh toán hoa hồng cho CTV sau khi một giao dịch mua CV Data thành công và không có khiếu nại.
    *   **Khi nào được dispatch:** Job này được dispatch sau khi NTD mua "CV Data" thành công và không có khiếu nại. Nó được lên lịch chạy sau 7 ngày kể từ khi giao dịch mua "CV Data" thành công.
    *   **Happy Path:**
        1.  Tìm `WareHouseCvSellingBuy` bằng ID.
        2.  Kiểm tra trạng thái tuyển dụng (`status_recruitment` là `BuyCVdatasuccessfull`) và trạng thái khiếu nại (`status_complain` là 0 hoặc 5).
        3.  Tính toán hoa hồng cho CTV: Nếu giao dịch là "ủy quyền" (`authority == 2`), CTV nhận 20% giá trị giao dịch; ngược lại, CTV nhận 100%.
        4.  Cập nhật tổng doanh thu của CTV trong `BonusRepository` và ghi lại giao dịch chi tiết trong `PayinMonthRepository`.
        5.  Cộng tiền vào ví của CTV (`wareHouseCvSellingBuy->rec->wallet->addPrice`).
        6.  Gửi thông báo cho CTV (`PaymentOpenTurnCv`).
    *   **Sad Path / Xử lý lỗi:**
        *   `WareHouseCvSellingBuy` không tìm thấy hoặc không đáp ứng điều kiện trạng thái: Job sẽ không thực hiện thanh toán.
        *   Lỗi trong quá trình cập nhật CSDL hoặc gửi thông báo: Ghi log lỗi.