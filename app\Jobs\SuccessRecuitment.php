<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SuccessRecuitment implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = app(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        //14 => 'Trial work',
        //Trạng thái khiếu nại: NTD khiếu nại = 1, CTV từ chối = 2, CTV xác nhận = 3, Admin xác nhân = 4, Admin từ chối = 5
        if ($wareHouseCvSellingBuy->status_recruitment == 14 &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
            //16 => 'Success Recruitment',
            $wareHouseCvSellingBuy->update(['status_recruitment' => 16, 'status_payment' => 4]);

            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy);
            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusSuccessRecruitmentToAdmin($wareHouseCvSellingBuy));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new ChangeStatusSuccessRecruitmentToRec($wareHouseCvSellingBuy));
            }
        }
    }




}
