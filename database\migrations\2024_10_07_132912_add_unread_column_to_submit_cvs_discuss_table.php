<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('submit_cvs_discuss', function (Blueprint $table) {
            $table->tinyInteger(column: 'unread')->after('comment')->default(1);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('submit_cvs_discuss', function (Blueprint $table) {
            $table->dropColumn('unread');
        });
    }
};
