<?php

namespace App\Jobs;

use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DepositRefundRejectOfferSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {

        $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
        $submitCvRepository = app(SubmitCvRepository::class);

        $submitCv = $submitCvRepository->find($this->submitCvId);

        $paymentData = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
        //hoàn bao nhiêu point
        $point = 0;
        if ($paymentData) {
            foreach ($paymentData as $key => $value) {
                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                $value->status = 1;
                $value->save();
                //ghi log hoan tien
                $submitCvHistoryPaymentRepository->create([
                    'user_id'                     => $submitCv->employer->id,
                    'warehouse_cv_selling_buy_id' => $submitCv->id,
                    'type'                        => 1,                                                     //0 trừ tiền, 1 hoàn tiền
                    'percent'                     => $value->percent,                                       //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale'                => $submitCv->bonus_type,
                    'amount'                      => $value->amount,
                    'balance'                     => $submitCv->employer->wallet->amount + $value->amount,
                    'status'                      => 0
                ]);
                $point += $value->amount;
            }
        }
        $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
        $submitCv->save();
        //Cộng point của NTD
        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
        $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
        $submitCv->employer->wallet->addAmount($point, $submitCv, 'Cộng tiền vào ví', 'deposit_refund_reject_offer_submit');
        // $submitCv->employer->wallet->save();
    }
}
