<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_onboards', function (Blueprint $table) {
            $table->id();
            $table->integer('ntd_id')->comment('NTD')->nullable();
            $table->integer('ctv_id')->comment('CTV')->nullable();
            $table->integer('submit_cvs_id')->comment('submit_cvs_id');
            $table->dateTime('date_book')->comment('ngày onboard')->nullable();
            $table->time('time_book')->comment('Giờ phút onboard')->nullable();
            $table->integer('status')->comment('Trang thai đặt lịch, 0: vừa đặt, 1: đã được x<PERSON>c nhận, 2 :bị từ chối')->default(0);
            $table->string('address')->comment('địa chỉ onboard');
            $table->string('name')->comment('Tên ứng viên')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_onboards');
    }
};
