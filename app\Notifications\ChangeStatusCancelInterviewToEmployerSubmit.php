<?php

namespace App\Notifications;

use App\Helpers\Common;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelInterviewToEmployerSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $bonus;

    public function __construct($submitCv,$bonus)
    {
        $this->submitCv = $submitCv;
        $this->bonus = $bonus;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->employer->name;
        $bonus       = $this->bonus;
        // $link        = route('employer-cv-bought',['view-detail' => $this->submitCv->wareHouseCvSelling->id]);

        $link = route('employer-submitcv', ['view-detail' => $this->submitCv->id]);
        return (new MailMessage)
            ->view('email.changeStatusCancelInterviewToEmployerSubmitCv', [
            'candidateName' => $candidateName,
            'companyName'   => $companyName,
            'position'      => $position,
            'type'          => $type,
            'bonus'         => Common::formatNumber($bonus),
            'urlWallet'     => route('employer-wallet'),
            'urlMarket'     => route('market-cv'),
            'link'          => $link,
            ])
            ->subject('[Recland] Thông báo hủy phỏng vấn ứng viên  '.$candidateName.' vị trí  '.$position);

    }




}
