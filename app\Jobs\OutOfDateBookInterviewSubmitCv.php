<?php

namespace App\Jobs;


use App\Notifications\CandidateRejectRecruitmentAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdminSubmitCv;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployer;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRec;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRecSubmit;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class OutOfDateBookInterviewSubmitCv implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCv;
    public $user;
    public function __construct($submitCv, $user)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
    }


    public function handle()
    {
        //  5 => 'Reject Interview schedule',
        if ($this->submitCv->status == config('constant.status_recruitment_revert.RejectInterviewschedule')) {
            $this->submitCv->update([
                'status' => config('constant.status_recruitment_revert.RecruiterCancelInterview'),
            ]);
            $this->submitCv->hoanTienChoNtd('Nhà tuyển dụng không đặt lại lịch', 'OutOfDateBookInterviewSubmitCv');

            $this->submitCv->employer->notify(new ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv($this->submitCv));
            if ($this->submitCv->authorize > 0) {
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusRecruiterCancelInterviewToAdminSubmitCv($this->submitCv));
            } else {
                $this->submitCv->rec->notify(new ChangeStatusRecruiterCancelInterviewToRecSubmit($this->submitCv));
            }
        }
    }
}
