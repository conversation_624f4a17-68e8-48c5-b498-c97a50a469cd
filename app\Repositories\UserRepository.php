<?php

namespace App\Repositories;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UserRepository extends BaseRepository
{
    const MODEL = User::class;

    public function getListUser($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }

        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->where('id', $params['search'])
                    ->orWhere('name', 'like', '%' . $params['search'] . '%')
                    ->orWhere('email', 'like', '%' . $params['search'] . '%')
                    ->orWhere('referral_define', 'like', '%' . $params['search'] . '%');
            });
        }
        if (isset($params['type'])) {
            $query->where('type', $params['type']);
            if (isset($params['cvt_new'])) {
                $query->where('created_at', '>=', $params['cvt_new']);
            }
        } else {
            $query->where('type', config('constant.role.admin'));
        }


        if (isset($params['start_date'])) {
            $query->where('created_at', '>=', Carbon::parse($params['start_date'])->format('Y-m-d'));
        }

        if (isset($params['is_active'])) {
            $query->where('is_active', isset($params['is_active']));
        }

        if (isset($params['end_date'])) {
            $query->where('created_at', '<', Carbon::parse($params['end_date'])->addDay()->format('Y-m-d'));
        }
        $query->with('userRole.role', 'userEmployerType');

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findEmail($type, $email, $id = null)
    {
        if (!$id) {
            return $this->query()->where('email', $email)->where('type', $type)->first();
        } else {
            return $this->query()->where('id', '!=', $id)->where('email', $email)->where('type', $type)->first();
        }
    }

    public function findPhone($type, $phone, $id = null)
    {
        if (!$id) {
            return $this->query()->where('mobile', $phone)->where('type', $type)->first();
        } else {
            return $this->query()->where('id', '!=', $id)->where('mobile', $phone)->where('type', $type)->first();
        }
    }

    public function findAdminById($id)
    {
        return $this->query()->where('id', $id)->with('userRole.role')->firstOrFail();
    }

    public function findAdminByEmail($email)
    {
        return $this->query()->where('email', $email)
            ->where('type', config('constant.role.admin'))
            ->first();
    }

    public function total($params = array())
    {
        $query = $this->query();
        foreach ($params as $key => $param) {
            switch ($key) {
                case 'after_date':
                    $query->where('created_at', '>=', $param);
                    break;
                case 'type':
                    $query->where('type', $param);
                    break;
                default:
                    break;
            }
        }
        $query->where('is_active', config('constant.active'));
        $query->where('is_real', 1); // Chỉ lấy dữ liệu thật

        return $query->count();
    }

    public function findEmailType($email, $type)
    {
        return $this->query()->where('email', $email)->where('type', $type)->first();
    }

    public function statisticaByMonth($type, $fromDate = null, $toDate = null)
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();
        return $query
            ->select(
                DB::raw("(count(id)) as total"),
                DB::raw("(DATE_FORMAT(created_at, '%m')) as month")
            )
            ->where('type', $type)
            ->where('is_active', config('constant.active'))
            ->where('is_real', 1) // Chỉ lấy dữ liệu thật
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->groupBy(DB::raw("DATE_FORMAT(created_at, '%m')"))
            ->orderBy(DB::raw("DATE_FORMAT(created_at, '%m')"))
            ->get();
    }

    public function findByToken($token)
    {
        return $this->query()->where('token', $token)->first();
    }

    public function getUserByType($type = 'admin', $option = [])
    {
        $query = $this->query()->where('type', $type);

        if (isset($option['company_id'])) {
            $query->where('company_id', $option['company_id']);
        }

        if (isset($option['is_active'])) {
            $query->where('is_active', $option['is_active']);
        }

        if (isset($option['flg'])) {
            $query->where('flg', $option['flg']);
        }

        return $query->get();
    }


    public function getUserWithLimitOffset($limit, $offset)
    {

        return $this->query()->select('*')
            ->where('type', 'rec')
            ->orderBy('referral_code', 'desc')
            ->offset($offset)->limit($limit)->get();
    }

    public function getUserWithRefLimitOffset($limit, $offset)
    {

        return $this->query()->select('*')
            ->where('type', 'rec')
            ->orderBy('id', 'asc')
            ->offset($offset)->limit($limit)->get();
    }

    public function getWithReferral($email)
    {

        return $this->query()->select('id', 'referral_define', 'referral_code', 'type', 'email')
            ->where('referral_code', $email)
            ->where('type', 'rec')
            ->get();
    }

    public function findByReferralDefine($referralCode)
    {
        return $this->query()->where('referral_define', $referralCode)->first();
    }

    public function getWithDefineReferral($referral_code)
    {

        return $this->query()->select('id', 'referral_define', 'referral_code', 'type', 'email')
            ->where('referral_code', $referral_code)
            ->where('type', 'rec')
            ->get();
    }

    public function getTeams($userId, $params)
    {
        $level = isset($params['level']) ? explode(',', $params['level']) : 2;
        $month = isset($params['month']) ? $params['month'] : date('m');
        $year = isset($params['year']) ? $params['year'] : date('Y');
        $search = isset($params['search']) ? $params['search'] : '';

        $page = isset($params['page']) ? $params['page'] : 1;
        $itemPerPage = isset($params['perPage']) ? $params['perPage'] : config('constant.limitPaginate');
        $offset = ($page - 1) * $itemPerPage;
        $year = date("Y");

        //mysql 5.7
        //query này lấy ra hệ thống cha con của user, 2 level: cha -> con1 -> con2. kèm theo số tiền của từng user
        $raw1 = "
            SELECT DATA.*, ID.level_ctv, coalesce(b.money, 0) as money, coalesce(b.money_kpi, 0) as money_kpi, coalesce(b.money + b.money_kpi, 0) as total, u.name as leader FROM(
                SELECT
                    @ids as _ids,

                    (   SELECT @ids := GROUP_CONCAT(id)

                        FROM users

                        WHERE FIND_IN_SET(parent_id, @ids)
                    ) as cids,
                    @l := @l+1 as level_ctv

                FROM users,

                    (SELECT @ids := :userId, @l := -1 ) b

                WHERE @ids IS NOT NULL

            ) ID, users as DATA
            INNER JOIN users u on DATA.parent_id = u.id
            LEFT JOIN (select id, user_id, sum(money) as money, sum(money_kpi) as money_kpi from bonus where year = :year group by user_id) as b on b.user_id = DATA.id
            WHERE FIND_IN_SET(DATA.id, ID._ids) and (DATA.email like :searchEmail or DATA.name like :searchName)
        ";

        if (isset($params['level'])) {
            $lvl = '';
            foreach ($level as $item) {
                if ($lvl == '') {
                    $lvl = intval($item);
                } else {
                    $lvl .= ',' . intval($item);
                }
            }

            $raw1 .= "
                and level_ctv in ($lvl)
            ";
        } else {
            $raw1 .= "
                and level_ctv > 0 and level_ctv <= $level
            ";
        }

        $raw1 .= "
        ORDER BY money_kpi DESC
        LIMIT :offset , :perPage
        ";
        // echo $raw1;
        // die;

        $query = DB::select(DB::raw($raw1), [
            'userId' => $userId,
            //            'level' => $level,
            'searchEmail' => "%{$search}%",
            'searchName' => "%{$search}%",
            'offset' => $offset,
            'perPage' => $itemPerPage,
            'year' => $year
        ]);
        return $query;
    }

    public function countTeams($userId, $params)
    {
        $level = isset($params['level']) ? explode(',', $params['level']) : 2;
        $month = isset($params['month']) ? $params['month'] : date('m');
        $year = isset($params['year']) ? $params['year'] : date('Y');
        $search = isset($params['search']) ? $params['search'] : '';

        //mysql 5.7
        $raw1 = "
            SELECT DATA.*, ID.level_ctv, b.money + b.money_kpi as total, u.name as leader FROM(
                SELECT
                    @ids as _ids,

                    (   SELECT @ids := GROUP_CONCAT(id)

                        FROM users

                        WHERE FIND_IN_SET(parent_id, @ids)
                    ) as cids,
                    @l := @l+1 as level_ctv

                FROM users,

                    (SELECT @ids := " . $userId . ", @l := -1 ) b

                WHERE @ids IS NOT NULL

            ) ID, users as DATA
            INNER JOIN users u on DATA.parent_id = u.id
            LEFT JOIN (select id, user_id, sum(money) as money, sum(money_kpi) as money_kpi from bonus group by user_id) as b on b.user_id = DATA.id
            WHERE FIND_IN_SET(DATA.id, ID._ids) and (DATA.email like '%" . $search . "%' or DATA.name like '%" . $search . "%')
        ";

        if (isset($params['level'])) {
            $lvl = '';
            foreach ($level as $item) {
                if ($lvl == '') {
                    $lvl = intval($item);
                } else {
                    $lvl .= ',' . intval($item);
                }
            }

            $raw1 .= "
                and level_ctv in ($lvl)
            ";
        } else {
            $raw1 .= "
                and level_ctv > 0 and level_ctv <= $level
            ";
        }
        // echo $raw1;
        // die;
        $query = DB::select(DB::raw($raw1));
        // dd($query);
        return count($query);
    }

    public function statisticalUser($userId)
    {
        $raw = "
            SELECT
                 u.name,
                 u.mobile,
                 u.email,
                 sum(b.money) as money,
                 sum(b.money_kpi) as money_kpi
            FROM
                users u
            left join bonus b on u.id = b.user_id
            where u.id = " . $userId . "
        ";
        $query = DB::select(DB::raw($raw));
        return $query[0];
    }

    public function findByTypeLastId($type)
    {
        return $this->query()->where('type', $type)->whereNotNull('referral_define')->orderBy('id', 'desc')->first();
    }

    public function getUserWithCompanyId($companyId, $params, $paginate = true)
    {
        $isLogin = auth('client')->user()->id;
        $query = $this->query();

        if (isset($params['search'])) {
            $query->where('name', 'like', '%' . $params['search'] . '%');
        }

        if (isset($params['is_invalid'])) {
            $query->whereHas('userEmployerType', function ($q) use ($params) {
                $q->where('is_invalid', $params['is_invalid']);
            });
        }

        $query->where('id', '!=', $isLogin)->where('type', 'employer')->where('company_id', $companyId);

        $query->with('userEmployerType')->orderBy('id', 'desc');

        if ($paginate) {
            $query = $query->paginate(config('constant.paginate_10'));
        } else {
            $query = $query->get();
        }
        return $query;
    }

    public function findType($limit, $offset)
    {
        return $this->query()->select('*')
            ->whereIn('type', ['rec', 'employer'])
            ->offset($offset)->limit($limit)->get();
    }

    public function getUserRecWithLimitOffset($limit, $offset)
    {

        return $this->query()->select('*')
            ->where('type', 'rec')
            ->orderBy('id', 'desc')
            ->offset($offset)->limit($limit)->get();
    }

    public function statusEmployerIsValid()
    {
        $user = auth('client')->user();
        $userDataWithIsInvalid = User::join('employer_types', 'employer_types.user_id', '=', 'users.id')
            ->select('employer_types.is_invalid')
            ->where('users.id', $user->id)
            ->get();
        return $userDataWithIsInvalid;
    }
    public function employerIsActive()
    {
        $user = auth('client')->user();
        return $user->is_active;
    }

    public function statisticalEmployerByMonth($type, $fromDate = null, $toDate = null)
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();
        return $query->where('is_active', config('constant.active'))
            ->where('is_real', 1) // Chỉ lấy dữ liệu thật
            ->where('type', $type)
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('count(id) as total, DATE_FORMAT(created_at, "%m") as month')
            ->groupByRaw('DATE_FORMAT(created_at, "%m")')
            ->orderByRaw('DATE_FORMAT(created_at, "%m")')
            ->get();
    }

    public function findByEmail($email, $type)
    {
        return $this->query()->where('email', $email)->where('type', $type)->first();
    }
}
