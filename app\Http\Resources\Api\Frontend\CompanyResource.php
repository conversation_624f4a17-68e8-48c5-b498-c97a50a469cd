<?php

namespace App\Http\Resources\Api\Frontend;

use App\Helpers\Common;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $cities = Common::getCities();
        $address = isset($this['address']) ? $this['address'] : $this->address;
        $address = json_decode($address, true);

        $area = [];
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a['area']) && isset($cities[$a['area']])) {
                    $area[] = [
                        'city'    => $cities[$a['area']],
                        'address' => $a['address'],
                    ];
                }
            }
        }
        return [
            'path_logo'       => $this->path_logo,
            'name'            => $this->name,
            'about'           => $this->about,
            'address'         => $this->address,
            'address_company' => $area,
            'scale_value'     => $this->scale_value,
            'website'         => $this->website,
            'detail_company'  => route('detail-company', $this->slug),
        ];
    }

}
