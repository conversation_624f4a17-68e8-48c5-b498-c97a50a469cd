<?php

namespace App\Services\Frontend;

use App\Repositories\TestimonialRepository;

class TestimonialService
{

    protected $testimonialRepository;

    public function __construct(TestimonialRepository $testimonialRepository)
    {
        $this->testimonialRepository = $testimonialRepository;
    }

    public function getListByType($type)
    {
        try {
            $data = $this->testimonialRepository->getListByType($type);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
