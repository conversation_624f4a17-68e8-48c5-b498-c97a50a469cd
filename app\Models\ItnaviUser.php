<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;

class ItnaviUser extends Authenticatable
{
    protected $table = 'itnavi_users';
    protected $guarded = ['id'];

    public function searchCvs()
    {
        return $this->hasMany(ItnaviSearchCv::class, 'user_id')->where('object_model_name', 'App\Models\User');
    }

    public function userCvs()
    {
        return $this->hasMany(ItnaviUserCv::class, 'user_id');
    }

    public function externalSources()
    {
        return $this->hasMany(ItnaviUserExternalSource::class, 'user_id');
    }
} 