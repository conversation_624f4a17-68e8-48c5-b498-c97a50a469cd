# RecLand - Coding Conventions & Style Guide

## PHP Coding Standards

### PSR Standards
- Follow PSR-1, PSR-2, PSR-4 standards
- Use PSR-12 for extended coding style
- Laravel-specific conventions

### Naming Conventions

#### Classes
```php
// Controllers
class UserController extends Controller
class AdminUserController extends Controller

// Models
class User extends Model
class WarehouseCv extends Model

// Services
class UserService
class EmailService

// Jobs
class SendWelcomeEmail implements ShouldQueue
class ProcessCvData implements ShouldQueue
```

#### Methods & Variables
```php
// Methods - camelCase
public function getUserProfile()
public function updateCompanyInfo()

// Variables - camelCase
$userName = 'john_doe';
$companyData = [];

// Constants - UPPER_SNAKE_CASE
const ACTIVE = 1;
const NON_ACTIVE = 0;
const CACHE_TTL = 86400;
```

#### Database
```php
// Tables - snake_case, plural
users, companies, warehouse_cvs, submit_cvs

// Columns - snake_case
user_name, created_at, is_active

// Foreign keys
user_id, company_id, warehouse_cv_id
```

### Code Structure

#### Controller Methods
```php
class UserController extends Controller
{
    public function index()
    {
        // List users
    }
    
    public function show($id)
    {
        // Show single user
    }
    
    public function store(Request $request)
    {
        // Create new user
    }
    
    public function update(Request $request, $id)
    {
        // Update user
    }
    
    public function destroy($id)
    {
        // Delete user
    }
}
```

#### Service Pattern
```php
class UserService
{
    protected $userRepository;
    
    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }
    
    public function createUser(array $data)
    {
        // Business logic here
        return $this->userRepository->create($data);
    }
}
```

#### Repository Pattern
```php
class UserRepository extends BaseRepository
{
    public function model()
    {
        return User::class;
    }
    
    public function findByEmail($email)
    {
        return $this->model->where('email', $email)->first();
    }
}
```

### Documentation

#### Method Documentation
```php
/**
 * Create a new user account
 *
 * @param array $data User data
 * @return User Created user instance
 * @throws ValidationException
 */
public function createUser(array $data): User
{
    // Implementation
}
```

#### Class Documentation
```php
/**
 * User management service
 * 
 * Handles user creation, updates, and business logic
 * 
 * @package App\Services\Frontend
 * <AUTHOR> Team
 */
class UserService
{
    // Implementation
}
```

## JavaScript/Vue.js Conventions

### Vue Components
```javascript
// Component naming - PascalCase
export default {
    name: 'UserProfile',
    components: {
        UserForm,
        UserList
    }
}
```

### Variables & Functions
```javascript
// Variables - camelCase
const userName = 'john_doe';
const companyData = {};

// Functions - camelCase
function getUserProfile() {
    // Implementation
}

const updateUserInfo = () => {
    // Implementation
}
```

### Constants
```javascript
// Constants - UPPER_SNAKE_CASE
const API_BASE_URL = '/api/v1';
const MAX_FILE_SIZE = 1024 * 1024; // 1MB
```

## Database Conventions

### Migration Naming
```php
// Create table
2024_01_01_000000_create_users_table.php

// Add column
2024_01_02_000000_add_phone_to_users_table.php

// Modify column
2024_01_03_000000_modify_email_in_users_table.php
```

### Model Relationships
```php
class User extends Model
{
    // belongsTo - singular
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    
    // hasMany - plural
    public function submitCvs()
    {
        return $this->hasMany(SubmitCv::class);
    }
    
    // belongsToMany - plural
    public function skills()
    {
        return $this->belongsToMany(Skill::class);
    }
}
```

## File Organization

### Directory Structure
```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/          # Admin controllers
│   │   ├── Frontend/       # Frontend controllers
│   │   └── Api/           # API controllers
│   ├── Requests/          # Form requests
│   └── Resources/         # API resources
├── Services/
│   ├── Admin/             # Admin services
│   └── Frontend/          # Frontend services
└── Repositories/          # Data repositories
```

### File Naming
- Controllers: `UserController.php`
- Models: `User.php`
- Services: `UserService.php`
- Repositories: `UserRepository.php`
- Requests: `UserRequest.php`
- Resources: `UserResource.php`

## Error Handling

### Exception Handling
```php
try {
    $user = $this->userService->createUser($data);
    return response()->json($user, 201);
} catch (ValidationException $e) {
    return response()->json(['errors' => $e->errors()], 422);
} catch (Exception $e) {
    Log::error('User creation failed: ' . $e->getMessage());
    return response()->json(['error' => 'Internal server error'], 500);
}
```

### Validation
```php
// Form Request
class UserRequest extends FormRequest
{
    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:8|confirmed'
        ];
    }
}
```

## Security Best Practices

### Input Validation
- Always validate user input
- Use Form Requests for validation
- Sanitize data before database operations

### Authentication & Authorization
- Use Laravel's built-in authentication
- Implement role-based permissions
- Protect sensitive routes with middleware

### Database Security
- Use Eloquent ORM to prevent SQL injection
- Implement proper indexing
- Use database transactions for critical operations

## Performance Guidelines

### Database Queries
- Use eager loading to prevent N+1 queries
- Implement proper indexing
- Use query optimization techniques

### Caching
- Cache frequently accessed data
- Use Redis for session and cache storage
- Implement cache invalidation strategies

### File Handling
- Use S3 for file storage
- Implement proper file validation
- Optimize image sizes and formats