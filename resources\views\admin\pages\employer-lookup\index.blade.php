@extends('admin.layouts.app')

@section('content')
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Tra Cứu Thông Tin Nhà Tuyển Dụng</h4>
    </div>
</div>
<!--End Page header-->

<!-- Search Form -->
<div class="row">
    <div class="col-xl-12 col-md-12 col-lg-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Tìm kiếm nhà tuyển dụng</h3>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="form-group">
                        <label for="employer_email">Email nhà tuyển dụng:</label>
                        <div class="input-group">
                            <input type="email" class="form-control" id="employer_email" name="email"
                                placeholder="Nhập email nhà tuyển dụng..." required
                                value="{{ $autoSearchEmail ?? '' }}">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fa fa-search"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading indicator -->
<div id="loading" class="text-center" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Đang tải...</span>
    </div>
</div>

<!-- Results container -->
<div id="results" style="display: none;">
    <!-- Employer Info Box -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thông tin nhà tuyển dụng</h3>
                </div>
                <div class="card-body">
                    <div id="employerInfo"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Jobs Table -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Danh sách Job</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="jobsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên Job</th>
                                    <th>Bonus (VND)</th>
                                    <th>Bonus Type</th>
                                    <th>Bonus CTV</th>
                                    <th>Manual Bonus CTV</th>
                                    <th>Số ứng tuyển</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody id="jobsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wallet Transactions Table -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Lịch sử giao dịch ví</h3>
                </div>
                <div class="card-body">
                    <div id="walletInfo" class="mb-3"></div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="transactionsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Loại</th>
                                    <th>Số tiền</th>
                                    <th>Số dư sau GD</th>
                                    <th>Mô tả</th>
                                    <th>Tên ứng viên</th>
                                    <th>Email ứng viên</th>
                                    <th>Tên Job</th>
                                    <th>CTV</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Submit CVs -->
<div class="modal fade" id="submitCvModal" tabindex="-1" role="dialog" aria-labelledby="submitCvModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="submitCvModalLabel">Danh sách ứng tuyển</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="submitCvsTable">
                        <thead>
                            <tr>
                                <th>Submit ID</th>
                                <th>Tên ứng viên</th>
                                <th>Email ứng viên</th>
                                <th>Job</th>
                                <th>Bonus cho CTV</th>
                                <th>Trạng thái</th>
                                <th>Thời gian ứng tuyển</th>
                            </tr>
                        </thead>
                        <tbody id="submitCvsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    $(document).ready(function() {
    // Tự động tìm kiếm nếu có email trong URL
    @if(isset($autoSearchEmail) && $autoSearchEmail)
        searchEmployer('{{ $autoSearchEmail }}');
    @endif

    // Form submit handler
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        
        const email = $('#employer_email').val();
        if (!email) {
            alert('Vui lòng nhập email nhà tuyển dụng');
            return;
        }

        searchEmployer(email);
    });

    // Search employer function
    function searchEmployer(email) {
        $('#loading').show();
        $('#results').hide();

        $.ajax({
            url: '{{ route("admin.employer-lookup.search") }}',
            type: 'POST',
            data: {
                email: email,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loading').hide();
                
                if (response.success) {
                    displayResults(response.data);
                } else {
                    alert(response.message || 'Có lỗi xảy ra');
                }
            },
            error: function(xhr) {
                $('#loading').hide();
                
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMsg = 'Dữ liệu không hợp lệ:\n';
                    for (let field in errors) {
                        errorMsg += '- ' + errors[field].join('\n- ') + '\n';
                    }
                    alert(errorMsg);
                } else {
                    alert('Có lỗi xảy ra khi tìm kiếm');
                }
            }
        });
    }

    // Display results function
    function displayResults(data) {
        // Display employer info
        displayEmployerInfo(data.employer);
        
        // Display jobs table
        displayJobsTable(data.jobs);
        
        // Display wallet transactions
        displayWalletTransactions(data.wallet_transactions);
        
        $('#results').show();
    }

    // Display employer information
    function displayEmployerInfo(employer) {
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td>${employer.email}</td>
                        </tr>
                        <tr>
                            <td><strong>Tên công ty:</strong></td>
                            <td>${employer.company_name || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td><strong>Số job đã đăng:</strong></td>
                            <td>${employer.total_jobs}</td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Ngày đăng ký:</strong></td>
                            <td>${employer.created_at}</td>
                        </tr>
                        <tr>
                            <td><strong>Đăng nhập gần nhất:</strong></td>
                            <td>${employer.last_login_at}</td>
                        </tr>
                        <tr>
                            <td><strong>Tình trạng:</strong></td>
                            <td><span class="badge badge-${employer.status === 'Thật' ? 'success' : 'warning'}">${employer.status}</span></td>
                        </tr>
                        <tr>
                            <td><strong>Số dư ví:</strong></td>
                            <td><strong class="text-primary">${employer.wallet_amount} VND</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
        $('#employerInfo').html(html);
    }

    // Display jobs table
    function displayJobsTable(jobs) {
        let html = '';
        jobs.forEach(function(job) {
            html += `
                <tr>
                    <td>
                        <span class="copy-id" data-id="${job.id}" style="cursor: pointer; color: #007bff;">
                            ${job.id} <i class="fa fa-copy"></i>
                        </span>
                    </td>
                    <td>${job.name}</td>
                    <td>${job.bonus}</td>
                    <td>${job.bonus_type || 'N/A'}</td>
                    <td>${job.bonus_for_ctv || 'N/A'}</td>
                    <td>${job.manual_bonus_for_ctv || 'N/A'}</td>
                    <td>${job.submit_count}</td>
                    <td>${job.created_at}</td>
                    <td>
                        <a href="${job.edit_url}" class="btn btn-sm btn-primary" target="_blank">
                            <i class="fa fa-edit"></i> Edit
                        </a>
                        <button class="btn btn-sm btn-info view-submits" data-job-id="${job.id}">
                            <i class="fa fa-eye"></i> Xem ứng tuyển
                        </button>
                    </td>
                </tr>
            `;
        });
        $('#jobsTableBody').html(html);
    }

    // Display wallet transactions
    function displayWalletTransactions(walletData) {
        // Display wallet ID info
        if (walletData.wallet_id) {
            const walletInfoHtml = `
                <div class="alert alert-info">
                    <strong>ID Ví:</strong> ${walletData.wallet_id}
                </div>
            `;
            $('#walletInfo').html(walletInfoHtml);
        } else {
            $('#walletInfo').html('<div class="alert alert-warning">Không tìm thấy thông tin ví</div>');
        }

        // Display transactions
        let html = '';
        if (walletData.transactions && walletData.transactions.length > 0) {
            walletData.transactions.forEach(function(transaction) {
                html += `
                    <tr>
                        <td>${transaction.id}</td>
                        <td><span class="badge badge-${transaction.type === 'Nạp tiền' ? 'success' : 'danger'}">${transaction.type}</span></td>
                        <td class="${transaction.amount.includes('-') ? 'text-danger' : 'text-success'}">${transaction.amount}</td>
                        <td>${transaction.balance_after}</td>
                        <td>${transaction.description}</td>
                        <td>${transaction.candidate_name}</td>
                        <td>${transaction.candidate_email}</td>
                        <td>${transaction.job_title}</td>
                        <td>${transaction.ctv_name}<br><sub>${transaction.ctv_code}</sub></td>
                        <td>${transaction.created_at}</td>
                    </tr>
                `;
            });
        } else {
            html = '<tr><td colspan="6" class="text-center">Không có giao dịch nào</td></tr>';
        }
        $('#transactionsTableBody').html(html);
    }

    // Copy ID to clipboard
    $(document).on('click', '.copy-id', function() {
        const id = $(this).data('id');
        copyToClipboard(id);
        
        // Show feedback
        const originalHtml = $(this).html();
        $(this).html(id + ' <i class="fa fa-check text-success"></i>');
        setTimeout(() => {
            $(this).html(originalHtml);
        }, 1500);
    });

    // View submits button click
    $(document).on('click', '.view-submits', function() {
        const jobId = $(this).data('job-id');
        loadJobSubmits(jobId);
    });

    // Load job submits
    function loadJobSubmits(jobId) {
        $.ajax({
            url: '{{ route("admin.employer-lookup.job-submits", ":jobId") }}'.replace(':jobId', jobId),
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    displayJobSubmits(response.data);
                    $('#submitCvModal').modal('show');
                } else {
                    alert('Không thể tải danh sách ứng tuyển');
                }
            },
            error: function() {
                alert('Có lỗi xảy ra khi tải danh sách ứng tuyển');
            }
        });
    }

    // Display job submits in modal
    function displayJobSubmits(submits) {
        let html = '';
        submits.forEach(function(submit) {
            html += `
                <tr>
                    <td>
                        <span class="copy-id" data-id="${submit.id}" style="cursor: pointer; color: #007bff;">
                            ${submit.id} <i class="fa fa-copy"></i>
                        </span>
                    </td>
                    <td>${submit.candidate_name || 'N/A'}</td>
                    <td>${submit.candidate_email || 'N/A'}</td>
                    <td>${submit.job_name}</td>
                    <td>${submit.bonus_for_ctv} VND</td>
                    <td>${submit.status}</td>
                    <td>${submit.created_at}</td>
                </tr>
            `;
        });
        $('#submitCvsTableBody').html(html);
    }

    // Copy to clipboard function
    function copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Fallback: Unable to copy', err);
            }
            document.body.removeChild(textArea);
        }
    }
});
</script>
@endsection