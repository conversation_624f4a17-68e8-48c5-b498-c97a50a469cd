<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class WareHouseCvSellingSave extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'warehouse_cv_selling_saves';
    public $timestamps = true;
    protected $guarded = ['id'];

    public function wareHouseCvSelling()
    {
        return $this->belongsTo(WareHouseCvSelling::class, 'warehouse_cv_selling_id', 'id');
    }
}
