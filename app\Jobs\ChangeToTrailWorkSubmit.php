<?php

namespace App\Jobs;

use App\Notifications\ChangeStatusTrailWorkToAdmin;
use App\Notifications\ChangeStatusTrailWorkToAdminSubmit;
use App\Notifications\ChangeStatusTrailWorkToAuthorityRec;
use App\Notifications\ChangeStatusTrailWorkToAuthorityRecSubmit;
use App\Notifications\ChangeStatusTrailWorkToRec;
use App\Notifications\ChangeStatusTrailWorkToRecSubmit;
use App\Notifications\RemindExpireTrailWork;
use App\Notifications\RemindExpireTrailWorkSubmit;
use App\Notifications\RemindPaymentDebit;
use App\Notifications\RemindPaymentDebitSubmit;
use App\Repositories\PaymentDebitRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\SubmitCvPaymentDebitRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class ChangeToTrailWorkSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $submitCvId;
    protected $user;

    public function __construct($submitCvId, $user)
    {
        $this->submitCvId = $submitCvId;
        $this->user = $user;
    }

    public function handle()
    {

        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCvPaymentDebitRepository = app(SubmitCvPaymentDebitRepository::class);
        $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
        $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
        $submitCvOnboardRepository = app(SubmitCvOnboardRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);

        if ($submitCv->status != 13) return false;

        $point = $submitCv->bonus_point - (0.1 * $submitCv->bonus_point);
        //check so point của NTD neu ko đủ thi dừng  + $submitCv->point
        if ($point > $submitCv->employer->wallet->amount) {
            //Luu vao 1 table Ghi nợ, user_id (ntd) status = 0 chưa trả nợ
            //sau khi NTD nạp tiền -> đủ tiền thì sẽ thanh toán -> status đã thanh toán = 1
            $submitCvPaymentDebitRepository->create([
                'submit_cv_id' => $submitCv->id,
                'user_id'      => $submitCv->user_id,
                'point'        => $point,
            ]);

            $submitCv->employer->notify((new RemindPaymentDebitSubmit($submitCv))
                ->delay([
                    'mail' => now()->addMinutes(24 * 60),
                ]));
            $start = 10;
            while ($start <= 15) {
                $submitCv->employer->notify((new RemindPaymentDebitSubmit($submitCv))
                    ->delay([
                        'mail' => now()->addMinutes($start * 24 * 60),
                    ]));
                $start++;
            }
        } else {
            $percent = 90; //đặt cọc lần 1 10%, trailwork thanh toan nốt 90%
            //trừ point của NTD
            $balance  = $submitCv->employer->wallet->amount - $point;
            // $submitCv->employer->wallet->amount = $balance;
            $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
            $submitCv->employer->wallet->subtractAmount($point, $submitCv, 'Trừ tiền từ ví', 'change_to_trailwork_submit');

            // $submitCv->employer->wallet->save();
            //ghi log mua
            $submitCvHistoryPaymentRepository->create([
                'user_id'      => $submitCv->employer->id,
                'submit_cv_id' => $submitCv->id,
                'type'         => 0,                         //0 trừ tiền, 1 hoàn tiền
                'percent'      => $percent,                  //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale' => $submitCv->bonus_type,
                'amount'        => $point,
                'balance'      => $balance,
                'status'       => 0
            ]);
            $paymentStatus = 4;
        }
        $updateSubmitCv = [
            'status' => 14,
        ];

        if (!empty($paymentStatus)) {
            $updateSubmitCv['status_payment'] = $paymentStatus;
        }

        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
        //update Trial work
        $submitCv->update($updateSubmitCv);
        //update status history: Trial work
        $submitCvHistoryStatusRepository->logStatus($submitCv, $this->user);
        if ($submitCv->authorize > 0) {
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusTrailWorkToAdminSubmit($submitCv));
            $submitCv->rec->notify(new ChangeStatusTrailWorkToAuthorityRecSubmit($submitCv));
        } else {
            $submitCv->rec->notify(new ChangeStatusTrailWorkToRecSubmit($submitCv));
        }
        //set queue sau 55 ngày  Trail work gửi email
        $start = 55;
        $onboard = $submitCvOnboardRepository->getFirstBySubmitCvId($submitCv->id);
        $carbonDate = $currentDateBook = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);
        while ($start < 60) {
            $addTime = clone $carbonDate;
            $submitCv->employer->notify((new RemindExpireTrailWorkSubmit($submitCv, $start))
                ->delay([
                    'mail' => $addTime->addMinutes($start * 24 * 60),
                ]));
            $start++;
        }

        //sau 7 ngay het han trailwork (2 thang) = 67 ngay nếu NTD ko đổi trạng thái thì tự động đổi sang 16 => 'Success Recruitment',
        $addRecuitmentTime = clone $carbonDate;
        SuccessRecuitmentSubmit::dispatch($submitCv->id)->delay($addRecuitmentTime->addMinutes(67 * 24 * 60));
        //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
        $add30Time = clone $carbonDate;
        PayOnboardSubmit::dispatch($submitCv->id, 15)->delay($add30Time->addMinutes(30 * 24 * 60));
        //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
        $add45Time = clone $carbonDate;
        PayOnboardSubmit::dispatch($submitCv->id, 10)->delay($add45Time->addMinutes(45 * 24 * 60));
        // Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
        $add67Time = clone $carbonDate;
        PayOnboardSubmit::dispatch($submitCv->id, 75)->delay($add67Time->addMinutes(67 * 24 * 60));
    }
}
