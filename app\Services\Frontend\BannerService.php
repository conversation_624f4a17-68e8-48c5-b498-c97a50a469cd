<?php

namespace App\Services\Frontend;

use App\Repositories\BannerRepository;
use App\Services\FileServiceS3;

class BannerService
{

    protected $bannerRepository;

    public function __construct(BannerRepository $bannerRepository)
    {
        $this->bannerRepository = $bannerRepository;
    }


    public function getListByType($type = '', $position, $limit = 0)
    {
        return $this->bannerRepository->getListByType($type, $position, $limit);
    }

    public function detailService($id)
    {
        return $this->bannerRepository->find($id);
    }

    public function updateService(array $request, $id)
    {
        if (isset($request['img_vn'])) {
            $request['img_vn'] = FileServiceS3::getInstance()->uploadToS3($request['img_vn'], config('constant.sub_path_s3.banner'));
        }

        if (isset($request['img_en'])) {
            $request['img_en'] = FileServiceS3::getInstance()->uploadToS3($request['img_en'], config('constant.sub_path_s3.banner'));
        }

        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->bannerRepository->update($id, [], $request);
    }

}
