<?php

namespace App\Services\Admin;

use App\Repositories\SkillRepository;

class SkillService
{
    protected $skillRepository;

    public function __construct(SkillRepository $skillRepository)
    {
        $this->skillRepository = $skillRepository;
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->skillRepository->getListSkill($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false'
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-orderable' => 'false'
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name',
                    'data-orderable' => 'false'
                ),
                'value' => 'Tên kỹ năng'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                    'data-orderable' => 'false'
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('skill-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function createService($params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $data = [
            'name' => $params['name'],
            'is_active' => $isActive
        ];

        return $this->skillRepository->create($data);;
    }

    public function createMultipleService($params)
    {
        return $this->skillRepository->insert($params);
    }

    public function detailService($id)
    {
        return $this->skillRepository->find($id);
    }

    public function updateService($params, $id)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $data = [
            'name' => $params['name'],
            'is_active' => $isActive
        ];

        return $this->skillRepository->update($id, [], $data);
    }

    public function getName()
    {
        return $this->skillRepository->getArrName();
    }

    public function getByName($name){
        return $this->skillRepository->getByName($name);
    }

}
