<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class Bonus extends BaseModel
{

    protected $table = 'bonus';

    protected $fillable = [
        'user_id',
        'month',
        'year',
        'money',
        'price',
        'money_kpi',
        'payment_status',
    ];
    protected $appends = [
        'monthyear_value',
        'money_value',
        'moneykpi_value',
        'price_value',
        'payment_status_value',
    ];

    public function getMonthyearValueAttribute()
    {
        return $this->month . '/' . $this->year;
    }

    public function getMoneyValueAttribute()
    {
        return $this->money > 0 ? number_format($this->money) : 0;
    }

    public function getMoneykpiValueAttribute()
    {
        return $this->money_kpi > 0 ? number_format($this->money_kpi) : 0;
    }


    public function getRevenueValueAttribute(){
        $revenue = (int)$this->money + (int)$this->price;
        return number_format($revenue);
    }


    public function getPriceValueAttribute()
    {
        return $this->price > 0 ? number_format($this->price) : 0;
    }

    public function getPaymentStatusValueAttribute()
    {
        return config('constant.payment_status_bonus.' . $this->payment_status);
    }

}
