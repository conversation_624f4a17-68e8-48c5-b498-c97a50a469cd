<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class WareHouseCvSellingBuyDiscuss extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'warehouse_cv_selling_buy_discuss';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['date_hour'];

    public function wareHouseCvSellingBuy()
    {
        return $this->belongsTo(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_buy_id', 'id');
    }

    public function employer(){
        return $this->hasOne(User::class,'id','ntd_id');
    }

    public function rec(){
        return $this->hasOne(User::class,'id','ctv_id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }



}
