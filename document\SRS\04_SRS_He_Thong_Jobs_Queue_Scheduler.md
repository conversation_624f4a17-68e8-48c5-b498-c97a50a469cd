# Tài liệu Đặc tả Hệ thống Jobs/Queue/Scheduler - RecLand

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**Tá<PERSON> gi<PERSON>:** AI Assistant

---

## 1. <PERSON><PERSON><PERSON> quan Hệ thống Background Jobs

### 1.1. <PERSON><PERSON>n trúc Queue System

RecLand sử dụng Laravel Queue system để xử lý các tác vụ bất đồng bộ:

- **Queue Driver:** Database (có thể mở rộng sang Redis)
- **Queue Table:** `jobs` (Laravel default)
- **Job Logging:** `job_logs` (custom logging)
- **Failed Jobs:** `failed_jobs` (<PERSON><PERSON> default)

### 1.2. <PERSON>ân loại Jobs

| Loại Job | Mô tả | Số lượng |
|----------|-------|----------|
| **Payment Jobs** | Xử lý thanh toán và hoa hồng | 8 jobs |
| **Status Transition Jobs** | Chuyển đổi trạng thái tự động | 6 jobs |
| **Notification Jobs** | Gửi email và thông báo | 4 jobs |
| **Timeout/Expiry Jobs** | Xử lý hết hạn và timeout | 5 jobs |
| **Refund Jobs** | Hoàn tiền và xử lý khiếu nại | 3 jobs |

---

## 2. Payment & Commission Jobs

### 2.1. RecSumPoint - Thanh toán hoa hồng MarketCV

**File:** `app/Jobs/RecSumPoint.php`

```php
class RecSumPoint implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    
    public function __construct($wareHouseCvSellingBuyId) {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }
    
    public function handle() {
        $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Điều kiện: Sau 7 ngày không khiếu nại
        if ($cvSellingBuy->status_recruitment == config('constant.status_recruitment_revert.BuyCVdatasuccessfull') &&
            ($cvSellingBuy->status_complain == 0 || $cvSellingBuy->status_complain == 5)) {
            
            // Tính hoa hồng
            $commissionRate = ($cvSellingBuy->authority == 2) ? 0.2 : 1.0;
            $commissionAmount = $cvSellingBuy->point * $commissionRate;
            
            // Cộng tiền vào ví CTV
            $cvSellingBuy->ctv->wallet->increment('price', $commissionAmount);
            
            // Ghi nhận payin
            $this->updatePayinRecords($cvSellingBuy, $commissionAmount);
            
            // Gửi thông báo
            $cvSellingBuy->ctv->notify(new PaymentOpenTurnCv($cvSellingBuy, $commissionAmount));
        }
    }
}
```

**Trigger:** Dispatch sau 7 ngày khi mua CV Data thành công
**Delay:** `now()->addDays(7)`

### 2.2. RecSumPointSubmit - Thanh toán hoa hồng Submit CV

**File:** `app/Jobs/RecSumPointSubmit.php`

```php
class RecSumPointSubmit implements ShouldQueue {
    public $submitCvId;
    
    public function handle() {
        $submitCv = $this->submitCvRepository->find($this->submitCvId);
        
        // Điều kiện tương tự RecSumPoint
        if ($submitCv->status == config('constant.status_recruitment_revert.BuyCVdatasuccessfull') &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {
            
            // Tính hoa hồng dựa trên authorize
            if ($submitCv->authorize == 1 && $submitCv->authorize_status == 1) {
                $commissionAmount = $submitCv->bonus * 0.2; // 20%
            } else {
                $commissionAmount = $submitCv->bonus; // 100%
            }
            
            // Cộng tiền và ghi nhận
            $this->processCommissionPayment($submitCv, $commissionAmount);
        }
    }
}
```

**Trigger:** Dispatch sau 7 ngày khi NTD thanh toán Submit CV
**Delay:** `now()->addDays(7)`

### 2.3. PayInterview - Thanh toán hoa hồng Interview (MarketCV)

**File:** `app/Jobs/PayInterview.php`

```php
class PayInterview implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    
    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Điều kiện: Status = 8 (Pass Interview) hoặc 10, không có khiếu nại
        if (($wareHouseCvSellingBuy->status_recruitment == 8 || $wareHouseCvSellingBuy->status_recruitment == 10) &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
            
            $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
            $wareHouseCvSellingBuyService->payCtv($wareHouseCvSellingBuy);
        }
    }
}
```

**Trigger:** Dispatch sau 24h khi ứng viên pass interview
**Delay:** `now()->addHours(24)`

### 2.4. PayInterviewSubmit - Thanh toán hoa hồng Interview (Submit CV)

**File:** `app/Jobs/PayInterviewSubmit.php`

Tương tự `PayInterview` nhưng cho luồng Submit CV.

**Trigger:** Dispatch sau 24h khi ứng viên pass interview
**Delay:** `now()->addHours(24)`

### 2.5. PayOnboard - Thanh toán hoa hồng Onboard theo giai đoạn (MarketCV)

**File:** `app/Jobs/PayOnboard.php`

```php
class PayOnboard implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    public $percent;
    
    public function __construct($wareHouseCvSellingBuyId, $percent) {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->percent = $percent;
    }
    
    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Điều kiện: Status = 14 (Trial work) hoặc 16 (Success), không có khiếu nại
        if (($wareHouseCvSellingBuy->status_recruitment == 14 || $wareHouseCvSellingBuy->status_recruitment == 16) &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
            
            $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
            $wareHouseCvSellingBuyService->payOnboardCtv($wareHouseCvSellingBuy, $this->percent);
        }
    }
}
```

**Trigger Schedule:**
- **15%:** Sau 30 ngày trial work - `now()->addDays(30)`
- **10%:** Sau 45 ngày trial work - `now()->addDays(45)`  
- **75%:** Sau 67 ngày trial work - `now()->addDays(67)`

### 2.6. PayOnboardSubmit - Thanh toán hoa hồng Onboard (Submit CV)

**File:** `app/Jobs/PayOnboardSubmit.php`

Tương tự `PayOnboard` nhưng cho luồng Submit CV với cùng schedule.

---

## 3. Status Transition Jobs

### 3.1. ChangeToTrailWork - Chuyển sang thử việc (MarketCV)

**File:** `app/Jobs/ChangeToTrailWork.php`

```php
class ChangeToTrailWork implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    public $user;
    
    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Kiểm tra số dư ví NTD cho 90% còn lại của onboard fee
        $remainingAmount = $wareHouseCvSellingBuy->point * 0.9;
        $wallet = $wareHouseCvSellingBuy->user->wallet;
        
        if ($wallet->point < $remainingAmount) {
            // Tạo ghi nợ
            $this->paymentDebitRepository->create([
                'user_id' => $wareHouseCvSellingBuy->user_id,
                'warehouse_cv_selling_buy_id' => $wareHouseCvSellingBuy->id,
                'amount' => $remainingAmount,
                'due_date' => now()->addDays(15)
            ]);
            
            // Gửi email nhắc nhở thanh toán nợ
            $this->scheduleDebtReminders($wareHouseCvSellingBuy->user);
        } else {
            // Trừ tiền ngay
            $wallet->decrement('point', $remainingAmount);
            $this->logPaymentHistory($wareHouseCvSellingBuy, 90);
        }
        
        // Update status
        $wareHouseCvSellingBuy->update([
            'status_recruitment' => 14, // Trial work
            'date_book' => now()
        ]);
        
        // Schedule các job tiếp theo
        $this->scheduleFollowUpJobs($wareHouseCvSellingBuy);
    }
    
    private function scheduleFollowUpJobs($wareHouseCvSellingBuy) {
        $carbonDate = Carbon::parse($wareHouseCvSellingBuy->date_book);
        
        // Tự động chuyển thành Success Recruitment sau 67 ngày
        SuccessRecruitment::dispatch($wareHouseCvSellingBuy->id)
            ->delay($carbonDate->copy()->addDays(67));
        
        // Thanh toán hoa hồng theo giai đoạn
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 15)
            ->delay($carbonDate->copy()->addDays(30));
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 10)
            ->delay($carbonDate->copy()->addDays(45));
        PayOnboard::dispatch($wareHouseCvSellingBuy->id, 75)
            ->delay($carbonDate->copy()->addDays(67));
        
        // Nhắc nhở hết hạn thử việc (từ ngày 55-59)
        for ($day = 55; $day <= 59; $day++) {
            RemindExpireTrailWork::dispatch($wareHouseCvSellingBuy->id)
                ->delay($carbonDate->copy()->addDays($day));
        }
    }
}
```

**Trigger:** Dispatch sau 7 ngày khi onboard được confirm
**Delay:** `now()->addDays(7)`

### 3.2. ChangeToTrailWorkSubmit - Chuyển sang thử việc (Submit CV)

**File:** `app/Jobs/ChangeToTrailWorkSubmit.php`

Tương tự `ChangeToTrailWork` nhưng cho luồng Submit CV.

### 3.3. SuccessRecruitment - Tự động chuyển thành tuyển dụng thành công

**File:** `app/Jobs/SuccessRecruitment.php`

```php
class SuccessRecruitment implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    
    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Tự động chuyển status nếu vẫn ở trial work
        if ($wareHouseCvSellingBuy->status_recruitment == 14) {
            $wareHouseCvSellingBuy->update([
                'status_recruitment' => 16, // Success Recruitment
                'status_payment' => 4 // Completed
            ]);
            
            // Ghi lịch sử
            $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy);
            
            // Gửi thông báo
            $this->sendSuccessNotifications($wareHouseCvSellingBuy);
        }
    }
}
```

**Trigger:** Dispatch sau 67 ngày trial work
**Delay:** `now()->addDays(67)`

---

## 4. Timeout & Expiry Jobs

### 4.1. RejectRecruitment - Tự động từ chối sau 48h

**File:** `app/Jobs/RejectRecruitment.php`

```php
class RejectRecruitment implements ShouldQueue {
    public $wareHouseCvSellingBuyId;
    
    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        
        // Chỉ xử lý nếu vẫn ở trạng thái chờ xác nhận
        if ($wareHouseCvSellingBuy->status_recruitment == config('constant.status_recruitment_revert.Waitingcandidateconfirm')) {
            
            if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                // Hoàn tiền 100% cho interview
                $this->refundPayment($wareHouseCvSellingBuy, 100);
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'),
                    'status_payment' => 3 // Refund
                ]);
            } elseif ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                // Hoàn tiền các khoản đã thanh toán
                $this->refundPreviousPayments($wareHouseCvSellingBuy);
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'),
                    'status_payment' => 2 // Deposit Refund
                ]);
            }
            
            // Gửi thông báo
            $this->sendRejectionNotifications($wareHouseCvSellingBuy);
        }
    }
}
```

**Trigger:** Dispatch sau 48h khi offer được gửi
**Delay:** `now()->addHours(48)`

### 4.2. OutOfDateBookInterview - Hết hạn lịch phỏng vấn

**File:** `app/Jobs/OutOfDateBookInterview.php`

```php
class OutOfDateBookInterview implements ShouldQueue {
    public function handle() {
        // Tìm các lịch phỏng vấn quá hạn 7 ngày
        $expiredBookings = WareHouseCvSellingBuyBook::where('status', 5) // Reject Interview schedule
                                                   ->where('created_at', '<', now()->subDays(7))
                                                   ->get();
        
        foreach ($expiredBookings as $booking) {
            $booking->update(['status' => 6]); // Cancel Interview
            
            $booking->wareHouseCvSellingBuy->update([
                'status_recruitment' => 6 // Cancel Interview
            ]);
            
            // Gửi thông báo
            $this->sendCancellationNotifications($booking);
        }
    }
}
```

**Trigger:** Scheduled job chạy định kỳ hoặc dispatch khi cần

---

## 5. Notification & Email Jobs

### 5.1. SendemailBook - Gửi email xác nhận lịch phỏng vấn

**File:** `app/Jobs/SendemailBook.php`

```php
class SendemailBook implements ShouldQueue {
    public $wareHouseCvSellingBuyBookId;

    public function handle() {
        $booking = $this->wareHouseCvSellingBuyBookRepository->find($this->wareHouseCvSellingBuyBookId);

        // Gửi email cho ứng viên
        Mail::to($booking->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_email)
            ->send(new InterviewScheduleNotification($booking));

        // Gửi email cho CTV
        $booking->wareHouseCvSellingBuy->ctv->notify(new InterviewScheduledNotification($booking));

        // Gửi email cho NTD
        $booking->wareHouseCvSellingBuy->user->notify(new InterviewConfirmedNotification($booking));
    }
}
```

**Trigger:** Dispatch ngay khi lịch phỏng vấn được tạo
**Delay:** Immediate

### 5.2. SendemailBookSubmit - Gửi email xác nhận lịch phỏng vấn (Submit CV)

**File:** `app/Jobs/SendemailBookSubmit.php`

Tương tự `SendemailBook` nhưng cho luồng Submit CV.

---

## 6. Refund & Complaint Jobs

### 6.1. DepositRefundRejectOffer - Hoàn tiền khi từ chối offer

**File:** `app/Jobs/DepositRefundRejectOffer.php`

```php
class DepositRefundRejectOffer implements ShouldQueue {
    public $wareHouseCvSellingBuyId;

    public function handle() {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);

        // Tính toán số tiền hoàn lại
        $refundAmount = $this->calculateRefundAmount($wareHouseCvSellingBuy);

        // Hoàn tiền vào ví NTD
        $wareHouseCvSellingBuy->user->wallet->increment('point', $refundAmount);

        // Ghi lịch sử hoàn tiền
        $this->wareHouseCvSellingHistoryBuyRepository->create([
            'user_id' => $wareHouseCvSellingBuy->user_id,
            'warehouse_cv_selling_buy_id' => $wareHouseCvSellingBuy->id,
            'type' => 1, // Hoàn tiền
            'type_of_sale' => $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
            'price' => $refundAmount,
            'point' => $refundAmount,
            'balance' => $wareHouseCvSellingBuy->user->wallet->point,
            'comment' => 'Refund due to offer rejection'
        ]);

        // Update status
        $wareHouseCvSellingBuy->update([
            'status_recruitment' => config('constant.status_recruitment_revert.RejectOffer'),
            'status_payment' => 3 // Refunded
        ]);

        // Gửi thông báo
        $this->sendRefundNotifications($wareHouseCvSellingBuy, $refundAmount);
    }
}
```

**Trigger:** Dispatch khi ứng viên từ chối offer
**Delay:** Immediate

### 6.2. DepositRefundRejectOfferSubmit - Hoàn tiền (Submit CV)

**File:** `app/Jobs/DepositRefundRejectOfferSubmit.php`

Tương tự `DepositRefundRejectOffer` nhưng cho luồng Submit CV.

---

## 7. Scheduled Commands (Cron Jobs)

### 7.1. Cấu hình Scheduler

**File:** `app/Console/Kernel.php`

```php
protected function schedule(Schedule $schedule) {
    // CV hết hạn - chạy hàng ngày
    $schedule->command('cv:expired')
             ->daily()
             ->withoutOverlapping()
             ->onOneServer()
             ->runInBackground();

    // Dọn dẹp dữ liệu - chạy lúc 3:00 sáng
    $schedule->command('command:clean-data')
             ->dailyAt('03:00')
             ->withoutOverlapping()
             ->onOneServer()
             ->runInBackground();

    // Xử lý từ chối ứng viên - chạy mỗi phút
    $schedule->command('candidate:reject-job')
             ->everyMinute()
             ->withoutOverlapping()
             ->onOneServer()
             ->runInBackground();

    // Cập nhật giao dịch - chạy mỗi 2 giờ
    $schedule->command('transactions:update')
             ->everyTwoHours();

    // Cập nhật level CTV - chạy đầu tháng
    $schedule->command('user:update-ctv-level')
             ->monthlyOn(1, '05:00')
             ->withoutOverlapping()
             ->onOneServer()
             ->runInBackground();

    // Gia hạn job - chạy lúc 23:00
    $schedule->command('job:extend-expiration')
             ->dailyAt('23:00')
             ->withoutOverlapping()
             ->onOneServer()
             ->runInBackground();
}
```

### 7.2. Command: cv:expired

**Chức năng:** Tự động hết hạn các CV và job posting
**Tần suất:** Hàng ngày
**Logic:**

```php
public function handle() {
    // Hết hạn các job posting
    $expiredJobs = Job::where('expire_at', '<', now())
                     ->where('status', 1)
                     ->get();

    foreach ($expiredJobs as $job) {
        $job->update(['status' => 2]); // Expired

        // Thông báo cho employer
        $job->user->notify(new JobExpiredNotification($job));

        // Hoàn tiền các submit CV chưa được xử lý
        $this->refundPendingSubmissions($job);
    }

    // Hết hạn các CV selling
    $expiredCvSellings = WareHouseCvSelling::where('created_at', '<', now()->subDays(30))
                                          ->where('status', 0)
                                          ->get();

    foreach ($expiredCvSellings as $cvSelling) {
        $cvSelling->update(['status' => 1]); // Expired

        // Thông báo cho CTV
        $cvSelling->user->notify(new CVSellingExpiredNotification($cvSelling));
    }
}
```

### 7.3. Command: candidate:reject-job

**Chức năng:** Xử lý tự động từ chối ứng viên sau timeout
**Tần suất:** Mỗi phút
**Logic:**

```php
public function handle() {
    // Tìm các offer quá hạn 48h chưa được xác nhận
    $expiredOffers = WareHouseCvSellingBuy::where('status_recruitment', 1) // Waiting candidate confirm
                                         ->where('date_change_status', '<', now()->subHours(48))
                                         ->get();

    foreach ($expiredOffers as $offer) {
        // Dispatch job từ chối
        RejectRecruitment::dispatch($offer->id);
    }

    // Tìm các submit CV quá hạn
    $expiredSubmits = SubmitCv::where('status', 9) // Pending confirm
                              ->where('date_change_status', '<', now()->subHours(48))
                              ->get();

    foreach ($expiredSubmits as $submit) {
        RejectRecruitmentSubmit::dispatch($submit->id);
    }
}
```

### 7.4. Command: user:update-ctv-level

**Chức năng:** Cập nhật level CTV dựa trên doanh thu tháng trước
**Tần suất:** Đầu tháng (ngày 1 lúc 5:00)
**Logic:**

```php
public function handle() {
    $lastMonth = now()->subMonth();

    // Lấy tất cả CTV
    $ctvUsers = User::where('type', 'rec')->get();

    foreach ($ctvUsers as $ctv) {
        // Tính tổng doanh thu tháng trước
        $monthlyRevenue = PayinMonth::where('user_id', $ctv->id)
                                   ->whereMonth('created_at', $lastMonth->month)
                                   ->whereYear('created_at', $lastMonth->year)
                                   ->sum('amount');

        // Xác định level mới
        $newLevel = $this->calculateCtvLevel($monthlyRevenue);

        if ($ctv->level != $newLevel) {
            $ctv->update(['level' => $newLevel]);

            // Gửi thông báo
            $ctv->notify(new CtvLevelUpdatedNotification($newLevel, $monthlyRevenue));
        }
    }
}

private function calculateCtvLevel($revenue) {
    if ($revenue >= 5000) return 3; // VIP
    if ($revenue >= 2000) return 2; // Premium
    if ($revenue >= 500) return 1;  // Standard
    return 0; // Basic
}
```

### 7.5. Command: transactions:update

**Chức năng:** Cập nhật trạng thái giao dịch ZaloPay
**Tần suất:** Mỗi 2 giờ
**Logic:**

```php
public function handle() {
    // Lấy các giao dịch pending
    $pendingTransactions = ZalopayTransaction::where('status', 0)
                                           ->where('created_at', '>', now()->subDays(1))
                                           ->get();

    foreach ($pendingTransactions as $transaction) {
        // Gọi API ZaloPay để check status
        $status = $this->checkZaloPayStatus($transaction->app_trans_id);

        if ($status['return_code'] == 1) {
            // Giao dịch thành công
            $transaction->update([
                'status' => 1,
                'zp_trans_id' => $status['zp_trans_id']
            ]);

            // Xử lý nạp tiền vào ví
            $this->processTopUp($transaction);
        } elseif ($status['return_code'] == 2) {
            // Giao dịch thất bại
            $transaction->update(['status' => 2]);
        }
    }
}
```

---

## 8. Job Dependencies và Chains

### 8.1. Onboard Job Chain

```php
// Khi chuyển sang Trial Work
ChangeToTrailWork::dispatch($buyId)->delay(now()->addDays(7));

// Trong ChangeToTrailWork, schedule các job tiếp theo:
public function handle() {
    // ... xử lý logic ...

    $carbonDate = Carbon::parse($this->dateBook);

    // Chain 1: Payment jobs
    PayOnboard::dispatch($this->buyId, 15)->delay($carbonDate->copy()->addDays(30));
    PayOnboard::dispatch($this->buyId, 10)->delay($carbonDate->copy()->addDays(45));
    PayOnboard::dispatch($this->buyId, 75)->delay($carbonDate->copy()->addDays(67));

    // Chain 2: Status transition
    SuccessRecruitment::dispatch($this->buyId)->delay($carbonDate->copy()->addDays(67));

    // Chain 3: Reminder jobs
    for ($day = 55; $day <= 59; $day++) {
        RemindExpireTrailWork::dispatch($this->buyId)->delay($carbonDate->copy()->addDays($day));
    }
}
```

### 8.2. Interview Job Chain

```php
// Khi ứng viên xác nhận phỏng vấn
public function confirmInterview($buyId) {
    // ... xử lý thanh toán ...

    // Schedule payment job
    PayInterview::dispatch($buyId)->delay(now()->addHours(24));

    // Schedule timeout job
    OutOfDateBookInterview::dispatch($buyId)->delay(now()->addDays(7));
}
```

---

## 9. Error Handling trong Jobs

### 9.1. Job Retry Configuration

```php
class RecSumPoint implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3; // Retry 3 lần
    public $timeout = 120; // Timeout sau 2 phút
    public $backoff = [60, 120, 300]; // Backoff strategy

    public function failed(Exception $exception) {
        // Log lỗi
        Log::error('RecSumPoint job failed', [
            'warehouse_cv_selling_buy_id' => $this->wareHouseCvSellingBuyId,
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Gửi thông báo cho admin
        Mail::to(config('settings.global.email_admin'))
            ->send(new JobFailedNotification($this, $exception));
    }
}
```

### 9.2. Database Transaction trong Jobs

```php
public function handle() {
    DB::beginTransaction();

    try {
        // Xử lý logic
        $this->processPayment();
        $this->updateStatus();
        $this->sendNotifications();

        DB::commit();
    } catch (Exception $e) {
        DB::rollback();

        // Log error
        Log::error('Job transaction failed', [
            'job' => get_class($this),
            'error' => $e->getMessage()
        ]);

        throw $e; // Re-throw để trigger retry
    }
}
```

---

## 10. Monitoring và Performance

### 10.1. Job Logging

```php
// Custom job logging
class JobLogger {
    public static function logJobStart($jobClass, $payload) {
        JobLog::create([
            'name' => $jobClass,
            'payload' => json_encode($payload),
            'status' => 1, // Processing
            'created_at' => now()
        ]);
    }

    public static function logJobComplete($jobClass, $payload) {
        JobLog::where('name', $jobClass)
               ->where('payload', json_encode($payload))
               ->update([
                   'status' => 2, // Completed
                   'updated_at' => now()
               ]);
    }
}
```

### 10.2. Queue Monitoring

```php
// Command để monitor queue
class QueueMonitorCommand extends Command {
    public function handle() {
        $queueSize = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();

        if ($queueSize > 1000) {
            // Alert admin
            Mail::to(config('settings.global.email_admin'))
                ->send(new QueueOverloadAlert($queueSize));
        }

        if ($failedJobs > 50) {
            // Alert admin
            Mail::to(config('settings.global.email_admin'))
                ->send(new FailedJobsAlert($failedJobs));
        }
    }
}
```

---

## 11. Deployment và Configuration

### 11.1. Queue Worker Configuration

```bash
# Supervisor configuration
[program:recland-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/recland/artisan queue:work database --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/path/to/recland/storage/logs/worker.log
stopwaitsecs=3600
```

### 11.2. Environment Configuration

```env
# Queue Configuration
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database

# Job Configuration
JOB_TIMEOUT=300
JOB_MAX_TRIES=3
JOB_BACKOFF=60,120,300

# Scheduler Configuration
SCHEDULER_TIMEZONE=Asia/Ho_Chi_Minh
```

---

*Tài liệu hệ thống Jobs/Queue/Scheduler hoàn tất với đầy đủ chi tiết về tất cả jobs, scheduled commands, error handling và monitoring.*
