<?php

namespace App\Repositories;

use App\Models\Bonus;
use Carbon\Carbon;
use DB;

class BonusRepository extends BaseRepository
{
    const MODEL = Bonus::class;

    public function getBonusByUser($userId, $month, $year)
    {
        return $this->query()->where('user_id', $userId)->where('month', $month)->where('year', $year)->first();
    }

    public function getListBonus($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        $limit = 10;
        $query = $this->query();

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }
        if (isset($params['search'])) {
            $monthYear = explode('/', $params['search']);
            if (!empty($monthYear[0])) {
                $query->where('month', $monthYear[0]);
            }
            if (!empty($monthYear[1])) {
                $query->where('year', $monthYear[1]);
            }
        }

        $query->where('user_id', $params['user_id']);

        $query->orderBy('year', 'DESC');
        $query->orderBy('month', 'DESC');

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findById($id)
    {
        return $this->query()->where('id', $id)->firstOrFail();
    }

    public function findByIdPaymentStatus($userId)
    {
        return $this->query()->where('user_id', $userId)->where('payment_status', 0)->count();
    }

    public function getTotalTurnoverCurrentMonthByUserId($userId)
    {
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;
        $result = DB::table('bonus')
            ->selectRaw('SUM(money) as money, SUM(money_kpi) as money_kpi, SUM(price) as price')
            ->where('user_id', $userId)
            ->where('month', $month)
            ->where('year', $year)
            ->first();
        return (int)$result->money + (int)$result->money_kpi + (int)$result->price;
    }

    public function getTotalTurnoverCurrentYearByUserId($userId)
    {
        $now = Carbon::now();
        $year = $now->year;
        $result = DB::table('bonus')
            ->selectRaw('SUM(money) as money, SUM(money_kpi) as money_kpi, SUM(price) as price')
            ->where('user_id', $userId)
            ->where('year', $year)
            ->first();
        return (int)$result->money + (int)$result->money_kpi + (int)$result->price;
    }

    public function getAllWithUserId($userId)
    {
        return $this->query()->where('user_id', $userId)->get()->toArray();
    }

    public function getYearWithUserId($userId)
    {
        return $this->query()->select('year')->where('user_id', $userId)->groupBy('year')->pluck('year')->toArray();
    }
}
