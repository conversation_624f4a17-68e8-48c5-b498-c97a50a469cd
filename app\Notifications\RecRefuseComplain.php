<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class RecRefuseComplain extends Mailable
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $cvSellingBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$cvSellingBuy)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->cvSellingBuy = $cvSellingBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $candidateName = '';
        $position = '';
        $type = '';
        $content = $this->cvSellingBuy->txt_complain;
        $image = $this->cvSellingBuy->img_complain;
        $companyName = $this->employer->name;

        if (!empty($this->cvSellingBuy->wareHouseCvSelling)){
            $type = $this->cvSellingBuy->wareHouseCvSelling->type_of_sale;
        }
        if (!empty($this->cvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->cvSellingBuy->job)){
                $position = $this->cvSellingBuy->job->name;
            }
        }
        $recName = $this->cvSellingBuy->rec->name;
        $url = route('luot-ban.show',['luot_ban' => $this->cvSellingBuy->id,'tab' => 'khieunai']);

        return new Content(
            view: 'email.ctv_tuchoi',
            with: [
                'name' => $this->rec->name,
                'recName' => $recName,
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type,
                'content' => $content,
                'recId' => $this->rec->id,
                'image' => gen_url_file_s3($image),
                'url' => $url,
                'buyId' => $this->cvSellingBuy->id,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $message->subject('[Recland][Case #'.$this->cvSellingBuy->id.'] Thông báo khiếu nại cần xử lý');
        return $this;
    }

}
