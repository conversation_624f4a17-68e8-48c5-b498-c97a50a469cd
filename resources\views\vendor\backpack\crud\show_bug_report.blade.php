@extends('admin.layouts.app_backpack')

@section('header')
<section class="container-fluid">
    <h2>
        <span class="text-capitalize">Chi tiết báo cáo lỗi #{{ $entry->id }}</span>
        <small>{{ $crud->getSubheading() ?? '' }}</small>
    </h2>
</section>
@endsection

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="la la-info-circle"></i> Thông tin báo cáo
                </h3>
                <div class="card-tools">
                    <a href="{{ url($crud->route) }}" class="btn btn-sm btn-secondary">
                        <i class="la la-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>ID:</strong></div>
                    <div class="col-sm-9">#{{ $entry->id }}</div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Người báo cáo:</strong></div>
                    <div class="col-sm-9">
                        @if($entry->user)
                        {{ $entry->user->name }} ({{ $entry->user->email }})
                        @else
                        <span class="text-muted">Người dùng không tồn tại</span>
                        @endif
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3"><strong>URL trang lỗi:</strong></div>
                    <div class="col-sm-9">
                        <a href="{{ $entry->url }}" target="_blank" class="text-break">
                            {{ $entry->url }}
                            <i class="la la-external-link-alt ml-1"></i>
                        </a>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Thời gian báo cáo:</strong></div>
                    <div class="col-sm-9">{{ $entry->created_at->format('d/m/Y H:i:s') }}</div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Trạng thái:</strong></div>
                    <div class="col-sm-9" id="statusBadge">
                        <span class="badge {{ $entry->status === 'pending' ? 'badge-warning' : 'badge-success' }}">
                            {{ $entry->status === 'pending' ? 'Chờ xử lý' : 'Đã xử lý' }}
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Mô tả lỗi:</strong></div>
                    <div class="col-sm-9">
                        <div class="border p-3 bg-light rounded">
                            {!! nl2br(e($entry->description)) !!}
                        </div>
                    </div>
                </div>

                @if($entry->image_path)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Ảnh minh họa:</strong></div>
                    <div class="col-sm-9">
                        <div class="image-container">
                            <img src="{{ gen_url_file_s3($entry->image_path) }}" alt="Bug Report Image"
                                class="img-thumbnail" style="max-width: 100%; max-height: 400px; cursor: pointer;"
                                onclick="showImageModal('{{ gen_url_file_s3($entry->image_path) }}')">
                            <br>
                            <small class="text-muted">Click vào ảnh để xem kích thước đầy đủ</small>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="la la-cogs"></i> Thao tác
                </h3>
            </div>
            <div class="card-body">
                <form id="statusForm">
                    @csrf
                    <div class="form-group">
                        <label for="status">Cập nhật trạng thái:</label>
                        <select id="status" class="form-control">
                            <option value="pending" {{ $entry->status === 'pending' ? 'selected' : '' }}>
                                Chờ xử lý
                            </option>
                            <option value="resolved" {{ $entry->status === 'resolved' ? 'selected' : '' }}>
                                Đã xử lý
                            </option>
                        </select>
                    </div>

                    <button type="button" class="btn btn-primary btn-block" onclick="updateStatus()">
                        <i class="la la-save"></i> Cập nhật trạng thái
                    </button>
                </form>

                <hr>

                <div class="text-muted">
                    <small>
                        <strong>Lần cập nhật cuối:</strong><br>
                        <span id="lastUpdated">{{ $entry->updated_at->format('d/m/Y H:i:s') }}</span>
                    </small>
                </div>

                <hr>

                {{-- <a href="{{ url($crud->route . '/' . $entry->id . '/edit') }}" class="btn btn-warning btn-block">
                    <i class="la la-edit"></i> Chỉnh sửa
                </a> --}}
            </div>
        </div>
    </div>
</div>

{{-- Image Modal --}}
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ảnh minh họa</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Bug Report Image" class="img-fluid">
            </div>
        </div>
    </div>
</div>
@endsection

@section('after_styles')
<style>
    .text-break {
        word-break: break-all;
    }

    .image-container img {
        transition: transform 0.2s;
    }

    .image-container img:hover {
        transform: scale(1.05);
    }

    #imageModal .modal-body {
        padding: 0;
    }

    #imageModal img {
        max-width: 100%;
        height: auto;
    }
</style>
@endsection

@section('after_scripts')
<script>
    function updateStatus() {
    const status = $('#status').val();
    const bugReportId = {{ $entry->id }};
    
    $.ajax({
        url: `{{ backpack_url('bug-reports') }}/${bugReportId}/status`,
        type: 'PATCH',
        data: {
            status: status,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                new Noty({
                    type: "success",
                    text: response.message
                }).show();
                
                // Update status badge
                const badge = status === 'pending' ? 
                    '<span class="badge badge-warning">Chờ xử lý</span>' :
                    '<span class="badge badge-success">Đã xử lý</span>';
                
                $('#statusBadge').html(badge);
                
                // Update last updated time
                $('#lastUpdated').text(moment().format('DD/MM/YYYY HH:mm:ss'));
            }
        },
        error: function(xhr) {
            new Noty({
                type: "error",
                text: 'Có lỗi xảy ra khi cập nhật trạng thái.'
            }).show();
        }
    });
}

function showImageModal(src) {
    $('#modalImage').attr('src', src);
    $('#imageModal').modal('show');
}
</script>
@endsection