<?php

namespace App\Rules\Admin;

use App\Repositories\UserRepository;
use Illuminate\Contracts\Validation\Rule;

class CheckPhoneRule implements Rule
{
    protected $id;
    protected $type;
    protected $isExists;
    protected $message;
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($type = null, $id = null, $isExists = 0)
    {
        //
        $this->type = $type;
        $this->id = $id;
        $this->isExists = $isExists;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
//        dd(0);
        $userRepository = resolve(UserRepository::class);
        if (!$this->id) {
            $user = $userRepository->findPhone($this->type, $value);
        } else {
            $user = $userRepository->findPhone($this->type, $value, $this->id);
        }
        if ($this->isExists) {
            if ($user) {
                return true;
            }
            return false;
        } else {
            if (!$user) {
                return true;
            } else {
                if ($user->provider) {
                    $this->message = __('message.phone_exists_social', ['provider' => ucfirst($user->provider)]);
                }
                return false;
            }
        }
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        if ($this->isExists) {
            return __('message.phone_not_exists');
        } else {
            if ($this->message) {
                return $this->message;
            }
            return __('message.phone_exists');
        }
    }
}
