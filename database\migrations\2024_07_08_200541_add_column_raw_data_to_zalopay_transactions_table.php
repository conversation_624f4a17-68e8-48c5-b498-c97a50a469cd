<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('zalopay_transactions', function (Blueprint $table) {
            $table->text('raw_data')->after('status')->nullable();
            $table->unique('app_trans_id');
        });
        // create index column app_trans_id
            
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('zalopay_transactions', function (Blueprint $table) {
            $table->dropIndex('raw_data');
            $table->dropColumn('raw_data');
        });
    }
};
