<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentTrialWork extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $number;
    protected $price;
    protected $percent;

    public function __construct($wareHouseCvSellingBuy,$number,$price,$percent)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->number = $number;
        $this->price = $price;
        $this->percent = $percent;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $url = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id]);
        $recTurnovers = route('rec-turnovers');

        $subject = '[Recland] Thanh toán đợt '.$this->number.' ứng viên Onboard - Marketplace';
        if ($this->number == 3){
            $subject = '[Recland] Hoàn tất thanh toán ứng viên Onboard - Marketplace';
        }

        return (new MailMessage)
            ->view('email.paymentTrialWork', [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'url' => $url,
                'recTurnovers' => $recTurnovers,
                'number' => $this->number,
                'price' => $this->price,
                'percent' => $this->percent,
                'totalPrice' => $this->wareHouseCvSellingBuy->price,
            ])
            ->subject($subject);

    }




}
