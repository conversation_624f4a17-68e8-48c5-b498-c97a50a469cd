<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\EmployerEditRoleRequest;
use App\Http\Requests\Frontend\EmployerRoleRequest;
use App\Http\Requests\Frontend\WithdrawRequest;
use App\Http\Resources\SubmitCv;
use App\Http\Resources\SubmitCvResource;
use App\Notifications\InviteUser;
use App\Services\Admin\CompanyService;
use App\Services\Admin\SkillService;
use App\Services\Frontend\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Frontend\BannerService;
use App\Services\Frontend\JobService;
use App\Services\Frontend\PayoutLogService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\UserService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use App\Services\Frontend\WareHouseCvSellingService;
use App\Services\Frontend\WareHouseSubmitCvService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use App\Notifications\CvRejectedNotification;

class AjaxController extends Controller
{

    protected $jobService;
    protected $settingService;
    protected $bannerService;
    protected $wareHouseSubmitCvService;
    protected $seoService;
    protected $skillService;
    protected $companyService;
    protected $userService;
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingBuyService;
    protected $payoutLogService;
    protected $submitCvService;

    protected $routeName;

    public function __construct(
        JobService               $jobService,
        SettingService           $settingService,
        BannerService            $bannerService,
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        SeoService               $seoService,
        SkillService             $skillService,
        CompanyService           $companyService,
        UserService              $userService,
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvSellingBuyService    $wareHouseCvSellingBuyService,
        PayoutLogService $payoutLogService,
        SubmitCvService $submitCvService,
    ) {
        $this->jobService = $jobService;
        $this->settingService = $settingService;
        $this->bannerService = $bannerService;
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->seoService = $seoService;
        $this->skillService = $skillService;
        $this->companyService = $companyService;
        $this->userService = $userService;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingBuyService = $wareHouseCvSellingBuyService;
        $this->payoutLogService = $payoutLogService;
        $this->submitCvService = $submitCvService;

        $this->routeName = \Route::currentRouteName();
    }

    //click vào job dùng ajax load ra detail job
    public function ajaxDetailJob($slug)
    {
        $job = $this->jobService->findBySlug($slug);
        $totalJobInCompany = $this->jobService->getTotalJob($job);

        $this->generateParams();
        $view = view('frontend.pages.job.detail', compact('job', 'totalJobInCompany'));
        $view = $view->render();
        return $view;
    }

    public function ajaxListJob(Request $request)
    {
        $params = $request->all();
        $params['status'] = config('constant.active');
        $params['is_active'] = config('constant.active');
        $data = $this->jobService->getJobAjax($params);

        $response = [];
        if (count($data)) {
            foreach ($data as $key => $value) {
                $response[$key]['id'] = $value->id;
                $response[$key]['text'] = $value->name . ' - ' . $value->company->name;
            }
        }

        return json_encode($response);
    }

    public function getJob(Request $request)
    {
        $job = $this->jobService->findById($request->id);
        return json_encode($job);
    }

    public function ajaxListSkill(Request $request)
    {
        $request = $request->all();
        $search = [
            'size' => config('constant.limit_search_ajax_selectbox'),
        ];
        if (isset($request['searchTerm']) && $request['searchTerm'] != '') $search['search'] = $request['searchTerm'];
        $companies = $this->skillService->indexService($search, [], true);
        $response = [];
        if ($companies) {
            foreach ($companies as $key => $value) {
                $response[$key]['id'] = $value->name;
                $response[$key]['text'] = $value->name;
            }
        }

        return json_encode($response);
    }

    public function ajaxListCompany(Request $request)
    {
        $request = $request->all();
        $search = [
            'size' => config('constant.limit_search_ajax_selectbox'),
        ];
        if (isset($request['searchTerm']) && $request['searchTerm'] != '') $search['search'] = $request['searchTerm'];
        $companies = $this->companyService->indexService($search, [], true, 'id', 'desc', ['id', 'name']);
        $response = [];
        if ($companies) {
            foreach ($companies as $key => $value) {
                $response[$key]['id'] = $value->id;
                $response[$key]['text'] = $value->name;
            }
        }

        return json_encode($response);
    }

    public function ajaxDetailCompany($id)
    {
        $data = $this->companyService->detail($id);
        return $data;
    }

    public function ajaxUpdateSubmitcv($id)
    {
        $client = auth('client')->user();
        $checkSubmitCv = $this->wareHouseSubmitCvService->updateStatusCvById($id, $client->id);
        if ($checkSubmitCv) {
            //warehouseCv
            Toast::success(__('frontend/collaborator/message.agree_cv_success', ['ungvien' => $checkSubmitCv->warehouseCv->candidate_name]));
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_fail'));
        }

        return true;
    }

    public function ajaxUpdateAllNoti()
    {
        $user = auth('client')->user();
        $user->unreadNotifications->markAsRead();

        return true;
    }

    public function ajaxCheckEmailCandidate(Request $request)
    {
        $data = $this->wareHouseSubmitCvService->findCvWithEmail($request->email);

        if ($data) {
            $response = [
                'success' => true,
                'status' => config('constant.submit_cvs_status.' . $data->status),
                'email' => $request->email
            ];
            //            echo "true";
        } else {
            $response = [
                'success' => false,
            ];
            //            echo "false";
        }
        return $response;
    }

    public function ajaxCheckCandidate(Request $request)
    {
        $data = $this->wareHouseSubmitCvService->findCv($request->cv);

        if ($data) {
            $response = [
                'success' => true,
                'status' => config('constant.submit_cvs_status.' . $data->status),
                'email' => $data->warehouseCv->candidate_email
            ];
            //            echo "true";
        } else {
            $response = [
                'success' => false,
            ];
            //            echo "false";
        }
        return $response;
    }

    public function ajaxCheckDuplicateSubmitByCv(Request $request)
    {
        if (!isset($request->job_id)) {
            $job = $this->jobService->findBySlug($request->slug);
            $jobId = $job->id;
        } else {
            $jobId = $request->job_id;
        }

        $check = $this->wareHouseSubmitCvService->checkDuplicateCvCompanyJobById($request->cv, $jobId);
        $response = [
            'success' => $check,
        ];
        return $response;
    }

    public function ajaxCheckDuplicateSubmitByNewInfo(Request $request)
    {
        if (!isset($request->job_id)) {
            $job = $this->jobService->findBySlug($request->slug);
            $jobId = $job->id;
        } else {
            $jobId = $request->job_id;
        }
        $check = $this->wareHouseSubmitCvService->checkDuplicateCvForCompany($request->email, $request->phone, $jobId);
        $response = [
            'success' => $check,
        ];
        return $response;
    }

    public function ajaxSubmitCvCountSearch(Request $request)
    {
        return $this->wareHouseSubmitCvService->countSubmitCvSearch($request->all());
    }

    public function ajaxWareHouseCountSearch(Request $request)
    {
        return $this->wareHouseSubmitCvService->countWareHouseCvSearch($request->all());
    }

    public function ajaxChangeRole($id, Request $request)
    {
        $result = $this->userService->changeRole($id, $request->all());

        if ($result) {
            Toast::success(__('message.change_user_success'));
        } else {
            Toast::warning(__('message.update_fail'));
        }

        return true;
    }

    public function ajaxChangeActive($id, Request $request)
    {
        $result = $this->userService->changeActive($id, $request->all());

        if ($result) {
            Toast::success(__('message.change_user_success'));
        } else {
            Toast::warning(__('message.update_fail'));
        }

        return true;
    }

    public function ajaxDeleteRole($id)
    {
        $result = $this->userService->deleteRole($id);

        if ($result) {
            Toast::success(__('message.delete_success'));
        } else {
            Toast::warning(__('message.delete_fail'));
        }

        return $result;
    }

    public function ajaxAddRole(EmployerRoleRequest $request)
    {
        $result = $this->userService->addRole($request->all());

        if ($result) {
            Toast::success(__('message.add_success'));
        } else {
            Toast::warning(__('message.add_fail'));
        }

        return $result;
    }

    public function ajaxGetRole($id)
    {
        $result = $this->userService->getRole($id);

        return $result;
    }

    public function ajaxUpdateRole($id, EmployerEditRoleRequest $request)
    {
        $result = $this->userService->updateRole($id, $request->all());

        if ($result) {
            Toast::success(__('message.change_user_success'));
        } else {
            Toast::warning(__('message.update_fail'));
        }

        return $result;
    }

    public function ajaxCheckEmailUserType(Request $request)
    {
        $result = $this->userService->checkEmailType($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }
        return $response;
    }

    public function ajaxResendMail(Request $request)
    {
        $result = $this->userService->resendMailInvite($request->all());

        /*if ($result) {
            Toast::success(__('message.change_user_success'));
        } else {
            Toast::warning(__('message.update_fail'));
        }*/

        return $result;
    }

    public function ajaxValidateCv(Request $request)
    {
        $data = $this->wareHouseSubmitCvService->findCv($request->cv);
        $response = [
            'success' => false,
        ];
        if ($data) {

            $response = [
                'success' => true,
                'status' => config('constant.submit_cvs_status.' . $data->status),
                'email' => $data->warehouseCv->candidate_email,
                'message' => __(
                    'frontend/validation.check_candidate_email',
                    [
                        'user' => $data->warehouseCv->candidate_email,
                        'status' => config('constant.submit_cvs_status.' . $data->status),
                    ]
                )
            ];
            return response()->json($response);
        }
        $jobId = $request->job_id;
        $check = $this->wareHouseSubmitCvService->checkDuplicateCvCompanyJobById($request->cv, $jobId);

        if ($check) {
            $response = [
                'success' => $check,
                'message' => __('frontend/job/message.error_duplicate_job')
            ];
            return response()->json($response);
        }


        return response()->json($response);
    }

    public function checkValidateCvEmail(Request $request)
    {
        $response = [
            'success' => false,
        ];
        $jobId = $request->job_id;
        $check = $this->wareHouseSubmitCvService->checkDuplicateCvForCompany($request->email, $request->phone, $jobId);
        if ($check) {
            $response = [
                'success' => $check,
                'message' => __('frontend/job/message.error_duplicate_job')
            ];
            return response()->json($response);
        }
        $data = $this->wareHouseSubmitCvService->findCvWithEmail($request->email);
        // dd($data);
        if ($data) {
            $response = [
                'success' => true,
                'status' => config('constant.submit_cvs_status.' . $data->status),
                'email' => $data->warehouseCv->candidate_email,
                'message' => __(
                    'frontend/validation.check_candidate_email',
                    [
                        'user' => $data->warehouseCv->candidate_email,
                        'status' => config('constant.submit_cvs_status.' . $data->status),
                    ]
                )
            ];
            return response()->json($response);
        }
        return $response;
    }

    public function getSkillMainIT(Request $request)
    {
        $lang = app()->getLocale();
        $data = $this->wareHouseCvSellingService->getSkillMainIT($request->all());
        return response()->json($data);
    }

    public function getLevel(Request $request)
    {
        $lang = app()->getLocale();
        $data = $this->wareHouseCvSellingService->getLevel($request->all());
        return response()->json($data);
    }

    public function getLevelByCareer(Request $request)
    {
        $lang = app()->getLocale();
        $data = $this->wareHouseCvSellingService->getLevelByCareer($request->all());
        return response()->json($data);
    }

    public function getPrice(Request $request)
    {
        $lang = app()->getLocale();
        $data = $this->wareHouseCvSellingService->getPrice($request->all());
        return response()->json($data);
    }


    public function getMinSubmitPrice(Request $request)
    {
        $lang = app()->getLocale();
        $data = $this->submitCvService->getMinSubmitPrice($request->all());
        return response()->json(['success' => 1, 'data' => ['min_price' => $data, 'min_price_str' => number_format($data, 0, ',', '.')]]);
    }

    public function checkCvSellingWithType(Request $request)
    {
        $result = $this->wareHouseCvSellingService->checkCvWithType($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }
        return $response;
    }

    public function getDetailCvSelling(Request $request, $id)
    {
        $user = auth('client')->user();
        $view_token = $request->view_token;
        if ($user && $user->type == config('constant.role.employer')) {
            $cvDetailSelling = $this->wareHouseCvSellingService->getDetailCvSelling($request->id);
        } elseif ($view_token) {
            $cvDetailSelling = $this->wareHouseCvSellingService->getDetailCvSelling($id);
            if ($cvDetailSelling->view_token == $view_token) {
                $cvDetailSelling = $this->wareHouseCvSellingService->getDetailCvSelling($id);
            } else {
                $cvDetailSelling = null;
            }
        } else {
            $cvDetailSelling = null;
        }
        if (!$cvDetailSelling) {
            abort(403, 'Bạn không có quyền truy cập');
        }

        $cvSeeMore = [];
        $cvAnother = [];
        if ($cvDetailSelling && false) {
            $cvSeeMore = $this->wareHouseCvSellingService->getCvSeeMore($cvDetailSelling->wareHouseCv->candidate_email, $cvDetailSelling->wareHouseCv->candidate_mobile, $id);
            $cvAnother = $this->wareHouseCvSellingService->getCvAnother($cvDetailSelling->skill, $cvDetailSelling->type_of_sale, $id);
        }


        $view = view('frontend.pages.market.detail', compact('cvDetailSelling', 'cvSeeMore', 'cvAnother', 'view_token'));
        $view = $view->render();
        return $view;
    }

    public function getDataDetailCvSellingBuy($id)
    {
        $cvSellingBuy = $this->wareHouseCvSellingBuyService->find($id);
        if ($cvSellingBuy->user_id != auth('client')->id()) {
            return response()->json(['error' => 'Bạn không có quyền truy cập'], 403);
        }
        $cvSellingBuy->load('wareHouseCvSelling.wareHouseCv');
        $cvSellingBuy = $cvSellingBuy->toArray();
        $historyStatus = $this->wareHouseCvSellingBuyService->getLastHistoryByStatus($id, 3);
        $cvSellingBuy['history_waiting_setup_interview'] = $historyStatus;
        unset($cvSellingBuy['token']);
        if (isset($cvSellingBuy['ware_house_cv_selling']['user'])) {
            unset($cvSellingBuy['ware_house_cv_selling']['user']['token']);
            unset($cvSellingBuy['ware_house_cv_selling']['user']['email']);
        }
        try {
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['cv_public']);
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv_selling_buy_ntd']['token']);
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['name_cv_public']);
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['url_cv_public']);
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['candidate_email']);
            unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['candidate_mobile']);
            unset($cvSellingBuy['ware_house_cv_selling']['candidate_email']);
            unset($cvSellingBuy['ware_house_cv_selling']['candidate_mobile']);
        } catch (\Throwable $th) {
            //throw $th;
        }
        return response()->json($cvSellingBuy);
    }

    public function getDataDetailSubmitCv($id)
    {
        $submitCv = $this->submitCvService->find($id);
        if ($submitCv->company_id != auth('client')->user()->company_id) {
            return response()->json(['error' => 'Bạn không có quyền truy cập'], 403);
        }
        // $submitCv->load('warehouseCv');
        // $submitCv = $submitCv->toArray();
        // $historyStatus = $this->wareHouseCvSellingBuyService->getLastHistoryByStatus($id,3);
        // $cvSellingBuy['history_waiting_setup_interview'] = $historyStatus;
        // unset($cvSellingBuy['token']);
        // if (isset($cvSellingBuy['ware_house_cv_selling']['user'])) {
        // unset($cvSellingBuy['ware_house_cv_selling']['user']['token']);
        // unset($cvSellingBuy['ware_house_cv_selling']['user']['email']);
        // }
        try {
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['cv_public']);
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv_selling_buy_ntd']['token']);
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['name_cv_public']);
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['url_cv_public']);
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['candidate_email']);
            // unset($cvSellingBuy['ware_house_cv_selling']['ware_house_cv']['candidate_mobile']);
            // unset($cvSellingBuy['ware_house_cv_selling']['candidate_email']);
            // unset($cvSellingBuy['ware_house_cv_selling']['candidate_mobile']);
        } catch (\Throwable $th) {
            //throw $th;
        }
        return response()->json(new SubmitCvResource($submitCv));
    }

    public function ajaxRecruiterRejectCv($id)
    {
        $user = auth('client')->user();
        $submitCv = $this->wareHouseSubmitCvService->find($id);
        if ($submitCv->company_id != $user->company_id) {
            return response()->json(['error' => 'Bạn không có quyền truy cập'], 403);
        }

        $assessment = request('assessment');
        // $submitCv->warehouseCv->user->notify(new CvRejectedNotification($submitCv, $assessment));
        // dd($submitCv);
        $result = $this->wareHouseSubmitCvService->recruiterRejectCv($id, $assessment);

        if ($result) {
            // Send email notification
            $submitCv->warehouseCv->user->notify(new CvRejectedNotification($submitCv, $assessment));

            Toast::success('Loại CV thành công');
            return response()->json(['success' => 1]);
        } else {
            Toast::warning('Lỗi loại CV');
        }

        return response()->json(['error' => 'Lỗi loại CV']);
    }


    public function buyCV(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->buyCV($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function paymentSubmitCv(Request $request)
    {
        $submitCvId = $request->id;
        $result = $this->wareHouseSubmitCvService->paymentSubmitCv($submitCvId);

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function evaluateCV(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->evaluateCV($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function getEvaluateCV($idCvSelling, Request $request)
    {
        $evaluates = $this->wareHouseCvSellingBuyService->getEvaluateCvSelling($idCvSelling, $request->all());

        $view = view('frontend.pages.market.detail-evaluate', compact('evaluates'));
        $view = $view->render();
        return $view;
    }

    public function getFaqCV($idCvSelling, Request $request)
    {
        $faqs = $this->wareHouseCvSellingBuyService->getFaqCvSelling($idCvSelling, $request->all());

        $view = view('frontend.pages.market.detail-faq', compact('faqs'));
        $view = $view->render();
        return $view;
    }

    public function faqCV(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->faqCV($request->all());

        if ($result) {
            $response = [
                'success' => true,
                'data'    => $result
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function saveCV(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->saveCV($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function unSaveCV(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->unSaveCV($request->all());

        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }

    public function checkCvBeforeBuy($id)
    {
        $data = $this->wareHouseCvSellingService->getDataBeforeBy($id);

        $priceUser = auth('client')->user()->wallet->amount;

        $view = view('frontend.pages.market.before-buy-cv', compact('data', 'priceUser'));
        $view = $view->render();
        return $view;
    }
    public function getSubmitPricePopup($id)
    {
        $data = $this->submitCvService->find($id);
        $priceUser = auth('client')->user()->wallet->amount;
        $view = view('frontend.pages.employer.modal.submitcv-show-price', compact('data', 'priceUser'));
        $view = $view->render();
        return $view;
    }

    public function checkJobBeforeBuy($id = 0)
    {
        $data = $this->wareHouseCvSellingService->getJobDataBeforeBy();
        $url_back = '';
        if ($id) {
            $url_back = route('market-cv', ['show_cv_buy' => $id]);
        }
        $view = view('frontend.pages.market.check-job-before-buy-cv', compact('data', 'url_back'));
        $view = $view->render();
        return $view;
    }

    /**
     * @param Request $request
     * Ứng viên đồng ý hoặc từ chối khi NTD mua CV
     */
    public function verifyEmailCandidate(Request $request)
    {
        $token = $request->token;
        $type = $request->type;
        if (!$token || (!$type || !in_array($type, [1, 2]))) {
            return redirect()->route('home');
        }

        $result = $this->wareHouseCvSellingBuyService->verifyEmailCandidate($token, $type);

        if (!$result) {
            return redirect()->route('home');
        }

        if ($type == 1) {
            return view('frontend.pages.other.approve');
        } else {
            return view('frontend.pages.other.reject');
        }
    }
    public function verifyEmailCandidateSubmitCv(Request $request)
    {
        $token = $request->token;
        $type = $request->type;
        if (!$token || (!$type || !in_array($type, [1, 2]))) {
            return redirect()->route('home');
        }

        $result = $this->submitCvService->verifyEmailCandidate($token, $type);

        if (!$result) {
            return redirect()->route('home');
        }

        if ($type == 1) {
            return view('frontend.pages.other.approve');
        } else {
            return view('frontend.pages.other.reject');
        }
    }
}