<?php

namespace App\Jobs;

use App\Notifications\PaymentInterviewToRec;
use App\Notifications\PaymentOpenTurnCv;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\PayinRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
/*
 * sau 7 ngày ko co khieu nai thi thanh toan cho CTV
 */

class RecSumPoint implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    /**
     * @return void
     */
    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        if (!empty($wareHouseCvSellingBuy)) {
            //18 => 'Buy CV data successfull',
            // danh cho type_of_sale = cv && status_complain = 0 khong co khieu nại
            if ($wareHouseCvSellingBuy->status_recruitment == config('constant.status_recruitment_revert.BuyCVdatasuccessfull') && ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
                try {
                    //Cộng tiền trả CTV
                    //1. payins
                    $bonusRepository = resolve(BonusRepository::class);
                    $payinMonthRepository = resolve(PayinMonthRepository::class);
                    $now = Carbon::now();
                    $year = $now->year;
                    $month = $now->month;
                    $bonusCheck = $bonusRepository->getBonusByUser($wareHouseCvSellingBuy->rec->id, $month, $year);
                    //nếu ủy quyền thì thanh toán 20% price
                    $pricePriority = $wareHouseCvSellingBuy->price;
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) $pricePriority = (int) round(($wareHouseCvSellingBuy->price * 20) / 100);

                    if (!$bonusCheck) {
                        //nếu tháng/năm chưa có thì insert mới
                        $bonusRepository->create(
                            [
                                'user_id'   => $wareHouseCvSellingBuy->rec->id,
                                'year'      => $year,
                                'month'     => $month,
                                'price'     => $pricePriority,
                            ]
                        );
                    } else {
                        //nếu có doanh thu từ trước thì cộng dồn
                        $bonusCheck->price += $pricePriority;
                        $bonusCheck->save();
                    }
                    //từng giao dịch trong tháng
                    $payinMonthRepository->create(
                        [
                            'user_id'                       => $wareHouseCvSellingBuy->rec->id,
                            'warehouse_cv_selling_buy_id'   => $wareHouseCvSellingBuy->id,
                            'year'                          => $year,
                            'month'                         => $month,
                            'price'                         => $pricePriority,
                            'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                        ]
                    );
                    //2. wallets
                    // $wareHouseCvSellingBuy->rec->wallet->price  = $wareHouseCvSellingBuy->rec->wallet->price + $pricePriority;
                    $wareHouseCvSellingBuy->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->rec->wallet->addPrice($pricePriority, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'rec_sum_point');
                    // $wareHouseCvSellingBuy->rec->wallet->save();
                    //3. update status_complain =4 để không hoàn tiền lần nữa
                    if ($wareHouseCvSellingBuy->status_complain) {
                        $wareHouseCvSellingBuy->save();
                    }
                    $wareHouseCvSellingBuy->rec->notify(new PaymentOpenTurnCv($wareHouseCvSellingBuy, $pricePriority));
                } catch (\Exception $e) {
                    Log::info('RecSumPoint error log: ', [
                        'RecSumPoint: ' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}
