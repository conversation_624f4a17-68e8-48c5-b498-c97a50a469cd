<?php

namespace App\Repositories;

use App\Models\SkillMain;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SkillMainRepository extends BaseRepository
{
    const MODEL = SkillMain::class;

    public function getSkill($params)
    {
        $query = $this->query();

        if (isset($params['searchTerm'])) {
            $query->where('name_en', 'like', '%' . $params['searchTerm'] . '%')
                ->orWhere('name_vi', 'like', '%' . $params['searchTerm'] . '%');
        }

        return $query->get();
    }
}
