<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DepositToEmployer extends Notification implements ShouldQueue
{

    use Queueable;

    protected $deposit;
    protected $employer;

    public function __construct($deposit,$employer)
    {
        $this->deposit = $deposit;
        $this->employer = $employer;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->view('email.depositToEmployer', [
                'id' => $this->deposit->id,
                'employerName' => $this->employer->name,
                'link' => route('employer-wallet')
            ])
            ->subject('[RECLAND] Thanh toán thành công đơn hàng số '.$this->deposit->id);
    }


}
