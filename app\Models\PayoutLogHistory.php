<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class PayoutLogHistory extends BaseModel
{
    use HasFactory, Notifiable;
    protected $appends = ['created_at_value'];

    protected $table = 'payout_log_histories';
    public $timestamps = true;
    protected $guarded = ['id'];

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }
}
