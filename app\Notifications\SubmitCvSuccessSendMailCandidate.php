<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class SubmitCvSuccessSendMailCandidate extends Mailable
{
    use Queueable;

    protected $submitCv;
    protected $job;

    public function __construct($submitCv, $job)
    {
        $this->submitCv = $submitCv;
        $this->job = $job;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $nameCTV = $this->submitCv->rec->name; //name ctv
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->company->name;
        $jobName = $this->job->name;
        // $link = $this->job->path_file_jd;
        $link = route('download.pdf', ['slug' => $this->job->slug]);
        $linkAccept = route('verify-email-candidate-submit-cv') . '?token=' . $this->submitCv->confirm_token . '&type=1';
        $linkReject = route('verify-email-candidate-submit-cv') . '?token=' . $this->submitCv->confirm_token . '&type=2';

        return new Content(
            view: 'email.mua_cv_thanhcong_ungvien',
            with: [
                'candidateName' => $candidateName,
                'nameCTV'       => $nameCTV,
                'position'      => $position,
                'companyName'   => $companyName,
                'link'          => $link,
                'linkAccept'    => $linkAccept,
                'linkReject'    => $linkReject,
                'jobName'       => $jobName,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->company->name;
        $message->subject('[Recland] Xác nhận ứng tuyển vị trí ' . $position . ' tại công ty ' . $companyName);
        return $this;
    }
}
