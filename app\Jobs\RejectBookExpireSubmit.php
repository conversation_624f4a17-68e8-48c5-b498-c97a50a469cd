<?php

namespace App\Jobs;

use App\Models\SubmitCv;
use App\Models\SubmitCvBook;
use App\Models\SubmitCvHistoryPayment;
use App\Models\SubmitCvHistoryStatus;
use App\Models\WareHouseCvSellingBuyBook;
use App\Notifications\ChangeStatusCancelInterviewToAdmin;
use App\Notifications\ChangeStatusCancelInterviewToAdminSubmit;
use App\Notifications\ChangeStatusCancelInterviewToEmployer;
use App\Notifications\ChangeStatusCancelInterviewToEmployerSubmit;
use App\Notifications\ChangeStatusCancelInterviewToRec;
use App\Notifications\ChangeStatusCancelInterviewToRecSubmit;
use App\Notifications\EmailRejectInterView;
use App\Notifications\EmailRejectInterViewSubmit;
use App\Repositories\SubmitCvBookRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class RejectBookExpireSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $submitCvBook;
    public $user;

    public function __construct(SubmitCvBook $submitCvBook, $user)
    {
        $this->submitCvBook = $submitCvBook;
        $this->user = $user;
    }


    public function handle()
    {
        //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
        if ($this->submitCvBook->status == 0) {
            $submitCvBookRepository = app(SubmitCvBookRepository::class);
            $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
            $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
            $submitCv = $this->submitCvBook->submitCv;
            //4 => 'Waiting confirm calendar',
            if ($submitCv->status == config('constant.status_recruitment_revert.Waitingconfirmcalendar')) {
                $this->submitCvBook->update([
                    'status' => 2 //tu choi
                ]);
                $employer = $this->submitCvBook->employer;
                $employer->notify(new EmailRejectInterViewSubmit($employer, $this->submitCvBook, $submitCv));
                $statusRecruitment = config('constant.status_recruitment_revert.RejectInterviewschedule'); //5 => 'Reject Interview schedule', chưa đủ 3 lần thì = 5, nếu lần 3 thì bằng 9
                $countBookReject = $submitCvBookRepository->countBookReject($this->submitCvBook->ntd_id, $submitCv->id);
                $bonus = 0;
                if ($countBookReject >= 3) {
                    $statusRecruitment = config('constant.status_recruitment_revert.CandidateCancelApply'); //2 => 'Candidate Cancel Apply',
                    // Từ chối quá 3 lần
                    //Hoan tien tra NTD
                    //CV interview
                    if ($submitCv->bonus_type == 'interview') {
                        //ghi log hoan tien
                        $submitCvHistoryPaymentRepository->create([
                            'user_id'                     => $submitCv->employer->id,
                            'submit_cv_id' => $submitCv->id,
                            'type'                        => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                            'percent'                     => 100,                                                           //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                => $submitCv->bonus_type,
                            'amount'                      => $submitCv->bonus_point,
                            'balance'                     => $submitCv->employer->wallet->amount + $submitCv->bonus_point,
                            'status'                      => 0
                        ]);
                        //update
                        $submitCv->status = $statusRecruitment;
                        $submitCv->status_payment = 3; //Hoan tien
                        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                        $submitCv->save();
                        //Cộng point của NTD
                        $bonus = $submitCv->bonus_point;
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->bonus_point;
                        $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                        $submitCv->employer->wallet->addAmount($submitCv->bonus_point, $submitCv, 'Quá hạn xác nhận lịch phỏng vấn 3 lần', 'reject_book_expire_submit');
                        // $submitCv->employer->wallet->save();
                    }
                    //cv onboard
                    if ($submitCv->bonus_type == 'onboard') {
                        $submitCvHistorPayments = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                        //hoàn bao nhiêu point
                        $bonus = 0;
                        if ($submitCvHistorPayments) {
                            foreach ($submitCvHistorPayments as $key => $value) {
                                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                $value->status = 1;
                                $value->save();
                                //ghi log hoan tien
                                $submitCvHistoryPaymentRepository->create([
                                    'user_id'                     => $submitCv->employer->id,
                                    'submit_cv_id' => $submitCv->id,
                                    'type'                        => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                                    'percent'                     => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                    'type_of_sale'                => $submitCv->bonus_type,
                                    'amount'                      => $value->amount,
                                    'balance'                     => $submitCv->employer->wallet->amount + $value->amount,
                                    'status'                      => 0
                                ]);
                                $bonus += $value->amount;
                            }
                        }
                        $submitCv->status = $statusRecruitment;
                        $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                        $submitCv->save();
                        //Cộng point của NTD
                        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                        $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                        $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Quá hạn xác nhận lịch phỏng vấn 3 lần', 'reject_book_expire_submit');
                        // $submitCv->employer->wallet->save();
                    }

                    $submitCv->employer->notify(new ChangeStatusCancelInterviewToEmployerSubmit($submitCv, $bonus));
                    if ($submitCv->authorize == 1) {
                        Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdminSubmit($submitCv, $bonus));
                    } else {
                        $submitCv->rec->notify(new ChangeStatusCancelInterviewToRecSubmit($submitCv, $bonus));
                    }
                } else {
                    OutOfDateBookInterviewSubmitCv::dispatch($submitCv, auth()->user())->delay(now()->addMinutes(7 * 24 * 60));
                    $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                    $submitCv->update(['status' => $statusRecruitment]);
                }

                $submitCvHistoryStatusRepository->logStatus($submitCv, $this->user);
            }
        }
    }
}
