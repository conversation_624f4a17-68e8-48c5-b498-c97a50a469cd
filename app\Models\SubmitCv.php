<?php

namespace App\Models;

use App\Repositories\SubmitCvBookRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvOnboardRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Zoha\Metable;
use App\Repositories\SubmitCvHistoryPaymentRepository;

class SubmitCv extends BaseModel implements Auditable
{
    use \OwenIt\Auditing\Auditable;
    use CrudTrait;
    use Metable;

    protected $appends = [
        'expected_date_value',
        'status_value',
        'payment_actual_format',
        'payment_fee_format',
        'bonus_authority',
        'bonus_val',
        'status_payment_value',
        'status_complain_value',
        'status_recruitment_value',
        'date_hour',
    ];

    protected $guarded = ['id'];
    protected $audit_tags = [];
    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }

    public function generateTags(): array
    {
        return $this->audit_tags;
    }


    public function warehouseCv()
    {
        // return $this->hasOne(WareHouseCv::class, 'id', 'warehouse_cv_id');
        return $this->belongsTo(WareHouseCv::class, 'warehouse_cv_id');
    }

    public function getDateHourAttribute()
    {
        if (!empty($this->created_at)) {
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function job()
    {
        // return $this->hasOne(Job::class, 'id', 'job_id');
        return $this->belongsTo(Job::class);
    }

    public function rec()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function company()
    {
        // return $this->hasOne(Company::class, 'id', 'company_id');
        return $this->belongsTo(Company::class);
    }

    public function discuss(): MorphMany
    {
        return $this->morphMany(Discuss::class, 'object');
    }

    public function employer()
    {
        return $this->hasOneThrough(
            User::class,
            Job::class,
            'id',
            'id',
            'job_id',
            'employer_id'
        )->where('users.type', config('constant.role.employer'));
    }

    public function submitCvMeta()
    {
        return $this->hasOne(SubmitCvMeta::class, 'submit_cv_id', 'id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function getUserEmailAttribute()
    {
        if ($this->user_id > 0 && $this->user) {
            return $this->user->email;
        } else {
            return '';
        }
    }

    public function getUserMobileAttribute()
    {
        if ($this->user_id > 0 && $this->user) {
            return $this->user->mobile;
        } else {
            return '';
        }
    }

    public function getWareHouseCvCandidateNameAttribute()
    {
        if ($this->warehouse_cv_id > 0 && $this->warehouseCv) {
            return $this->warehouseCv->candidate_name;
        } else {
            return '';
        }
    }

    public function getSubmitCvMetaCandidateNameAttribute()
    {

        return $this->relationLoaded('submitCvMeta') ? optional($this->submitCvMeta)->candidate_name : '';
    }

    public function getSubmitCvMetaCandidateEmailAttribute()
    {
        return $this->relationLoaded('submitCvMeta') ? optional($this->submitCvMeta)->candidate_email : '';
    }

    public function getSubmitCvMetaCandidateMobileAttribute()
    {
        return $this->relationLoaded('submitCvMeta') ? optional($this->submitCvMeta)->candidate_mobile : '';
    }

    public function getSubmitCvMetaUrlCvPublicAttribute()
    {
        return $this->relationLoaded('submitCvMeta') ? optional($this->submitCvMeta)->url_cv_public : '';
    }

    public function getSubmitCvMetaUrlCvPrivateAttribute()
    {
        return $this->relationLoaded('submitCvMeta') ? optional($this->submitCvMeta)->url_cv_private : '';
    }

    public function getWareHouseCvCandidateEmailAttribute()
    {
        if ($this->warehouse_cv_id > 0 && $this->warehouseCv) {
            return $this->warehouseCv->candidate_email;
        } else {
            return '';
        }
    }

    public function getWareHouseCvCandidateMobileAttribute()
    {
        if ($this->warehouse_cv_id > 0 && $this->warehouseCv) {
            return $this->warehouseCv->candidate_mobile;
        } else {
            return '';
        }
    }

    public function getWareHouseCvUrlCvPublicAttribute()
    {
        if ($this->warehouse_cv_id > 0 && $this->warehouseCv) {
            return $this->warehouseCv->url_cv_public;
        } else {
            return '';
        }
    }

    public function getWareHouseCvUrlCvPrivateAttribute()
    {
        if ($this->warehouse_cv_id > 0 && $this->warehouseCv) {
            return $this->warehouseCv->url_cv_private;
        } else {
            return '';
        }
    }

    public function getJobNameAttribute()
    {
        if ($this->job_id > 0 && $this->job) {
            return $this->job->name;
        } else {
            return '';
        }
    }

    public function getJobFileJdAttribute()
    {
        if ($this->job_id > 0 && $this->job) {
            return gen_url_file_s3($this->job->file_jd);
        } else {
            return '';
        }
    }

    public function getRecNameAttribute()
    {
        if ($this->rec) {
            return $this->rec->name;
        } else {
            return '';
        }
    }

    public function getRecEmailAttribute()
    {
        if ($this->rec) {
            return $this->rec->email;
        } else {
            return '';
        }
    }

    public function getRecMobileAttribute()
    {
        if ($this->rec) {
            return $this->rec->mobile;
        } else {
            return '';
        }
    }

    public function getCompanyNameAttribute()
    {
        if ($this->company_id > 0 && $this->company) {
            return $this->company->name;
        } else {
            return '';
        }
    }

    public function getExpectedDateValueAttribute()
    {
        if ($this->expected_date) {
            $data = Carbon::createFromFormat('Y-m-d', $this->expected_date);
            return $data->format('d/m/Y');
        }
        return '';
    }

    public function getStatusValueAttribute()
    {
        return config('constant.status_recruitment.vi.' . $this->status);
        // return config('constant.submit_cvs_status.' . $this->status);
    }

    public function getEmployerEmailAttribute()
    {
        if ($this->job_id > 0 && $this->employer) {
            return $this->employer->email;
        }
        return '';
    }


    public function getEmployerMobileAttribute()
    {
        if ($this->job_id > 0 && $this->employer) {
            return $this->employer->mobile;
        }
        return '';
    }

    public function getCreatedValueAttribute()
    {
        if ($this->created_at) {
            return $this->created_at->format('d/m/Y');
        }
        return '';
    }

    public function getTimeAgoAttribute()
    {
        if ($this->created_at) {
            return $this->created_at->diffForHumans();
        }
        return '';
    }

    public function getCandidateCurrencyValueAttribute()
    {
        if ($this->candidate_currency == 'USD') {
            return '$';
        }
        return $this->candidate_currency;
    }

    public function getStatusPaymentValueAttribute()
    {
        // return config('constant.status_payment.' . $this->status_payment);
        return isset($this->status_payment) ? config('constant.status_payments.' .  app()->getLocale() . '.' . $this->status_payment) : '';
    }
    public function getStatusComplainValueAttribute()
    {
        return  !empty($this->status_complain) ? config('constant.status_complain.' .  app()->getLocale() . '.' . $this->status_complain) : (app()->getLocale() == 'en' ? 'No complaints' : 'Không có khiếu nại');
    }
    public function getStatusRecruitmentValueAttribute()
    {
        if (!empty($this->status)) {
            $user = auth('client')->user();
            if ($user) {
                if (!empty(config('constant.status_recruitment_label.' . app()->getLocale() . '.' . $user->type . '.' . $this->status))) {
                    return config('constant.status_recruitment_label.' . app()->getLocale() . '.' . $user->type . '.' . $this->status);
                }
            }
        }
        return !empty($this->status) ? config('constant.status_recruitment.' .  app()->getLocale() . '.' . $this->status) : '';
    }

    public function getPaymentFeeFormatAttribute()
    {
        return number_format($this->payment_fee);
    }

    public function getPaymentActualFormatAttribute()
    {
        return number_format($this->payment_actual);
    }


    public function getBonusAuthorityAttribute()
    {
        if ($this->is_self_apply == 1) {
            $money = $this->bonus_self_apply;
        } else {
            $money = $this->bonus;
        }

        return $this->authorize == 1 ? number_format($money * config('settings.global.authority') / 100) : $money;
    }

    public function getBonusValAttribute()
    {
        if ($this->is_self_apply == 1) {
            $money = $this->bonus_self_apply;
        } else {
            $money = $this->bonus;
        }

        return number_format($money);
    }

    public function getBonusPointAttribute()
    {
        if ($this->is_self_apply == 1) {
            $money = $this->bonus_self_apply;
        } else {
            $money = $this->bonus;
        }
        // nếu có bonus ctv nhập tay thì lấy bonus ctv nhập tay
        if ($this->job->manual_bonus_for_ctv) {
            $money = $this->job->manual_bonus_for_ctv;
        }

        return $money;
    }

    public function getSubmitBonusForCtv()
    {
        $percent = 20;
        if ($this->is_self_apply == 1) {
            $money = $this->bonus_self_apply;
        } else {
            $money = $this->bonus;
        }
        // nếu có bonus ctv nhập tay thì lấy bonus ctv nhập tay
        if ($this->job->manual_bonus_for_ctv) {
            $money = $this->job->manual_bonus_for_ctv;
        }

        return $this->authorize == 1 ? $money * $percent / 100 : $money;
    }

    public function getBonusNtdAttribute()
    {
        return $this->job->bonus;
    }

    public function canOpenCv()
    {
        $bonus_type = $this->bonus_type;
        $open_status = config('constant.open_cv_status');
        if (isset($open_status[$bonus_type])) {
            if (in_array($this->status, $open_status[$bonus_type])) {
                return true;
            }
        }
        return false;
    }

    public function isWaitingPayment()
    {
        return $this->status == config('constant.status_recruitment_revert.WaitingPayment');
    }
    public function isShowInterviewTime()
    {
        return in_array($this->status, [
            config('constant.status_recruitment_revert.Waitingsetupinterview'),
            config('constant.status_recruitment_revert.Waitingconfirmcalendar'),
            config('constant.status_recruitment_revert.WaitingInterview'),
        ]);
    }

    public function isWaitingsetupinterview()
    {
        // return $this->status == config('constant.status_recruitment_revert.Waitingsetupinterview');
        return in_array($this->status, [
            config('constant.status_recruitment_revert.Waitingsetupinterview'),
            config('constant.status_recruitment_revert.RejectInterviewschedule'),
        ]);
    }
    public function isWaitingsetupOnboard()
    {
        return $this->status == config('constant.status_recruitment_revert.Offering');
    }

    public function getComplainDeadlineAttribute()
    {
        $now = Carbon::now();
        if ($this->created_at) {
            $deadline = $this->created_at->addMinutes(config('constant.buy_cv_success'));
            if ($deadline->timestamp < $now->timestamp) {
                return  false;
            } else {
                return true;
            }
        }
        return true;
    }

    public function getBookTime()
    {
        $submitCvBookRepository = app(SubmitCvBookRepository::class);
        $book = $submitCvBookRepository->getLastBookBySubmitIdAllStatus($this->id);
        if (!empty($book)) {
            return Carbon::createFromFormat('H:i:s', time: $book->time_book)->format('H:i') . ' ' . Carbon::createFromFormat('Y-m-d H:i:s', time: $book->date_book)->format('d/m');
        }
        return null;
    }

    public function getCanComplainAttribute()
    {
        $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
        $submitCvBookRepository = app(SubmitCvBookRepository::class);
        $submitCvOnboardRepository = app(SubmitCvOnboardRepository::class);
        $now = Carbon::now();
        if ($this->status_complain != 0 && $this->status_complain != 5) {
            return false;
        }

        if ($this->bonus_type == 'cv') {
            if ($this->status == config('constant.status_recruitment_revert.BuyCVdatasuccessfull') && $this->count_complain < 1) { // waiting setup interview
                return true;
            }
        }
        if ($this->bonus_type == 'interview') {
            if ($this->status == config('constant.status_recruitment_revert.Waitingsetupinterview') && $this->count_complain < 1) { // waiting setup interview
                return true;
            }
            if ($this->status == config('constant.status_recruitment_revert.WaitingInterview') && $this->count_complain < 1) {
                $book = $submitCvBookRepository->getWaitingInterviewBook($this->id);
                if (!empty($book)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book)
                        ->startOfDay()
                        ->addMinutes($book->book_time_minute + 7 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        if ($this->bonus_type == 'onboard') {
            if ($this->status == config('constant.status_recruitment_revert.WaitingInterview') && $this->count_complain < 1) {
                $book = $submitCvBookRepository->getWaitingInterviewBook($this->id);
                if (!empty($book)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book)
                        ->startOfDay()
                        ->addMinutes($book->book_time_minute + 7 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
            if ($this->status == config('constant.status_recruitment_revert.Trialwork') && $this->count_complain < 2) {
                $onboard = $submitCvOnboardRepository->getFirstBySubmitCvId($this->id);
                if (!empty($onboard)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book)
                        ->startOfDay()
                        ->addMinutes($onboard->book_time_minute + 67 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        // if ($this->bonus_type == 'cv' && $this->status == 18) {
        //     if ($this->created_at) {
        //         if ($this->created_at->addMinutes(7 * 24 * 60)->timestamp < $now->timestamp) {
        //             return false;
        //         } else {
        //             return true;
        //         }
        //     }
        // }

        return false;
    }


    public function getUnreadDiscussCountAttribute()
    {
        $user = auth('client')->user();
        if ($user->type == config('constant.role.employer')) {
            return $this->hasMany(SubmitCvDiscuss::class, 'submit_cvs_id', 'id')
                ->whereNull('ntd_id')
                ->where('unread', 1)
                ->count();
        } elseif ($user->type == config('constant.role.rec')) {
            return $this->hasMany(SubmitCvDiscuss::class, 'submit_cvs_id', 'id')
                ->whereNull('ctv_id')
                ->where('unread', 1)
                ->count();
        }
        return 0;
    }

    public function hoanTienChoNtd($note = '', $source_call = '')
    {

        $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
        // Từ chối quá 3 lần
        // Trả tiền Cọc
        //CV interview
        $bonus = 0;
        if ($this->bonus_type == 'interview' || $this->bonus_type == 'cv') {
            //ghi log hoan tien
            $submitCvHistoryPaymentRepository->create([
                'user_id'      => $this->employer->id,
                'submit_cv_id' => $this->id,
                'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                'percent'      => 100,                                                           //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale' => $this->bonus_type,
                'amount'       => $this->bonus_point,
                'balance'      => $this->employer->wallet->amount + $this->bonus_point,
                'status'       => 0
            ]);
            //update
            $this->status_payment = 3; //Hoan tien
            $this->save();
            //Cộng point của NTD
            $bonus = $this->bonus_point;
            $this->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $this->id]);
            $this->employer->wallet->addAmount($this->bonus_point, $this, $note, $source_call);
        }
        //cv onboard
        if ($this->bonus_type == 'onboard') {
            $submitCvHistorPayments = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($this->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
            //hoàn bao nhiêu point
            $bonus = 0;
            if ($submitCvHistorPayments) {
                foreach ($submitCvHistorPayments as $key => $value) {
                    //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                    $value->status = 1;
                    $value->save();
                    //ghi log hoan tien
                    $submitCvHistoryPaymentRepository->create([
                        'user_id'      => $this->employer->id,
                        'submit_cv_id' => $this->id,
                        'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                        'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale' => $this->bonus_type,
                        'amount'       => $value->amount,
                        'balance'      => $this->employer->wallet->amount + $value->amount,
                        'status'       => 0
                    ]);
                    $bonus += $value->amount;
                }
            }
            $this->status_payment = 2; //2 => 'Hoàn cọc'
            $this->save();
            //Cộng point của NTD
            $this->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $this->id]);
            $this->employer->wallet->addAmount($bonus, $this, $note, $source_call);
        }
        // return số tiền đã hoàn
        return $bonus;
    }
}
