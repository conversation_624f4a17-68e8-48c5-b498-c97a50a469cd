<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelInterviewToAdmin extends Mailable
{

    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $point;

    public function __construct($wareHouseCvSellingBuy,$point)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->point = $point;
    }


    public function content(): Content
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $point = $this->point;
        $linkPreview = route('luot-ban.show',['luot_ban' => $this->wareHouseCvSellingBuy->id]);

        return new Content(
            view: 'email.changeStatusCancelInterviewToAdmin',
            with: [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'point' => $point,
                'urlWallet' => route('employer-wallet'),
                'urlMarket' => route('market-cv'),
                'linkPreview' => $linkPreview
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $message->subject('[Ủy quyền] Thông báo hủy phỏng vấn ứng viên  '.$candidateName.' vị trí  '.$position);
        return $this;
    }




}
