<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\JobStatisticsService;
use Illuminate\Http\Request;

class JobStatisticsController extends Controller
{
    protected $jobStatisticsService;

    public function __construct(JobStatisticsService $jobStatisticsService)
    {
        $this->jobStatisticsService = $jobStatisticsService;
    }

    /**
     * Hiển thị trang thống kê danh sách job
     */
    public function index(Request $request)
    {
        $filters = [
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'company_id' => $request->input('company_id'),
            'service_type' => $request->input('service_type'), // cv, interview, onboard
            'status' => $request->input('status'),
            'search' => $request->input('search'),
        ];

        $statistics = $this->jobStatisticsService->getJobStatistics($filters);
        $companies = $this->jobStatisticsService->getCompaniesForFilter();

        return view('admin.pages.job-statistics.index', compact('statistics', 'filters', 'companies'));
    }

    /**
     * Filter dữ liệu thống kê job qua AJAX
     */
    public function filterData(Request $request)
    {
        $filters = [
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'company_id' => $request->input('company_id'),
            'service_type' => $request->input('service_type'),
            'status' => $request->input('status'),
            'search' => $request->input('search'),
            'page' => $request->input('page', 1), // Thêm page parameter
        ];

        $statistics = $this->jobStatisticsService->getJobStatistics($filters);

        $view = view('admin.pages.job-statistics.filter-statistics', compact('statistics'))->render();

        return response()->json([
            'status' => 'success',
            'html' => $view,
        ]);
    }

    /**
     * Xuất dữ liệu thống kê ra CSV
     */
    public function exportCsv(Request $request)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        // Validation: Phải có filter ngày
        if (empty($fromDate) || empty($toDate)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Vui lòng chọn khoảng thời gian để xuất dữ liệu (từ ngày - đến ngày).'
            ], 400);
        }

        // Validation: Khoảng ngày không được lớn hơn 3 tháng
        try {
            $fromDateTime = \Carbon\Carbon::createFromFormat('Y-m-d', $fromDate);
            $toDateTime = \Carbon\Carbon::createFromFormat('Y-m-d', $toDate);

            $diffInMonths = $fromDateTime->diffInMonths($toDateTime);

            if ($diffInMonths > 3) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Khoảng thời gian xuất dữ liệu không được vượt quá 3 tháng.'
                ], 400);
            }

            // Validation: Từ ngày không được lớn hơn đến ngày
            if ($fromDateTime->gt($toDateTime)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Từ ngày không được lớn hơn đến ngày.'
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Định dạng ngày không hợp lệ.'
            ], 400);
        }

        $filters = [
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'company_id' => $request->input('company_id'),
            'service_type' => $request->input('service_type'),
            'status' => $request->input('status'),
            'search' => $request->input('search'),
        ];

        return $this->jobStatisticsService->exportToCsv($filters);
    }
}
