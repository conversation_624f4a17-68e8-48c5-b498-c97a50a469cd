<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FakeLead extends Model
{
    use SoftDeletes;

    protected $connection = 'fake_info';
    protected $table = 'leads';

    protected $fillable = [
        'company_id',
        'contact_id',
        'company_name',
        'contact_name',
        'email',
        'phone',
        'source',
        'status',
        'lead_content',
        'last_contact',
        'user_id',
        'created_by',
        'service_id',
    ];

    protected $dates = [
        'last_contact',
        'deleted_at',
    ];

    protected $casts = [
        'last_contact' => 'datetime',
    ];

    /**
     * Clean email - loại bỏ khoảng trắng đầu/cuối
     */
    public function getCleanEmailAttribute(): ?string
    {
        return $this->email ? trim($this->email) : null;
    }

    /**
     * Clean phone - loại bỏ khoảng trắng đầu/cuối
     */
    public function getCleanPhoneAttribute(): ?string
    {
        return $this->phone ? trim($this->phone) : null;
    }

    /**
     * Clean contact name - loại bỏ khoảng trắng đầu/cuối
     */
    public function getCleanContactNameAttribute(): ?string
    {
        return $this->contact_name ? trim($this->contact_name) : null;
    }

    /**
     * Clean company name - loại bỏ khoảng trắng đầu/cuối
     */
    public function getCleanCompanyNameAttribute(): ?string
    {
        return $this->company_name ? trim($this->company_name) : null;
    }
}
