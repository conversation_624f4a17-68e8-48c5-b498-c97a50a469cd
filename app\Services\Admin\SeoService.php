<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\SeoRepository;
use App\Services\FileServiceS3;

class SeoService
{
    protected $seoRepository;

    public function __construct(SeoRepository $seoRepository)
    {
        $this->seoRepository = $seoRepository;
    }

    public function index($params)
    {
        $data = $this->getKey($params);
        return $data;
    }

    public function createSeo($dataSideBar)
    {
        $data = [];
        foreach ($dataSideBar as $key => $value) {
            $check = $this->seoRepository->findByKeyword($key);
            if ($check) {
                continue;
            }
            $data[] = [
                'key' => $key,
                'title_vi' => $value['vi']['title'],
                'description_vi' => $value['vi']['description'],
                'keyword_vi' => $value['vi']['keyword'],
                'img_thumb_vi' => $value['vi']['img_thumb'],
                'title_en' => $value['en']['title'],
                'description_en' => $value['en']['description'],
                'keyword_en' => $value['en']['keyword'],
                'img_thumb_en' => $value['en']['img_thumb'],
            ];
        }

        if(count($data) > 0 ) $this->seoRepository->insert($data);
    }

    public function getKey($params)
    {
        return $this->seoRepository->findByKeyword($params['keyword']);
    }

    public function create($params)
    {
        $seo = $this->seoRepository->findByKeyword($params['key']);
        if ($params['lang'] == 'vi') {
            $data = [
                'key' => $params['key'],
                'title_vi' => $params['title_vi'],
                'keyword_vi' => $params['keyword_vi'],
                'description_vi' => $params['description_vi'],
            ];

            if (isset($params['image_vi']) && is_file($params['image_vi'])) {
                $data['img_thumb_vi'] = FileServiceS3::getInstance()->uploadToS3($params['image_vi'], config('constant.sub_path_s3.seo'));
            } else {
                $data['img_thumb_vi'] = $params['img_vi'];
            }

            return $this->seoRepository->update($seo->id, [], $data);
        } else {
            $data = [
                'key' => $params['key'],
                'title_en' => $params['title_en'],
                'keyword_en' => $params['keyword_en'],
                'description_en' => $params['description_en'],
            ];

            if (isset($params['image_en']) && is_file($params['image_en'])) {
                $data['img_thumb_en'] = FileServiceS3::getInstance()->uploadToS3($params['image_en'], config('constant.sub_path_s3.seo'));
            } else {
                $data['img_thumb_en'] = $params['img_en'];
            }

            return $this->seoRepository->update($seo->id, [], $data);
        }
    }
}
