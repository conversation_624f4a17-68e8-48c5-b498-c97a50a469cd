<?php

namespace App\Http\Resources\Frontend\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'referral_define' => $this->referral_define,
            // 'email' => $this->email,
            // 'phone' => $this->phone,
            // 'avatar' => $this->avatar,
            // 'address' => $this->address,
            // 'birthday' => $this->birthday,
            // 'gender' => $this->gender,
            // 'created_at' => $this->created_at,
            // 'updated_at' => $this->updated_at,
            // 'team' => new TeamResource($this->whenLoaded('team')),
            // 'roles' => RoleResource::collection($this->whenLoaded('roles')),
        ];
    }
} 