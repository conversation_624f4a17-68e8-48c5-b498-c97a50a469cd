<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckDateBeforeNowRule;
use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\File\File;

class ProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rule = [
            'birthday' => ['required',new CheckDateBeforeNowRule()],
            'name'     => 'required',
            'email'    => ['required', 'email', new CheckEmailRule(config('constant.role.rec'),auth('client')->id())],
            'mobile'   => 'required|regex:/^[0-9()+.-]*$/|min:10|max:16',
            'address'  => 'required',
        ];
        if ($this->avatar){
            $rule['avatar'] = 'mimes:jpeg,jpg,png|max:5120';
        }
        return $rule;
    }

    public function messages()
    {
        return [
            'min'          => __('frontend/validation.min', ['min' => 10]),
            'max'          => __('frontend/validation.max', ['max' => 16]),
            'email'        => __('frontend/validation.email'),
            'mobile.regex' => __('frontend/validation.regex_phone'),
            'required'     => __('frontend/validation.required'),
        ];
    }

    protected function prepareForValidation()
    {
        $base64File = $this->input('avatar');
        if ($base64File){
            $fileData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64File));
            $tmpFilePath = sys_get_temp_dir() . '/' . Str::uuid()->toString();
            file_put_contents($tmpFilePath, $fileData);
            $tmpFile = new File($tmpFilePath);
            $file = new UploadedFile(
                $tmpFile->getPathname(),
                $tmpFile->getFilename(),
                $tmpFile->getMimeType(),
                0,
                true // Mark it as test, since the file isn't from real HTTP POST.
            );
            $this->merge(['avatar'=>$file]);
        }
    }

}
