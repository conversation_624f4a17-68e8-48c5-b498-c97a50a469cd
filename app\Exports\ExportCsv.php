<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ExportCsv implements WithMapping, WithHeadings, FromCollection
{
    use Exportable;
    protected $header;
    protected $collection;


    public function collection()
    {
        return $this->collection;
    }

    public function headings() : array
    {
        return $this->header;
    }

    public function map($data): array
    {
        return $data;
    }
}

