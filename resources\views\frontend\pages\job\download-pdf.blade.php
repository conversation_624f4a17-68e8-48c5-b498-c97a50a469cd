@php
    use League\HTMLToMarkdown\HtmlConverter;
    use Illuminate\Support\Str;

    $converter = new HtmlConverter();
@endphp
<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">

<head>
    <!-- Google tag (gtag.js) -->
    {{--    <script async src="https://www.googletagmanager.com/gtag/js?id=G-G4VPZYJ5H7"></script> --}}

    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=yes">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-title" content="" />
    {{--    <meta name="csrf-token" content="{{ csrf_token() }}"> --}}

    <style>
        @page {
            margin: 20px;
        }
        body {
            font-family: 'NotoSansJP', 'DejaVu Sans', sans-serif;
            padding-top: 40px;
        }

        * {
            margin: 0;
            padding: 0;
            text-indent: 0;
        }

        .s1 {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            font-size: 11pt;
        }

        h1 {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: bold;
            text-decoration: none;
            font-size: 18pt;
        }

        .s2 {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: bold;
            text-decoration: none;
            font-size: 12pt;
        }

        .s3 {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            font-size: 12pt;
        }

        h2 {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: bold;
            text-decoration: none;
            font-size: 12pt;
        }

        p {
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            font-size: 12pt;
            margin: 0pt;
        }

        li {
            display: block;
        }

        #l1 {
            padding-left: 0pt;
        }

        #l1>li>*:first-child:before {
            content: "- ";
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            font-size: 12pt;
        }

        #l2 {
            padding-left: 0pt;
        }

        #l2>li>*:first-child:before {
            content: "- ";
            color: black;
            font-family: "NotoSansJP", "DejaVu Sans", serif;
            font-style: normal;
            font-weight: normal;
            text-decoration: none;
            font-size: 12pt;
        }

        table,
        tbody {
            vertical-align: top;
            overflow: visible;
        }

        * {
            font-family: "NotoSansJP", "DejaVu Sans" !important;
        }
    </style>
</head>

<body>
    <div id="pdf-content" ref="pdfContent" style="max-width: 1000px;padding: 50px;margin: 0 auto; padding-top: 10px">
        <table style="width:100%">
            <tr>
                <td style="width:50%">
                    <picture class="logo-dark">
                        <source media="(min-width:650px)"
                            srcset="{{ asset2('frontend/assets_v2/images/graphics/logo.png') }}">
                        <img style="max-width: 50%" src="{{ asset2('frontend/assets_v2/images/graphics/logo.png') }}"
                            alt="">
                    </picture>
                </td>
                <td style="width:50%; text-align:right">
                    <p>5F,Simco Tower, Me Tri, Nam Tu Liem, Hanoi</p>
                    <a href="https://recland.co">Recland.co</a>
                </td>
            </tr>
        </table>
        <hr style="border: 1px solid #000; margin: 20px 0;">
        <p style="padding-top: 8pt;text-indent: 0pt;text-align: left;"><br /></p>
        <h1 style="text-indent: 0pt;text-align: center;">{{ $job->name ?? '' }}</h1>
        <p style="padding-top: 2pt;text-indent: 0pt;text-align: left;"><br /></p>
        <table style="border-collapse:collapse;margin-left:11.5pt; width: 100%" cellspacing="0">
            <tr style="height:51pt">
                <td style="width:222pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 5pt;padding-left: 15px;text-align: left;">Công ty</p>
                    <p class="company-name s3"
                        style="padding-top: 6pt;padding-left: 15px;text-align: left; padding-bottom: 10px;">
                        {{ $job->company ? $job->company->name : '' }}
                    </p>
                </td>
                <td style="width:234pt;border:1pt solid;">
                    <p class="salary s2" style="padding-top: 5pt;padding-left: 10px;text-align: left;">Mức lương</p>
                    <p class="salary s3"
                        style="padding-top: 6pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        <span id="salary_min">{{ number_format($job->salary_min ?? 0, 0, ',', '.') }}</span> -
                        <span id="salary_max">{{ number_format($job->salary_max ?? 0, 0, ',', '.') }}</span>
                        ({{ $job->salary_currency ?? '' }})
                    </p>
                </td>
            </tr>
            <tr style="height:49pt">
                <td style="width:222pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 6pt;padding-left: 10px;text-align: left;">Lĩnh vực</p>
                    <p class="career s3"
                        style="padding-top: 7pt; padding-left: 10px; text-align: left; padding-bottom: 10px;">
                        {{ $career ?? '' }}
                    </p>

                    <!--                        <p-->
                    <!--                            class="career s3"-->
                    <!--                            style="padding-top: 7pt; padding-left: 24pt; text-align: left;"-->
                    <!--                            v-else-if="isJobDetail">-->
                    {{--                                                {{ this.detail.career_value.join(', ') }} --}}
                    <!--                        </p>-->
                </td>
                <td style="width:234pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 6pt;padding-left: 10px;text-align: left;">Hình thức</p>
                    <p class="job-type s3"
                        style="padding-top: 7pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        {{ $job->type ?? '' }} <!-- Giả sử có thuộc tính job_type trong detail -->
                    </p>
                </td>
            </tr>
            <tr style="height:48pt">
                <td style="width:222pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 5pt;padding-left: 10px;text-align: left;">Cấp bậc</p>
                    <p class="rank s3"
                        style="padding-top: 7pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        {{ $rank ?? '' }}
                    </p>
                </td>
                <td style="width:234pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 5pt;padding-left: 10px;text-align: left;">Hạn nộp</p>
                    <p class="expire_at s3"
                        style="padding-top: 7pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        {{ $job->expire_at ?? '' }}
                    </p>
                </td>
            </tr>
            <tr style="height:48pt">
                <td style="width:222pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 6pt;padding-left: 10px;text-align: left;">Địa điểm</p>
                    <p class="area_string s3"
                        style="padding-top: 6pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        {{ $areaString ?? '' }}
                    </p>
                </td>
                <td style="width:234pt;border:1pt solid;">
                    <p class="s2" style="padding-top: 6pt;padding-left: 10px;text-align: left;">Số lượng tuyển</p>
                    <p class="vacancies s3"
                        style="padding-top: 6pt;padding-left: 10px;text-align: left; padding-bottom: 10px;">
                        {{ $job->vacancies ?? '' }} <!-- Giả sử có thuộc tính vacancies trong detail -->
                    </p>
                </td>
            </tr>
        </table>
        <!--            <h2>Phúc Lợi</h2>-->
        <!--            <p v-html="this.detail.jd_welfare"></p>-->
        <p style="padding-top: 1pt;text-indent: 0pt;text-align: left;"><br /></p>
        <h2 style="padding-left: 5pt;text-indent: 0pt;text-align: left;">MÔ TẢ CÔNG VIỆC</h2>
        <ul id="l1">

            <p class="jd_description"
                style="padding-top: 6pt;padding-left: 5pt;text-indent: 0pt;line-height: 129%;text-align: left;">
                @php
                    // dd($jd_description);
                    // $jd_description = strip_tags($jd_description);
                    // $jd_description = str_replace('<li ', "- <li ", $jd_description);
                    // $jd_description = str_replace(['<br>', '<br />', '</p>'], "\n", $jd_description);
                    // $jd_description = str_replace("\t", "", $jd_description);
                    // $jd_description = trim($jd_description);
                    // $jd_description = preg_replace('/\s*\n\s*\n\s*/', "\n", $jd_description);
                    // $jd_description = nl2br($jd_description);
                    $converter->getConfig()->setOption('strip_tags', true);
                    $jd_description = $job->jd_description ?? '';
                    $jd_description = $converter->convert($jd_description);
                    $jd_description = trim($jd_description);
                    // Loại bỏ các dòng chỉ chứa dấu gạch ngang
                    $jd_description = preg_replace('/^\s*[-]+\s*$/m', '', $jd_description);
                    // Loại bỏ nhiều dòng trống thành một dòng trống
                    $jd_description = preg_replace('/\s*\n\s*\n\s*/', "\n", $jd_description);
                    $jd_description = str_replace('\-', "-", $jd_description);
                    $jd_description = str_replace('\+', "+", $jd_description);
                @endphp
                {!! nl2br($jd_description) !!}
            </p>

        </ul>
        <p style="padding-top: 1pt;text-indent: 0pt;text-align: left;"><br /></p>
        <h2 style="padding-left: 5pt;text-indent: 0pt;text-align: left;">YÊU CẦU CÔNG VIỆC</h2>
        <ul id="l1">
            <p class="jd_request"
                style="padding-top: 6pt;padding-left: 5pt;text-indent: 0pt;line-height: 129%;text-align: left;">
                {{-- {{ nl2br(strip_tags($job->jd_request ?? '')) }} --}}
                @php
                    $jd_description = $job->jd_request ?? '';
                    $jd_description = $converter->convert($jd_description);
                    $jd_description = trim($jd_description);
                    // Loại bỏ các dòng chỉ chứa dấu gạch ngang
                    $jd_description = preg_replace('/^\s*[-]+\s*$/m', '', $jd_description);
                    // Loại bỏ nhiều dòng trống thành một dòng trống
                    $jd_description = preg_replace('/\s*\n\s*\n\s*/', "\n", $jd_description);
                    $jd_description = str_replace('\-', "-", $jd_description);
                    $jd_description = str_replace('\+', "+", $jd_description);
                @endphp
                {!! nl2br($jd_description) !!}
            </p>
        </ul>
        <p style="padding-top: 1pt;text-indent: 0pt;text-align: left;"><br /></p>
        <h2 style="padding-left: 5pt;text-indent: 0pt;text-align: left;">PHÚC LỢI</h2>
        <ul id="l1">
            <p class="jd_welfare"
                style="padding-top: 6pt;padding-left: 5pt;text-indent: 0pt;line-height: 129%;text-align: left;">
                {{-- {{ nl2br(strip_tags($job->jd_welfare ?? '')) }} --}}
                @php
                    $jd_description = $job->jd_welfare ?? '';
                    $jd_description = $converter->convert($jd_description);
                    $jd_description = trim($jd_description);
                    // Loại bỏ các dòng chỉ chứa dấu gạch ngang
                    $jd_description = preg_replace('/^\s*[-]+\s*$/m', '', $jd_description);
                    // Loại bỏ nhiều dòng trống thành một dòng trống
                    $jd_description = preg_replace('/\s*\n\s*\n\s*/', "\n", $jd_description);
                    $jd_description = str_replace('\-', "-", $jd_description);
                    $jd_description = str_replace('\+', "+", $jd_description);
                @endphp
                {!! nl2br($jd_description) !!}
            </p>
        </ul>
    </div>
</body>
</html>
