<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Http\Controllers\Controller;
use App\Models\JobComment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Repositories\JobComment\JobCommentRepositoryInterface;
use App\Repositories\JobComment\JobCommentRepository;

class JobCommentController extends Controller
{
    protected $jobCommentRepository;

    public function __construct(JobCommentRepository $jobCommentRepository)
    {
        $this->jobCommentRepository = $jobCommentRepository;
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách comments của một job
     */
    public function index($jobId)
    {
        $comments = $this->jobCommentRepository->getCommentsByJobId($jobId);
        
        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    /**
     * Tạo comment mới
     */
    public function store(Request $request)
    {
        $user = auth('client')->user();
        $validator = Validator::make($request->all(), [
            'job_id' => 'required|exists:job,id',
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:job_comments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn phải đăng nhập để bình luận'
            ], 401);
        }

        try {
            $comment = $this->jobCommentRepository->create([
                'job_id' => $request->job_id,
                'user_id' => $user->id,
                'content' => $request->content,
                'parent_id' => $request->parent_id ?? null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Đã thêm bình luận thành công',
                'data' => [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'user_name' => $user->name,
                    'created_at' => $comment->created_at,
                    'parent_id' => $comment->parent_id
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra, vui lòng thử lại sau'
            ], 500);
        }
    }

    /**
     * Xóa comment
     */
    public function destroy($id)
    {
        $user = auth('client')->user();
        $comment = JobComment::findOrFail($id);

        // Kiểm tra quyền xóa
        if ($comment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn không có quyền xóa bình luận này'
            ], 403);
        }

        $comment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa bình luận thành công'
        ]);
    }
} 