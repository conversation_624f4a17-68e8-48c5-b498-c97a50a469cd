
# SRS - F04: <PERSON><PERSON><PERSON><PERSON> lý Tin tuyển dụng (Jobs)

**<PERSON><PERSON><PERSON> bản:** 6.0
**Ngày:** 2025-07-08
**<PERSON><PERSON><PERSON> gi<PERSON>:** Gemini

---

### 1. <PERSON><PERSON> tả

Tính năng này là nền tảng cho luồng "Ứng tuyển CV", cho phép NTD tạo và quản lý các tin tuyển dụng. Logic được xử lý chủ yếu trong `JobController` và `JobService`.

### 2. <PERSON><PERSON><PERSON> tượng tham gia

*   **Nhà tuyển dụng (NTD):** Người tạo và quản lý tin tuyển dụng.
*   **Ứng viên (UV), <PERSON><PERSON><PERSON> tá<PERSON> viên (CTV):** Người xem và tìm kiếm tin tuyển dụng.
*   **Quản trị viên (Admin):** Ngư<PERSON><PERSON> có toàn quyền quả<PERSON> lý.

### 3. <PERSON><PERSON> hình dữ liệu cốt lõi

*   **`jobs`**: Bảng trung tâm chứa mọi thông tin về một tin tuyển dụng.
    *   **Thông tin cơ bản:** `name`, `slug`, `company_id`, `employer_id`, `expire_at`.
    *   **Nội dung:** `jd_description`, `jd_request`, `jd_welfare`.
    *   **Lương & Thưởng:** `salary_min`, `salary_max`, `bonus` (hoa hồng CTV), `bonus_self_apply` (thưởng UV).
    *   **Cấu hình luồng:** `bonus_type` (enum: 'cv', 'onboard').
    *   **Trạng thái:** `is_active` (0: inactive, 1: active).
*   **`job_logs`**: Bảng ghi lại các sự kiện quan trọng (tạo, sửa, duyệt, hết hạn).
*   **`job_comments`**: Bảng lưu các bình luận công khai.
*   **Vue Components:** `JobList.vue`, `DetailJob.vue`, `JobComments.vue` được sử dụng để render giao diện phía người dùng.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi Validation:**
    *   **Nguyên nhân:** NTD nhập thiếu trường bắt buộc (ví dụ: `name`) hoặc sai định dạng (ví dụ: `salary_min` không phải là số).
    *   **Xử lý:** `JobRequest` FormRequest sẽ tự động chặn request, không cho nó đến Controller. Laravel sẽ tự động redirect người dùng trở lại form, kèm theo danh sách các lỗi validation để hiển thị ngay bên dưới các trường bị lỗi.

*   **5.2. Lỗi không có quyền:**
    *   **Nguyên nhân:** Một NTD cố gắng sửa hoặc xóa một tin tuyển dụng không thuộc sở hữu của họ bằng cách thay đổi ID trên URL.
    *   **Xử lý:** Trong `JobService` (hoặc sử dụng Policy), trước khi thực hiện hành động, hệ thống phải kiểm tra `job->employer_id` có khớp với `auth()->id()` hay không. Nếu không, Service sẽ ném ra một `AuthorizationException`. Controller bắt lỗi này và trả về HTTP status `403 Forbidden`.

### 4. Yêu cầu chức năng

#### 4.1. Tạo/Sửa tin tuyển dụng

1.  **Bước 1 (HTTP Request):** NTD submit form từ giao diện. Request (chứa tất cả các trường của job) được gửi đến phương thức `store` hoặc `update` trong `JobController` (Frontend) hoặc `createJob` trong `JobController` (API).
2.  **Bước 2 (Controller & Service):** Controller thực hiện validation (sử dụng một `JobRequest` FormRequest class) để đảm bảo dữ liệu hợp lệ. Nếu thành công, Controller gọi phương thức `createJobApi` hoặc `updateService` trong `JobService`.
3.  **Bước 3 (Service xử lý):**
    *   **a. Chuẩn bị dữ liệu:** Service xử lý dữ liệu đầu vào, ví dụ như tạo `slug` từ `name`, xử lý địa chỉ.
    *   **b. Lưu vào CSDL:** Service sử dụng `JobRepository` để tạo (`create`) hoặc cập nhật (`update`) bản ghi trong bảng `jobs`.
    *   **c. Ghi log:** Service tạo một bản ghi trong `job_logs` để ghi lại hành động (ví dụ: "NTD [Tên] đã tạo tin tuyển dụng [Tên Job]").

4.  **Bước 4 (Phản hồi & Thông báo):** Service trả về đối tượng Job vừa được tạo/cập nhật. Controller trả về một redirect response đến trang quản lý tin tuyển dụng, kèm theo một thông báo thành công. Đồng thời, một trong các Notification sau sẽ được gửi:
    *   **`RegisterJob` (gửi cho NTD):** Nếu là tạo mới job thành công.
    *   **`UpdateJob` (gửi cho NTD):** Nếu là cập nhật job thành công.

#### 4.2. Quản lý danh sách tin tuyển dụng (dành cho NTD)

*   NTD có một trang dashboard để xem danh sách các tin đã đăng, trạng thái, số lượng ứng viên.
*   Các hành động (Sửa, Tạm dừng, Xóa) đều trỏ đến các phương thức tương ứng trong `JobController` và `JobService`.

#### 4.3. Chức năng tự động của hệ thống

*   **`job:extend-expiration` Command**
    *   **Mô tả:** Command này được sử dụng để tự động gia hạn thời gian hết hạn của các tin tuyển dụng.
    *   **Khi nào được chạy:** Được chạy theo lịch định kỳ (ví dụ: hàng ngày vào lúc 23:00) thông qua Laravel Scheduler (`app/Console/Kernel.php`).
    *   **Happy Path:** Command sẽ tìm các tin tuyển dụng thỏa mãn điều kiện gia hạn (ví dụ: sắp hết hạn, có cấu hình cho phép gia hạn tự động) và cập nhật trường `expire_at` của chúng.
    *   **Sad Path / Xử lý lỗi:** Nếu có lỗi trong quá trình cập nhật (ví dụ: lỗi CSDL), lỗi sẽ được ghi log. Command sẽ tiếp tục xử lý các job khác.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi Validation:**
    *   **Nguyên nhân:** NTD nhập thiếu trường bắt buộc (ví dụ: `name`) hoặc sai định dạng (ví dụ: `salary_min` không phải là số).
    *   **Xử lý:** `JobRequest` FormRequest sẽ tự động chặn request, không cho nó đến Controller. Laravel sẽ tự động redirect người dùng trở lại form, kèm theo danh sách các lỗi validation để hiển thị ngay bên dưới các trường bị lỗi.

*   **5.2. Lỗi không có quyền:**
    *   **Nguyên nhân:** Một NTD cố gắng sửa hoặc xóa một tin tuyển dụng không thuộc sở hữu của họ bằng cách thay đổi ID trên URL.
    *   **Xử lý:** Trong `JobService` (hoặc sử dụng Policy), trước khi thực hiện hành động, hệ thống phải kiểm tra `job->employer_id` có khớp với `auth()->id()` hay không. Nếu không, Service sẽ ném ra một `AuthorizationException`. Controller bắt lỗi này và trả về HTTP status `403 Forbidden`.

### 6. Yêu cầu phi chức năng

*   **Bảo mật:** Việc sử dụng FormRequest cho validation và Policy (hoặc kiểm tra thủ công) cho authorization là bắt buộc để ngăn chặn các cuộc tấn công và truy cập trái phép.
*   **Hiệu năng:** Trang danh sách tin tuyển dụng cho UV/CTV phải được cache để giảm tải cho CSDL. Dữ liệu được lấy bởi `JobList.vue` nên được cache ở tầng API.

### 7. Các thông báo liên quan

*   **`RegisterJob` (gửi cho NTD):**
    *   **Mô tả:** Thông báo tạo job mới thành công.
    *   **Nội dung chính:** Tên NTD.
    *   **Kênh:** Mail, Database.
*   **`UpdateJob` (gửi cho NTD):**
    *   **Mô tả:** Thông báo cập nhật job thành công.
    *   **Nội dung chính:** Tên NTD.
    *   **Kênh:** Mail, Database.

