# Tài liệu Đặc tả Deployment & Configuration - RecLand

**<PERSON><PERSON><PERSON> bản:** 2.0  
**Ng<PERSON>y:** 2025-01-09  
**<PERSON><PERSON><PERSON> gi<PERSON>:** AI Assistant

---

## 1. System Requirements

### 1.1. Server Requirements

**Minimum Requirements:**
- **OS:** Ubuntu 20.04 LTS / CentOS 8 / Amazon Linux 2
- **CPU:** 2 vCPU cores
- **RAM:** 4GB
- **Storage:** 50GB SSD
- **Network:** 100 Mbps

**Recommended Requirements:**
- **OS:** Ubuntu 22.04 LTS
- **CPU:** 4 vCPU cores
- **RAM:** 8GB
- **Storage:** 100GB SSD
- **Network:** 1 Gbps

### 1.2. Software Requirements

**Core Stack:**
- **PHP:** 8.0.2 or higher
- **Web Server:** Nginx 1.18+ or Apache 2.4+
- **Database:** MySQL 8.0 or MariaDB 10.6+
- **Cache:** Redis 6.0+
- **Process Manager:** Supervisor
- **SSL:** Let's Encrypt or commercial certificate

**PHP Extensions:**
```bash
php8.1-cli
php8.1-fpm
php8.1-mysql
php8.1-redis
php8.1-curl
php8.1-gd
php8.1-mbstring
php8.1-xml
php8.1-zip
php8.1-bcmath
php8.1-intl
php8.1-soap
```

---

## 2. Environment Configuration

### 2.1. Environment Variables

**File:** `.env`

```env
# Application
APP_NAME="RecLand"
APP_ENV=production
APP_KEY=base64:your-32-character-secret-key
APP_DEBUG=false
APP_URL=https://recland.com
ASSET_VERSION=20250624

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=recland_production
DB_USERNAME=recland_user
DB_PASSWORD=secure_password

# Secondary Database (for data migration)
DB_CONNECTION_OLD=mysql
DB_HOST_OLD=old-server.com
DB_PORT_OLD=3306
DB_DATABASE_OLD=recland_old
DB_USERNAME_OLD=old_user
DB_PASSWORD_OLD=old_password

# Cache & Session
CACHE_DRIVER=redis
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=redis_password
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# Queue
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mailgun_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="RecLand"

# AWS S3 Storage
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=secret_key
AWS_DEFAULT_REGION=ap-southeast-1
AWS_BUCKET=recland-storage
AWS_URL=https://recland.s3.ap-southeast-1.amazonaws.com
AWS_CLOUDFRONT_URL=https://cdn.recland.com
FILESYSTEM_DISK=s3

# Payment Gateway
ZALO_APPID=56770
ZALO_KEY1=your_zalo_key1
ZALO_KEY2=your_zalo_key2
ZALO_ENDPOINT=https://openapi.zalopay.vn/v2/create
ZALO_QUERY=https://openapi.zalopay.vn/v2/query

# External APIs
N8N_WEBHOOK_URL=https://n8n.hri.com.vn
MATCH_CV_API_KEY=hri@1008
ITNAVI_API_KEY=your_itnavi_key
ITNAVI_API_BASE_URL=https://api.itnavi.com

# Social Login
GOOGLE_APP_ID=your_google_client_id
GOOGLE_APP_SECRET=your_google_secret
GOOGLE_APP_CALLBACK_URL=https://recland.com/auth/google/callback

FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_secret
FACEBOOK_APP_CALLBACK_URL=https://recland.com/auth/facebook/callback

# Security
BCRYPT_ROUNDS=12
PASSWORD_TIMEOUT=10800

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Monitoring
DEBUGBAR_ENABLED=false
TELESCOPE_ENABLED=false
```

### 2.2. Configuration Files

#### 2.2.1. Database Configuration

**File:** `config/database.php`

```php
'connections' => [
    'mysql' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST', '127.0.0.1'),
        'port' => env('DB_PORT', '3306'),
        'database' => env('DB_DATABASE'),
        'username' => env('DB_USERNAME'),
        'password' => env('DB_PASSWORD'),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'strict' => false,
        'engine' => 'InnoDB',
        'options' => [
            PDO::ATTR_TIMEOUT => 30,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        ],
    ],
    
    'mysql_old' => [
        'driver' => 'mysql',
        'host' => env('DB_HOST_OLD'),
        'port' => env('DB_PORT_OLD', '3306'),
        'database' => env('DB_DATABASE_OLD'),
        'username' => env('DB_USERNAME_OLD'),
        'password' => env('DB_PASSWORD_OLD'),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
    ],
],

'redis' => [
    'client' => env('REDIS_CLIENT', 'phpredis'),
    'default' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_DB', '0'),
    ],
    'cache' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD'),
        'port' => env('REDIS_PORT', '6379'),
        'database' => env('REDIS_CACHE_DB', '1'),
    ],
],
```

#### 2.2.2. File Storage Configuration

**File:** `config/filesystems.php`

```php
'default' => env('FILESYSTEM_DISK', 's3'),

'disks' => [
    'local' => [
        'driver' => 'local',
        'root' => storage_path('app'),
        'throw' => false,
    ],
    
    'public' => [
        'driver' => 'local',
        'root' => storage_path('app/public'),
        'url' => env('APP_URL').'/storage',
        'visibility' => 'public',
        'throw' => false,
    ],
    
    's3' => [
        'driver' => 's3',
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION'),
        'bucket' => env('AWS_BUCKET'),
        'url' => env('AWS_URL'),
        'endpoint' => env('AWS_ENDPOINT'),
        'use_path_style_endpoint' => false,
        'throw' => false,
    ],
],

'aws_url' => env('AWS_CLOUDFRONT_URL'),
```

#### 2.2.3. Payment Configuration

**File:** `config/payment.php`

```php
return [
    'zalo_appid' => env('ZALO_APPID', 56770),
    'zalo_key1' => env('ZALO_KEY1'),
    'zalo_key2' => env('ZALO_KEY2'),
    'zalo_endpoint' => env('ZALO_ENDPOINT', 'https://openapi.zalopay.vn/v2/create'),
    'zalo_query' => env('ZALO_QUERY', 'https://openapi.zalopay.vn/v2/query'),
];
```

---

## 3. Dependencies Management

### 3.1. Composer Dependencies

**File:** `composer.json`

```json
{
    "require": {
        "php": "^8.0.2",
        "laravel/framework": "^9.2",
        "laravel/sanctum": "^2.14.1",
        "laravel/socialite": "^5.5",
        "backpack/crud": "^5.6",
        "backpack/pro": "^1.6",
        "league/flysystem-aws-s3-v3": "3.0",
        "owen-it/laravel-auditing": "^13.6",
        "maatwebsite/excel": "^3.1",
        "barryvdh/laravel-dompdf": "2.2",
        "predis/predis": "^2.2",
        "guzzlehttp/guzzle": "^7.2"
    },
    "require-dev": {
        "fakerphp/faker": "^1.9.1",
        "laravel/pint": "^1.0",
        "laravel/sail": "^1.0.1",
        "mockery/mockery": "^1.4.4",
        "nunomaduro/collision": "^6.1",
        "phpunit/phpunit": "^9.5.10",
        "spatie/laravel-ignition": "^1.0"
    }
}
```

### 3.2. NPM Dependencies

**File:** `package.json`

```json
{
    "devDependencies": {
        "@popperjs/core": "^2.11.5",
        "axios": "^1.1.2",
        "bootstrap": "^5.2.3",
        "laravel-mix": "^6.0.49",
        "lodash": "^4.17.19",
        "postcss": "^8.1.14",
        "resolve-url-loader": "^5.0.0",
        "sass": "^1.56.1",
        "sass-loader": "^13.1.0",
        "vue": "^3.2.31",
        "vue-loader": "^17.0.0"
    },
    "dependencies": {
        "bootstrap-vue-next": "^0.5.1",
        "vue-router": "^4.1.6",
        "vuex": "^4.0.2"
    }
}
```

---

## 4. Server Configuration

### 4.1. Nginx Configuration

**File:** `/etc/nginx/sites-available/recland.com`

```nginx
server {
    listen 80;
    listen [::]:80;
    server_name recland.com www.recland.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name recland.com www.recland.com;

    root /www/wwwroot/production/public;
    index index.php index.html index.htm;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/recland.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/recland.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # File Upload Limits
    client_max_body_size 100M;
    client_body_timeout 120s;
    client_header_timeout 120s;

    # Static Files Caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|woff|svg|ttf|eot|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Laravel Application
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM Configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;

        # Increase timeouts for long-running requests
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.env {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Admin Panel (optional separate subdomain)
    location /admin {
        try_files $uri $uri/ /index.php?$query_string;

        # IP Whitelist for admin (optional)
        # allow ***********/24;
        # deny all;
    }

    # API Rate Limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Logging
    access_log /var/log/nginx/recland.access.log;
    error_log /var/log/nginx/recland.error.log;
}

# Rate Limiting Zones
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
}
```

### 4.2. PHP-FPM Configuration

**File:** `/etc/php/8.1/fpm/pool.d/recland.conf`

```ini
[recland]
user = www-data
group = www-data
listen = /var/run/php/php8.1-fpm-recland.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000

; Performance tuning
request_terminate_timeout = 300
request_slowlog_timeout = 10
slowlog = /var/log/php8.1-fpm-slow.log

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP settings
php_admin_value[error_log] = /var/log/php8.1-fpm-recland.log
php_admin_flag[log_errors] = on
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/sessions
php_value[soap.wsdl_cache_dir] = /var/lib/php/wsdlcache

; Upload limits
php_value[upload_max_filesize] = 100M
php_value[post_max_size] = 100M
php_value[max_execution_time] = 300
php_value[max_input_time] = 300
php_value[memory_limit] = 512M
```

### 4.3. MySQL Configuration

**File:** `/etc/mysql/mysql.conf.d/recland.cnf`

```ini
[mysqld]
# Basic Settings
bind-address = 127.0.0.1
port = 3306
datadir = /var/lib/mysql
socket = /var/run/mysqld/mysqld.sock

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# Performance Tuning
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Connection Settings
max_connections = 200
max_connect_errors = 1000
connect_timeout = 60
wait_timeout = 600
interactive_timeout = 600

# Query Cache (if using MySQL 5.7)
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# Binary Logging
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Security
local-infile = 0
```

### 4.4. Redis Configuration

**File:** `/etc/redis/redis.conf`

```ini
# Network
bind 127.0.0.1
port 6379
protected-mode yes

# Authentication
requirepass your_redis_password

# Memory Management
maxmemory 1gb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log

# Performance
tcp-keepalive 300
timeout 0
tcp-backlog 511
```

---

## 5. Deployment Scripts

### 5.1. Production Deployment Script

**File:** `prod-deploy.sh`

```bash
#!/bin/bash

# Production Deployment Script for RecLand
# Usage: ./prod-deploy.sh

set -e

echo "🚀 Starting RecLand Production Deployment..."

# Configuration
PROJECT_DIR="/www/wwwroot/production"
BACKUP_DIR="/backups/recland"
LOG_FILE="/var/log/deployment.log"

# Create backup directory if not exists
mkdir -p $BACKUP_DIR

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# Function to backup database
backup_database() {
    log "📦 Creating database backup..."
    BACKUP_FILE="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    mysqldump -u $DB_USERNAME -p$DB_PASSWORD $DB_DATABASE > $BACKUP_FILE
    gzip $BACKUP_FILE
    log "✅ Database backup created: $BACKUP_FILE.gz"
}

# Function to backup files
backup_files() {
    log "📦 Creating files backup..."
    BACKUP_FILE="$BACKUP_DIR/files_backup_$(date +%Y%m%d_%H%M%S).tar.gz"
    tar -czf $BACKUP_FILE -C $PROJECT_DIR --exclude=node_modules --exclude=vendor .
    log "✅ Files backup created: $BACKUP_FILE"
}

# Main deployment process
main() {
    log "🔄 Changing to project directory..."
    cd $PROJECT_DIR

    # Set git configuration
    log "⚙️ Configuring git..."
    git config core.fileMode false
    git config --global core.fileMode false

    # Reset any local changes
    log "🔄 Resetting local changes..."
    git reset --hard HEAD

    # Pull latest changes
    log "📥 Pulling latest changes..."
    git pull origin main

    # Set permissions
    log "🔐 Setting file permissions..."
    chown -R www-data:www-data $PROJECT_DIR
    chmod -R 755 $PROJECT_DIR
    chmod -R 775 $PROJECT_DIR/storage $PROJECT_DIR/bootstrap/cache

    # Install/Update Composer dependencies
    log "📦 Installing Composer dependencies..."
    COMPOSER_ALLOW_SUPERUSER=1 composer install --no-dev --no-ansi --no-interaction --prefer-dist --optimize-autoloader

    # Generate optimized autoloader
    log "⚡ Optimizing autoloader..."
    COMPOSER_ALLOW_SUPERUSER=1 composer dumpautoload -o

    # Run database migrations
    log "🗄️ Running database migrations..."
    php artisan migrate --force

    # Seed admin permissions (if needed)
    log "👤 Seeding admin permissions..."
    php artisan db:seed --class=AdminFullPermission --force

    # Generate version for cache busting
    log "🔄 Generating version..."
    php artisan version:generate

    # Clear and optimize caches
    log "🧹 Clearing caches..."
    php artisan optimize:clear
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache

    # Restart services
    log "🔄 Restarting services..."
    systemctl reload nginx
    systemctl restart php8.1-fpm
    systemctl restart redis-server

    # Restart queue workers
    log "👷 Restarting queue workers..."
    supervisorctl restart recland-worker:*

    log "✅ Deployment completed successfully!"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run as root (use sudo)"
    exit 1
fi

# Create backups before deployment
backup_database
backup_files

# Run main deployment
main

# Verify deployment
log "🔍 Verifying deployment..."
if curl -f -s http://localhost > /dev/null; then
    log "✅ Application is responding"
else
    log "❌ Application is not responding"
    exit 1
fi

log "🎉 Deployment completed successfully!"
```

### 5.2. Queue Worker Supervisor Configuration

**File:** `/etc/supervisor/conf.d/recland-worker.conf`

```ini
[program:recland-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /www/wwwroot/production/artisan queue:work database --sleep=3 --tries=3 --max-time=3600 --memory=512
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/log/supervisor/recland-worker.log
stopwaitsecs=3600
```

### 5.3. Cron Jobs Configuration

**File:** `/etc/cron.d/recland`

```bash
# RecLand Scheduled Tasks
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# Laravel Scheduler (every minute)
* * * * * www-data cd /www/wwwroot/production && php artisan schedule:run >> /dev/null 2>&1

# Database backup (daily at 2 AM)
0 2 * * * root /usr/local/bin/backup-database.sh

# Log rotation (weekly)
0 0 * * 0 root /usr/sbin/logrotate /etc/logrotate.d/recland

# Clear expired sessions (daily at 3 AM)
0 3 * * * www-data cd /www/wwwroot/production && php artisan session:gc

# Update sitemap (daily at 4 AM)
0 4 * * * www-data cd /www/wwwroot/production && php artisan sitemap:generate
```

---

## 6. SSL Certificate Setup

### 6.1. Let's Encrypt Installation

```bash
# Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d recland.com -d www.recland.com

# Auto-renewal setup
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

### 6.2. SSL Configuration Verification

```bash
# Test SSL configuration
sudo nginx -t
sudo systemctl reload nginx

# Verify SSL certificate
openssl s_client -connect recland.com:443 -servername recland.com
```

---

## 7. Monitoring & Logging

### 7.1. Log Rotation Configuration

**File:** `/etc/logrotate.d/recland`

```
/var/log/nginx/recland.*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}

/www/wwwroot/production/storage/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 7.2. System Monitoring Script

**File:** `/usr/local/bin/system-monitor.sh`

```bash
#!/bin/bash

# System monitoring script for RecLand
LOG_FILE="/var/log/system-monitor.log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    log "WARNING: Disk usage is ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
    log "WARNING: Memory usage is ${MEMORY_USAGE}%"
fi

# Check MySQL connection
if ! mysqladmin ping -h localhost --silent; then
    log "ERROR: MySQL is not responding"
fi

# Check Redis connection
if ! redis-cli ping > /dev/null 2>&1; then
    log "ERROR: Redis is not responding"
fi

# Check queue workers
WORKER_COUNT=$(supervisorctl status recland-worker:* | grep RUNNING | wc -l)
if [ $WORKER_COUNT -lt 4 ]; then
    log "WARNING: Only $WORKER_COUNT queue workers are running"
fi
```

---

*Tài liệu Deployment & Configuration hoàn tất với đầy đủ chi tiết về server setup, deployment scripts và monitoring.*
