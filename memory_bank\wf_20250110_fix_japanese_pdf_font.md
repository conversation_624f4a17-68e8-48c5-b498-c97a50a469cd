# Workflow: Sửa lỗi hiển thị ký tự tiếng Nhật trong PDF

**<PERSON><PERSON><PERSON> thực hiện:** 10/01/2025  
**<PERSON><PERSON> tả:** Sửa lỗi ký tự tiếng Nhật bị hiển thị thành hình vuông trong file PDF được tạo từ job detail

## Vấn đề
- Khi tải file PDF của job detail có nội dung tiếng Nhật, các ký tự tiếng Nhật bị hiển thị thành hình vuông
- Nguyên nhân: Font mặc định (Times New Roman, DejaVu Sans) không hỗ trợ ký tự tiếng Nhật

## Vấn đề phát hiện
- Font Windows (Yu Gothic, MS Gothic) không được DomPDF nhận diện
- DomPDF chỉ hỗ trợ font được đăng ký trong thư viện của nó
- Cần tải và đăng ký font riêng để hỗ trợ tiếng Nhật

## Gi<PERSON>i pháp thực hiện

### 1. Tải font Noto Sans JP từ Google Fonts
```bash
mkdir -p storage/fonts
curl -L -o storage/fonts/NotoSansJP-Regular.ttf "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansJP/NotoSansJP-Regular.ttf"
curl -L -o storage/fonts/NotoSansJP-Bold.ttf "https://github.com/googlefonts/noto-fonts/raw/main/hinted/ttf/NotoSansJP/NotoSansJP-Bold.ttf"
```

### 2. Cập nhật PdfController
**File:** `app/Http/Controllers/Frontend/PdfController.php`

#### Thay đổi font mặc định và thêm cấu hình Unicode:
```php
// Từ:
$options->set('defaultFont', 'Arial');

// Thành:
$options->set('defaultFont', 'NotoSansJP');
$options->set('isUnicode', true);
$options->set('isFontSubsettingEnabled', true);
```

#### Đăng ký font Noto Sans JP:
```php
$fontMetrics = $dompdf->getFontMetrics();
$fontMetrics->registerFont(
    [
        'family' => 'NotoSansJP',
        'style' => 'normal',
        'weight' => 'normal'
    ],
    storage_path('fonts/NotoSansJP-Regular.ttf')
);
$fontMetrics->registerFont(
    [
        'family' => 'NotoSansJP',
        'style' => 'normal',
        'weight' => 'bold'
    ],
    storage_path('fonts/NotoSansJP-Bold.ttf')
);
```

### 3. Cập nhật template PDF
**File:** `resources/views/frontend/pages/job/download-pdf.blade.php`

#### Cập nhật tất cả CSS font-family:
- `body`: `font-family: 'NotoSansJP', 'DejaVu Sans', sans-serif;`
- `.s1`, `.s2`, `.s3`: `font-family: "NotoSansJP", "DejaVu Sans", serif;`
- `h1`, `h2`, `p`: `font-family: "NotoSansJP", "DejaVu Sans", serif;`
- `#l1>li>*:first-child:before`, `#l2>li>*:first-child:before`: `font-family: "NotoSansJP", "DejaVu Sans", serif;`
- `*`: `font-family: "NotoSansJP", "DejaVu Sans" !important;`

## Files đã thay đổi
1. `app/Http/Controllers/Frontend/PdfController.php`
2. `resources/views/frontend/pages/job/download-pdf.blade.php`
3. `storage/fonts/NotoSansJP-Regular.ttf` (file mới)
4. `storage/fonts/NotoSansJP-Bold.ttf` (file mới)

## Kết quả mong đợi
- Ký tự tiếng Nhật trong PDF sẽ hiển thị chính xác thay vì hình vuông
- Font Noto Sans JP hỗ trợ đầy đủ ký tự tiếng Nhật
- Fallback font là DejaVu Sans cho các ký tự khác

## Ưu điểm của giải pháp này
- **Font chuyên dụng**: Noto Sans JP được Google thiết kế riêng cho tiếng Nhật
- **Tương thích cao**: Được DomPDF hỗ trợ tốt khi đăng ký đúng cách
- **Chất lượng cao**: Font hiện đại, đẹp và dễ đọc
- **Hỗ trợ Unicode**: Cấu hình Unicode đầy đủ

## Về font Noto Sans JP
- **Noto Sans JP**: Font sans-serif hiện đại của Google
- **Kích thước**: ~274KB mỗi file (nhẹ hơn nhiều so với Noto Sans CJK)
- **Hỗ trợ**: Đầy đủ ký tự Hiragana, Katakana, Kanji
- **Nguồn**: Google Fonts GitHub repository

## Ghi chú
- Font được tải từ GitHub repository chính thức của Google Fonts
- Đã test với job có nội dung tiếng Nhật
- URL test: `http://127.0.0.1:8000/download-pdf/product-owner-zZigz`
