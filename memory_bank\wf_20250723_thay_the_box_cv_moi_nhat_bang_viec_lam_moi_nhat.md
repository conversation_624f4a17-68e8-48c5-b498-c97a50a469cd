# Workflow: Thay thế box "CV đã gửi gần đây nhất" bằng "Việc làm mới nhất"

**<PERSON><PERSON><PERSON> thực hiện:** 23/07/2025  
**<PERSON><PERSON><PERSON> tiêu:** Thay thế box hiện tại có tên "cvdaguigandaynhat" bằng một box mới có tiêu đề "Việc làm mới nhất" trong dashboard collaborator

## <PERSON><PERSON><PERSON> thay đổi đã thực hiện:

### 1. Thêm method mới trong JobRepository
**File:** `app/Repositories/JobRepository.php`
- Thêm method `getLatestJobs($limit = 5)` để lấy 5 việc làm mới nhất
- Method này lọc các job đang active, chưa hết hạn và sắp xếp theo thời gian tạo mới nhất
- Include relationship với company và jobMeta

```php
public function getLatestJobs($limit = 5)
{
    $now = Carbon::now()->format('Y-m-d');
    return $this->query()
        ->select('job.*')
        ->where('job.is_active', config('constant.active'))
        ->where('job.status', config('constant.active'))
        ->where('job.expire_at', '>=', $now)
        ->with(['company:id,name,slug', 'jobMeta:job_id,priority'])
        ->orderBy('job.created_at', 'DESC')
        ->limit($limit)
        ->get();
}
```

### 2. Thêm method tương ứng trong JobService
**File:** `app/Services/Frontend/JobService.php`
- Thêm method `getLatestJobs($limit = 5)` để gọi repository

```php
public function getLatestJobs($limit = 5)
{
    return $this->jobRepository->getLatestJobs($limit);
}
```

### 3. Cập nhật RecController
**File:** `app/Http/Controllers/Frontend/RecController.php`
- Thêm việc lấy dữ liệu việc làm mới nhất trong method `index()`
- Truyền biến `$latestJobs` vào view

```php
// Lấy 5 việc làm mới nhất
$latestJobs = $this->jobService->getLatestJobs(5);

// Thêm vào compact
return view('frontend.pages.collaborator.dashboard', compact(
    'totalWareHouseCv',
    'totalSubmitCvService',
    'countJobOnboard',
    'getNewCvs',
    'latestJobs', // Thêm dòng này
    'end',
    'daysOfMonth',
    'statistical',
    'arrLang',
    'now',
));
```

### 4. Cập nhật View Dashboard
**File:** `resources/views/frontend/pages/collaborator/dashboard.blade.php`
- Thay thế toàn bộ box "cvdaguigandaynhat" (dòng 129-151) bằng box "Việc làm mới nhất"
- Hiển thị thông tin: tên công việc (có link), tên công ty, địa điểm, ngày đăng
- Sử dụng cùng style/layout như box cũ để đảm bảo tính nhất quán

**Cấu trúc mới:**
```blade
<div class="card card-type3 mt-3">
    <div class="header">
        Việc làm mới nhất
    </div>
    <div class="list-new-cv">
        @if($latestJobs && $latestJobs->count() > 0)
            @foreach($latestJobs as $job)
                <div class="item-new-cv">
                    <div class="date-time">
                        <span class="date">{{$job->created_at->format('d/m')}}</span> 
                        {{$job->created_at->diffForHumans()}}
                    </div>
                    <div class="job-name">
                        <a href="{{ route('job-detail', ['slug' => $job->slug]) }}" 
                           class="text-decoration-none text-dark">
                            {{$job->name}}
                        </a>
                    </div>
                    <div class="name">
                        @if($job->company) 
                            {{$job->company->name}} 
                        @endif
                        @if($job->group_address)
                            @php
                                $firstAddress = array_values($job->group_address)[0] ?? null;
                            @endphp
                            @if($firstAddress)
                                - {{$firstAddress['city']}}
                            @endif
                        @endif
                    </div>
                </div>
            @endforeach
        @else
            <div class="item-new-cv">
                <div class="name text-muted">
                    Chưa có việc làm mới nào
                </div>
            </div>
        @endif
    </div>
</div>
```

## Tính năng đã hoàn thành:

✅ **Hiển thị 5 việc làm mới nhất** - Lấy từ database với điều kiện active và chưa hết hạn  
✅ **Sử dụng cùng style/layout** - Giữ nguyên class CSS và cấu trúc HTML như box cũ  
✅ **Hiển thị thông tin cơ bản** - Tên công việc, tên công ty, địa điểm, ngày đăng  
✅ **Có thể click để xem chi tiết** - Link đến trang chi tiết việc làm  
✅ **Sắp xếp theo thứ tự mới nhất** - ORDER BY created_at DESC  

## Ghi chú:
- Route `job-detail` đã tồn tại và hoạt động bình thường
- Sử dụng attribute `group_address` của model Job để hiển thị địa điểm
- Fallback hiển thị "Chưa có việc làm mới nào" khi không có dữ liệu
- Giữ nguyên các class CSS để đảm bảo giao diện nhất quán
