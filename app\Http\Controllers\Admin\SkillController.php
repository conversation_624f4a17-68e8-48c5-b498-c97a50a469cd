<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SkillRequest;
use App\Services\Admin\SkillService;
use Illuminate\Http\Request;

class SkillController extends Controller
{

    protected $skillService;

    public function __construct(SkillService $skillService)
    {
        $this->skillService = $skillService;
    }

    public function index()
    {
        $datatable = $this->skillService->buildDatatable();
        return view('admin.pages.skill.index',
            compact('datatable'));
    }

    public function store(SkillRequest $request)
    {
        return $this->skillService->createService($request->all());
    }

    public function show($id)
    {
        return $this->skillService->detailService($id);
    }

    public function update(SkillRequest $request, $id)
    {
        $data = $this->skillService->updateService($request->all(), $id);
        return $data;
    }

    public function datatable(Request $request)
    {
        $data = $this->skillService->datatable($request->all(), true);
        return response($data);
    }
}
