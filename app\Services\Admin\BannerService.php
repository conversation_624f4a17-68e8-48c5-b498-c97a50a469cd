<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\BannerRepository;
use App\Services\FileServiceS3;

class BannerService
{

    protected $bannerRepository;

    public function __construct(BannerRepository $bannerRepository)
    {
        $this->bannerRepository = $bannerRepository;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderLogo',
                    'data-mdata' => 'image_url_vn',
                ),
                'value' => 'Hình ảnh VN'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderLogo',
                    'data-mdata' => 'image_url_en',
                ),
                'value' => 'Hình ảnh EN'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'position_value',
                ),
                'value' => 'Khu vực hiển thị'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'type_value',
                ),
                'value' => 'Role'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('banner-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->bannerRepository->getListBanner($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()
        ];
        return $response;
    }

    public function createService(array $request)
    {
        $request['img_vn'] = FileServiceS3::getInstance()->uploadToS3($request['img_vn'], config('constant.sub_path_s3.banner'));
        $request['img_en'] = FileServiceS3::getInstance()->uploadToS3($request['img_en'], config('constant.sub_path_s3.banner'));

        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->bannerRepository->create($request);
    }

    public function detailService($id)
    {
        return $this->bannerRepository->find($id);
    }

    public function updateService(array $request, $id)
    {
        if (isset($request['img_vn'])) {
            $request['img_vn'] = FileServiceS3::getInstance()->uploadToS3($request['img_vn'], config('constant.sub_path_s3.banner'));
        }

        if (isset($request['img_en'])) {
            $request['img_en'] = FileServiceS3::getInstance()->uploadToS3($request['img_en'], config('constant.sub_path_s3.banner'));
        }

        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->bannerRepository->update($id, [], $request);
    }

}
