<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PayOnboard implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;
    public $percent;

    public function __construct($wareHouseCvSellingBuyId, $percent)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->percent = $percent;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
//        14 => 'Trial work',  16 => 'Success Recruitment',
        if (($wareHouseCvSellingBuy->status_recruitment == 14 || $wareHouseCvSellingBuy->status_recruitment == 16) &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5) ) {

            $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
            $wareHouseCvSellingBuyService->payOnboardCtv($wareHouseCvSellingBuy, $this->percent);
        }
    }




}
