<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintsAcceptedToEmployerSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $point;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv,$point = 0)
    {
        $this->submitCv = $submitCv;
        $this->point = $point;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $employerName = $this->submitCv->employer->name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $point          = $this->point;
        $employerWallet = route('employer-wallet');
        $marketCv       = route('market-cv');
        $link           = route('employer-submitcv', ['view-detail' => $this->submitCv->id]);
        return (new MailMessage)
            ->view('email.complaintsAcceptedToEmployerSubmit', [
                'employerName'   => $employerName,
                'companyName'    => $employerName,
                'position'       => $position,
                'point'          => $point,
                'employerWallet' => $employerWallet,
                'marketCv'       => $marketCv,
                'link'           => $link,
                'submitCvId'     => $this->submitCv->id
            ])
            ->subject('[Recland][Case #'.$this->submitCv->id.'] Kết quả khiếu nại');
    }

}
