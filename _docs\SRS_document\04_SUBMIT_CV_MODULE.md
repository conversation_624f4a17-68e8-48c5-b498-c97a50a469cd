# Module Giới thiệu Ứng viên (Submit CV Module)

## 1. Tổng quan

Module Giới thiệu Ứng viên là chức năng core của hệ thống RecLand, cho phép cộng tác viên giới thiệu ứng viên phù hợp cho các vị trí tuyển dụng và theo dõi quá trình tuyển dụng để nhận hoa hồng.

## 2. Quy trình Giới thiệu Ứng viên

### 2.1. Workflow Tổng quan
```
CTV tìm job → Chọn/Upload CV → Submit → NTD review → Interview → Onboard → Commission
```

### 2.2. Chi tiết Các Bước

#### Bước 1: T<PERSON><PERSON> kiếm Job phù hợp
- CTV browse danh sách job
- Xem chi tiết yêu cầu và hoa hồng
- <PERSON><PERSON>h gi<PERSON> độ phù hợp với ứng viên

#### Bước 2: <PERSON><PERSON><PERSON> b<PERSON> sơ
- Option 1: Chọn CV từ kho có sẵn
- Option 2: Upload CV mới
- Option 3: Nhập thông tin ứng viên trực tiếp

#### Bước 3: Submit Hồ sơ
- Điền thông tin bổ sung
- Viết lời giới thiệu
- Xác nhận và gửi

#### Bước 4: Theo dõi Tiến trình
- Nhận thông báo cập nhật trạng thái
- Trao đổi với NTD qua hệ thống
- Cập nhật thông tin khi cần

#### Bước 5: Nhận Hoa hồng
- Khi ứng viên onboard thành công
- Tiền vào ví điện tử
- Có thể rút về tài khoản

## 3. Chức năng Chi tiết

### 3.1. Form Giới thiệu Ứng viên

#### 3.1.1. Thông tin Ứng viên
- **Thông tin cơ bản**:
  - Họ tên (*) 
  - Email (*)
  - Số điện thoại (*)
  - Ngày sinh
  - Giới tính
  - Địa chỉ hiện tại

- **Thông tin nghề nghiệp**:
  - Vị trí hiện tại
  - Công ty hiện tại
  - Năm kinh nghiệm
  - Mức lương hiện tại
  - Mức lương mong muốn

#### 3.1.2. CV và Hồ sơ
- **Upload CV**: 
  - Format: PDF, DOC, DOCX
  - Size: Max 5MB
  - Multiple files support

- **CV từ kho**:
  - Search theo tên, skill
  - Preview trước khi chọn
  - Check duplicate submit

#### 3.1.3. Thông tin Bổ sung
- **Lời giới thiệu**: 
  - Tại sao phù hợp với vị trí
  - Điểm mạnh của ứng viên
  - Sẵn sàng onboard khi nào

- **Ghi chú nội bộ**:
  - Chỉ CTV và Admin xem được
  - Lưu ý đặc biệt về ứng viên

### 3.2. Quản lý Danh sách Submit

#### 3.2.1. Danh sách Submit CV
- **Filters**:
  - Trạng thái (All/Pending/Interview/Rejected/Hired)
  - Thời gian (Hôm nay/Tuần này/Tháng này)
  - Job/Company
  - Ứng viên

- **Columns hiển thị**:
  - Ứng viên (Tên, SĐT)
  - Vị trí ứng tuyển
  - Công ty
  - Ngày submit
  - Trạng thái hiện tại
  - Hoa hồng dự kiến
  - Actions

- **Bulk Actions**:
  - Export danh sách
  - Gửi nhắc nhở cho NTD

#### 3.2.2. Chi tiết Submit
- **Tab Thông tin**:
  - Thông tin ứng viên đầy đủ
  - CV đã submit
  - Lịch sử trạng thái

- **Tab Trao đổi**:
  - Chat với NTD
  - Lịch sử tin nhắn
  - File đính kèm

- **Tab Lịch sử**:
  - Timeline các sự kiện
  - Ai làm gì, khi nào
  - IP và device info

### 3.3. Hệ thống Trạng thái

#### 3.3.1. Luồng Trạng thái Chính
```
Pending → Viewed → Interview → Hired → Onboarded
   ↓         ↓          ↓         ↓
Rejected  Rejected  Rejected  Cancelled
```

#### 3.3.2. Chi tiết Trạng thái

1. **Pending** (Chờ xem xét)
   - Vừa được submit
   - NTD chưa xem
   - Có thể hủy bởi CTV

2. **Viewed** (Đã xem)
   - NTD đã xem hồ sơ
   - Đang xem xét
   - Auto sau 5 phút mở

3. **Interview** (Phỏng vấn)
   - Đã hẹn phỏng vấn
   - Có thể có nhiều vòng
   - Track kết quả từng vòng

4. **Offered** (Đã offer)
   - Đã gửi offer letter
   - Chờ ứng viên xác nhận
   - Timeout sau 7 ngày

5. **Hired** (Đã tuyển)
   - Ứng viên đồng ý offer
   - Xác nhận ngày onboard
   - Chờ onboard thực tế

6. **Onboarded** (Đã đi làm)
   - Đã đi làm ngày đầu
   - Trigger thanh toán hoa hồng
   - Không thể thay đổi

7. **Rejected** (Từ chối)
   - Không phù hợp
   - Phải có lý do
   - CTV có thể khiếu nại

8. **Cancelled** (Hủy bỏ)
   - Ứng viên không đi làm
   - Hoàn tiền cho NTD
   - Không có hoa hồng

### 3.4. Tính năng Trao đổi (Discussion)

#### 3.4.1. Chat System
- **Participants**: CTV, NTD, Admin (nếu cần)
- **Features**:
  - Real-time messaging
  - File attachment
  - Read receipts
  - Typing indicators

#### 3.4.2. Notification
- Email khi có tin nhắn mới
- In-app notification
- Mobile push (future)

### 3.5. Hệ thống Phỏng vấn

#### 3.5.1. Lên lịch Phỏng vấn
- **Thông tin lịch**:
  - Ngày giờ
  - Địa điểm/Link online
  - Người phỏng vấn
  - Vòng phỏng vấn (1/2/3...)
  - Ghi chú cho ứng viên

- **Notification**:
  - Email cho ứng viên
  - SMS nhắc nhở
  - Calendar invite

#### 3.5.2. Kết quả Phỏng vấn
- Pass/Fail/Pending
- Nhận xét từ interviewer
- Điểm đánh giá (nếu có)
- Next steps

### 3.6. Xử lý Tranh chấp

#### 3.6.1. Khiếu nại từ CTV
- **Lý do khiếu nại**:
  - Reject không hợp lý
  - Không trả hoa hồng
  - Thông tin không chính xác

- **Quy trình**:
  1. CTV submit khiếu nại
  2. Admin review
  3. Thu thập evidence
  4. Quyết định final
  5. Thông báo các bên

#### 3.6.2. Complain từ NTD
- Ứng viên không đúng thông tin
- Ứng viên nghỉ sớm
- Yêu cầu hoàn tiền

## 4. Business Rules

### 4.1. Duplicate Check
- Không cho phép submit cùng 1 ứng viên cho cùng 1 job
- Check theo email + phone
- Thời hạn block: 6 tháng

### 4.2. Commission Rules
- **Điều kiện nhận**:
  - Ứng viên phải làm đủ X ngày (do NTD set)
  - Default: 3 ngày
  - Max: 30 ngày

- **Thời gian thanh toán**:
  - Sau khi đủ điều kiện
  - T+1 working day
  - Auto vào ví

### 4.3. Refund Policy
- **Ứng viên nghỉ trong 7 ngày**: Hoàn 100%
- **Nghỉ trong 8-14 ngày**: Hoàn 50%
- **Sau 14 ngày**: Không hoàn

### 4.4. Quality Control
- **Auto reject nếu**:
  - CV không đúng format
  - Thông tin không đầy đủ
  - Spam content detected

- **Warning cho CTV nếu**:
  - Tỷ lệ reject > 70%
  - Nhiều complain
  - Submit CV rác

## 5. Database Schema

### 5.1. Bảng submit_cvs
```sql
- id
- job_id
- user_id (CTV)
- company_id
- candidate_name
- candidate_email
- candidate_phone
- candidate_birthday
- candidate_gender
- candidate_address
- current_position
- current_company
- years_experience
- current_salary
- expected_salary
- cv_file_path
- warehouse_cv_id (if from warehouse)
- introduction_letter
- internal_note
- status
- reject_reason
- commission_amount
- commission_paid_at
- interview_date
- interview_location
- interview_note
- onboard_date
- confirm_token
- created_at
- updated_at
```

### 5.2. Bảng submit_cv_history_status
```sql
- id
- submit_cv_id
- old_status
- new_status
- changed_by
- reason
- created_at
```

### 5.3. Bảng submit_cv_discuss
```sql
- id
- submit_cv_id
- user_id
- message
- attachments (json)
- is_read
- read_at
- created_at
```

### 5.4. Bảng submit_cv_books (Interview Schedule)
```sql
- id
- submit_cv_id
- interview_date
- interview_time
- interview_type (online/offline)
- interview_location
- interview_link
- interviewer_name
- interview_round
- note
- result (pass/fail/pending)
- feedback
- created_by
- created_at
- updated_at
```

### 5.5. Bảng submit_cv_payment_debits
```sql
- id
- submit_cv_id
- amount
- reason
- status (pending/completed/cancelled)
- processed_at
- created_at
```

## 6. API Endpoints

### 6.1. Submit CV APIs
- `POST /api/jobs/{id}/submit` - Submit CV cho job
- `GET /api/rec/submits` - Danh sách CV đã submit
- `GET /api/rec/submits/{id}` - Chi tiết submit
- `PUT /api/rec/submits/{id}/cancel` - Hủy submit
- `POST /api/rec/submits/{id}/complain` - Khiếu nại

### 6.2. Employer APIs
- `GET /api/employer/submits` - DS CV nhận được
- `PUT /api/employer/submits/{id}/status` - Đổi trạng thái
- `POST /api/employer/submits/{id}/interview` - Lên lịch PV
- `POST /api/employer/submits/{id}/reject` - Từ chối CV

### 6.3. Discussion APIs
- `GET /api/submits/{id}/messages` - Lấy tin nhắn
- `POST /api/submits/{id}/messages` - Gửi tin nhắn
- `PUT /api/submits/{id}/messages/read` - Đánh dấu đã đọc

## 7. Notification & Email

### 7.1. Email Templates
- **Cho CTV**:
  - CV đã được xem
  - Lịch phỏng vấn
  - Kết quả phỏng vấn
  - Ứng viên đã onboard
  - Hoa hồng đã thanh toán

- **Cho NTD**:
  - Có CV mới
  - CTV phản hồi
  - Nhắc nhở xử lý CV

- **Cho Ứng viên**:
  - Lịch phỏng vấn
  - Kết quả phỏng vấn
  - Offer letter

### 7.2. Push Notification
- Real-time status update
- New message alert
- Commission credited

## 8. Reporting & Analytics

### 8.1. Cho CTV
- Tổng CV đã submit
- Tỷ lệ thành công
- Tổng hoa hồng kiếm được
- Top job/company success

### 8.2. Cho Employer
- Conversion funnel
- Time to hire
- Source quality (CTV nào tốt)
- Cost per hire

### 8.3. Cho Admin
- Overall platform metrics
- Top performing CTVs
- Problem detection
- Revenue reports

## 9. Mobile Optimization

### 9.1. Mobile Submit Flow
- Step-by-step form
- Camera CV capture
- Voice note for introduction
- Quick candidate entry

### 9.2. Mobile Tracking
- Push notification for updates
- Quick status check
- Swipe actions

## 10. Future Enhancements

### 10.1. AI Features
- CV parsing và auto-fill
- Matching score calculation
- Salary recommendation
- Success probability prediction

### 10.2. Advanced Features
- Video introduction
- Skill assessment integration
- Background check integration
- Contract generation
