<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\InformationContactsRequest;
use App\Http\Requests\Admin\TestimonialRequest;
use App\Services\Admin\InformationContactService;
use App\Services\Admin\TestimonialService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class InformationContactController extends Controller
{

    protected $informationContactService;

    public function __construct(InformationContactService $informationContactService)
    {
        $this->informationContactService = $informationContactService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->informationContactService->buildDatatable();
        return view('admin.pages.information_contact.index',
            compact('datatable'));
    }

    public function store(InformationContactsRequest $request)
    {
        return $this->informationContactService->createService($request->all());
    }
//
    public function show($id)
    {
        return $this->informationContactService->detailService($id);
    }
//
    public function update(InformationContactsRequest $request, $id)
    {
        return $this->informationContactService->updateService($request->all(), $id);
    }

    public function datatable(Request $request)
    {
        $data = $this->informationContactService->datatable($request->all());
        return response($data);
    }
}
