<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;
use Zoha\Metable;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class WareHouseCv extends BaseModel implements Auditable
{
    use HasFactory, Notifiable;
    use \OwenIt\Auditing\Auditable;
    use Metable;
    use CrudTrait;

    protected $table = 'warehouse_cvs';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $fillable = [
        'user_id',
        'candidate_name',
        'candidate_mobile',
        'candidate_email',
        'candidate_job_title',
        'candidate_salary_expect',
        'candidate_salary_expect_to',
        'candidate_portfolio',
        'candidate_currency',
        'candidate_address',
        'candidate_location',
        'candidate_formwork',
        'candidate_est_timetowork',
        'career',
        'cv_public',
        'cv_private',
        'assessment',
        'main_skill',
        'year_experience',
        'rank',
        'is_active',
        'is_real',
        'source',
        'created_at',
        'updated_at',
    ];
    protected $appends = ['url_cv_private', 'url_cv_public', 'name_cv_private', 'name_cv_public', 'created_at_value', 'updated_at_value', 'arr_main_skill'];
    protected $audit_tags = [];


    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
    // relation morph to meta data
    public function metaData()
    {
        return $this->morphMany(MetaData::class, 'object');
    }

    public function getUrlCvPrivateAttribute()
    {
        return gen_url_file_s3($this->cv_private, '', false);
    }
    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }

    public function getUrlCvPublicAttribute()
    {
        return gen_url_file_s3($this->cv_public, '', false);
    }

    public function getNameCvPrivateAttribute()
    {
        if ($this->cv_private) {
            $path = config('constant.sub_path_s3.cv') . '/';
            $name = str_replace($path, '', $this->cv_private);
            return $name;
        }
        return null;
    }

    public function getNameCvPublicAttribute()
    {
        if ($this->cv_public) {
            $path = config('constant.sub_path_s3.cv') . '/';
            $name = str_replace($path, '', $this->cv_public);
            return $name;
        }
        return null;
    }

    public function getCandidateCurrencyValueAttribute()
    {
        if ($this->candidate_currency == 'USD') {
            return '$';
        }
        return $this->candidate_currency;
    }

    public function routeNotificationForMail($notification)
    {
        return $this->candidate_email;
    }

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }

    public function getUpdatedAtValueAttribute()
    {
        return $this->updated_at ? $this->updated_at->format('d/m/Y H:i') : null;
    }

    public function getCareerValueAttribute()
    {
        $lang = app()->getLocale();
        $career = config('job.career.' . $lang);
        $careerArr = explode(',', $this->career);
        $careers = [];
        foreach ($careerArr as $item) {
            if (isset($career[$item])) {
                $careers[] = $career[$item];
            }
        }
        return $careers;
    }

    public function getArrMainSkillAttribute()
    {
        $arr = [];
        if ($this->main_skill) {
            $jsonToArr = json_decode($this->main_skill, true);
            $arr = array_keys($jsonToArr);
        }
        return $arr;
    }
}
