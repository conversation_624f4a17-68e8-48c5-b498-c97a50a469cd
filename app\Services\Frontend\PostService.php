<?php

namespace App\Services\Frontend;

use App\Helpers\Common;
use App\Repositories\PostMetaRepository;
use App\Repositories\PostRepository;
use App\Repositories\PostSeoRepository;

class PostService
{
    protected $postRepository;
    protected $postMetaRepository;
    protected $postSeoRepository;

    public function __construct(
        PostRepository $postRepository,
        PostMetaRepository $postMetaRepository,
        PostSeoRepository $postSeoRepository
    )
    {
        $this->postRepository = $postRepository;
        $this->postMetaRepository = $postMetaRepository;
        $this->postSeoRepository = $postSeoRepository;
    }

    public function getPostByPositon($position, $type, $limit = 1)
    {
        return $this->postRepository->getPostByPositon($position, $type, $limit);
    }


    public function getPostByPositonCategory($category_id, $position, $type, $limit = 10)
    {
        return $this->postRepository->getPostByPositonCategory($category_id, $position, $type, $limit);
    }

    public function getPostByView($limit = 6)
    {
        return $this->postRepository->getPostByView($limit);
    }

    public function getPostRandom($limit = 6)
    {
        return $this->postRepository->getPostRandom($limit);
    }

    public function getRandom($params, $limit = 0)
    {
        $data = $this->postRepository->getRandom($params, $limit);
        return $data;
    }
    public function getPostPaginate($params)
    {
        $data = $this->postRepository->getListPost($params, [], true, 'created_at', 'desc');
        return $data;
    }

    public function detailPost($arrslug = [], $params = [])
    {
        if (count($arrslug)) {
            $blog = $this->postRepository->findBySlug($arrslug);

            $this->postMetaRepository->updateIncrementTotalView($blog->id);

            return $blog;
        } else if (count($params)) {
            return $this->postRepository->findNextPrevBySlug($params);
        }
    }

    public function getPostNewFirst(){
        return $this->postRepository->getPostNewFirst();
    }

    public function getPostNew($take= 10){
        return $this->postRepository->getPostNew($take);
    }

    public function getKeySearchPost($key){
        return $this->postRepository->searchPost($key);
    }

}
