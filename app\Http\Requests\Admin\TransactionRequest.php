<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
            {
                return [
                    'name'           => 'required',
                    'money'          => 'required|numeric',
                    'payment_date'          => 'required',
                    'payment_method' => 'required',
                    'file.*'         => "mimes:doc,docx,pdf,png,jpg,jpeg|max:10000",
                    'file'           => "max:5",
                ];
            }
            default:
                break;
        }
    }

    public function messages()
    {
        return [
            'required'     => __('message.required'),
            'mimes'        => __('message.mimes'),
            'numeric'      => __('message.number'),
            'file.*.mimes' => __('message.file_multiple_mimes', ['mimes' => 'doc,docx,pdf,png,jpg,jpeg']),
            'file.*.max'   => __('message.file_multiple_max_size', ['max' => 10]),
            'file.max'     => __('message.file_multiple_max_length', ['max' => 5]),
        ];
    }
}
