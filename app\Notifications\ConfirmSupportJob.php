<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ConfirmSupportJob extends Notification
{
    use Queueable;

    protected $job;
    protected $warehouseCv;
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($job, $warehouseCv, $user)
    {

        $this->job = $job;
        $this->warehouseCv = $warehouseCv;
        $this->user = $user;

    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $employer = $this->user;
        $candidate = $this->warehouseCv;
        $job = $this->job;
        return (new MailMessage)
            ->view('email.verifyJobSupport', compact('employer', 'candidate', 'job'))
            ->subject('[HRI RECLAND] [ XÁC NHẬN HỖ TRỢ GIỚI THIỆU CÔNG VIỆC ]');
    }


    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
