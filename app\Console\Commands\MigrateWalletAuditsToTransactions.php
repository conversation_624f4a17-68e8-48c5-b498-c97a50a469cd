<?php

namespace App\Console\Commands;

use App\Models\Audit;
use App\Models\SubmitCv;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateWalletAuditsToTransactions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallet:migrate-audits 
        {--chunk=100 : Số lượng bản ghi xử lý mỗi lần} 
        {--from=null : <PERSON><PERSON><PERSON> bắt đầu (Y-m-d)} 
        {--to=null : <PERSON><PERSON><PERSON> kết thúc (Y-m-d)} 
        {--skip-processed : Bỏ qua các audit đã được xử lý} 
        {--dry-run : Ch<PERSON>y thử không lưu dữ liệu}
        {--audit-id= : ID của audit cụ thể cần xử lý}
        {--wallet-id= : ID của ví cụ thể cần xử lý}
        {--field= : Tr<PERSON><PERSON>ng cần xử lý (amount, price hoặc all)}';
    // {--verbose : <PERSON><PERSON><PERSON> thị thông tin chi tiết}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Di chuyển dữ liệu từ bảng audits sang wallet_transactions cho các giao dịch ví';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Bắt đầu di chuyển dữ liệu từ bảng audits sang wallet_transactions');

        $chunkSize = $this->option('chunk');
        $fromDate = $this->option('from') !== 'null' ? $this->option('from') : null;
        $toDate = $this->option('to') !== 'null' ? $this->option('to') : null;
        $skipProcessed = $this->option('skip-processed');
        $dryRun = $this->option('dry-run');
        $auditId = $this->option('audit-id');
        $walletId = $this->option('wallet-id');
        $field = $this->option('field') ?: 'all';
        $verbose = $this->option('verbose');

        if (!in_array($field, ['amount', 'price', 'all'])) {
            $this->error('Trường không hợp lệ. Chỉ chấp nhận: amount, price hoặc all');
            return Command::FAILURE;
        }

        if ($dryRun) {
            $this->warn('Chế độ chạy thử: Không lưu dữ liệu vào cơ sở dữ liệu');
        }

        $query = Audit::query()
            ->where('auditable_type', 'App\\Models\\Wallet')
            ->where('event', 'updated');

        if ($fromDate) {
            $query->where('created_at', '>=', Carbon::parse($fromDate)->startOfDay());
        }

        if ($toDate) {
            $query->where('created_at', '<=', Carbon::parse($toDate)->endOfDay());
        }

        if ($auditId) {
            $query->where('id', $auditId);
        }

        if ($walletId) {
            $query->where('auditable_id', $walletId);
        }

        // Hiển thị thống kê trước khi xử lý
        $this->showStatistics($query);

        $total = $query->count();
        $this->info("Tổng số bản ghi cần xử lý: {$total}");

        if ($total === 0) {
            $this->warn('Không có bản ghi nào để xử lý.');
            return Command::SUCCESS;
        }

        $bar = $this->output->createProgressBar($total);
        $bar->start();

        $processed = 0;
        $success = 0;
        $failed = 0;
        $skipped = 0;

        $query->orderBy('id')->chunk($chunkSize, function ($audits) use (&$processed, &$success, &$failed, &$skipped, $bar, $skipProcessed, $dryRun) {
            if (!$dryRun) {
                DB::beginTransaction();
            }

            try {
                foreach ($audits as $audit) {
                    $processed++;

                    // Kiểm tra xem audit này đã được xử lý chưa
                    if ($skipProcessed && $this->isAuditAlreadyProcessed($audit)) {
                        $this->line("Audit ID {$audit->id} đã được xử lý trước đó, bỏ qua.");
                        $skipped++;
                        $bar->advance();
                        continue;
                    }

                    // Xử lý audit
                    $this->processAudit($audit, $success, $failed, $dryRun);

                    $bar->advance();
                }

                if (!$dryRun) {
                    DB::commit();
                }
            } catch (\Exception $e) {
                if (!$dryRun) {
                    DB::rollBack();
                }

                Log::error('Lỗi khi xử lý audit: ' . $e->getMessage(), [
                    'trace' => $e->getTraceAsString()
                ]);
                $this->error('Lỗi khi xử lý audit: ' . $e->getMessage());
                $failed += count($audits);
            }
        });

        $bar->finish();
        $this->newLine(2);

        $this->info("Hoàn thành di chuyển dữ liệu:");
        $this->info("- Tổng số bản ghi đã xử lý: {$processed}");
        $this->info("- Thành công: {$success}");
        $this->info("- Bỏ qua: {$skipped}");
        $this->info("- Thất bại: {$failed}");

        return Command::SUCCESS;
    }

    /**
     * Xử lý một bản ghi audit
     *
     * @param Audit $audit
     * @param int $success
     * @param int $failed
     * @param bool $dryRun
     * @return void
     */
    private function processAudit(Audit $audit, &$success, &$failed, bool $dryRun = false)
    {
        try {
            $oldValues = $audit->old_values;
            $newValues = $audit->new_values;
            $field = $this->option('field') ?: 'all';
            $verbose = $this->option('verbose');

            if ($verbose) {
                $this->info("Xử lý Audit ID: {$audit->id}, Wallet ID: {$audit->auditable_id}, Event: {$audit->event}");
                $this->info("Old values: " . json_encode($oldValues));
                $this->info("New values: " . json_encode($newValues));
            }

            // Kiểm tra xem có thay đổi về amount hoặc price không
            if (($field === 'amount' || $field === 'all') && isset($oldValues['amount']) && isset($newValues['amount'])) {
                $this->createTransactionFromAudit($audit, 'amount', $oldValues['amount'], $newValues['amount'], $dryRun);
                $success++;
            } elseif (($field === 'price' || $field === 'all') && isset($oldValues['price']) && isset($newValues['price'])) {
                $this->createTransactionFromAudit($audit, 'price', $oldValues['price'], $newValues['price'], $dryRun);
                $success++;
            } else {
                // Không có thay đổi về amount hoặc price hoặc không phải trường cần xử lý
                if ($verbose) {
                    $this->warn("Audit ID {$audit->id} không có thay đổi về amount hoặc price hoặc không phải trường cần xử lý");
                }
                $failed++;
            }
        } catch (\Exception $e) {
            Log::error('Lỗi khi xử lý audit ID ' . $audit->id . ': ' . $e->getMessage(), [
                'audit' => $audit->toArray(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->error('Lỗi khi xử lý audit ID ' . $audit->id . ': ' . $e->getMessage());
            $failed++;
        }
    }

    /**
     * Kiểm tra xem audit đã được xử lý chưa
     *
     * @param Audit $audit
     * @return bool
     */
    private function isAuditAlreadyProcessed(Audit $audit): bool
    {
        return WalletTransaction::where('note', 'like', "Audit ID: {$audit->id}%")->exists();
    }

    /**
     * Tạo giao dịch từ audit
     *
     * @param Audit $audit
     * @param string $field
     * @param float $oldValue
     * @param float $newValue
     * @param bool $dryRun
     * @return void
     */
    private function createTransactionFromAudit(Audit $audit, string $field, float $oldValue, float $newValue, bool $dryRun = false)
    {
        // Tìm ví
        $wallet = Wallet::find($audit->auditable_id);
        if (!$wallet) {
            throw new \Exception("Không tìm thấy ví với ID {$audit->auditable_id}");
        }

        // Tính toán số tiền thay đổi
        $amount = $newValue - $oldValue;

        // Xác định loại giao dịch
        $type = $amount > 0 ? 'deposit' : 'withdraw';

        // Tạo ghi chú
        $note = "Audit ID: {$audit->id} - " . ($amount > 0 ? 'Cộng tiền vào ví' : 'Trừ tiền từ ví');

        $verbose = $this->option('verbose');
        if ($verbose) {
            $this->info("Tạo giao dịch cho ví ID {$wallet->id}, {$field} thay đổi từ {$oldValue} thành {$newValue}, số tiền thay đổi: {$amount}");
        }

        if ($dryRun) {
            $this->info("DRY RUN: Tạo giao dịch cho ví ID {$wallet->id}, {$field} thay đổi từ {$oldValue} thành {$newValue}, số tiền thay đổi: {$amount}");
            return;
        }

        // get Objectable
        $objectable = null;
        $tags = $audit->tags;
        $tags = explode(',', $tags);
        if (count($tags) > 0 && isset($tags[1])) {
            $function_name = $tags[0];
            if (strpos(strtolower($function_name), 'submit') !== false) {
                $submitCv_id = $tags[1];
                $submitCv = SubmitCv::find($submitCv_id);
                $objectable = $submitCv;
            }
        }
        // Tạo giao dịch mới
        if ($field === 'amount') {
            WalletTransaction::createTransaction(
                $wallet,
                $amount,
                $note,
                $type,
                $objectable,
                $newValue,
                $audit->created_at
            );
        } else {
            WalletTransaction::createTransactionPrice(
                $wallet,
                $amount,
                $note,
                $type,
                $objectable,
                $newValue,
                $audit->created_at
            );
        }
    }

    /**
     * Hiển thị thống kê về các audit
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return void
     */
    private function showStatistics($query)
    {
        $this->info('Thống kê audit:');

        // Thống kê theo ngày
        $dailyStats = $query->clone()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->limit(10)
            ->get();

        if ($dailyStats->count() > 0) {
            $this->info('Thống kê theo ngày (10 ngày gần nhất):');
            $this->table(
                ['Ngày', 'Số lượng'],
                $dailyStats->map(function ($item) {
                    return [$item->date, $item->count];
                })
            );
        }

        // Thống kê theo wallet_id
        $walletStats = $query->clone()
            ->selectRaw('auditable_id, COUNT(*) as count')
            ->groupBy('auditable_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        if ($walletStats->count() > 0) {
            $this->info('Thống kê theo wallet_id (10 ví có nhiều audit nhất):');
            $this->table(
                ['Wallet ID', 'Số lượng'],
                $walletStats->map(function ($item) {
                    return [$item->auditable_id, $item->count];
                })
            );
        }

        // Thống kê theo user_id
        $userStats = $query->clone()
            ->selectRaw('user_id, COUNT(*) as count')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        if ($userStats->count() > 0) {
            $this->info('Thống kê theo user_id (10 người dùng có nhiều audit nhất):');
            $this->table(
                ['User ID', 'Số lượng'],
                $userStats->map(function ($item) {
                    return [$item->user_id, $item->count];
                })
            );
        }
    }
}