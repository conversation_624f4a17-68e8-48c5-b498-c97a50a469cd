<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;

class RedirectStagingEmail
{
    public function handle(MessageSending $event)
    {
        // return;
        if (!app()->environment('production')) {
            // if (app()->environment('staging')) {
            $original = [];

            foreach (['Cc', 'Bcc'] as $type) {
                if ($address = $event->message->{'get' . $type}()) {
                    $original[$type] = $address;
                    $event->message->{strtolower($type)}(config('app.debug_email'));
                    // $event->message->{strtolower($type)}(null);
                    // $event->message->{'set' . $type}(null);
                }
            }

            $event->message->to(config('app.debug_email'));

            $event->message->getHeaders()->addTextHeader(
                'X-Original-Emails',
                json_encode($original)
            );
        }
    }
}
