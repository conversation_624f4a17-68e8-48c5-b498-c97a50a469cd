<div class="table-responsive">
    <table class="statistics-table table table-striped">
        <thead>
            <tr>
                <th style="width: 5%">ID Job</th>
                <th style="width: 10%">Công ty</th>
                <th style="width: 8%"><PERSON><PERSON><PERSON> nghề</th>
                <th style="width: 6%">D<PERSON><PERSON> vụ</th>
                <th style="width: 15%">Vị trí đang tuyển</th>
                <th style="width: 8%">Ng<PERSON><PERSON> đăng</th>
                <th style="width: 8%">Đơn giá</th>
                <th style="width: 6%">Số lượng Apply</th>
                <th style="width: 8%">Số lượng CV được duyệt</th>
                <th style="width: 10%">Thành tiền</th>
                <th style="width: 10%">Địa điểm làm việc</th>
                <th style="width: 6%">Tình trạng</th>
            </tr>
        </thead>
        <tbody>
            @if($statistics->count() > 0)
                @foreach($statistics as $job)
                    <tr>
                        <td class="text-center">
                            <strong class="text-primary">#{{ $job->id }}</strong>
                        </td>
                        <td>
                            <div class="font-weight-semibold">{{ $job->company_name ?? 'N/A' }}</div>
                            @if($job->company && $job->company->scale)
                                <small class="text-muted">{{ $job->company->scale }} nhân viên</small>
                            @endif
                        </td>
                        <td>
                            <span class="badge badge-info">{{ $job->career_display }}</span>
                        </td>
                        <td>
                            <small>{{ $job->service_type_display }}</small>
                        </td>
                        <td>
                            <a href="{{ $job->frontend_url }}" target="_blank" class="job-link">
                                {{ $job->name }}
                            </a>
                            @if($job->level == 1)
                                <span class="badge badge-warning ml-1">Cao cấp</span>
                            @endif
                        </td>
                        <td class="text-center">
                            <small class="text-muted">{{ $job->created_date_display }}</small>
                        </td>
                        <td class="number-cell">
                            <strong>{{ number_format($job->bonus ?? 0) }}</strong>
                            <small class="text-muted d-block">VNĐ</small>
                        </td>
                        <td class="number-cell">
                            <span class="badge badge-primary">{{ $job->total_apply }}</span>
                        </td>
                        <td class="number-cell">
                            <span class="badge badge-success">{{ $job->approved_cv_count }}</span>
                        </td>
                        <td class="number-cell">
                            <strong class="text-success">{{ number_format($job->total_amount) }}</strong>
                            <small class="text-muted d-block">VNĐ</small>
                        </td>
                        <td>
                            <i class="fe fe-map-pin mr-1"></i>{{ $job->location_display }}
                        </td>
                        <td>
                            @if($job->status == 1)
                                <span class="status-badge status-active">Đang tuyển</span>
                            @elseif($job->status == 0)
                                <span class="status-badge status-inactive">Dừng tuyển</span>
                            @elseif($job->status == 2)
                                <span class="status-badge status-expired">Hết hạn</span>
                            @else
                                <span class="status-badge">{{ $job->status_display }}</span>
                            @endif
                        </td>
                    </tr>
                @endforeach
                
                <!-- Tổng kết -->
                <tr class="table-info font-weight-bold">
                    <td colspan="7" class="text-right">
                        <strong>TỔNG CỘNG:</strong>
                    </td>
                    <td class="number-cell">
                        <strong>{{ $statistics->sum('total_apply') }}</strong>
                    </td>
                    <td class="number-cell">
                        <strong>{{ $statistics->sum('approved_cv_count') }}</strong>
                    </td>
                    <td class="number-cell">
                        <strong class="text-success">{{ number_format($statistics->sum('total_amount')) }} VNĐ</strong>
                    </td>
                    <td colspan="2"></td>
                </tr>
            @else
                <tr>
                    <td colspan="12" class="text-center py-4">
                        <div class="empty-state">
                            <i class="fe fe-inbox" style="font-size: 48px; color: #6c757d;"></i>
                            <h5 class="mt-3 mb-1">Không có dữ liệu</h5>
                            <p class="text-muted">Không tìm thấy job nào phù hợp với bộ lọc hiện tại.</p>
                        </div>
                    </td>
                </tr>
            @endif
        </tbody>
    </table>
</div>

@if($statistics->hasPages())
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <p class="text-muted mb-0">
                Hiển thị {{ $statistics->firstItem() }} - {{ $statistics->lastItem() }}
                trong tổng số {{ $statistics->total() }} job
            </p>
        </div>
        <div>
            {{ $statistics->appends(request()->all())->links() }}
        </div>
    </div>
@endif

<style>
.empty-state {
    padding: 40px 20px;
}

.table-info td {
    background-color: #d1ecf1 !important;
    border-color: #bee5eb !important;
}

.statistics-table .badge {
    font-size: 10px;
}

.job-link {
    font-weight: 500;
    color: #007bff;
    text-decoration: none;
}

.job-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.number-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-expired {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}
</style>
