<?php

namespace App\Services\Admin;

use App\Repositories\SettingRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SettingService
{
    protected $settingRepository;

    public function __construct(SettingRepository $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    protected $type = 'textarea';

    public function getListSetting($params)
    {
        $settings = [];

        if (isset($params['tab']) && ($params['tab'] == 'vi' || $params['tab'] == 'en')) {
            $data = $this->settingRepository->getShortSettingByKey('settings.' . $params['tab']);
            $settingConfigs = config('settings.' . $params['tab']);
            $arrV = [];
            foreach ($settingConfigs as $k => $v) {
                $keyConfig = 'settings.' . $params['tab'] . '.' . $k;
                $type = 'text';
                //
                $arrItem = [];
                foreach ($v as $k1 => $item) {
                    if ($k1 != 'title') {
                        $kNew = 'settings.' . $params['tab'] . '.' . $k . '.' . $k1;
                        $dataSetting = $data->where('key', $kNew)->first();
                        $groupKey = explode('.', $kNew);
                        $lastKey = end($groupKey);
                        $type = 'text';

                        if ($lastKey == $this->type) {
                            $type = $lastKey;
                        }

                        $arrItem[$kNew]['type'] = $type;
                        $arrItem[$kNew]['value'] = !empty($dataSetting) ? $dataSetting->value : (is_array($item) && key_exists('textarea', $item) ? $item['textarea'] : $item);
                    } else {
                        $kNew = $k1;
                        $arrItem[$kNew] = $item;
                    }
                }
                $arrV[$keyConfig]['type'] = $type;
                $arrV[$keyConfig]['value'] = $arrItem;
                //
            }
            $settings[$params['tab']] = $arrV;
        } else {
            $settingConfigs = config('settings.global');
            $data = $this->settingRepository->getShortSettingByKey('settings.global');
            foreach ($settingConfigs as $k => $v) {
                $keyConfig = 'settings.' . 'global' . '.' . $k;
                $dataSetting = $data->where('key', $keyConfig)->first();

                $groupKey = explode('.', $keyConfig);
                $lastKey = end($groupKey);
                $type = 'text';

                if ($lastKey == $this->type) {
                    $type = $lastKey;
                }
                if (Str::endsWith($k, '-textarea')) {
                    $type = 'textarea';
                }

                $settings['global'][$keyConfig] = [
                    'type' => $type,
                    'value' => !empty($dataSetting) ? $dataSetting->value : (is_array($v) && key_exists('textarea', $v) ? $v['textarea'] : $v),
                ];
            }
        }

        return $settings;

    }

    public function updateService(array $request, $id)
    {
        return $this->settingRepository->update($id, [], $request);
    }

    public function createService(array $request)
    {
        return $this->settingRepository->create($request);
    }

    public function updateAllService(array $request)
    {
        try {
            DB::beginTransaction();
            foreach ($request as $key => $value) {
                $setting = $this->getByKey($key);
                if (!isset($value)) {
                    $value = '';
                }
                if ($setting) {
                    $this->updateService(['key' => $key, 'value' => $value], $setting->id);
                } else {
                    $this->createService(['key' => $key, 'value' => $value]);
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
        }
    }

    public function getByKey($key)
    {
        return $this->settingRepository->getFirst(['key' => $key]);
    }

    public function getAllByKey($str, $lang)
    {
        $key = 'settings.' . $lang . '.' . $str;

        $array = [];
        foreach (config($key) as $k => $item) {
            $kNew = str_replace($key . '.', '', $k);
            $kNew = str_replace('.textarea', '', $kNew);
            $array[$kNew] = $item;
        }

        return $array;
    }

}
