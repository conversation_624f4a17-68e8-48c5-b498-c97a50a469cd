<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\Admin\SubmitCvService;
use Illuminate\Http\Request;

class ConfirmCandidateController extends Controller
{

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        $this->submitCvService = $submitCvService;
    }

    public function index($candidateId, $jobId)
    {
        $checkSuccess = $this->submitCvService->updateCandidateConfirm($candidateId, $jobId);
        return view('frontend.pages.home.confirm-candidate', compact('checkSuccess'));
    }
}
