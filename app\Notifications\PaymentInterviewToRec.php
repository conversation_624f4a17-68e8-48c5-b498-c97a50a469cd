<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentInterviewToRec extends Notification implements ShouldQueue
{
    use Queueable;


    protected $wareHouseCvSellingBuy;
    protected $point;

    public function __construct($wareHouseCvSellingBuy,$point)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->point = $point;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $statusRecruitment = $this->wareHouseCvSellingBuy->status_recruitment;
        $url = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id]);
        $recTurnovers = route('rec-turnovers');
        return (new MailMessage)
            ->view('email.paymentInterviewToRec', [
                'candidateName'     => $candidateName,
                'recName'           => $recName,
                'companyName'       => $companyName,
                'url'               => $url,
                'point'             => $this->point,
                'recTurnovers'      => $recTurnovers,
                'statusRecruitment' => $statusRecruitment
            ])
            ->subject('[RECLAND] Thanh toán thành công Ứng viên Interview - Marketplace');

    }

}

