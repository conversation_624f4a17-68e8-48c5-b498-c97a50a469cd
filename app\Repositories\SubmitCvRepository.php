<?php

namespace App\Repositories;

use App\Models\SubmitCv;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SubmitCvRepository extends BaseRepository
{

    const MODEL = SubmitCv::class;

    public function getListSubmitCv($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->getQueryListSubmitCv($params, false, $order_by, $sort);
        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            if (isset($params['size'])) {
                $query->take($limit);
            }
            return $query->get();
        }
    }

    public function total($params = array())
    {
        $query = $this->query();
        foreach ($params as $key => $param) {
            switch ($key) {
                case 'id':
                    $query->where('id', $param);
                    break;
                case 'user_id':
                    $query->where('user_id', $param);
                    break;
                case 'company_id':
                    $query->where('company_id', $param);
                    break;
                case 'status':
                    $query->where('status', $param);
                    break;
                case 'not_in_status':
                    $query->where('status', '!=', $param);
                    break;
                case 'in_status':
                    $query->whereIn('status', $param);
                    break;
                case 'employer_id':
                    $query->whereHas('job', function ($query) use ($param) {
                        $query->where('employer_id', '=', $param);
                    });
                    break;
                default:
                    break;
            }
        }
        $query->where('is_active', config('constant.active'));
        return $query->count();
    }

    public function statisticalByStatus($userId, $status)
    {
        $start = Carbon::now()->startOfMonth();
        $end = Carbon::now()->endOfMonth();
        $query = $this->query();
        return $query
            ->select(
                DB::raw("(count(id)) as total"),
                DB::raw("(DATE_FORMAT(created_at, '%d')) as day")
            )
            ->where('is_active', config('constant.active'))
            ->where('user_id', $userId)
            ->where('status', $status)
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->groupBy(DB::raw("DATE_FORMAT(created_at, '%d')"))
            ->orderBy(DB::raw("DATE_FORMAT(created_at, '%d')"))
            ->get();
    }

    public function statisticalJobByStatus($userId, $status)
    {
        $start = Carbon::now()->startOfMonth();
        $end = Carbon::now()->endOfMonth();
        $query = $this->query();
        return $query
            ->select(
                DB::raw("(count(id)) as total"),
                DB::raw("(DATE_FORMAT(created_at, '%d')) as day")
            )
            ->whereHas('job', function ($query) use ($userId) {
                $query->where('employer_id', '=', $userId);
            })
            ->where('is_active', config('constant.active'))
            ->where('status', $status)
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->groupBy(DB::raw("DATE_FORMAT(created_at, '%d')"))
            ->orderBy(DB::raw("DATE_FORMAT(created_at, '%d')"))
            ->get();
    }


    /**
     * @param $params
     *
     * @return mixed
     * Danh sach gioi thieu ung vien
     * /rec/submitcv
     */
    public function getListSubmitCvSearch($params)
    {
        $perPage = 5;
        $query = $this->query()->select('submit_cvs.*');
        $query->selectRaw('coalesce((select status from payin_months where submit_cv_id = submit_cvs.id order by id desc limit 1), 0) as status_payment_ctv');
        if (isset($params['user_ids'])) {
            $query->whereIn('user_id', $params['user_ids']);
            $query->with('user');
        }
        if (isset($params['with_meta'])) {
            $query->with('submitCvMeta');
        }
        if (isset($params['job_id'])) {
            $query->where('job_id', $params['job_id']);
        }
        if (isset($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }
        if (isset($params['perPage'])) {
            $perPage = $params['perPage'];
        }
        //tên, email, sdt, tên cong ty , ten job
        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->orWhereHas('submitCvMeta', function ($q) use ($params) {
                    $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_email', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_mobile', 'like', '%' . $params['search'] . '%');
                })
                    ->orWhereHas('company', function ($q) use ($params) {
                        $q->where('name', 'like', '%' . $params['search'] . '%');
                    })
                    ->orWhereHas('job', function ($q) use ($params) {
                        $q->where('name', 'like', '%' . $params['search'] . '%');
                    });
            });
        }
        //status
        if (isset($params['status']) && $params['status'] != 'all') {
            $status = explode(',', $params['status']);
            $query->whereIn('status', $status);
        }

        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }

        $query = $query->orderBy('id', 'DESC')
            ->with('company', 'job', 'warehouseCv', 'employer', 'submitCvMeta')
            ->paginate($perPage, $page);
        return $query;
    }

    public function getSubmitCvByIdUserStatus($id, $userId)
    {
        return $this->query()
            ->where('id', $id)
            ->where('user_id', $userId)
            ->with('company', 'job', 'warehouseCv', 'employer')
            ->first();
    }

    /**
     * @param $id
     * @param $employerId
     *
     * @return mixed
     */
    public function getDetailSubmitCvByEmployer($id, $employerId)
    {
        $query = $this->query()->where('id', $id);
        if (!empty($employerId)) {
            $query->whereHas('job', function ($query) use ($employerId) {
                $query->where('employer_id', '=', $employerId);
            });
        }

        return $query->with('company', 'job', 'warehouseCv', 'employer', 'submitCvMeta')
            ->first();
    }

    /**
     * @param $params
     *
     * @return mixed
     * Search danh sach gioi thieu ung vien
     */
    public function getListSubmitCvByJobSearch($params)
    {
        $query = $this->query()->select('*');
        if (isset($params['job_id']) && $params['job_id'] != '') {
            $query->where('job_id', $params['job_id']);
        }
        //email, sdt
        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->orWhereHas('warehouseCv', function ($q) use ($params) {
                    $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_email', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_mobile', 'like', '%' . $params['search'] . '%');
                });
            });
        }
        $notin_status = config('constant.status_recruitment.ntd_not_display');
        $query->whereNotIn('status', $notin_status);

        //status
        // $status = [
        //     config('constant.submit_cvs_status_value.pending-review'),
        //     config('constant.submit_cvs_status_value.accepted'),
        //     config('constant.submit_cvs_status_value.rejected'),
        //     config('constant.submit_cvs_status_value.pass-interview'),
        //     config('constant.submit_cvs_status_value.fail-interview'),
        //     config('constant.submit_cvs_status_value.onboarded'),
        //     config('constant.submit_cvs_status_value.cancel'),
        //     config('constant.submit_cvs_status_value.draft'),
        //     config('constant.submit_cvs_status_value.offering'),
        // ];
        // $query->whereIn('status', $status);

        //uy quyen
        //authorize = 0 || (authorize == 1 && authorize_status = 1)
        if (isset($params['authorize'])) {
            $query->where(function ($query) {
                $query->where('authorize', '=', 0)
                    ->orWhere(function ($query) {
                        $query->where('authorize', '=', 1)->where('authorize_status', '=', 1);
                    });
            });
        }

        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }

        $query = $query->orderBy('id', 'DESC')
            //            ->with('company', 'job', 'warehouseCv', 'employer')
            ->with('company', 'job', 'warehouseCv', 'employer')
            ->paginate(config('constant.paginate_10'), $page);

        return $query;
    }

    public function getListSubmitByNtd($params)
    {
        $query = $this->query()->select('*');

        // $notin_status = [
        //     config('constant.status_recruitment_revert.Waitingcandidateconfirm'),
        // ];
        $notin_status = config('constant.status_recruitment.ntd_not_display');
        $ntd_priority = config('constant.status_recruitment.ntd_priority');
        // order query, set status in array ntd_priority then priority to top
        $query->orderByRaw('FIELD(status, ' . implode(',', $ntd_priority) . ') DESC');
        $query->orderBy('last_discuss_time', 'DESC');
        $query->orderBy('updated_at', 'DESC');

        $query = $this->querySearchCv($query, $params);
        if (empty($params['user_id'])) {
            $query = $query->whereHas('job');
        }
        if (isset($params['job_slug']) && $params['job_slug']) {
            $query->whereHas('job', function ($query) use ($params) {
                $query->where('slug', $params['job_slug']);
            });
        }

        $query->whereNotIn('status', $notin_status);

        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }

        $query = $query->orderBy('id', 'DESC')
            ->with('company', 'job', 'warehouseCv', 'employer', 'submitCvMeta')
            ->paginate(config('constant.paginate_10'), $page);

        return $query;
    }

    public function querySearchCv($query, $params)
    {
        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->orWhereHas('submitCvMeta', function ($q) use ($params) {
                    $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_email', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_mobile', 'like', '%' . $params['search'] . '%');
                });
            });
        }

        if (isset($params['company_id'])) {
            $query->where('company_id', $params['company_id']);
        }

        if (isset($params['career'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                $query->where(function ($query) use ($params) {
                    foreach ($params['career'] as $career) {
                        $query->orWhereRaw("CONCAT(',', career, ',') like '%," . $career . ",%'");
                    }
                });
            });
        }

        if (isset($params['location'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                $query->where(function ($query) use ($params) {
                    foreach ($params['location'] as $address) {
                        $query->orWhere('candidate_location', 'like', '%' . $address . '%');
                    }
                });
            });
        }

        if (isset($params['skill'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                $query->where(function ($query) use ($params) {
                    foreach ($params['skill'] as $skill) {
                        $query->orWhere('main_skill', 'like', '%' . $skill . '%');
                    }
                });
            });
        }

        if (isset($params['year_experience'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                $query->where('year_experience', $params['year_experience']);
            });
        }

        if (isset($params['candidate_est_timetowork'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                $query->where('candidate_est_timetowork', $params['candidate_est_timetowork']);
            });
        }
        //uy quyen
        //authorize = 0 || (authorize == 1 && authorize_status = 1)
        if (isset($params['authorize'])) {
            $query->where(function ($query) {
                $query->where('authorize', '=', 0)
                    ->orWhere(function ($query) {
                        $query->where('authorize', '=', 1)->where('authorize_status', '=', 1);
                    });
            });
        }
        //manager thi xem tat car, employee thi chi dc xem cua employee
        if (!empty($params['user_id'])) {
            $query->whereHas('job', function ($query) use ($params) {
                $query->where('employer_id', '=', $params['user_id']);
            });
        }
        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }



        //status
        // $status = [
        //     config('constant.submit_cvs_status_value.pending-review'),
        //     config('constant.submit_cvs_status_value.accepted'),
        //     config('constant.submit_cvs_status_value.rejected'),
        //     config('constant.submit_cvs_status_value.pass-interview'),
        //     config('constant.submit_cvs_status_value.fail-interview'),
        //     config('constant.submit_cvs_status_value.onboarded'),
        //     config('constant.submit_cvs_status_value.offering'),
        // ];
        // $query->whereIn('status', $status);

        return $query;
    }

    public function countSubmitCvSearch($params)
    {
        $query = $this->query();
        $query = $this->querySearchCv($query, $params);
        return $query->count();
    }

    public function updateSubmitCvById($id, $userId, $params)
    {
        return $this->query()->where('id', $id)->where('user_id', $userId)->update($params);
    }

    public function getWithLimitOffset($userId, $statusCv, $limit, $offset, $now)
    {
        $startOfMonth = $now->copy()->startOfMonth();
        $dateTmp = strtotime('-60 day', strtotime($startOfMonth));    //- 60 ngay tính từ ngày đầu tháng
        $date = date("Y-m-d", $dateTmp);

        return $this->query()->select('*')
            ->where('user_id', $userId)
            ->where('date_change_status', '>=', $date)
            ->whereIn('status', $statusCv)
            ->where(function ($query) {
                $query->where('authorize', '=', 0)
                    ->orWhere(function ($query) {
                        $query->where('authorize', '=', 1)->where('authorize_status', '=', 1);
                    });
            })
            ->offset($offset)->limit($limit)->get();
    }

    public function getAllSubmitCvWithStatus($status)
    {
        $now = date('Y-m-d') . ' 00:00:00';
        $query = $this->query()->where('status', $status);

        if ($status == config('constant.submit_cvs_status_value.pending-review')) {
            $query->whereRaw("(
                (
                    (DAYOFWEEK(date_change_status) = 2 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 259200)
                    OR
                    (DAYOFWEEK(date_change_status) = 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 345600)
                    OR
                    (DAYOFWEEK(date_change_status) = 1 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 259200)
                    OR
                    (DAYOFWEEK(date_change_status) > 2 AND DAYOFWEEK(date_change_status) < 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 432000)
                )

                    AND

                TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) < 2592000
            )");
        } else {
            $query->whereRaw("(
                (
                    (DAYOFWEEK(date_change_status) = 6 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 1987200)
                    OR
                    (DAYOFWEEK(date_change_status) = 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 1900800)
                    OR
                    (DAYOFWEEK(date_change_status) >= 1 AND DAYOFWEEK(date_change_status) <= 5 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) <= 1814400)
                )

                    AND

                TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) < 2592000
            )");
        }

        return $query->get();
    }

    public function updateCvExpired($status)
    {
        $now = Carbon::now();

        $from_date = $now->subWeekdays(config('settings.global.submit_cv_pending_date', 3));
        //        dd($from_date->format('Y-m-d'));
        //345600/60/60/24 = 4 ngay
        //432000/60/60/24 = 5 ngay
        //259200/60/60/24 = 3 ngay
        //DAYOFWEEK=1 chu nhat, 7 thu 7, 6 thu 6, 5 thu 5

        //thu 3-4-5-6 lay du lieu 5 ngay truoc
        //chu nhat - thu 2 lay du lieu 3 ngay truoc
        //thu 7 lay du lieu 4 ngay truoc
        //tất cả nhỏ hơn 30 ngày
        $now = date('Y-m-d H:i:s');
        $query = $this->query()
            ->where('status', $status)
            ->where('date_change_status', '<', $from_date)
            //            ->whereRaw("(
            //                (
            //                    (DAYOFWEEK(date_change_status) = 2 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 259200)
            //                    OR
            //                    (DAYOFWEEK(date_change_status) = 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 345600)
            //                    OR
            //                    (DAYOFWEEK(date_change_status) = 1 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 259200)
            //                    OR
            //                    (DAYOFWEEK(date_change_status) > 2 AND DAYOFWEEK(date_change_status) < 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 432000)
            //                )
            //
            //                    AND
            //
            //                TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) < 2592000
            //            )")
            ->get();

        return $query;
    }

    public function getCvAcceptExpired($status)
    {
        // $query = $this->query()->where('status', $status)->whereRaw('DATE_ADD(`date_change_status` , INTERVAL 15 DAY) > ' . "'" . $time . "'")->get();
        // return $query;

        //1814400/60/60/24 = 21 ngay
        //1900800/60/60/24 = 22 ngay
        //1987200/60/60/24 = 23 ngay
        //DAYOFWEEK=1 chu nhat, 7 thu 7, 6 thu 6, 5 thu 5

        //chu nhat -> thu 5 lay du lieu 21 ngay truoc
        //thu 6 lay du lieu 23 ngay truoc
        //thu 7 lay du lieu 22 ngay truoc
        //tất cả nhỏ hơn 30 ngày
        $now = Carbon::now();

        $from_date = $now->subWeekdays(config('settings.global.submit_cv_pending_date', 3));

        $now = date('Y-m-d H:i:s');
        $query = $this->query()
            ->where('status', $status)
            ->where('date_change_status', '<', $from_date)
            //            ->whereRaw("(
            //                (
            //                    (DAYOFWEEK(date_change_status) = 6 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 1987200)
            //                    OR
            //                    (DAYOFWEEK(date_change_status) = 7 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 1900800)
            //                    OR
            //                    (DAYOFWEEK(date_change_status) >= 1 AND DAYOFWEEK(date_change_status) <= 5 AND TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) > 1814400)
            //                )
            //
            //                    AND
            //
            //                TIME_TO_SEC(TIMEDIFF('$now', date_change_status)) < 2592000
            //            )")
            ->get();

        return $query;
    }

    public function countGroupCvInJob($groupId, $jobId)
    {
        $not_in_status = [
            config('constant.status_recruitment_revert.CandidateCancelApply'),
            config('constant.status_recruitment_revert.RejectOffer'),
            config('constant.status_recruitment_revert.Cancelonboard'),
            config('constant.status_recruitment_revert.CancelBuyCVdata'),
        ];
        return $this->query()->whereIn('warehouse_cv_id', $groupId)->whereNotIn('status', $not_in_status)->where('job_id', $jobId)->count();
    }

    public function countGroupCvInCompany(array $groupId, $companyId)
    {
        return $this->query()->whereIn('warehouse_cv_id', $groupId)->where('company_id', $companyId)->count();
    }

    public function getWithIdWareHouse($warehouseCv)
    {
        $status = [
            config('constant.submit_cvs_status_value.onboarded'),
        ];
        return $this->query()->whereIn('warehouse_cv_id', $warehouseCv)->whereIn('status', $status)->first();
    }

    public function getListSubmitCvPayment($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }

        if (isset($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        if (isset($params['employer_id'])) {
            $employerId = $params['employer_id'];
            $query->whereHas('job', function ($query) use ($employerId) {
                $query->where('employer_id', '=', $employerId);
            });
        }

        if (isset($params['job_id'])) {
            $query->where('job_id', $params['job_id']);
        }

        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->where('id', $params['search']);
            });
        }
        //bonus_type == onboard => status in $statusOnboard
        //bonus_type == cv => status in $statusCv
        //        $query->whereIn('status', $status);
        $query->whereRaw('IF (`bonus_type` = "onboard", `status` = ?, `status` IN (?,?))', [config('constant.submit_cvs_status_value.onboarded'), config('constant.submit_cvs_status_value.pass-interview'), config('constant.submit_cvs_status_value.fail-interview')]);
        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            if (isset($params['size'])) {
                $query->take($limit);
            }
            return $query->get();
        }
    }

    public function getIdMissing($arrId)
    {
        return $this->query()->whereNotIn('id', $arrId)->get();
    }

    public function updateCandidateConfirm($candidateId, $jobId): bool
    {
        $submitCv = $this->query()->where('job_id', $jobId)->where('warehouse_cv_id', $candidateId)->firstOrFail();
        if (
            Carbon::parse($submitCv->created_at)->addDay(2)->addHour()->greaterThan(Carbon::now()) &&
            $submitCv->status == config('constant.submit_cvs_status_value')['pending-confirm']
        ) {
            // $submitCv->update(['status' => config('constant.submit_cvs_status_value')['admin-review']]);
            $submitCv->update(['status' => config('constant.status_recruitment_revert.Waitingsetupinterview')]);
            return true;
        } elseif ($submitCv->status == config('constant.submit_cvs_status_value')['admin-review']) {
            return true;
        }

        return false;
    }

    public function getQueryListSubmitCv($params, $datatable = false, $order_by = 'id', $sort = 'desc')
    {
        $query = $this->query();
        if ($datatable) {
            $query->with(['warehouseCv', 'submitCvMeta', 'user', 'job', 'job.user']);
        }

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }

        if (isset($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        if (isset($params['status'])) {
            $params['authorized'] = false;
            $query->where('status', $params['status']);
        }
        if (isset($params['employer_id'])) {
            $employerId = $params['employer_id'];
            $query->whereHas('job', function ($query) use ($employerId) {
                $query->where('employer_id', '=', $employerId);
            });
        }
        if (isset($params['job_id'])) {
            $query->where('job_id', $params['job_id']);
        }


        //ủy quyền
        if (isset($params['authorize'])) {

            $query->where('authorize', $params['authorize']);
            if ($params['authorize'] == 1 && isset($params['authorized'])) {
                $query->where('authorize_status', 1);
            }
            $params['authorized'] = false;
        }
        if (isset($params['authorize_status'])) {
            $query->where('authorize_status', $params['authorize_status']);
        }
        if (isset($params['authorized']) && $params['authorized'] == true) {

            $query->where(function ($q) {
                $q->where('authorize', 0)->orWhere([['authorize', '=', 1], ['authorize_status', '=', 1]]);
            });
        }
        if (isset($params['confirm_candidate'])) {

            $query->where(function ($q) use ($params) {
                $q->whereNotNull('confirm_candidate')->where('confirm_candidate', '<', $params['confirm_candidate']);
            });
        }
        if (isset($params['start_date'])) {
            $query->where('created_at', '>', Carbon::createFromFormat('d/m/Y', $params['start_date'])->format('Y-m-d'));
        }

        if (isset($params['end_date'])) {
            $query->where('created_at', '<', Carbon::createFromFormat('d/m/Y', $params['end_date'])->addDay()->format('Y-m-d'));
        }


        if (isset($params['search'])) {
            $query->where(function ($q) use ($params) {
                $q->orWhereHas('submitCvMeta', function ($q) use ($params) {
                    $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_email', 'like', '%' . $params['search'] . '%');
                    $q->orWhere('candidate_mobile', 'like', '%' . $params['search'] . '%');
                })
                    ->orWhereHas('company', function ($q) use ($params) {
                        $q->where('name', 'like', '%' . $params['search'] . '%');
                    })
                    ->orWhereHas('job', function ($q) use ($params) {
                        $q->where('name', 'like', '%' . $params['search'] . '%');
                    });
            });
        }

        $query->orderBy($order_by, $sort);
        // dd($query->toSql(), $query->getBindings());
        return $query;
    }

    public function findToken($token)
    {
        return $this->query()->where('confirm_token', $token)->first();
    }

    public function findByRec($id, $userId)
    {
        $query = $this->query()->select('submit_cvs.*');
        $query->selectRaw('coalesce((select status from payin_months where submit_cv_id = submit_cvs.id order by id desc limit 1), 0) as status_payment_ctv');

        return $query->where('id', $id)->where('user_id', $userId)->first();
    }


    public function changeCvFile($warehouse_cv_id, $params = [])
    {
        $data = [];
        if (isset($params['cv_private']) && $params['cv_private'] && $params['cv_private'] != $params['old_cv_private']) {
            $data['cv_private'] = $params['cv_private'];
        }
        // if (isset($params['cv_public']) && $params['cv_public'] && $params['cv_public'] != $params['old_cv_public']) {
        //     $data['cv_public'] = $params['cv_public'];
        // }
        if (count($data) > 0) {
            # update new link to submit CV
            // $pending_status = [0, 9, 10];
            $pending_status = [1, 21];

            // Sửa lại chỉ cho update cv không che thông tin

            $submitCvs = SubmitCv::where('warehouse_cv_id', $warehouse_cv_id)->whereIn('status', $pending_status)->get();
            if (count($submitCvs) > 0) {
                foreach ($submitCvs as $submitCv) {
                    $submitMeta = $submitCv->submitCvMeta;
                    if ($submitMeta) {
                        $submitMeta->update($data);
                    }
                }
            }
            // ->whereIn('status', $pending_status)
            // ->update($data);
        }
    }

    public function numberSubmitCv($fromDate = null, $toDate = null)
    {
        $query = $this->query();
        $lang = app()->getLocale();

        $query->selectRaw('count(id) as total, status');
        if ($fromDate && $toDate) {
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        }
        $query->groupBy('status');
        $data = $query->get();
        $data = $data->keyBy('status')->toArray();
        // sort data by key asc
        ksort($data);
        return $data;
        // dd($data);
        // dd($data->toArray());



        $totalWaitingCandidateConfirm = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.1'))->toSql();
        dd($totalWaitingCandidateConfirm);

        $totalCandidateCancelApply = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.2'))->count();

        $totalWaitingSetupInterview = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.3'))->count();

        $totalWaitingInterview = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.7'))->count();

        $totalCandidateCancelInterview = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.9'))->count();

        $totalTrialWork = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.14'))->count();

        $totalSuccessRecruitment = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.16'))->count();

        $totalFailTrialWork = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.17'))->count();

        $totalBuyCvSuccess = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.18'))->count();

        $totalCancelBuyCv = $query->whereHas('job', function ($query) {
            $query->where('status', config('constant.active'));
        })->where('status', config('constant.' . $lang . '.status_recruitment.19'))->count();

        return [
            'totalWaitingCandidateConfirm' => $totalWaitingCandidateConfirm,
            'totalCandidateCancelApply' => $totalCandidateCancelApply,
            'totalWaitingSetupInterview' => $totalWaitingSetupInterview,
            'totalWaitingInterview' => $totalWaitingInterview,
            'totalCandidateCancelInterview' => $totalCandidateCancelInterview,
            'totalTrialWork' => $totalTrialWork,
            'totalSuccessRecruitment' => $totalSuccessRecruitment,
            'totalFailTrialWork' => $totalFailTrialWork,
            'totalBuyCvSuccess' => $totalBuyCvSuccess,
            'totalCancelBuyCv' => $totalCancelBuyCv,
        ];
    }

    public function numberSubmitCvJob($fromDate = null, $toDate = null)
    {
        $query = "
        SELECT
            j.name AS 'Job Title',
            c.name AS 'Company Name',
            j.created_at,
            j.expire_at AS 'Expire_at',
            CASE
                WHEN j.is_active = 1 THEN 'Active'
                WHEN j.is_active = 0 THEN 'Inactive'
                ELSE 'Unknown'
            END AS 'Active Status',
            CASE
                WHEN j.status = '0' THEN 'Dừng tuyển'
                WHEN j.status = '1' THEN 'Đang tuyển'
                WHEN j.status = '2' THEN 'Hết hạn tuyển'
                ELSE 'Unknown'
            END AS 'Job Status',
            COUNT(sc.id) AS 'Total Submissions',
            SUM(CASE WHEN sc.status = 1 THEN 1 ELSE 0 END) AS 'Waiting candidate confirm',
            SUM(CASE WHEN sc.status = 2 THEN 1 ELSE 0 END) AS 'Candidate Cancel Apply',
            SUM(CASE WHEN sc.status = 3 THEN 1 ELSE 0 END) AS 'Waiting setup interview',
            SUM(CASE WHEN sc.status = 4 THEN 1 ELSE 0 END) AS 'Waiting confirm calendar',
            SUM(CASE WHEN sc.status = 5 THEN 1 ELSE 0 END) AS 'Reject Interview schedule',
            SUM(CASE WHEN sc.status = 6 THEN 1 ELSE 0 END) AS 'Recruiter Cancel Interview',
            SUM(CASE WHEN sc.status = 7 THEN 1 ELSE 0 END) AS 'Waiting Interview',
            SUM(CASE WHEN sc.status = 8 THEN 1 ELSE 0 END) AS 'Pass Interview',
            SUM(CASE WHEN sc.status = 9 THEN 1 ELSE 0 END) AS 'Candidate Cancel Interview',
            SUM(CASE WHEN sc.status = 10 THEN 1 ELSE 0 END) AS 'Fail Interview',
            SUM(CASE WHEN sc.status = 11 THEN 1 ELSE 0 END) AS 'Offering',
            SUM(CASE WHEN sc.status = 12 THEN 1 ELSE 0 END) AS 'Reject Offer',
            SUM(CASE WHEN sc.status = 13 THEN 1 ELSE 0 END) AS 'Waiting onboard',
            SUM(CASE WHEN sc.status = 14 THEN 1 ELSE 0 END) AS 'Trial work',
            SUM(CASE WHEN sc.status = 15 THEN 1 ELSE 0 END) AS 'Cancel onboard',
            SUM(CASE WHEN sc.status = 16 THEN 1 ELSE 0 END) AS 'Success Recruitment',
            SUM(CASE WHEN sc.status = 17 THEN 1 ELSE 0 END) AS 'Fail trail work',
            SUM(CASE WHEN sc.status = 18 THEN 1 ELSE 0 END) AS 'Buy CV data successful',
            SUM(CASE WHEN sc.status = 19 THEN 1 ELSE 0 END) AS 'Cancel Buy CV data',
            SUM(CASE WHEN sc.status = 20 THEN 1 ELSE 0 END) AS 'Waiting confirm onboard calendar'
        FROM
            job j
        LEFT JOIN
            companies c ON j.company_id = c.id
        LEFT JOIN
            submit_cvs sc ON j.id = sc.job_id
    ";

        if ($fromDate && $toDate) {
            $query .= " WHERE j.created_at BETWEEN :fromDate AND :toDate";
        }

        $query .= "
        GROUP BY
            j.id, j.name, c.name
        ORDER BY
            j.id;
    ";

        $bindings = [];
        if ($fromDate && $toDate) {
            $bindings = ['fromDate' => $fromDate, 'toDate' => $toDate];
        }

        return DB::select($query, $bindings);
    }

    public function searchSubmitCvJob($key = null)
    {
        $query = "
        SELECT
            j.name AS 'Job Title',
            c.name AS 'Company Name',
            j.created_at,
            j.expire_at AS 'Expire_at',
            CASE
                WHEN j.is_active = 1 THEN 'Active'
                WHEN j.is_active = 0 THEN 'Inactive'
                ELSE 'Unknown'
            END AS 'Active Status',
            CASE
                WHEN j.status = '0' THEN 'Dừng tuyển'
                WHEN j.status = '1' THEN 'Đang tuyển'
                WHEN j.status = '2' THEN 'Hết hạn tuyển'
                ELSE 'Unknown'
            END AS 'Job Status',
            COUNT(sc.id) AS 'Total Submissions',
            SUM(CASE WHEN sc.status = 1 THEN 1 ELSE 0 END) AS 'Waiting candidate confirm',
            SUM(CASE WHEN sc.status = 2 THEN 1 ELSE 0 END) AS 'Candidate Cancel Apply',
            SUM(CASE WHEN sc.status = 3 THEN 1 ELSE 0 END) AS 'Waiting setup interview',
            SUM(CASE WHEN sc.status = 4 THEN 1 ELSE 0 END) AS 'Waiting confirm calendar',
            SUM(CASE WHEN sc.status = 5 THEN 1 ELSE 0 END) AS 'Reject Interview schedule',
            SUM(CASE WHEN sc.status = 6 THEN 1 ELSE 0 END) AS 'Recruiter Cancel Interview',
            SUM(CASE WHEN sc.status = 7 THEN 1 ELSE 0 END) AS 'Waiting Interview',
            SUM(CASE WHEN sc.status = 8 THEN 1 ELSE 0 END) AS 'Pass Interview',
            SUM(CASE WHEN sc.status = 9 THEN 1 ELSE 0 END) AS 'Candidate Cancel Interview',
            SUM(CASE WHEN sc.status = 10 THEN 1 ELSE 0 END) AS 'Fail Interview',
            SUM(CASE WHEN sc.status = 11 THEN 1 ELSE 0 END) AS 'Offering',
            SUM(CASE WHEN sc.status = 12 THEN 1 ELSE 0 END) AS 'Reject Offer',
            SUM(CASE WHEN sc.status = 13 THEN 1 ELSE 0 END) AS 'Waiting onboard',
            SUM(CASE WHEN sc.status = 14 THEN 1 ELSE 0 END) AS 'Trial work',
            SUM(CASE WHEN sc.status = 15 THEN 1 ELSE 0 END) AS 'Cancel onboard',
            SUM(CASE WHEN sc.status = 16 THEN 1 ELSE 0 END) AS 'Success Recruitment',
            SUM(CASE WHEN sc.status = 17 THEN 1 ELSE 0 END) AS 'Fail trail work',
            SUM(CASE WHEN sc.status = 18 THEN 1 ELSE 0 END) AS 'Buy CV data successful',
            SUM(CASE WHEN sc.status = 19 THEN 1 ELSE 0 END) AS 'Cancel Buy CV data',
            SUM(CASE WHEN sc.status = 20 THEN 1 ELSE 0 END) AS 'Waiting confirm onboard calendar'
        FROM
            job j
        LEFT JOIN
            companies c ON j.company_id = c.id
        LEFT JOIN
            submit_cvs sc ON j.id = sc.job_id
        WHERE (j.name LIKE :key_job OR c.name LIKE :key_company)
        GROUP BY
            j.id, j.name, c.name
        ORDER BY
            j.id;
    ";

        $bindings = [
            'key_job' => "%$key%",
            'key_company' => "%$key%"
        ];

        // Trả về kết quả query
        return DB::select($query, $bindings);
    }

    public function statisticalByMonth($fromDate = null, $toDate = null)
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();

        return $query->where('submit_cvs.is_active', config('constant.active'))
            ->join('job', 'submit_cvs.job_id', '=', 'job.id')
            ->where('job.is_real', 1) // Chỉ lấy submit CV từ job thật
            ->whereBetween('submit_cvs.created_at', [$start, $end])
            ->selectRaw('COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, "%m") as month')
            ->groupByRaw('DATE_FORMAT(submit_cvs.created_at, "%m")')
            ->orderByRaw('DATE_FORMAT(submit_cvs.created_at, "%m")')
            ->get();
    }

    public function getListJobByTeam($params)
    {
        $user_ids = $params['user_ids'];
        $query = $this->query();
        $data = $query->whereIn('user_id', $user_ids)
            ->join('job', 'submit_cvs.job_id', '=', 'job.id')
            ->selectRaw('DISTINCT job.id, job.name')
            ->when(isset($params['user_id']), function ($query) use ($params) {
                $query->where('submit_cvs.user_id', $params['user_id']);
            })
            ->orderBy('job.id', 'asc')
            ->get();
        return $data;
    }
}
