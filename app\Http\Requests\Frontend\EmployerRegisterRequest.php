<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class EmployerRegisterRequest extends FormRequest
{


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'company_name' => 'required',
            // 'work_position' => 'required',
            // 'work_location' => 'required',
            'email' => ['required', 'email', new CheckEmailRule(config('constant.role.employer'))],
            'mobile' => 'required|regex:/^[0-9()+.-]*$/|max:16|min:10',
            'password' => 'required|min:6|max:20',
            'confirm_password' => 'required|same:password',
            'accept_terms' => 'accepted'
        ];
    }
}
