# Workflow: Thê<PERSON> nút X cho phép ứng viên hủy ứng tuyển với hoàn tiền

**<PERSON><PERSON><PERSON> thực hiện:** 06/01/2025
**Ng<PERSON><PERSON><PERSON> thực hiện:** AI Assistant
**M<PERSON><PERSON> tiêu:** Thêm nút "X" ở góc mỗi item submit để cho phép ứng viên chuyển submit về trạng thái "Candidate Cancel Apply" và thực hiện hoàn tiền cho NTD

## Tổng quan yêu cầu

-   Thêm nút "X" ở góc mỗi item submit trong trang submitcv
-   <PERSON>hi bấm X, ứng viên có thể chuyển submit về trạng thái "Candidate Cancel Apply"
-   Thực hiện hoàn tiền cho NTD
-   Nút X chỉ hiển thị nếu chưa có thanh toán cho CTV

## Các file đã chỉnh sửa

### 1. View Template

**File:** `resources/views/frontend/pages/collaborator/submitcv.blade.php`

**Thay đổi:**

-   Thêm nút X với điều kiện kiểm tra `status_payment_ctv`
-   Thêm style position relative cho item container
-   Thêm modal xác nhận candidate cancel
-   Thêm JavaScript xử lý event click và AJAX call

**Điều kiện hiển thị nút X:**

```php
@if (!$item->status_payment_ctv || $item->status_payment_ctv == 0)
```

### 2. Routes

**File:** `routes/web.php`

**Thay đổi:**

-   Thêm route mới: `Route::post('submitcv/candidate-cancel/{id}', [RecController::class, 'candidateCancelSubmitCv'])->name('rec-candidate-cancel-submit');`

### 3. Controller

**File:** `app/Http/Controllers/Frontend/RecController.php`

**Thay đổi:**

-   Thêm method `candidateCancelSubmitCv()` để xử lý candidate cancel request
-   Method trả về JSON response với thông báo thành công/thất bại

### 4. Service Logic

**File:** `app/Services/Frontend/SubmitCvService.php`

**Thay đổi:**

-   Thêm method `candidateCancelSubmitWithRefund()` để xử lý logic cancel và hoàn tiền
-   Kiểm tra điều kiện hủy: chưa thanh toán CTV, trạng thái phù hợp
-   Xử lý hoàn tiền cho NTD dựa trên bonus_type
-   Log history status và payment history
-   Cập nhật wallet NTD

## Logic hoàn tiền

### Kiểm tra điều kiện

1. Submit CV phải thuộc về user đang thực hiện
2. Chưa có thanh toán cho CTV (`status_payment_ctv == 0`)
3. Trạng thái cho phép cancel:
    - Waiting candidate confirm
    - Waiting Payment
    - Waiting setup interview
    - Waiting confirm calendar
    - Waiting Interview

### Xử lý hoàn tiền theo bonus_type

-   **Interview/CV:** Hoàn 100% `bonus_ntd`
-   **Onboard:** Hoàn số tiền đã thanh toán (kiểm tra history payment)

### Database operations

1. Cập nhật `submit_cvs.status` = 2 (Candidate Cancel Apply)
2. Tạo record `submit_cvs_history_status`
3. Tạo record `submit_cvs_history_payment` với type = 1 (hoàn tiền)
4. Cập nhật `wallets.amount` của employer
5. Cập nhật `submit_cvs.status_payment` = 3 (hoàn tiền)

## UI/UX

### Nút Cancel

-   Màu đỏ (#dc3545)
-   Hình tròn 30x30px
-   Icon "×"
-   Hiển thị ở góc phải trên item
-   Tooltip "Hủy ứng tuyển"

### Modal xác nhận

-   Tiêu đề: "Hủy ứng tuyển"
-   Nội dung xác nhận với tên ứng viên
-   Thông báo về hoàn tiền trong 24h
-   Buttons: "Quay lại", "Xác nhận"

### JavaScript

-   Event delegation cho nút X
-   AJAX call tới route mới
-   Loading state và error handling
-   Reload page sau khi thành công

## Cần hoàn thiện

1. **Notification Classes:** Tạo email templates cho:

    - `CandidateCancelSubmitNotificationEmployer`
    - `CandidateCancelSubmitNotificationAdmin`

2. **Testing:** Test các trường hợp:

    - Đã thanh toán CTV
    - Các trạng thái khác nhau
    - Các bonus_type khác nhau
    - Edge cases

3. **Validation:** Thêm validation phía server cho điều kiện cancel

## Kết quả

✅ Nút X hiển thị đúng điều kiện
✅ Modal xác nhận hoạt động
✅ Logic hoàn tiền implement đầy đủ
✅ AJAX response thành công
✅ Database operations đầy đủ
🔄 Email notifications (TODO)
🔄 Testing (TODO)
