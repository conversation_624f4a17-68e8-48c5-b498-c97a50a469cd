<?php

namespace App\Services\Frontend;



use App\Repositories\ZalopayTransactionRepository;

class ZalopayTransactionService
{

    protected $zalopayTransactionRepository;

    public function __construct(ZalopayTransactionRepository $zalopayTransactionRepository)
    {
        $this->zalopayTransactionRepository = $zalopayTransactionRepository;
    }


    public function insert($params)
    {
        return $this->zalopayTransactionRepository->insert($params);
    }

    public function create($attributes = [])
    {
//        $a = json_encode($attributes);
        return $this->zalopayTransactionRepository->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function update($id, $options = [], $attributes = [])
    {
        return $this->zalopayTransactionRepository->update($id, $options, $attributes);

    }

    public function delete($id, $options)
    {
        return $this->zalopayTransactionRepository->delete($id, $options);
    }

    public function getTransactionPayment($transId)
    {
        return $this->zalopayTransactionRepository->getTransId($transId);
    }

}
