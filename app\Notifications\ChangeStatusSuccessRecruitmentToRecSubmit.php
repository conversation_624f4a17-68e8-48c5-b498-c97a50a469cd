<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusSuccessRecruitmentToRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName       = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id]);
        return (new MailMessage)
            ->view('email.changeStatusSuccessRecruitmentToRecSubmit', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'url'           => $url,
            ])
            ->subject('[Recland]Thông báo thử việc thành công Ứng viên'.$candidateName.' vị trí '.$position.' tại Công ty  '.$companyName);

    }


}
