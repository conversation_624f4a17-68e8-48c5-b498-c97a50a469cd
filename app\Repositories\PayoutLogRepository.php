<?php

namespace App\Repositories;

use App\Models\PayoutLog;

class PayoutLogRepository extends BaseRepository
{
    const MODEL = PayoutLog::class;
    const STATUS_PENDING = 0;
    const STATUS_SUCCESS = 1;

    public function getHistoryWithdraw($params, $userId)
    {
        $query = $this->query()->where('user_id', $userId)->orderBy('id', 'desc');

        return $query->paginate(10);
    }

    public function countStatusPending($userId)
    {
        $query = $this->query()->where('user_id', $userId)->where('status', static::STATUS_PENDING);

        return $query->count();
    }

    public function totalWithdraw($userId, $status)
    {
        $query = $this->query()->select('amount')->where('user_id', $userId)->where('status', $status);

        return $query->sum('amount');
    }

    public function totalRemaining($userId)
    {
        $query = $this->query()->select('balance')->where('user_id', $userId)->where('status', 1);

        return $query->first();
    }

    public function getDetail($id, $userId)
    {
        return $this->query()->where('id', $id)->where('user_id', $userId)->first();
    }

    public function sumAmountPending($userId)
    {
        $query = $this->query()->where('user_id', $userId)->where('status', static::STATUS_PENDING);
        return $query->sum('amount');
    }

}
