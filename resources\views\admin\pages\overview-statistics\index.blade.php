@extends('admin.layouts.app')

@section('content')

    <div class="page-header d-xl-flex d-block">
        <div class="page-leftheader">
            <h4 class="page-title">Thống kê tổng quan</h4>
        </div>
    </div>
    
    <div class="row justify-content-center align-items-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header text-center">
                    <h4>Bộ lọc thống kê</h4>
                </div>
                <div class="card-body">
                    <form id="filterForm" class="row g-3">
                        @csrf
                        <div class="col-md-3">
                            <label for="from_date" class="form-label">Từ ngày</label>
                            <input type="date" id="from_date" name="from_date" class="form-control" 
                                   value="{{ $filters['from_date'] ?? '' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="to_date" class="form-label"><PERSON><PERSON><PERSON> ng<PERSON></label>
                            <input type="date" id="to_date" name="to_date" class="form-control"
                                   value="{{ $filters['to_date'] ?? '' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="is_real" class="form-label">Loại dữ liệu</label>
                            <select id="is_real" name="is_real" class="form-control">
                                <option value="">Tất cả</option>
                                <option value="1" {{ ($filters['is_real'] ?? '') == '1' ? 'selected' : '' }}>Dữ liệu thật</option>
                                <option value="0" {{ ($filters['is_real'] ?? '') == '0' ? 'selected' : '' }}>Dữ liệu ảo</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="cv_source" class="form-label">Nguồn CV</label>
                            <select id="cv_source" name="cv_source" class="form-control">
                                <option value="">Tất cả</option>
                                <option value="internal" {{ ($filters['cv_source'] ?? '') == 'internal' ? 'selected' : '' }}>Nguồn nội bộ</option>
                                <option value="collaborator" {{ ($filters['cv_source'] ?? '') == 'collaborator' ? 'selected' : '' }}>Nguồn cộng tác viên</option>
                            </select>
                        </div>
                        <div class="col-12 text-center mt-2">
                            <button type="submit" class="btn btn-primary">Lọc dữ liệu</button>
                            <button type="button" id="resetBtn" class="btn btn-secondary">Đặt lại</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="tableContent">
        @include('admin.pages.overview-statistics.filter-statistics', ['statistics' => $statistics])
    </div>

@endsection

@section('scripts')
    <!-- INTERNAL ECharts js-->
    <script src="{{asset2('backend/assets/plugins/echarts/echarts.5.3.2.min.js')}}"></script>

    <script>
        $(document).ready(function() {
            $('#filterForm').on('submit', function(e) {
                e.preventDefault();

                let fromDate = $('#from_date').val();
                let toDate = $('#to_date').val();
                let isReal = $('#is_real').val();
                let cvSource = $('#cv_source').val();

                $.ajax({
                    url: '{{ route('overview-statistics.filter-data') }}',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        from_date: fromDate,
                        to_date: toDate,
                        is_real: isReal,
                        cv_source: cvSource
                    },
                    success: function(response) {
                        if(response.status === 'success') {
                            $.toast({
                                title: 'Success',
                                content: response.message,
                                type: 'success',
                                delay: 5000
                            });
                            $('#tableContent').html(response.html);
                        }
                    },
                    error: function(xhr) {
                        toastr.error('Có lỗi xảy ra khi gửi yêu cầu!');
                    }
                });
            });

            $('#resetBtn').on('click', function() {
                $('#from_date').val('');
                $('#to_date').val('');
                $('#is_real').val('');
                $('#cv_source').val('');
                $('#filterForm').submit();
            });
        });
    </script>
@endsection

@section('css_custom')
    <style>
        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }
        .statistics-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        .statistics-table th, 
        .statistics-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
            white-space: nowrap;
        }
        .statistics-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .statistics-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .statistics-table tbody tr:hover {
            background-color: #e9ecef;
        }
        .number-cell {
            font-weight: 600;
            color: #007bff;
        }
        .period-cell {
            font-weight: bold;
            background-color: #e3f2fd !important;
        }
    </style>
@endsection
