<?php

namespace App\Http\Controllers\Admin;

use App\Models\JobComment;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class JobCommentCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    public function setup()
    {
        CRUD::setModel(JobComment::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/job-comment');
        CRUD::setEntityNameStrings('bình luận', 'danh sách bình luận');
        // Disable all buttons
        CRUD::denyAccess(['create', 'update', 'delete', 'show']);
    }

    protected function setupListOperation()
    {
        // Thêm các cột hiển thị
        CRUD::column('job.name')
            ->label('Tên công việc')
            ->wrapper([
                'href' => function ($crud, $column, $entry) {
                    return backpack_url('job/'.$entry->job_id.'/edit');
                },
            ]);

        CRUD::column('content')
            ->label('Nội dung câu hỏi')
            ->limit(100);

        CRUD::addColumn([
            'name' => 'latest_replies',
            'label' => '3 câu trả lời gần nhất',
            'type' => 'closure',
            'function' => function($entry) {
                $replies = JobComment::where('parent_id', $entry->id)
                    ->latest()
                    ->take(3)
                    ->orderBy('created_at', 'desc')
                    ->get()
                    ->map(function($reply) {
                        return sprintf(
                            "<div class='reply-item mb-2'>%s <small class='text-muted'>(%s)</small></div>",
                            \Str::limit($reply->content, 70),
                            $reply->created_at->format('d/m/Y H:i')
                        );
                    })
                    ->implode('');
                return $replies ?: '<em>Chưa có câu trả lời</em>';
            },
            'escaped' => false,
        ]);

        CRUD::column('updated_at')
            ->label('Cập nhật lúc');

        // Thêm bộ lọc
        CRUD::filter('job_name')
            ->type('text')
            ->label('Tên công việc')
            ->whenActive(function($value) {
                $this->crud->addClause('whereHas', 'job', function($query) use ($value) {
                    $query->where('name', 'LIKE', "%$value%");
                });
            });

        // Sắp xếp mặc định theo thời gian cập nhật mới nhất
        CRUD::orderBy('last_reply_at', 'desc');

        // Chỉ hiển thị các comment gốc (không phải reply)
        $this->crud->addClause('whereNull', 'parent_id');
    }
} 