<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\CompanyService;
use App\Services\Admin\JobService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\UserService;
use App\Services\Admin\WareHouseCvService;
use App\Services\Frontend\WareHouseCvSellingService;
use Illuminate\Http\Request;

class DashboardController extends Controller
{

    protected $userService;
    protected $jobService;
    protected $companyService;
    protected $wareHouseCvService;
    protected $submitCvService;
    protected $wareHouseCvSellingService;

    public function __construct(
        UserService               $userService,
        JobService $jobService,
        CompanyService            $companyService,
        WareHouseCvService $wareHouseCvService,
        SubmitCvService           $submitCvService,
        WareHouseCvSellingService $wareHouseCvSellingService,
    ) {
        $this->userService = $userService;
        $this->jobService = $jobService;
        $this->companyService = $companyService;
        $this->wareHouseCvService = $wareHouseCvService;
        $this->submitCvService = $submitCvService;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
    }

    public function index()
    {
        $totalEmployer = $this->userService->totalEmployer();
        $totalCollaborator = $this->userService->totalCollaborator();
        $totalCv = $this->wareHouseCvService->total();
        $totalJob = $this->jobService->totalActive();
        $totalCompany = $this->companyService->total();
        $wareHouseStatisticalByMonth = $this->wareHouseCvService->statisticalByMonth();
        $collaboratorStatisticalByMonth = $this->userService->statisticalCollaboratorByMonth();
        $wareHouseCvSellingByMonth = $this->wareHouseCvSellingService->statisticalByMonth();
        $submiCvByMonth = $this->submitCvService->statisticalByMonth();
        $employerStatisticalByMonth = $this->userService->statisticalEmployerByMonth();
        $jobStatisticalByMonth = $this->jobService->jobStatisticalByMonth();

        return view('admin.pages.dashboard.index', compact(
            'totalEmployer',
            'totalCollaborator',
            'totalCv',
            'totalJob',
            'totalCompany',
            'wareHouseStatisticalByMonth',
            'collaboratorStatisticalByMonth',
            'wareHouseCvSellingByMonth',
            'submiCvByMonth',
            'employerStatisticalByMonth',
            'jobStatisticalByMonth'
        ));
    }

    public function filterDate(Request $request)
    {
        $fromDate = $request->from_date;
        $toDate = $request->to_date;
        $employerStatisticalByMonth = $this->userService->statisticalEmployerByMonth($fromDate, $toDate);
        $jobStatisticalByMonth = $this->jobService->jobStatisticalByMonth($fromDate, $toDate);
        $wareHouseStatisticalByMonth = $this->wareHouseCvService->statisticalByMonth($fromDate, $toDate);
        $collaboratorStatisticalByMonth = $this->userService->statisticalCollaboratorByMonth($fromDate, $toDate);
        $wareHouseCvSellingByMonth = $this->wareHouseCvSellingService->statisticalByMonth($fromDate, $toDate);
        $submiCvByMonth = $this->submitCvService->statisticalByMonth($fromDate, $toDate);

        return response()->json([
            'employer' => $employerStatisticalByMonth,
            'job' => $jobStatisticalByMonth,
            'cv' => $wareHouseStatisticalByMonth,
            'collaborator' => $collaboratorStatisticalByMonth,
            'cv_selling' => $wareHouseCvSellingByMonth,
            'submit_cv' => $submiCvByMonth
        ]);
    }
}
