<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PostSeoRequest;
use App\Services\Admin\PostService;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;

class PostSeoController extends Controller
{

    protected $postService;

    public function __construct(PostService $postService)
    {
        $this->postService = $postService;
    }

    public function updatePostSeo(Request $request, $postId)
    {
        $this->postService->updatePostSeo($request->all(), $postId);
        Toast::success(__('message.edit_success'));
        return back();
    }
}
