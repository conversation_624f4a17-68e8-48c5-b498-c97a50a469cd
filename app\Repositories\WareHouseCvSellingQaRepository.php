<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingQa;

class WareHouseCvSellingQaRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingQa::class; //test

    public function getWithCvSelling($idCvSelling, $params)
    {
        $query = $this->query();

        $query->where('warehouse_cv_selling_id', $idCvSelling)->where('parent_id', 0);

        return $query->orderBy('id', 'desc')->get();
    }

    public function getAllWithCvSelling($idCvSelling)
    {
        $query = $this->query();

        $query->where('warehouse_cv_selling_id', $idCvSelling)->where('parent_id', 0)->with(['qaChild.user', 'user']);

        return $query->orderBy('id', 'asc')->get();
    }

}
