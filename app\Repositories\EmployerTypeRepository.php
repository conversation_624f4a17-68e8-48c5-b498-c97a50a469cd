<?php

namespace App\Repositories;

use App\Models\EmployerType;

class EmployerTypeRepository extends BaseRepository
{
    const MODEL = EmployerType::class;

    public function updateWithUserId($userId, $data)
    {
        return $this->query()->where('user_id', $userId)->update($data);
    }

    public function findWithUserId($userId)
    {
        return $this->query()->where('user_id', $userId)->first();
    }

    public function updateWithEmployeeRole($employeeRoleId)
    {
        return $this->query()->where('employee_role_id', $employeeRoleId)->update([
            'employee_role_id' => null
        ]);
    }
}
