<?php

namespace App\Services\Frontend;

use App\Jobs\CandidateCancelInterview;
use App\Jobs\DepositRefundRejectOffer;
use App\Jobs\OutOfDateBookInterview;
use App\Jobs\PayInterview;
use App\Jobs\PayOnboard;
use App\Jobs\RecSumExpiredPoint;
use App\Jobs\RecSumPoint;
use App\Jobs\PassInterview;
use App\Jobs\RejectRecruitment;
use App\Jobs\SuccessRecuitment;
use App\Notifications\BuyCvSuccess;
use App\Notifications\BuyCvSuccessSendMailAdmin;
use App\Notifications\BuyCvSuccessSendMailCandidate;
use App\Notifications\CandidateConfirmRecruitment;
use App\Notifications\CandidateRejectRecruitmentAdmin;
use App\Notifications\CandidateRejectRecruitmentEmployer;
use App\Notifications\CandidateRejectRecruitmentRec;
use App\Notifications\ChangeStatusCancelInterviewToAdmin;
use App\Notifications\ChangeStatusCancelInterviewToEmployer;
use App\Notifications\ChangeStatusCancelInterviewToRec;
use App\Notifications\ChangeStatusCancelOnboardToAdmin;
use App\Notifications\ChangeStatusCancelOnboardToEmployer;
use App\Notifications\ChangeStatusCancelOnboardToRec;
use App\Notifications\ChangeStatusFailIInterview;
use App\Notifications\ChangeStatusFailIInterviewToAdmin;
use App\Notifications\ChangeStatusFailTrialWorkToAdmin;
use App\Notifications\ChangeStatusFailTrialWorkToEmployer;
use App\Notifications\ChangeStatusFailTrialWorkToRec;
use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\ChangeStatusPassInterviewAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployer;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRec;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Notifications\ChangeStatusTrailWorkToAdmin;
use App\Notifications\ChangeStatusTrailWorkToAuthorityRec;
use App\Notifications\ChangeStatusTrailWorkToRec;
use App\Notifications\EmailConfirmInterView;
use App\Notifications\EmailConfirmOnboard;
use App\Notifications\EmailRejectInterView;
use App\Notifications\EmailRejectOnboard;
use App\Notifications\EmployerComplain;
use App\Notifications\EmployerComplainAdmin;
use App\Notifications\PaymentInterviewToRec;
use App\Notifications\PaymentTrialWork;
use App\Notifications\RecAgreeComplain;
use App\Notifications\RecAgreeComplainEmployer;
use App\Notifications\RecRefuseComplain;
use App\Notifications\RemindExpireTrailWork;
use App\Notifications\RemindPaymentDebit;
use App\Notifications\SendQaToRec;
use App\Repositories\BonusRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\JobRepository;
use App\Repositories\JobTopRepository;
use App\Repositories\LevelBySkillMainRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\PaymentDebitRepository;
use App\Repositories\SkillMainRepository;
use App\Repositories\UserRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use App\Repositories\WareHouseCvRepository;
use App\Repositories\WareHouseCvSellingEvaluateRepository;
use App\Repositories\WareHouseCvSellingQaRepository;
use App\Repositories\WareHouseCvSellingRepository;
use App\Repositories\WareHouseCvSellingSaveRepository;
use App\Services\FileServiceS3;
use App\Services\Frontend\WareHouseCvSellingService;
use App\Services\Frontend\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class WareHouseCvSellingBuyService
{
    protected $wareHouseCvSellingRepository;
    protected $skillMainRepository;
    protected $levelBySkillMainRepository;
    protected $jobTopRepository;
    protected $levelByJobTopRepository;
    protected $wareHouseCvRepository;
    protected $wareHouseCvSellingBuyRepository;
    protected $wareHouseCvSellingHistoryBuyRepository;
    protected $wareHouseCvSellingEvaluateRepository;
    protected $wareHouseCvSellingQaRepository;
    protected $wareHouseCvSellingSaveRepository;
    protected $companyRepository;
    protected $userRepository;
    protected $wareHouseCvSellingBuyBookRepository;
    protected $wareHouseCvSellingBuyHistoryStatusRepository;
    protected $jobRepository;
    protected $wareHouseCvSellingBuyOnboardRepository;
    protected $paymentDebitRepository;
    protected $wareHouseCvSellingService;
    protected $userService;


    public function __construct(
        WareHouseCvSellingRepository $wareHouseCvSellingRepository,
        SkillMainRepository $skillMainRepository,
        LevelBySkillMainRepository $levelBySkillMainRepository,
        JobTopRepository $jobTopRepository,
        LevelBySkillMainRepository $levelByJobTopRepository,
        WareHouseCvRepository  $wareHouseCvRepository,
        WareHouseCvSellingBuyRepository  $wareHouseCvSellingBuyRepository,
        WareHouseCvSellingHistoryBuyRepository  $wareHouseCvSellingHistoryBuyRepository,
        WareHouseCvSellingEvaluateRepository  $wareHouseCvSellingEvaluateRepository,
        WareHouseCvSellingQaRepository  $wareHouseCvSellingQaRepository,
        WareHouseCvSellingSaveRepository  $wareHouseCvSellingSaveRepository,
        CompanyRepository  $companyRepository,
        UserRepository $userRepository,
        WareHouseCvSellingBuyBookRepository $wareHouseCvSellingBuyBookRepository,
        WareHouseCvSellingBuyHistoryStatusRepository $wareHouseCvSellingBuyHistoryStatusRepository,
        JobRepository $jobRepository,
        WareHouseCvSellingBuyOnboardRepository $wareHouseCvSellingBuyOnboardRepository,
        PaymentDebitRepository $paymentDebitRepository,
        WareHouseCvSellingService $wareHouseCvSellingService,
        UserService $userService
    ) {
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
        $this->skillMainRepository = $skillMainRepository;
        $this->levelBySkillMainRepository = $levelBySkillMainRepository;
        $this->jobTopRepository = $jobTopRepository;
        $this->levelByJobTopRepository = $levelByJobTopRepository;
        $this->wareHouseCvRepository = $wareHouseCvRepository;
        $this->wareHouseCvSellingBuyRepository = $wareHouseCvSellingBuyRepository;
        $this->wareHouseCvSellingHistoryBuyRepository = $wareHouseCvSellingHistoryBuyRepository;
        $this->wareHouseCvSellingEvaluateRepository = $wareHouseCvSellingEvaluateRepository;
        $this->wareHouseCvSellingQaRepository = $wareHouseCvSellingQaRepository;
        $this->wareHouseCvSellingSaveRepository = $wareHouseCvSellingSaveRepository;
        $this->companyRepository = $companyRepository;
        $this->userRepository = $userRepository;
        $this->wareHouseCvSellingBuyBookRepository = $wareHouseCvSellingBuyBookRepository;
        $this->wareHouseCvSellingBuyHistoryStatusRepository = $wareHouseCvSellingBuyHistoryStatusRepository;
        $this->jobRepository = $jobRepository;
        $this->wareHouseCvSellingBuyOnboardRepository = $wareHouseCvSellingBuyOnboardRepository;
        $this->paymentDebitRepository = $paymentDebitRepository;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->userService = $userService;
    }

    public function buyCV($params)
    {
        try {
            DB::beginTransaction();
            $cvSelling = $this->wareHouseCvSellingRepository->find($params['id']);

            if (!$cvSelling) {
                return false;
            }

            if (!auth('client')->user()) {
                return false;
            }

            $user = auth('client')->user();
            if ($cvSelling->type_of_sale == 'onboard' || $cvSelling->type_of_sale == 'interview') {
                $statusRecruitment = config('constant.status_recruitment_revert.Waitingcandidateconfirm'); //1
            }

            if ($cvSelling->type_of_sale == 'cv') {
                $statusRecruitment = config('constant.status_recruitment_revert.BuyCVdatasuccessfull'); //18
            }
            //NTD chỉ được mua 1 lần
            $checkwareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->findByUserSelling($user->id, $cvSelling->id);
            if ($checkwareHouseCvSellingBuy) return false;
            $statusPayment = $cvSelling->type_of_sale == 'onboard' ? 1 : 4; //1 => 'Đã cọc',4 => 'NTD Đã thanh toán',
            $sellingBuy = [
                'user_id'                   =>  auth('client')->user()->id, //NTD mua
                'ctv_id'                    =>  $cvSelling->user_id, //CTV đã tạo CV đăng bán
                'price'                     =>  $cvSelling->price,
                'point'                     =>  $cvSelling->point,
                'warehouse_cv_selling_id'   =>  $params['id'],
                'status_recruitment'        =>  $statusRecruitment,
                'status_payment'            =>  $statusPayment,
                'job_id'                    =>  isset($params['job']) ? $params['job'] : null,
                'token'                     =>  Str::random(16),
            ];

            $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->create($sellingBuy);

            //update wallet user
            //onboard: users.total = users.total - số tiền đặt cọc (10 % warehouse_cv_sellings.price)
            //cv, interview: users.total = users.total - warehouse_cv_sellings.price
            $point = $cvSelling->type_of_sale == 'onboard' ? 0.1 * $cvSelling->point : $cvSelling->point;
            $percent = $cvSelling->type_of_sale == 'onboard' ? 10 : 100;
            //check so point của NTD neu ko đủ thi dừng
            // dd($point, $user->wallet->amount);
            if ($point > $user->wallet->amount) return false;
            //trừ point của NTD
            $remainingPoint = $user->wallet->amount - $point;
            // $user->wallet->amount = $remainingPoint;
            $user->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $user->wallet->id]);
            $user->wallet->subtractAmount($point, $wareHouseCvSellingBuy, 'Trừ tiền mua CV', 'buy_cv');
            // $user->wallet->save();
            //ghi log mua
            $wareHouseCvSellingHistoryBuy = $this->wareHouseCvSellingHistoryBuyRepository->create([
                'user_id'                       =>  $user->id,
                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                'type'                          =>  0, //0 trừ tiền, 1 hoàn tiền
                'percent'                       =>  $percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale'                  =>  $cvSelling->type_of_sale,
                'point'                         =>  $point,
                'balance'                       =>  $remainingPoint,
                'status'                        =>  0
            ]);
            //Log thay đổi trang thái
            $historyBuyStatus = $this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                'user_id'                       =>  $user->id,
                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                'status_recruitment'            =>  $statusRecruitment,
                'type'                          =>  'employer',
                'authority'                     =>  $cvSelling->authority
            ]);
            DB::commit();

            //sau 7 ngày mà trạng thái tuyển dụng :
            //1. status_recruitment = 18 => 'Buy CV data successfull'
            //2. không bị NTD khiếu nại, hoặc khiếu nại thất bại: CTV-> Admin từ chối thì cộng tiền trả CTV status_complain Khác 4, 3 CTV xác nhận
            if ($cvSelling->type_of_sale == 'cv') {
                //send mail to CTV
                $ctv = $this->userRepository->find($cvSelling->user_id);
                $ctv->notify(new BuyCvSuccess($ctv, $cvSelling, $user, $wareHouseCvSellingBuy));

                // cong tien cho CTV sau 7 ngay khong co khieu nai
                RecSumPoint::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(7 * 24 * 60));
            }

            //CV data ko co Logic nay
            //sau 48h mà Ứng viên ko xác nhận, thì Hoàn tiền trả NTD
            if ($cvSelling->type_of_sale == 'interview' || $cvSelling->type_of_sale == 'onboard') {
                if ($cvSelling->authority == 0) {
                    //send mail to CTV
                    $ctv = $this->userRepository->find($cvSelling->user_id);
                    $ctv->notify(new BuyCvSuccess($ctv, $cvSelling, $user, $wareHouseCvSellingBuy));
                } else if ($cvSelling->authority == 2) {
                    //send mail to admin
                    Mail::to(config('settings.global.email_admin'))->send(new BuyCvSuccessSendMailAdmin('admin', $wareHouseCvSellingBuy, $user, $wareHouseCvSellingBuy->id));
                }
                //send mail to candidate_email
                $job = $this->jobRepository->find($params['job']);
                Mail::to($cvSelling->wareHouseCv->candidate_email)->send(new BuyCvSuccessSendMailCandidate($wareHouseCvSellingBuy, $wareHouseCvSellingBuy->token, $job));

                Log::info('RejectRecruitment 111111111: ', [
                    '$wareHouseCvSellingBuy->id: ' => $wareHouseCvSellingBuy->id
                ]);
                //set queue check status_recruitment sau 48h
                RejectRecruitment::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(48 * 60));
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log: ', [
                'content buy: ' => $e->getMessage()
            ]);
        }

        return true;
    }

    public function evaluateCV($params)
    {
        $cvSelling = $this->wareHouseCvSellingRepository->find($params['id']);

        if (!$cvSelling) {
            return false;
        }

        $this->wareHouseCvSellingEvaluateRepository->create([
            'user_id'                   =>  auth('client')->user()->id,
            'warehouse_cv_selling_id'   =>  $params['id'],
            'star'                      =>  $params['stars'],
            'type'                      =>  !empty($params['evaluate_type']) ? implode(",", $params['evaluate_type']) : '',
            'comment'                   =>  $params['comment'],
            'evaluate'                  =>  isset($params['report']) ? 1 : 0,
        ]);

        return true;
    }

    public function getEvaluateCvSelling($idCvSelling, $params)
    {
        return $this->wareHouseCvSellingEvaluateRepository->getWithCvSelling($idCvSelling, $params);
    }

    public function getFaqCvSelling($idCvSelling, $params)
    {
        return $this->wareHouseCvSellingQaRepository->getWithCvSelling($idCvSelling, $params);
    }

    public function faqCV($params)
    {
        $cvSelling = $this->wareHouseCvSellingRepository->find($params['id']);

        if (!$cvSelling) {
            return false;
        }

        $qa = $this->wareHouseCvSellingQaRepository->create([
            //NTD hoi dap
            'user_id'                   =>  auth('client')->user()->id,
            'warehouse_cv_selling_id'   =>  $params['id'],
            'comment'                   =>  $params['comment'],
        ]);

        $data = [
            'user_name' => auth('client')->user()->name,
            'comment'   => $params['comment'],
            'created_at' => $qa->created_at_value
        ];

        //send mail CTV
        $rec = $this->userRepository->find($cvSelling->user_id);
        $rec->notify(new SendQaToRec($rec, $cvSelling, $data));

        return $data;
    }

    public function saveCV($params)
    {
        $cvSelling = $this->wareHouseCvSellingRepository->find($params['id']);

        if (!$cvSelling) {
            return false;
        }

        $this->wareHouseCvSellingSaveRepository->create([
            'user_id'                   =>  auth('client')->user()->id,
            'warehouse_cv_selling_id'   =>  $params['id'],
        ]);

        return true;
    }

    public function unSaveCV($params)
    {
        $cvSelling = $this->wareHouseCvSellingRepository->find($params['id']);

        if (!$cvSelling) {
            return false;
        }

        $saved = $this->wareHouseCvSellingSaveRepository->findCvSaved(auth('client')->user()->id, $params['id']);

        if (!$saved) {
            return false;
        }
        $saved->delete();

        return true;
    }

    public function getCvBought($params)
    {
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingBuyRepository->getCvBought($userId, $params, true);
    }

    public function getCvSold($params)
    {
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingBuyRepository->getCvSold($userId, $params, true);
    }

    public function findDetailCvSold($id)
    {
        try {
            $user = auth('client')->user();
            $data = $this->wareHouseCvSellingBuyRepository->findByRec($id, $user->id);
            if (!$data) return false;
            $data->load(['wareHouseCvSelling.wareHouseCv', 'employer']);
            $data = $data->toArray();
            $data['company'] = [];
            if ($data['ware_house_cv_selling']['exclude_company']) {
                foreach (explode(',', $data['ware_house_cv_selling']['exclude_company']) as $companyId) {
                    $company = $this->companyRepository->find($companyId);
                    array_push($data['company'], $company->name);
                }
            }

            $data['level_name'] = '';
            if (isset($data['ware_house_cv_selling']['ware_house_cv']['career'])) {
                $career = isset($data['ware_house_cv_selling']['ware_house_cv']['career']) ? (string) $data['ware_house_cv_selling']['ware_house_cv']['career'] : '';
                $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
                if ($isIT) {
                    $level = $this->levelBySkillMainRepository->find($data['ware_house_cv_selling']['level']);
                } else {
                    $level =  $this->levelByJobTopRepository->find($data['ware_house_cv_selling']['level']);
                }

                $data['level_name'] = $level->name_en;
            }

            $data['skill_name'] = '';
            if ($data['ware_house_cv_selling']['skill']) {
                $career = isset($data['ware_house_cv_selling']['ware_house_cv']['career']) ? (string) $data['ware_house_cv_selling']['ware_house_cv']['career'] : '';
                $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
                if ($isIT) {
                    $skill = $this->skillMainRepository->find($data['ware_house_cv_selling']['skill']);
                } else {
                    $skill =  $this->jobTopRepository->find($data['ware_house_cv_selling']['skill']);
                }

                $data['skill_name'] = $skill->name_en;
            }
            return $data;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @param $request
     * @return mixed
     * NTD gửi khiếu nại
     */
    public function complain($request)
    {

        $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($request['warehouse_cv_selling_buys_id']);
        if ($cvSellingBuy->can_complain === false) {
            throw new \Exception('Khiếu nại thất bại');
        }

        $imgComplain = '';
        if (!empty($request['image_complain'])) {
            $imgComplain = FileServiceS3::getInstance()->uploadToS3($request['image_complain'], config('constant.sub_path_s3.complain'));
        }

        //nếu gửi khiếu nại rồi hoặc chưa đủ dk thi return false
        if ($cvSellingBuy->status_complain != 0 && $cvSellingBuy->status_complain != 5) return false;
        //cv &&  18 => 'Buy CV data successfull',//danh cho type_of_sale = cv
        if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' && $cvSellingBuy->status_recruitment != 18) return false;
        //onboard &&  7 => 'Waiting Interview', && 14 => 'Trial work',
        if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard' && $cvSellingBuy->status_recruitment != 7 && $cvSellingBuy->status_recruitment != 14) return false;
        //interview &&  7 => 'Waiting Interview',
        if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview' && $cvSellingBuy->status_recruitment != 7) return false;

        //neu uy quyen thi khi NTD khieu nai , coi nhu CTV tu choi, de chuyen cho admin phe duyet
        if ($cvSellingBuy->wareHouseCvSelling->authority == 2) {
            $statusComplain = 2;
            //sau 7 ngày khieu nai mà admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPoint::dispatch($cvSellingBuy->id, 2)->delay(now()->addMinutes(7 * 24 * 60));
        } else if ($cvSellingBuy->wareHouseCvSelling->authority == 0) {
            $statusComplain = 1;
            //sau 7 ngày khieu nai mà CTV không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPoint::dispatch($cvSellingBuy->id, 1)->delay(now()->addMinutes(7 * 24 * 60));
        }

        $data = [
            'txt_complain'      => !empty($request['content']) ? $request['content'] : '',
            'status_complain'   => $statusComplain,
            'img_complain'      => $imgComplain,
            'count_complain' => $cvSellingBuy->count_complain + 1,
            'date_complain' => now(),
        ];

        $cvSellingBuy->update($data);
        $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($cvSellingBuy, auth()->user());
        $rec = $cvSellingBuy->rec;
        $employer = $cvSellingBuy->employer;
        //uy quyen thi gui email cho admin
        if ($cvSellingBuy->wareHouseCvSelling->authority == 2) {
            Mail::to(config('settings.global.email_admin'))->send(new EmployerComplainAdmin($rec, $employer, $cvSellingBuy));
        } else if ($cvSellingBuy->wareHouseCvSelling->authority == 0) {
            //khong uy quyen thi gui email cho CTV
            $rec->notify(new EmployerComplain($rec, $employer, $cvSellingBuy));
        }


        return $cvSellingBuy;
    }

    /**
     * @param $id
     * @param $status
     * @return mixed
     * CTV xác nhận hoặc từ chối khiếu nại
     * HT NTD
     */
    public function recChangeStatus($id, $status)
    {
        $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($id);
        if (!$cvSellingBuy) return false;
        //1 là trạng thái khi NTD vừa khiếu nại, nếu khác 1 thì CTV đã thay đổi rồi
        if ($cvSellingBuy->status_complain != 1) return false;
        $cvSellingBuyData = [
            'status_complain' => $status,
        ];
        //CTV xac nhan
        if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' && $status == 3) {
            $cvSellingBuyData['status_recruitment'] = config('constant.status_recruitment_revert.CancelBuyCVdata');
        }

        if ($status == 3 && ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' ||  $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview')) {
            $cvSellingBuyData['status_payment'] = 3; //Hoan tien
        }

        if ($status == 3 && $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
            $cvSellingBuyData['status_payment'] = 3; //Hoàn tien
        }

        $rec = $cvSellingBuy->rec;
        $employer = $cvSellingBuy->employer;
        //xác nhận thi gửi email cho cả CTV, NTD
        if ($status == 3) {
            //Hoan tien tra NTD
            //CV data
            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' || $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                //ghi log mua
                $this->wareHouseCvSellingHistoryBuyRepository->create([
                    'user_id'                       =>  $cvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                    'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                    'point'                         =>  $cvSellingBuy->point,
                    'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point,
                    'status'                        =>  0
                ]);

                if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv') {
                    //interview ko thay đổi trạng thái
                    //Log thay đổi trang thái
                    //fixbug Khiếu nại CV đã mua  NO12
                    /*$this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id'                       =>  $cvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                        'status_recruitment'            =>  config('constant.status_recruitment_revert.CancelBuyCVdata'),
                        'type'                          =>  'employer',
                        'authority'                     =>  $cvSellingBuy->wareHouseCvSelling->authority
                    ]);*/
                }

                //Cộng point của NTD
                $point = $cvSellingBuy->point;
                // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point;
                $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                $cvSellingBuy->employer->wallet->addAmount($cvSellingBuy->point, $cvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                // $cvSellingBuy->employer->wallet->save();
            }
            //cv onboard
            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                //14 => 'Trial work',
                if ($cvSellingBuy->status_recruitment == 14) {
                    $onboard = $this->wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($cvSellingBuy->id);
                    $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                    $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                    $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                    $dateComplain = Carbon::createFromFormat('Y-m-d H:i:s', $cvSellingBuy->date_complain);
                    $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                    $dateComplain =  strtotime($dateComplain);
                    $percent = $point = 0;
                    //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                    if ($createDateTrailWork30 >= $dateComplain) {
                        $percent = 100;
                        $point = $cvSellingBuy->point;
                    }
                    //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                    if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                        $percent = 70;
                        $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                    }
                    //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                    if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                        $percent = 50;
                        $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                    }
                    //ghi log hoan tien
                    $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $cvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  $percent,
                        'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $point,
                        'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $point,
                        'status'                        =>  0
                    ]);

                    //Cộng point của NTD
                    // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                    $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                    $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                    // $cvSellingBuy->employer->wallet->save();
                }
                //Hoàn cả
                else {
                    $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($cvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($wareHouseCvSellingHistoryBuyData) {
                        foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $this->wareHouseCvSellingHistoryBuyRepository->create([
                                'user_id'                       =>  $cvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                                'point'                         =>  $value->point,
                                'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $value->point,
                                'status'                        =>  0
                            ]);
                            $point += $value->point;
                        }
                    }
                    //Cộng point của NTD
                    // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                    $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                    $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                    // $cvSellingBuy->employer->wallet->save();
                    $cvSellingBuyData['status_payment'] = 2; //Hoàn cọc
                }
            }

            //gửi email cho CTV
            if ($rec) {
                $rec->notify(new RecAgreeComplain($rec, $employer, $cvSellingBuy));
            }
            //gửi email cho NTD
            if ($employer) {
                $employer->notify(new RecAgreeComplainEmployer($rec, $employer, $cvSellingBuy, $point));
            }
        }
        // từ chối thì gửi email cho admin phê duyệt
        if ($status == 2) {
            Mail::to(config('settings.global.email_admin'))->send(new RecRefuseComplain($rec, $employer, $cvSellingBuy));
            //sau 7 ngày CTV từ chối, mà Admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPoint::dispatch($cvSellingBuy->id, 2)->delay(now()->addMinutes(7 * 24 * 60));
        }


        $cvSellingBuy->update($cvSellingBuyData);
        return $cvSellingBuy;
    }
    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * CTV xác nhận, tù chối lịch phỏng vấn
     * HT NTD
     */
    public function updateScheduleInterview($id, $statusRecruitment, $user = null)
    {
        $wareHouseCvSellingBuyBook = $this->wareHouseCvSellingBuyBookRepository->find($id);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyBook->wareHouseCvSellingBuy;

        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview' || $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
            if ($wareHouseCvSellingBuyBook->status > 0) {
                throw new \Exception('Lịch phỏng vấn đã được xử lý');
            }
            if ($user == null) {
                $user = auth('client')->user();
            }
            //Tu choi phong van 5 => 'Reject Interview schedule',
            if ($statusRecruitment == 5) {
                //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
                $wareHouseCvSellingBuyBook->update([
                    'status' => 2
                ]);
                $employer = $wareHouseCvSellingBuyBook->employer;
                $employer->notify(new EmailRejectInterView($employer, $wareHouseCvSellingBuyBook, $wareHouseCvSellingBuy));

                $countBookReject = $this->wareHouseCvSellingBuyBookRepository->countBookReject($wareHouseCvSellingBuyBook->ntd_id, $wareHouseCvSellingBuy->id);
                if ($countBookReject >= 3) {
                    $statusRecruitment = 9; //9 => 'Candidate Cancel Interview',
                    // Từ chối quá 3 lần
                    // Trả tiền Cọc
                    //CV interview
                    $point = 0;
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                        //ghi log hoan tien
                        $this->wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $wareHouseCvSellingBuy->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                            'status'                        =>  3 //Hoan tien
                        ]);
                        //update status_payment
                        $wareHouseCvSellingBuy->status_payment = 3; //Hoan tien
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        $point = $wareHouseCvSellingBuy->point;
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }
                    //cv onboard
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                        $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                        //hoàn bao nhiêu point
                        $point = 0;
                        if ($wareHouseCvSellingHistoryBuyData) {
                            foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                $value->status = 1;
                                $value->save();
                                //ghi log hoan tien
                                $this->wareHouseCvSellingHistoryBuyRepository->create([
                                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                    'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                                    'point'                         =>  $value->point,
                                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                                    'status'                        =>  0
                                ]);
                                $point += $value->point;
                            }
                        }
                        $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }

                    $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
                        Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
                    } else {
                        $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
                    }
                } else {
                    OutOfDateBookInterview::dispatch($wareHouseCvSellingBuy, $user)->delay(now()->addMinutes(7 * 24 * 60));
                }
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $statusRecruitment,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $user);
            }
            //dong y phong van 7 => 'Waiting Interview',
            if ($statusRecruitment == 7) {
                //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
                $wareHouseCvSellingBuyBook->update([
                    'status' => 1
                ]);
                $employer = $wareHouseCvSellingBuyBook->employer;
                $employer->notify(new EmailConfirmInterView($employer, $wareHouseCvSellingBuyBook, $wareHouseCvSellingBuy));
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $statusRecruitment,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $user);
                //Sau 120h sau khi đổi sang status "Waiting Interview" NTD k cập nhật status sẽ tự động chuyển sang status Pass Interview
                //sửa logic thành:
                //Sau 7 ngày sau khi đổi sang status “Waiting interview” , NTD ko cập nhật trạng thái sẽ tự động chuyển về  “Pass Interview”
                //thời gian 7 ngày bắt đầu tính từ thời điểm phỏng vấn"
                //                PassInterview::dispatch($wareHouseCvSellingBuy->id,$user)->delay(now()->addMinutes(7 * 24 * 60));
                //DungDQ sua tiep vao day ,tinh lai time
                $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $wareHouseCvSellingBuyBook->date_book)
                    ->startOfDay()
                    ->addMinutes($wareHouseCvSellingBuyBook->book_time_minute + 7 * 24 * 60);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') PassInterview::dispatch($wareHouseCvSellingBuy->id, $user)->delay($timeInterval);
            }
        }

        return $wareHouseCvSellingBuyBook;
    }


    public function updateScheduleOnboard($onboardId, $statusRecruitment)
    {
        $onboard = $this->wareHouseCvSellingBuyOnboardRepository->find($onboardId);
        $wareHouseCvSellingBuy = $onboard->wareHouseCvSellingBuy;
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {

            $user = auth('client')->user();
            //13 => 'Waiting onboard'
            if ($statusRecruitment == 13) {
                $onboard->update([
                    'status' => 1
                ]);
                $employer = $onboard->employer;
                $employer->notify(new EmailConfirmOnboard($employer, $onboard, $wareHouseCvSellingBuy));
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $statusRecruitment,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $user);
            }
            //12 => 'Reject Offer',
            if ($statusRecruitment == 12) {
                $onboard->update([
                    'status' => 2
                ]);
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $statusRecruitment,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $user);

                $employer = $wareHouseCvSellingBuy->employer;
                $employer->notify(new EmailRejectOnboard($employer, $onboard, $wareHouseCvSellingBuy));
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelOnboardToRec($wareHouseCvSellingBuy));
                }
                //todo hoàn cọc
                DepositRefundRejectOffer::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(48 * 60));;
            }
        }
    }
    /**
     * @param $id
     * @param $statusRecruitment
     * @return mixed
     * Thanh toan tien cho CTV : Interview
     *
     */
    public function payCtv($wareHouseCvSellingBuy)
    {
        //Cộng tiền trả CTV
        //1. payins
        $bonusRepository = resolve(BonusRepository::class);
        $payinMonthRepository = resolve(PayinMonthRepository::class);
        $payinMonth = $payinMonthRepository->findByWarehouseCvSellingBuyId($wareHouseCvSellingBuy->id);
        //nếu thanh toán rồi , thì ko thanh toán lại
        Log::info('start pay ', [
            'xxxx: ' => $wareHouseCvSellingBuy
        ]);
        if (!$payinMonth) {
            Log::info('start 11111111111 pay ', [
                'yyyy: ' => $wareHouseCvSellingBuy
            ]);
            $now = \Carbon\Carbon::now();
            $year = $now->year;
            $month = $now->month;
            $bonusCheck = $bonusRepository->getBonusByUser($wareHouseCvSellingBuy->rec->id, $month, $year);
            //nếu ủy quyền thì thanh toán 20% price
            $price = $wareHouseCvSellingBuy->price;
            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) $price = (int) round(($wareHouseCvSellingBuy->price * 20) / 100);


            if (!$bonusCheck) {
                //nếu tháng/năm chưa có thì insert mới
                $bonusRepository->create(
                    [
                        'user_id'   => $wareHouseCvSellingBuy->rec->id,
                        'year'      => $year,
                        'month'     => $month,
                        'price'     => $price,
                    ]
                );
            } else {
                //nếu có doanh thu từ trước thì cộng dồn
                $bonusCheck->price += $price;
                $bonusCheck->save();
            }
            //từng giao dịch trong tháng
            $payinMonthRepository->create(
                [
                    'user_id'                       => $wareHouseCvSellingBuy->rec->id,
                    'warehouse_cv_selling_buy_id'   => $wareHouseCvSellingBuy->id,
                    'year'                          => $year,
                    'month'                         => $month,
                    'price'                         => $price,
                    'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                ]
            );
            //2. wallets
            // $wareHouseCvSellingBuy->rec->wallet->price  = $wareHouseCvSellingBuy->rec->wallet->price + $price;
            $wareHouseCvSellingBuy->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
            $wareHouseCvSellingBuy->rec->wallet->addPrice($price, $wareHouseCvSellingBuy, 'Thanh toán tiền', 'change_status_recruitment');
            // $wareHouseCvSellingBuy->rec->wallet->save();
            //3. update status_complain =4 để không hoàn tiền lần nữa
            if ($wareHouseCvSellingBuy->status_complain) {
                $wareHouseCvSellingBuy->save();
            }
            $wareHouseCvSellingBuy->rec->notify(new PaymentInterviewToRec($wareHouseCvSellingBuy, $price));
        }
    }
    /**
     * @param $id
     * @param
     * @return mixed
     * Thanh toan tien cho CTV : Onboard
     *
     */
    public function payOnboardCtv($wareHouseCvSellingBuy, $percent)
    {
        //Cộng tiền trả CTV
        //1. payins
        $bonusRepository = resolve(BonusRepository::class);
        $payinMonthRepository = resolve(PayinMonthRepository::class);
        $countPayinMonth = $payinMonthRepository->countByWarehouseCvSellingBuyId($wareHouseCvSellingBuy->id);
        //nếu thanh toán rồi , thì ko thanh toán lại
        Log::info('start pay onboard', [
            'xxxx: ' => $wareHouseCvSellingBuy
        ]);
        //nếu ủy quyền thì thanh toán 20% price
        $pricePriority = $wareHouseCvSellingBuy->price;
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) $pricePriority = (int) round(($wareHouseCvSellingBuy->price * 20) / 100);

        //THanh toan toi da 3 lan
        if ($countPayinMonth <= 3) {
            Log::info('start 11111111111 pay onboard', [
                'yyyy: ' => $wareHouseCvSellingBuy
            ]);
            $now = \Carbon\Carbon::now();
            $year = $now->year;
            $month = $now->month;
            $bonusCheck = $bonusRepository->getBonusByUser($wareHouseCvSellingBuy->rec->id, $month, $year);
            $price = (int) round(($pricePriority * $percent) / 100);
            if (!$bonusCheck) {
                //nếu tháng/năm chưa có thì insert mới
                $bonusRepository->create(
                    [
                        'user_id'   => $wareHouseCvSellingBuy->rec->id,
                        'year'      => $year,
                        'month'     => $month,
                        'price'     => $price
                    ]
                );
            } else {
                //nếu có doanh thu từ trước thì cộng dồn
                $bonusCheck->price += $price;
                $bonusCheck->save();
            }

            //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
            $status = 8;
            if ($percent == 15) {
                //TODO Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
                //Gửi mail cho CTV : https://docs.google.com/document/d/1audIV5bLm_84XyAYFGhzL2ybA6gFM8aAObSLxLyXXBo/edit?usp=sharing
                $wareHouseCvSellingBuy->rec->notify(new PaymentTrialWork($wareHouseCvSellingBuy, 1, $price, $percent));
                $status = 5;
            }
            //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
            if ($percent == 10) {
                //TODO Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
                //Gửi mail cho CTV: https://docs.google.com/document/d/1Yc9bDcm8yLE3UGjSzzle1miPw4mqOUaGlMkEBGM3qMA/edit?usp=sharing
                $wareHouseCvSellingBuy->rec->notify(new PaymentTrialWork($wareHouseCvSellingBuy, 2, $price, $percent));
                $status = 6;
            }
            //Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
            if ($percent == 75) {
                //TODO Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
                //Gửi mail cho CTV: https://docs.google.com/document/d/1ujQjeIIbXOJukZHLu_Xsjw0AwS9siZOKq4G512Nvztk/edit?usp=sharing
                $wareHouseCvSellingBuy->rec->notify(new PaymentTrialWork($wareHouseCvSellingBuy, 3, $price, $percent));
            }

            //từng giao dịch trong tháng
            $payinMonthRepository->create(
                [
                    'user_id'                       => $wareHouseCvSellingBuy->rec->id,
                    'warehouse_cv_selling_buy_id'   => $wareHouseCvSellingBuy->id,
                    'year'                          => $year,
                    'month'                         => $month,
                    'price'                         => $price,
                    'percent'                       => $percent,
                    'status'                        => $status //8 => 'Hoàn tất thanh toán',
                ]
            );
            //2. wallets
            // $wareHouseCvSellingBuy->rec->wallet->price  = $wareHouseCvSellingBuy->rec->wallet->price + $price;
            $wareHouseCvSellingBuy->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
            $wareHouseCvSellingBuy->rec->wallet->addPrice($price, $wareHouseCvSellingBuy, 'Thanh toán tiền', 'change_status_recruitment');
            // $wareHouseCvSellingBuy->rec->wallet->save();
            //3. update status_complain =4 để không hoàn tiền lần nữa
            if ($wareHouseCvSellingBuy->status_complain) {
                $wareHouseCvSellingBuy->save();
            }
        }
    }

    public function updateScheduleRecruitment($id, $statusRecruitment)
    {
        $wareHouseCvSellingBuyHistoryStatus = $this->wareHouseCvSellingBuyHistoryStatusRepository->find($id);
        $wareHouseCvSellingBuyHistoryStatus->update([
            'status_recruitment' => $statusRecruitment,
        ]);

        return $wareHouseCvSellingBuyHistoryStatus;
    }

    /**
     * @param $request
     * @return mixed
     * Popup thay doi trang thai
     */
    public function warehouseCvSellingBuyChangeStatus($request)
    {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($request['warehouse_cv_selling_buy_id']);

        //interview
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
            //10 => 'Fail Interview',
            if ($request['status_recruitment'] == 10) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailIInterviewToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusFailIInterview($wareHouseCvSellingBuy));
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
                //sau 24h thi Thanh toan tien tra CTV -> interview
                PayInterview::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(24 * 60));
            }
            //            8 => 'Pass Interview',
            if ($request['status_recruitment'] == 8) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusPassInterview($wareHouseCvSellingBuy));
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
                //sau 24h thi Thanh toan tien tra CTV -> interview
                PayInterview::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(24 * 60));
            }
            //9 => 'Candidate Cancel Interview',
            if ($request['status_recruitment'] == 9) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                //line số 52, 96 TODO dũng
                //ghi log hoan tien
                $this->wareHouseCvSellingHistoryBuyRepository->create([
                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                    'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                    'point'                         =>  $wareHouseCvSellingBuy->point,
                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                    'status'                        =>  3 //Hoan tien
                ]);
                //update status_payment
                $wareHouseCvSellingBuy->status_payment = 3; //Hoan tien
                $wareHouseCvSellingBuy->save();
                //Cộng point của NTD
                $point = $wareHouseCvSellingBuy->point;
                // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                // $wareHouseCvSellingBuy->employer->wallet->save();

                $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
            }
        }

        // onboard
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {

            //Pass Interview
            if ($request['status_recruitment'] == 8) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);

                //offering 11
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => 11
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);

                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusPassInterview($wareHouseCvSellingBuy));
                }
            }
            //10 => 'Fail Interview',
            if ($request['status_recruitment'] == 10) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailIInterviewToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusFailIInterview($wareHouseCvSellingBuy));
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);

                $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $point = 0;
                if ($wareHouseCvSellingHistoryBuyData) {
                    foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $this->wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $value->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                            'status'                        =>  0
                        ]);
                        $point += $value->point;
                    }
                    $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                    $wareHouseCvSellingBuy->save();
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                }
            }

            //9 => 'Candidate Cancel Interview',
            if ($request['status_recruitment'] == 9) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);                //cv onboard
                $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $point = 0;
                if ($wareHouseCvSellingHistoryBuyData) {
                    foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $this->wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $value->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                            'status'                        =>  0
                        ]);
                        $point += $value->point;
                    }
                    $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                    $wareHouseCvSellingBuy->save();
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                }
                $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
            }

            //15 => 'Cancel onboard',
            if ($request['status_recruitment'] == 15) {
                //13 => 'Waiting onboard' chi o trang thai nay thi moi dc thay doi sang 15 => 'Cancel onboard',
                if ($wareHouseCvSellingBuy->status_recruitment != 13) return false;
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment']
                ]);
                $onboard = $this->wareHouseCvSellingBuyOnboardRepository->getCurrentByWarehouseCvBuyId($wareHouseCvSellingBuy->id);
                if ($onboard) {
                    $onboard->update([
                        'status' => 2
                    ]);
                }
                $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $point = 0;
                if ($wareHouseCvSellingHistoryBuyData) {
                    foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $this->wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $value->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                            'status'                        =>  0
                        ]);
                        $point += $value->point;
                    }
                    $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                    $wareHouseCvSellingBuy->save();
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                }

                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);

                $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelOnboardToEmployer($wareHouseCvSellingBuy, $point));
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelOnboardToRec($wareHouseCvSellingBuy));
                }
            }

            //14 => 'Trial work',
            if ($request['status_recruitment'] == 14) {

                $point = $wareHouseCvSellingBuy->point - (0.1 * $wareHouseCvSellingBuy->point);
                //13 => 'Waiting onboard' chi o trang thai nay thi moi dc thay doi sang 14 => 'Trial work',
                if ($wareHouseCvSellingBuy->status_recruitment != 13) return false;
                //check so point của NTD neu ko đủ thi dừng  + $wareHouseCvSellingBuy->point
                if ($point > $wareHouseCvSellingBuy->employer->wallet->amount) {
                    //Luu vao 1 table Ghi nợ, user_id (ntd) status = 0 chưa trả nợ
                    //sau khi NTD nạp tiền -> đủ tiền thì sẽ thanh toán -> status đã thanh toán = 1
                    $this->paymentDebitRepository->create([
                        'warehouse_cv_selling_buy_id' => $wareHouseCvSellingBuy->id,
                        'user_id' => $wareHouseCvSellingBuy->user_id,
                        'point' => $point,
                    ]);
                    $wareHouseCvSellingBuy->employer->notify((new RemindPaymentDebit($wareHouseCvSellingBuy))
                        ->delay([
                            'mail' => now()->addMinutes(24 * 60),
                        ]));
                    $start = 10;
                    while ($start <= 15) {
                        $wareHouseCvSellingBuy->employer->notify((new RemindPaymentDebit($wareHouseCvSellingBuy))
                            ->delay([
                                'mail' => now()->addMinutes($start * 24 * 60),
                            ]));
                        $start++;
                    }
                } else {
                    $percent = 90; //đặt cọc lần 1 10%, trailwork thanh toan nốt 90%
                    //trừ point của NTD
                    $balance  = $wareHouseCvSellingBuy->employer->wallet->amount - $point;
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $balance;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Thanh toán tiền', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                    //ghi log mua
                    $wareHouseCvSellingHistoryBuy = $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'type'                          =>  0, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  $percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $point,
                        'balance'                       =>  $balance,
                        'status'                        =>  0
                    ]);
                    $paymentStatus = 4;
                }

                $updateWareHouseCvSellingBuy = [
                    'status_recruitment' => $request['status_recruitment'],
                ];

                if (!empty($paymentStatus)) {
                    $updateWareHouseCvSellingBuy['status_payment'] = $paymentStatus;
                }

                //update Trial work
                $wareHouseCvSellingBuy->update($updateWareHouseCvSellingBuy);
                //update status history: Trial work
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusTrailWorkToAdmin($wareHouseCvSellingBuy));
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusTrailWorkToAuthorityRec($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusTrailWorkToRec($wareHouseCvSellingBuy));
                }
                //set queue sau 55 ngày  Trail work gửi email
                $start = 55;
                $onboard = $this->wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($wareHouseCvSellingBuy->id);
                $carbonDate = $currentDateBook = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);
                while ($start < 60) {
                    $addTime = clone $carbonDate;
                    $wareHouseCvSellingBuy->employer->notify((new RemindExpireTrailWork($wareHouseCvSellingBuy, $start))
                        ->delay([
                            'mail' => $addTime->addMinutes($start * 24 * 60),
                        ]));
                    $start++;
                }

                //sau 7 ngay het han trailwork (2 thang) = 67 ngay nếu NTD ko đổi trạng thái thì tự động đổi sang 16 => 'Success Recruitment',
                $addRecuitmentTime = clone $carbonDate;
                SuccessRecuitment::dispatch($wareHouseCvSellingBuy->id)->delay($addRecuitmentTime->addMinutes(67 * 24 * 60));
                //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
                $add30Time = clone $carbonDate;
                PayOnboard::dispatch($wareHouseCvSellingBuy->id, 15)->delay($add30Time->addMinutes(30 * 24 * 60));
                //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
                $add45Time = clone $carbonDate;
                PayOnboard::dispatch($wareHouseCvSellingBuy->id, 10)->delay($add45Time->addMinutes(45 * 24 * 60));
                // Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
                $add67Time = clone $carbonDate;
                PayOnboard::dispatch($wareHouseCvSellingBuy->id, 75)->delay($add67Time->addMinutes(67 * 24 * 60));
            }

            //16 => 'Success Recruitment',
            if ($request['status_recruitment'] == 16) {
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment'],
                    'status_payment' => 4,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusSuccessRecruitmentToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusSuccessRecruitmentToRec($wareHouseCvSellingBuy));
                }
            }

            //17 => 'Fail trail work',
            if ($request['status_recruitment'] == 17) {
                //14 => 'Trial work', chỉ 14 dc đổi sang 17
                if ($wareHouseCvSellingBuy->status_recruitment != 14) return false;
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $request['status_recruitment'],
                    'status_payment' => 3,
                ]);
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailTrialWorkToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusFailTrialWorkToRec($wareHouseCvSellingBuy));
                }
                //Hoàn tiền NTD
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth('client')->user(), $request['assessment']);
                $bookOnboard = $this->wareHouseCvSellingBuyOnboardRepository->getBookBySellingBuyId($wareHouseCvSellingBuy->id);
                $createDateTrailWork30 = strtotime('+30 day', strtotime($bookOnboard->date_book));
                $createDateTrailWork60 = strtotime('+60 day', strtotime($bookOnboard->date_book));
                $createDateTrailWork67 = strtotime('+67 day', strtotime($bookOnboard->date_book));
                $now = \Carbon\Carbon::now()->format('Y-m-d 23:59:59');
                $now =  strtotime($now);
                $percent = $point = 0;
                //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                if ($createDateTrailWork30 >= $now) {
                    $percent = 100;
                    $point = $wareHouseCvSellingBuy->point;
                }
                //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                if ($createDateTrailWork30 < $now && $now <= $createDateTrailWork60) {
                    $percent = 70;
                    $point = (int) round(($wareHouseCvSellingBuy->point * $percent) / 100);
                }
                //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                if ($createDateTrailWork60 < $now && $now <= $createDateTrailWork67) {
                    $percent = 50;
                    $point = (int) round(($wareHouseCvSellingBuy->point * $percent) / 100);
                }
                $wareHouseCvSellingBuy->employer->notify(new ChangeStatusFailTrialWorkToEmployer($wareHouseCvSellingBuy, $point));
                //ghi log hoan tien
                $this->wareHouseCvSellingHistoryBuyRepository->create([
                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                    'percent'                       =>  $percent,
                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                    'point'                         =>  $point,
                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $point,
                    'status'                        =>  0
                ]);

                //Cộng point của NTD
                // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                // $wareHouseCvSellingBuy->employer->wallet->save();
            }
        }


        return $wareHouseCvSellingBuy;
    }
    /**
     * @param Request $request
     * Ứng viên đồng ý hoặc từ chối khi NTD mua CV
     */
    public function verifyEmailCandidate($token, $type)
    {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->findToken($token);
        $statusRecruitment = $type == 1 ? 3 : 2;   //type = 1 dong y, type = 2 tu choi
        //3 => 'Waiting setup interview',
        //2 => 'Candidate Cancel Apply',
        //1 => 'Waiting candidate confirm',
        if ($wareHouseCvSellingBuy && $wareHouseCvSellingBuy->status_recruitment == config('constant.status_recruitment_revert.Waitingcandidateconfirm')) {
            //dong y
            if ($statusRecruitment == 3) {
                $update = [
                    'status_recruitment' => $statusRecruitment,
                    'token' => null
                ];
                $wareHouseCvSellingBuy->update($update);
                //gui mail cho ntd
                $wareHouseCvSellingBuy->employer->notify(new CandidateConfirmRecruitment($wareHouseCvSellingBuy));
                $this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                    'status_recruitment'            =>  $statusRecruitment,
                    'candidate_name'                =>  $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name,
                    'authority'                     =>  $wareHouseCvSellingBuy->wareHouseCvSelling->authority,
                ]);
                //queue check sau 7 ngay chua xac nhan thi tu dong tu choi
                CandidateCancelInterview::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addDays(7));
            }
            //tu choi
            else {
                //gui mail cho ctv
                $wareHouseCvSellingBuy->wareHouseCvSelling->user->notify(new CandidateRejectRecruitmentRec($wareHouseCvSellingBuy));

                //gui mail cho ntd
                $wareHouseCvSellingBuy->employer->notify(new CandidateRejectRecruitmentEmployer($wareHouseCvSellingBuy));

                //gui mail cho admin
                Mail::to(config('settings.global.email_admin'))->send(new CandidateRejectRecruitmentAdmin($wareHouseCvSellingBuy));

                //interview
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                    Log::info('interview $wareHouseCvSellingBuy 222222: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                    //ghi log hoan tien
                    $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $wareHouseCvSellingBuy->point,
                        'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                        'status'                        =>  0
                    ]);
                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $wareHouseCvSellingBuy->status_recruitment = config('constant.status_recruitment_revert.CandidateCancelApply');
                    $wareHouseCvSellingBuy->status_payment = 3; //3 => 'Hoàn tiền'
                    $wareHouseCvSellingBuy->token = null;
                    $wareHouseCvSellingBuy->save();
                    //Log thay đổi trang thái
                    $this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'status_recruitment'            =>  config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name'                =>  $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name,
                        'authority'                     =>  $wareHouseCvSellingBuy->wareHouseCvSelling->authority
                    ]);
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                    Log::info('interview $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                }
                //onboard
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                    //2 => 'Candidate Cancel Apply',
                    $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    Log::info('onboard $wareHouseCvSellingBuy 222222: ', [
                        'xxxx: ' => $wareHouseCvSellingHistoryBuyData
                    ]);
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($wareHouseCvSellingHistoryBuyData) {
                        foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $this->wareHouseCvSellingHistoryBuyRepository->create([
                                'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                                'point'                         =>  $value->point,
                                'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                                'status'                        =>  0
                            ]);
                            $point += $value->point;
                        }
                    }

                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $wareHouseCvSellingBuy->status_recruitment = config('constant.status_recruitment_revert.CandidateCancelApply');
                    $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                    $wareHouseCvSellingBuy->token = null;
                    $wareHouseCvSellingBuy->save();
                    //Log thay đổi trang thái
                    $this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'status_recruitment'            =>  config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name'                =>  $wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name,
                        'type'                          =>  'employer',
                        'authority'                     =>  $wareHouseCvSellingBuy->wareHouseCvSelling->authority
                    ]);
                    //Cộng point của NTD
                    // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                    $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                    $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
                    // $wareHouseCvSellingBuy->employer->wallet->save();
                    Log::info('onboard $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $wareHouseCvSellingBuy
                    ]);
                }
            }
            return true;
        }
        return false;
    }

    public function find($id)
    {
        return $this->wareHouseCvSellingBuyRepository->find($id);
    }

    public function getLastHistoryByStatus($warehouseCvSellingBuyId, $statusRecruitment)
    {
        return $this->wareHouseCvSellingBuyHistoryStatusRepository->getLastHistoryByStatus($warehouseCvSellingBuyId, $statusRecruitment);
    }

    public function getMessageRejectInterview($cvSellingBuy, $bock)
    {
        if ($cvSellingBuy->status_recruitment != 5) {
            return null;
        }
        return $bock->last();
    }

    public function cancelInterview($id)
    {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($id);
        $user = auth('client')->user();
        $statusRecruitment = 9;
        $point = 0;
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
            //ghi log hoan tien
            $this->wareHouseCvSellingHistoryBuyRepository->create([
                'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                'point'                         =>  $wareHouseCvSellingBuy->point,
                'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                'status'                        =>  3 //Hoan tien
            ]);
            //update status_payment
            $wareHouseCvSellingBuy->status_payment = 3; //Hoan tien
            $wareHouseCvSellingBuy->save();
            //Cộng point của NTD
            $point = $wareHouseCvSellingBuy->point;
            // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
            $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
            $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Hoàn tiền', 'change_status_recruitment');
            // $wareHouseCvSellingBuy->employer->wallet->save();
        }
        //cv onboard
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
            $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
            //hoàn bao nhiêu point
            $point = 0;
            if ($wareHouseCvSellingHistoryBuyData) {
                foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                    //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                    $value->status = 1;
                    $value->save();
                    //ghi log hoan tien
                    $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $value->point,
                        'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                        'status'                        =>  0
                    ]);
                    $point += $value->point;
                }
            }
            $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
            $wareHouseCvSellingBuy->save();
            //Cộng point của NTD
            // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
            $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
            $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Hoàn cọc', 'change_status_recruitment');
            // $wareHouseCvSellingBuy->employer->wallet->save();
        }

        $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
        } else {
            $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
        }
        $wareHouseCvSellingBuy->update([
            'status_recruitment' => $statusRecruitment,
        ]);
        $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $user);
        return $wareHouseCvSellingBuy;
    }

    public function buyCVByToken($email_employer, $view_id, $view_token)
    {
        $selling = $this->wareHouseCvSellingService->checkView($view_id, $view_token);
        if ($selling) {
            if ($selling->type_of_sale != 'cv') {
                return response()->json(['success' => false, 'message' => 'Không thể xem hồ sơ này']);
            }
            if (!$this->userService->checkEmpoyerHasBonusByEmail($email_employer)) { // kiểm tra xem đã được thưởng tiền refer hay chưa
                $this->userService->addBonusEmployerRefer($email_employer); // thưởng tiền refer (200.000đ)
            }
            // mua CV
            $params = [
                'id' => $selling->id,
            ];
            $result = $this->buyCV($params);
            // dd($result);
            if ($result) {
                $user = auth('client')->user();
                $amount = $user->wallet->amount;
                return true;
            }
        }
        return false;
    }
    public function addBonusEmployerRefer($email_employer)
    {
        if (!$this->userService->checkEmpoyerHasBonusByEmail($email_employer)) { // kiểm tra xem đã được thưởng tiền refer hay chưa
            $this->userService->addBonusEmployerRefer($email_employer); // thưởng tiền refer (200.000đ)
            return true;
        }
        return false;
    }
}