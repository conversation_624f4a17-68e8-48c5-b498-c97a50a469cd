<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\PostMetaRepository;
use App\Repositories\PostRepository;
use App\Repositories\PostSeoRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;

class PostService
{
    protected $postRepository;
    protected $postMetaRepository;
    protected $postSeoRepository;

    public function __construct(
        PostRepository $postRepository,
        PostMetaRepository $postMetaRepository,
        PostSeoRepository $postSeoRepository
    )
    {
        $this->postRepository = $postRepository;
        $this->postMetaRepository = $postMetaRepository;
        $this->postSeoRepository = $postSeoRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        return $this->postRepository->getListPost($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'title_vi',
                    'data-orderable' => 'false',
                ],
                'value' => 'Title VN',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'title_en',
                    'data-orderable' => 'false',
                ],
                'value' => 'Title EN',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'post_meta.position',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderPostPosition',
                ],
                'value' => 'Khu Vực',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'category',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCategory',
                ],
                'value' => 'Danh mục',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderStatusPost',
                ],
                'value' => 'Trạng thái bài viết',
            ],
        ];

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('post-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function createService($params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');

        $type = isset($params['type']) ? config('constant.type_post.video') : config('constant.type_post.post');

        $data = [
            'title_vi' => $params['title_vi'],
            'title_en' => $params['title_en'],
            'slug_vi' => Common::buildSlug($params['title_vi']),
            'slug_en' => Common::buildSlug($params['title_en']),
            'category_id' => $params['category_id'],
            'description_vi' => $params['description_vi'],
            'description_en' => $params['description_en'],
            'date' => Carbon::createFromFormat('d/m/Y', $params['date'])->format('Y-m-d'),
            'type' => $type,
            'tags' => implode(',', $params['tags']),
            'content' => $params['content'],
            'url_video' => $params['url_video'],
            'status' => $params['status'],
            'script' => $params['script'],
            'is_active' => $isActive
        ];

        if (isset($params['banner_img']) && is_file($params['banner_img'])) {
            $data['banner_img'] = FileServiceS3::getInstance()->uploadToS3($params['banner_img'], config('constant.sub_path_s3.post'));
        }

        if (isset($params['banner_detail_img']) && is_file($params['banner_detail_img'])) {
            $data['banner_detail_img'] = FileServiceS3::getInstance()->uploadToS3($params['banner_img'], config('constant.sub_path_s3.banner_detail_img'));
        }

        $post = $this->postRepository->create($data);

        $dataSeo = [
            'post_id' => $post->id,
            'title_vi' => $params['title_vi'],
            'title_en' => $params['title_en'],
            'description_vi' => $params['description_vi'],
            'description_en' => $params['description_en'],
            'keyword_vi' => implode(',', $params['tags']),
            'keyword_en' => implode(',', $params['tags']),
        ];

       $this->postSeoRepository->create($dataSeo);

        $dataMeta = [
            'post_id' => $post->id,
            'view' => rand(100, 500),
            'like' => rand(100, 500)
        ];

        $this->postMetaRepository->create($dataMeta);

        return $post;
    }

    public function detailService($id)
    {
        return $this->postRepository->find($id);
    }

    public function updateService($params, $id)
    {
        $post = $this->detailService($id);
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');

        $type = isset($params['type']) ? config('constant.type_post.video') : config('constant.type_post.post');

        $data = [
            'title_vi' => $params['title_vi'],
            'title_en' => $params['title_en'],
            'slug_vi' => $post->title_vi != $params['title_vi'] ? Common::buildSlug($params['title_vi']) : $post->slug_vi,
            'slug_en' => $post->title_en != $params['title_en'] ? Common::buildSlug($params['title_en']) : $post->slug_en,
            'category_id' => $params['category_id'],
            'description_vi' => $params['description_vi'],
            'description_en' => $params['description_en'],
            'date' => Carbon::createFromFormat('d/m/Y', $params['date'])->format('Y-m-d'),
            'type' => $type,
            'tags' => implode(',', $params['tags']),
            'content' => $params['content'],
            'url_video' => $params['url_video'],
            'status' => $params['status'],
            'script' => $params['script'],
            'is_active' => $isActive
        ];

        if (isset($params['banner_img']) && is_file($params['banner_img'])) {
            $data['banner_img'] = FileServiceS3::getInstance()->uploadToS3($params['banner_img'], config('constant.sub_path_s3.post'));
        }

        if (isset($params['banner_detail_img']) && is_file($params['banner_detail_img'])) {
            $data['banner_detail_img'] = FileServiceS3::getInstance()->uploadToS3($params['banner_detail_img'], config('constant.sub_path_s3.banner_detail_img'));
        }

        $post = $this->postRepository->update($id, [], $data);

        return $post;
    }

    public function updatePostMeta($params, $postId)
    {
        $postMeta = $this->postMetaRepository->findWithPostId($postId);

        if (!$postMeta) {
            $post = $this->postMetaRepository->create([
                'post_id' => $postId,
                'position' => $params['position'],
                'view' => $params['view'],
                'like' => $params['like'],
            ]);
        } else {
            $data = [
                'post_id' => $postId,
                'position' => $params['position'],
                'view' => $params['view'],
                'like' => $params['like'],
            ];
            $post = $this->postMetaRepository->updateWithPostId($postId, $data);
        }
        return $post;
    }

    public function updatePostSeo($params, $postId)
    {
        $postSeo = $this->postSeoRepository->findWithPostId($postId);

        if (!$postSeo) {
            $data = [
                'post_id' => $postId,
                'title_vi' => $params['seo_title_vi'],
                'title_en' => $params['seo_title_en'],
                'description_vi' => $params['seo_description_vi'],
                'description_en' => $params['seo_description_en'],
                'keyword_vi' => $params['seo_keyword_vi'],
                'keyword_en' => $params['seo_keyword_en'],
            ];

            if (isset($params['image']) && is_file($params['image'])) {
                $data['image'] = FileServiceS3::getInstance()->uploadToS3($params['image'], config('constant.sub_path_s3.post-seo'));
            }

            $post = $this->postSeoRepository->create($data);
        } else {

            $data = [
                'post_id' => $postId,
                'title_vi' => $params['seo_title_vi'],
                'title_en' => $params['seo_title_en'],
                'description_vi' => $params['seo_description_vi'],
                'description_en' => $params['seo_description_en'],
                'keyword_vi' => $params['seo_keyword_vi'],
                'keyword_en' => $params['seo_keyword_en'],
            ];

            if (isset($params['image']) && is_file($params['image'])) {
                $data['image'] = FileServiceS3::getInstance()->uploadToS3($params['image'], config('constant.sub_path_s3.post-seo'));
            }

            $post = $this->postSeoRepository->updateWithPostId($postId, $data);
        }
        return $post;
    }

    public function detailPostMeta($id)
    {
        return $this->postMetaRepository->findWithPostId($id);
    }

    public function detailPostSeo($id)
    {
        return $this->postSeoRepository->findWithPostId($id);
    }
}
