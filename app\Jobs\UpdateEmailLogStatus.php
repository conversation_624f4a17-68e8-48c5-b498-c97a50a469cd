<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Mime\Email;
use App\Models\EmailLog;

class UpdateEmailLogStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $email;
    public function __construct(Email $email)
    {
        $this->email = $email;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = $this->email;
        $from = [];
        if (count($email->getFrom())) {
            foreach ($email->getFrom() as $item) {
                $from[] = $item->getAddress();
            }
        }
        $to = [];
        if (count($email->getTo())) {
            foreach ($email->getTo() as $item) {
                $to[] = $item->getAddress();
            }
        }
        $cc = [];
        if (count($email->getCc())) {
            foreach ($email->getCc() as $item) {
                $cc[] = $item->getAddress();
            }
        }

        $hash = md5(implode(',', $to) . ',' . $email->getSubject() . ',' . $email->getHtmlBody());
        // print_r(implode(',', $to) . ',' . $email->getSubject() . ',' . $email->getHtmlBody());
        // print_r($hash);
        $email_log = EmailLog::where('hash', $hash)->where('created_at', '>=', date('Y-m-d H:i:s', strtotime('-5 minute')))->get();
        if ($email_log && count($email_log)) {
            foreach ($email_log as $item) {
                $item->status = 1;
                $item->save();
            }
        }
    }
}