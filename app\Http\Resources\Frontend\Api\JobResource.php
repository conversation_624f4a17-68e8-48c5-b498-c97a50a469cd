<?php

namespace App\Http\Resources\Frontend\Api;

use Carbon\Carbon;
use App\Helpers\Common;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        $cities = Common::getCities();
        $address = isset($this['address']) ? $this['address'] : $this->address;
        $address = json_decode($address, true);

        $area = [];
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a['area']) && isset($cities[$a['area']])) {
                    $area[] = $cities[$a['area']];
                }
            }
        }
        $job_type = isset($this['type']) ? $this['type'] : $this->type;
        $job_meta = $this->jobMeta;
        $job_meta = json_decode($job_meta, true);
        return [
            'title'                     => isset($this['name']) ? $this['name'] : $this->name,
            'slug'                      => isset($this['slug']) ? $this['slug'] : $this->slug,
            'salary_min'                => isset($this['salary_min']) ? $this['salary_min'] : $this->salary_min,
            'salary_max'                => isset($this['salary_max']) ? $this['salary_max'] : $this->salary_max,
            'salary_currency'           => isset($this['salary_currency']) ? $this['salary_currency'] : $this->salary_currency,
            'bonus_self_apply'          => isset($this['bonus_self_apply']) ? $this['bonus_self_apply'] : $this->bonus_self_apply,
            'bonus_currency'            => isset($this['bonus_currency']) ? $this['bonus_currency'] : $this->bonus_currency,
            'bonus_self_apply_currency' => isset($this['bonus_self_apply_currency']) ? $this['bonus_self_apply_currency'] : $this->bonus_self_apply_currency,
            'bonus'                     => isset($this['bonus']) ? $this['bonus'] : $this->bonus,
            'bonus_for_ctv'             => isset($this['bonus_for_ctv']) ? $this['bonus_for_ctv'] : $this->bonus_for_ctv,
            'area'                      => $area,
            'company'                   => [
                'name' => isset($this['company_name']) ? $this['company_name'] : optional($this->company)->name,
                'logo' => isset($this['company_logo']) ? gen_url_file_s3($this['company_logo'], '', false) : gen_url_file_s3(optional($this->company)->logo, '', false),
            ],
            'url'                       => route('job-detail', ['slug' => (isset($this['slug']) ? $this['slug'] : $this->slug)]),
            'type_str'                  => !empty($job_type) ? Common::getJobType($job_type) : '',
            'is_premium'                => isset($job_meta['priority']) && $job_meta['priority'] == config('job.priority')[0] ? true : false,
            'is_hot'                    => isset($job_meta['priority']) && $job_meta['priority'] == config('job.priority')[1] ? true : false,
            'is_new'                    => isset($job_meta['priority']) && $job_meta['priority'] == config('job.priority')[2] ? true : false,
            'job_meta'                  => $job_meta,
            'bonus_type'                => isset($this['bonus_type']) ? ucfirst($this['bonus_type']) : ucfirst($this->bonus_type),
            'priority'                  => optional($this->jobMeta)->priority,
            'urgent'                    => $this->urgent,
            'remote'                    => $this->remote,
            'is_save'                   => Common::checkUserJobCare(auth('sanctum')->id(), $this->id, 'save'),

        ];
    }
}
