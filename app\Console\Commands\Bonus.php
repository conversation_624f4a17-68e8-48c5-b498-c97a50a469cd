<?php

namespace App\Console\Commands;

use App\Repositories\PayinMonthRepository;
use App\Repositories\SettingRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\UserRepository;
use App\Services\Frontend\BonusService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class Bonus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:bonus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tinh tien bonus theo Thang cho tung CTV';
    protected $bonusService;
    protected $year;
    protected $month;
    protected $now;

    public function __construct(
        BonusService $bonusService
    ){
        parent::__construct();
        $this->bonusService = $bonusService;
        $now = Carbon::now();
        $this->now = $now;
        $this->year = $now->year;
        $this->month = $now->month;
    }
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('Command start');
        $userRepo = resolve(UserRepository::class);
        $payinMonthRepo = resolve(PayinMonthRepository::class);
        $numRow = 500;
        $i = 0;
        do {
            $limit = $numRow;
            $offset = $i * $numRow;

            $listUser = $userRepo->getUserWithLimitOffset($limit, $offset);
            if($listUser){
                foreach ($listUser as $value) {
                    //Tinh tien bonus theo thang cho từng CTV
                    $money = self::submitCvByUser($value->id);
                    //Neu la leader thì được thưởng thêm
                    $moneyKpi = $this->bonusService->kpiLeader($value->referral_define, $money, $this->month, $this->year);
                    if($moneyKpi > 0){
                        //cập nhật  money theo thang cho từng CTV
                        //1 money_kpi, 2 money
                        $payinMonth = $payinMonthRepo->findByUser($value->id, 1);
                        if($payinMonth){
                            if($payinMonth->money_kpi != $moneyKpi) {
                                $payinMonth->money_kpi = $moneyKpi;
                                $payinMonth->save();
                            }
                        }else{
                            $now = \Carbon\Carbon::now();
                            $year = $now->year;
                            $month = $now->month;
                            //từng giao dịch trong tháng
                            $payinMonthRepo->create(
                                [
                                    'user_id'                       => $value->id,
                                    'year'                          => $year,
                                    'month'                         => $month,
                                    'money_kpi'                     => $moneyKpi,
                                    'type'                          => 1,
                                ]
                            );
                        }
                    }

                    $this->bonusService->addBonus($value->id, $money, $moneyKpi, $this->month, $this->year);
                }
            }

            $i++;
        } while (count($listUser) == $numRow);
        \Log::info('Command end');
    }

    public function submitCvByUser($userId){
        $submitCvRepo = resolve(SubmitCvRepository::class);
        $settingRepo = resolve(SettingRepository::class);
        $settingData = $settingRepo->getFirstByKey('setting.global.usd_to_vnd');
        $currency = isset($settingData->value) ? $settingData->value : 0;

        $statusAll = [
            config('constant.submit_cvs_status_value.accepted'),
            config('constant.submit_cvs_status_value.pass-interview'),
            config('constant.submit_cvs_status_value.fail-interview'),
            config('constant.submit_cvs_status_value.onboarded')
        ];
        //onboard
        $statusOnboard = config('constant.submit_cvs_status_value.onboarded');
        //interview
        $statusInterview = [
            config('constant.submit_cvs_status_value.pass-interview'),
            config('constant.submit_cvs_status_value.fail-interview')
        ];
        $startOfMonth = date('Y-m-d', strtotime($this->now->copy()->startOfMonth()));
        $startOfNextMonth = date('Y-m-d', strtotime($this->now->copy()->addMonthsNoOverflow()->startOfMonth()));

        //$startOfMonth = $this->now->copy()->startOfMonth();
        //$startOfNextMonth = $this->now->copy()->addMonthsNoOverflow()->startOfMonth();
        $payinMonthRepo = resolve(PayinMonthRepository::class);
        $numRow = 500;
        $i = 0;
        $money = 0;
        do {
            $limit = $numRow;
            $offset = $i * $numRow;
            $listSubmitCv = $submitCvRepo->getWithLimitOffset($userId, $statusAll, $limit, $offset,$this->now);
            if($listSubmitCv){
                foreach ($listSubmitCv as $value) {
                    //cv accepted, pass-interview, fail-interview, onboarded
                    if($value->bonus_type == 'cv'){
                        //tính từ đầu tháng đến cuối tháng 1/6 =< && < 1/7
                        if($value->date_change_status >= $startOfMonth && $value->date_change_status < $startOfNextMonth){
                            //tu ung tuyen
                            if($value->is_self_apply == 1){
                                    //VND
                                $money = $money + $value->bonus_self_apply + $value->bonus_self_apply_incentive;
                            }
                            //gioi thieu ung vien moi
                            else{
                                //VND
                                $money = $money + $value->bonus + $value->incentive;
                            }
                            //cập nhật  money theo thang cho từng CTV
                            //1 money_kpi, 2 money
                            $payinMonth = $payinMonthRepo->findByUserSb($userId, 2, $value->id);
                            if($payinMonth){
                                if($payinMonth->money != $money) {
                                    $payinMonth->money = $money;
                                    $payinMonth->save();
                                }
                            }else{
                                $now = \Carbon\Carbon::now();
                                $year = $now->year;
                                $month = $now->month;
                                //từng giao dịch trong tháng
                                $payinMonthRepo->create(
                                    [
                                        'user_id'                       => $userId,
                                        'year'                          => $year,
                                        'month'                         => $month,
                                        'money'                         => $money,
                                        'type'                          => 2,
                                        'submit_cv_id'                  => $value->id,
                                    ]
                                );
                            }
                        }
                    }
                    //onboard: onboarded
                    if($value->bonus_type == 'onboard' && $value->status == $statusOnboard){
                        //ủy quyền
                        if($value->authorize == 1){
                            //sau 60 ngày thì dc 80% bonus + 100% incentive
                            $dateTmp = strtotime('+60 day',strtotime($value->date_change_status));    //+60 ngay tính từ ngày update status
                            $date_change_status = date("Y-m-d", $dateTmp);
                            if($date_change_status >= $startOfMonth && $date_change_status < $startOfNextMonth){
                                //tu ung tuyen
                                if($value->is_self_apply == 1){
                                    $money = $money + ($value->bonus_self_apply * config('settings.global.authority') / 100);
                                }
                                //gioi thieu ung vien moi
                                else{
                                    $money = $money + ($value->bonus * config('settings.global.authority') / 100);
                                }

                                //cập nhật  money theo thang cho từng CTV
                                //1 money_kpi, 2 money
                                $payinMonth = $payinMonthRepo->findByUserSb($userId, 2, $value->id);
                                if($payinMonth){
                                    if($payinMonth->money != $money) {
                                        $payinMonth->money = $money;
                                        $payinMonth->save();
                                    }
                                }else{
                                    $now = \Carbon\Carbon::now();
                                    $year = $now->year;
                                    $month = $now->month;
                                    //từng giao dịch trong tháng
                                    $payinMonthRepo->create(
                                        [
                                            'user_id'                       => $userId,
                                            'year'                          => $year,
                                            'month'                         => $month,
                                            'money'                         => $money,
                                            'type'                          => 2,
                                            'submit_cv_id'                  => $value->id,
                                        ]
                                    );
                                }
                            }
                        }else{
                            //sau 30 ngày lên onboard thì dc 20% tiền bonus
                            $dateTmp = strtotime('+30 day',strtotime($value->date_change_status));    //+30 ngay tính từ ngày update status
                            $date_change_status = date("Y-m-d", $dateTmp);
                            if($date_change_status >= $startOfMonth && $date_change_status < $startOfNextMonth){
                                //tu ung tuyen
                                if($value->is_self_apply == 1){
                                        //VND
                                    $money = $money + ($value->bonus_self_apply * 0.2);
                                }
                                //gioi thieu ung vien moi
                                else{
                                        //VND
                                    $money = $money + ($value->bonus * 0.2);
                                }
                            }
                            //sau 60 ngày thì dc 80% bonus + 100% incentive
                            $dateTmp = strtotime('+60 day',strtotime($value->date_change_status));    //+60 ngay tính từ ngày update status
                            $date_change_status = date("Y-m-d", $dateTmp);
                            if($date_change_status >= $startOfMonth && $date_change_status < $startOfNextMonth){
                                //tu ung tuyen
                                if($value->is_self_apply == 1){
                                        //VND
                                    $money = $money + ($value->bonus_self_apply * 0.8) + $value->bonus_self_apply_incentive;
                                }
                                //gioi thieu ung vien moi
                                else{
    //                                if($value->bonus_currency == 'USD'){
    //                                    $money = $money + ($value->bonus * $currency * 0.8) + ($value->incentive * $currency);
    //                                }else {
                                        //VND
                                        $money = $money + ($value->bonus * 0.8) + $value->incentive;
    //                                }
                                }
                            }
                            //cập nhật  money theo thang cho từng CTV
                            //1 money_kpi, 2 money
                            $payinMonth = $payinMonthRepo->findByUserSb($userId, 2, $value->id);
                            if($payinMonth){
                                if($payinMonth->money != $money) {
                                    $payinMonth->money = $money;
                                    $payinMonth->save();
                                }
                            }else{
                                $now = \Carbon\Carbon::now();
                                $year = $now->year;
                                $month = $now->month;
                                //từng giao dịch trong tháng
                                $payinMonthRepo->create(
                                    [
                                        'user_id'                       => $userId,
                                        'year'                          => $year,
                                        'month'                         => $month,
                                        'money'                         => $money,
                                        'type'                          => 2,
                                        'submit_cv_id'                  => $value->id,
                                    ]
                                );
                            }

                        }
                    }
                    //interview accepted, pass-interview, fail-interview, onboarded
                    if($value->bonus_type == 'interview' && in_array($value->status, $statusInterview)){
                        //tính từ đầu tháng đến cuối tháng 1/6 =< && < 1/7
                        if($value->date_change_status >= $startOfMonth && $value->date_change_status < $startOfNextMonth){
                            //ủy quyền
                            if($value->authorize == 1){
                                //tu ung tuyen
                                if($value->is_self_apply == 1){
                                    $money = $money + ($value->bonus_self_apply * config('settings.global.authority') / 100);
                                }
                                //gioi thieu ung vien moi
                                else{
                                    $money = $money + ($value->bonus * config('settings.global.authority') / 100);
                                }
                            }else{
                                //tu ung tuyen
                                if($value->is_self_apply == 1){
                                    //VND
                                    $money = $money + $value->bonus_self_apply + $value->bonus_self_apply_incentive;
                                }
                                //gioi thieu ung vien moi
                                else{
                                    //VND
                                    $money = $money + $value->bonus + $value->incentive;
                                }
                            }
                            //cập nhật  money theo thang cho từng CTV
                            //1 money_kpi, 2 money
                            $payinMonth = $payinMonthRepo->findByUserSb($userId, 2, $value->id);
                            if($payinMonth){
                                if($payinMonth->money != $money) {
                                    $payinMonth->money = $money;
                                    $payinMonth->save();
                                }
                            }else{
                                $now = \Carbon\Carbon::now();
                                $year = $now->year;
                                $month = $now->month;
                                //từng giao dịch trong tháng
                                $payinMonthRepo->create(
                                    [
                                        'user_id'                       => $userId,
                                        'year'                          => $year,
                                        'month'                         => $month,
                                        'money'                         => $money,
                                        'type'                          => 2,
                                        'submit_cv_id'                  => $value->id,
                                    ]
                                );
                            }
                        }
                    }
                }
            }

            $i++;
        } while (count($listSubmitCv) == $numRow);

        return $money;
    }
}
