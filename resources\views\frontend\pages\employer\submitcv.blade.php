@php
    use App\Helpers\Common;
@endphp
@extends('frontend.layouts.employer.app')


@section('css_custom')
    <link rel="stylesheet" href="{{ asset2('frontend/asset/css/modal-filter.css') }}">
    <link rel="stylesheet" href="{{ asset2('frontend/asset/css/employer-cv-submit.css') }}">
@endsection

@section('content-collaborator')
    <div id="dashboard-list-recruitment" class="page-modal-filter">
        <div class="content-dashboard-list-recruitment">
            @if ($job)
                <div class="row">
                    <div class="col-md-12">
                        <div class="card card-recruitment">
                            <div class="content-header-recruitment">
                                <div class="title">
                                    {{ $arrLang['thongtincongviec'] }}: <span>{{ $job->name }}</span>
                                </div>
                                <div class="list">
                                    <ul>
                                        <li>{{ $arrLang['trangthai'] }}: @if ($job->is_active)
                                                <span>Active</span>
                                            @else
                                                <span>Inactive</span>
                                            @endif
                                        </li>
                                        <li>{{ $arrLang['soluongtuyen'] }}: <span>{{ $job->vacancies }}</span></li>
                                        <li>{{ $arrLang['ngayhethan'] }}: <span>{{ $job->expire_at_value }}</span></li>
                                        @if (strtotime(date('d-m-Y')) > strtotime(date('d-m-Y', strtotime($job->expire_at))))
                                            <li><span style="color: red">{{ $arrLang['dahethan'] }}</span></li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="row">
                    <div class="col-md-12">
                        <div class="layout1">
                            <div class="row">
                                <div class="col-md-3 col-item-count">
                                    <div class="card">
                                        <div class="row">
                                            <div class="col-4 icon1">
                                                <img
                                                    src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-number9.svg') }}">
                                            </div>
                                            <div class="col-8">
                                                <div class="text-right">
                                                    <div class="title-number">{{ $arrLang['hosoungviendatuyenduoc'] }}
                                                    </div>
                                                    <div class="count-number">{{ $countJobDt }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-item-count">
                                    <div class="card">
                                        <div class="row">
                                            <div class="col-4 icon1">
                                                <img
                                                    src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-number10.svg') }}">
                                            </div>
                                            <div class="col-8">
                                                <div class="text-right">
                                                    <div class="title-number">{{ $arrLang['hosoungviendangcho'] }}</div>
                                                    <div class="count-number">{{ $countJobPending }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 col-item-count">
                                    <div class="card">
                                        <div class="row">
                                            <div class="col-4 icon1">
                                                <img
                                                    src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-number11.svg') }}">
                                            </div>
                                            <div class="col-8">
                                                <div class="text-right">
                                                    <div class="title-number">{{ $arrLang['hosoungviendaduyet'] }}</div>
                                                    <div class="count-number">{{ $countJobAccepted }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-3 col-item-count">
                                    <div class="card">
                                        <div class="row">
                                            <div class="col-4 icon1">
                                                <img
                                                    src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-number12.svg') }}">
                                            </div>
                                            <div class="col-8">
                                                <div class="text-right">
                                                    <div class="title-number">{{ $arrLang['hosoungvienbiloai'] }}</div>
                                                    <div class="count-number">{{ $countJobReject }}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            @endif
            <div class="row">
                <div class="col-md-12">
                    <div class="card-table">
                        <div class="header">
                            <div class="row">
                                <div class="col-md-7">
                                    <div class="row">
                                        <div class="col-md-5">
                                            <div class="title">
                                                {{ $arrLang['danhsachcvdaungtuyen'] }}
                                            </div>
                                        </div>
                                        <div class="col-md-7">

                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="right-header" style="padding-left: 10px">
                                        <form id="form-search-status" class="form-search-status"
                                            action="{{ $url }}">
                                            <div class="input-table">
                                                <select class="select-2-single-choice " id="status_recruitment"
                                                    name="status_recruitment" style="width: 100%">
                                                    <option value="">---</option>
                                                    @foreach ($submitCvsStatus as $k => $item)
                                                        <option value="{{ $k }}"
                                                            {{ isset($_GET['status']) && $_GET['status'] == $k ? 'selected' : '' }}>
                                                            {{ $item }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <input type="text" name="page" value="1" hidden>
                                        </form>
                                        {{--                                        <button class="button-open-filter-modal" type="button">{{ config('settings.' . app()->getLocale() . '.home_job.boloc') }}</button> --}}
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="right-header" style="padding-left: 10px">
                                        <form id="form-search" action="{{ $url }}">
                                            <div class="input-table">
                                                <input placeholder="{{ $arrLang['timungvien'] }}" type="text"
                                                    name="search"
                                                    value="{{ isset($_GET['search']) ? $_GET['search'] : '' }}">
                                            </div>
                                            <input type="text" name="status" id="status_recruitment_val"
                                                value="{{ request()->get('status') }}" hidden>
                                            <input type="text" name="page" value="" hidden>
                                        </form>
                                        {{--                                        <button class="button-open-filter-modal" type="button">{{ config('settings.' . app()->getLocale() . '.home_job.boloc') }}</button> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="content">
                            <div class="table">
                                <table class="table-cv-submit">
                                    <tr class="t-header">
                                        <th>{{ $arrLang['tenungvien'] }}</th>
                                        <th>{{ $arrLang['vitriungtuyen'] }}</th>
                                        <th>{{ $arrLang['tencongviec'] }}</th>
                                        <th>Link CV</th>
                                        <th width="150">TT tuyển dụng</th>
                                        <th>TT thanh toán</th>
                                        <th>TT khiếu Nại</th>
                                        <th>{{ $arrLang['ngayungtuyen'] }}</th>
                                        <th>Liên hệ</th>
                                        <th class="fix-col"></th>
                                    </tr>
                                    @if ($arrSubmitCv->total() > 0)
                                        @foreach ($arrSubmitCv as $k => $item)
                                            @if ($item->submitCvMeta)
                                                @php
                                                    $can_open = $item->canOpenCv();
                                                    $unread_count = $item->unread_discuss_count;
                                                @endphp
                                                <tr>
                                                    <td>{{ $can_open ? $item->submitCvMeta->candidate_name : Common::cutName($item->submitCvMeta->candidate_name) }}
                                                    </td>
                                                    <td>{{ $item->submitCvMeta->candidate_job_title }}</td>
                                                    <td>{{ $item->job->name }}</td>
                                                    <td>
                                                        <div class="view-cv">
                                                            @if (!empty($item->warehouseCv->url_cv_private) && !empty($item->warehouseCv->url_cv_public))
                                                                {{-- @if (($item->bonus_type == 'cv' && $item->status == 18) || ($item->bonus_type == 'interview' && ($item->status == 8 || $item->status == 10)) || ($item->bonus_type == 'onboard' && ($item->status == 14 || $item->status == 16))) --}}
                                                                @if ($can_open)
                                                                    <a href="{{ $item->warehouseCv->url_cv_public }}"
                                                                        target="_blank"><img
                                                                            src="{{ asset2('frontend/asset/images/cv-bought/icon-eye.svg') }}"></a>
                                                                    <a
                                                                        href="{{ route('employer-download-file', ['url' => $item->warehouseCv->url_cv_public]) }}"><img
                                                                            src="{{ asset2('frontend/asset/images/cv-bought/icon-download.svg') }}"></a>
                                                                @else
                                                                    <a href="{{ $item->warehouseCv->url_cv_private }}"
                                                                        target="_blank">
                                                                        <img
                                                                            src="{{ asset2('frontend/asset/images/cv-bought/icon-eye.svg') }}"></a>
                                                                    <a
                                                                        href="{{ route('employer-download-file', ['url' => $item->warehouseCv->url_cv_private]) }}"><img
                                                                            src="{{ asset2('frontend/asset/images/cv-bought/icon-download.svg') }}"></a>
                                                                @endif
                                                            @endif
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span
                                                            class="text-c-1">{{ $item->status_recruitment_value }}</span>
                                                        @if ($item->isShowInterviewTime())
                                                            <div class="interview-time">Thời gian: <br>
                                                                {{ $item->getBookTime() }}</div>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if ($item->isWaitingPayment())
                                                            @php
                                                                $label_payment =
                                                                    $item->bonus_type == 'onboard'
                                                                        ? 'Thanh toán đặt cọc'
                                                                        : 'Thanh toán ngay';
                                                            @endphp
                                                            <button type="button"
                                                                class="btn btn-primary btn-sm btn-block"
                                                                onclick="showPricePopup({{ $item->id }})">{{ $label_payment }}</button>
                                                            <button type="button"
                                                                class="btn btn-secondary btn-sm btn-block"
                                                                onclick="showRejectCvModal({{ $item->id }})">Loại
                                                                CV</button>
                                                        @else
                                                            <span
                                                                class="text-c-1">{{ $item->status_payment_value }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        {{ $item->status_complain_value }}
                                                    </td>
                                                    {{-- <td>
                                                        @if ($item->status == 0)
                                                            <span
                                                                class="pending-review">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 1)
                                                            <span
                                                                class="status-accepted">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 2)
                                                            <span
                                                                class="status rejected">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 3)
                                                            <span
                                                                class="status pass-interview">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 4)
                                                            <span
                                                                class="status fail-interview">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 5)
                                                            <span
                                                                class="status onboarded">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 6)
                                                            <span
                                                                class="status cancel">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 7)
                                                            <span
                                                                class="status onboarded">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @elseif($item->status == 8)
                                                            <span
                                                                class="status offering">{{ config('constant.submit_cvs_status.' . $item->status) }}</span>
                                                        @endif
                                                    </td> --}}
                                                    <td>{{ date('d/m/Y', strtotime($item->created_at)) }}</td>
                                                    <td>
                                                        @if ($can_open)
                                                            {{ $item->submitCvMeta->candidate_email }}
                                                            <br>{{ $item->submitCvMeta->candidate_mobile }}
                                                        @else
                                                            Email: **********
                                                            <br>Tel: **********
                                                        @endif
                                                    </td>
                                                    <td class="text-right fix-col">
                                                        <div class="btn-group">
                                                            <button type="button" class="button-action-table"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                @if ($unread_count)
                                                                    <span
                                                                        class="badge top-right-badge badge-danger">{{ $unread_count }}</span>
                                                                @endif
                                                            </button>
                                                            <ul class="dropdown-menu dropdown-menu-end">
                                                                @if (
                                                                    $capnhattrangthai == 1 && $item->can_change_status_manual)
                                                                    <li><a href="javascript:void(0)" {{-- class="update_status_submitcv" --}}
                                                                            class="change-status"
                                                                            data-id="{{ $item->id }}">
                                                                            <img
                                                                                src="{{ asset2('frontend/asset/images/dashboard-ctv/pen-table-edit.svg') }}">
                                                                            {{ $arrLang['capnhattrangthai'] }}</a></li>
                                                                @endif
                                                                <li><a href="javascript:void(0)"
                                                                        class="detail_submit_cv_popup"
                                                                        data-id="{{ $item->id }}">
                                                                        <img
                                                                            src="{{ asset2('frontend/asset/images/dashboard-ctv/icon-table-user.svg') }}">
                                                                        {{ $arrLang['xemchitiet'] }}</a></li>
                                                                {{-- @if ($item->bonus_type != 'cv') --}}
                                                                <li>
                                                                    <a data-toggle="view-discuss"
                                                                        data-type_of_sale="{{ $item->bonus_type }}"
                                                                        data-status_recruitment="{{ $item->status }}"
                                                                        data-id="{{ $item->id }}"
                                                                        href="javascript:void(0)">
                                                                        <img
                                                                            src="{{ asset2('frontend/asset/images/cv-bought/icon-menu-5.svg') }}">
                                                                        Thảo luận với CTV

                                                                        @if ($unread_count)
                                                                            <span
                                                                                class="badge badge-danger">{{ $unread_count }}</span>
                                                                        @endif
                                                                    </a>
                                                                </li>
                                                                {{-- @endif --}}

                                                                @if ($item->can_complain == true)
                                                                    <li class="report">
                                                                        <a href="javascript:void(0)"
                                                                            data-toggle="open-complain"
                                                                            data-id="{{ $item->id }}"
                                                                            @if (!empty($item->warehouseCv->candidate_name)) data-name="{{ $item->warehouseCv->candidate_name }}" @endif>
                                                                            <img
                                                                                src="{{ asset2('frontend/asset/images/cv-bought/icon-menu-6.svg') }}">
                                                                            Khiếu nại
                                                                        </a>
                                                                    </li>
                                                                @endif
                                                                @if ($item->isWaitingPayment())
                                                                    @php
                                                                        $label_payment =
                                                                            $item->bonus_type == 'onboard'
                                                                                ? 'Thanh toán đặt cọc'
                                                                                : 'Thanh toán ngay';
                                                                    @endphp

                                                                    <li class="report">
                                                                        <a
                                                                            href="javascript:showPricePopup({{ $item->id }})">
                                                                            {{ $label_payment }}
                                                                        </a>
                                                                    </li>
                                                                    {{-- <button type="button" class="btn btn-primary" onclick="showPricePopup({{ $item->id }})">{{ $label_payment }}</button> --}}
                                                                @endif
                                                                @if ($item->isWaitingsetupinterview())
                                                                    <li class="report">
                                                                        <a href="javascript:void(0)"
                                                                            data-toggle="open-setup-interview"
                                                                            data-id="{{ $item->id }}">
                                                                            <img
                                                                                src="http://recland.local/frontend/asset/images/cv-bought/icon-calendar.svg?v=1704351924">
                                                                            Đặt lịch phỏng vấn
                                                                        </a>
                                                                    </li>
                                                                @endif
                                                                @if ($item->isWaitingsetupOnboard())
                                                                    <li class="report">
                                                                        <a href="javascript:void(0)"
                                                                            data-toggle="open-setup-onboard"
                                                                            data-id="{{ $item->id }}">
                                                                            <img
                                                                                src="http://recland.local/frontend/asset/images/cv-bought/icon-calendar.svg?v=1704351924">
                                                                            Đặt lịch Onboard
                                                                        </a>
                                                                    </li>
                                                                @endif
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endif
                                </table>
                            </div>
                        </div>

                        @if ($arrSubmitCv->total() > 0)
                            <div class="group-paginate">
                                <div class="paginate">
                                    @if (!$arrSubmitCv->onFirstPage())
                                        <div class="pre paginate_prev"
                                            data-paginate="{{ $arrSubmitCv->currentPage() - 1 <= 1 ? 1 : $arrSubmitCv->currentPage() - 1 }}">
                                        </div>
                                    @endif
                                    <div class="number">
                                        <span class="paginate_current"
                                            data-value="{{ $arrSubmitCv->currentPage() }}">{{ $arrSubmitCv->currentPage() }}</span>
                                        /
                                        <span class="paginate_total"
                                            data-value="{{ $arrSubmitCv->lastPage() }}">{{ $arrSubmitCv->lastPage() }}</span>
                                    </div>
                                    @if (!$arrSubmitCv->onLastPage())
                                        <div class="next paginate_next"
                                            data-paginate="{{ $arrSubmitCv->currentPage() + 1 >= $arrSubmitCv->lastPage() ? $arrSubmitCv->lastPage() : $arrSubmitCv->currentPage() + 1 }}">
                                        </div>
                                    @endif
                                </div>
                                <div class="goto">
                                    {{ $arrLang['didentrang'] }}:
                                    <input type="number" id="gotoPage" min="1"
                                        oninput="validity.valid||(value='');" value="{{ $arrSubmitCv->currentPage() }}">
                                </div>
                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('frontend.pages.employer.modal.status_submitcv')
    <div id="detail-submit-cv">

    </div>
    <input type="hidden" name="detail-id" class="detail-id">
    <!-- Modal CV -->
    <div class="modal fade" id="modal-cv">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="header back-header" style="cursor: pointer"><img
                            src="{{ asset2('frontend/asset/images/dashboard-ctv/arrow-right.svg') }}">
                        {{ $arrLang['chitietcvungvien'] }}
                    </div>
                    <div class="content">
                        <iframe class="iframe" src="" style="width: 100%">

                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Modal CV -->

    {{--    <div class="modal-filter-ctv" id="modal-filter-v2"> --}}
    {{--        <form id="form-search" action="{{route('employer-submitcv')}}"> --}}
    {{--        <div class="header-modal-filter"> --}}
    {{--            {{ config('settings.' . app()->getLocale() . '.home_job.boloc') }} --}}
    {{--            <span class="close-modal-filter"></span> --}}
    {{--        </div> --}}
    {{--        <div class="content-modal-filter"> --}}
    {{--            <div class="item-filter item-filter-multiple"> --}}
    {{--                <label>{{$arrLang['diadiemlamviec']}}</label> --}}
    {{--                <?php
    $arrAddress = [];
    if (isset($_GET['location'])) {
        $arrAddress = array_flip($_GET['location']);
    }
    ?> --}}
    {{--                <select style="width: 100%" --}}
    {{--                        class="select2-custom select2-style select2-job custom-select-2-single-v2 item-search-job-v2 select-2-multiple-choice" name="location[]" multiple> --}}
    {{--                    <option value=" ">{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}</option> --}}
    {{--                    @foreach ($cities as $k => $city) --}}
    {{--                        <option @if ($k == 0) disabled @endif  value="{{$k}}" {{isset($arrAddress[$k]) ? 'selected' : ''}}>{{$city}}</option> --}}
    {{--                    @endforeach --}}
    {{--                </select> --}}
    {{--            </div> --}}


    {{--            <div class="item-filter item-filter-single"> --}}
    {{--                <label>{{$arrLang['namkinhnghiem']}}</label> --}}
    {{--                <select style="width: 100%" --}}
    {{--                        class="select-2-single-choice item-search-job-v2" name="year_experience" > --}}
    {{--                    <option value=" ">{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}</option> --}}
    {{--                    @foreach ($yearOfExperience as $k => $year) --}}
    {{--                    <option value="{{$k}}" {{isset($_GET['year_experience']) && $_GET['year_experience'] == $k ? 'selected' : ''}}>{{$year}}</option> --}}
    {{--                    @endforeach --}}
    {{--                </select> --}}
    {{--            </div> --}}


    {{--            <div class="item-filter item-filter-multiple"> --}}
    {{--                <label>{{$arrLang['skill']}}</label> --}}
    {{--                <?php
    $arrSkill = [];
    if (isset($_GET['skill'])) {
        $arrSkill = array_flip($_GET['skill']);
    }
    ?> --}}
    {{--                <select style="width: 100%" --}}
    {{--                        class="select2-custom select2-style select2-job custom-select-2-single-v2 item-search-job-v2 select-2-multiple-choice" name="skill[]" multiple> --}}
    {{--                    <option value=" ">{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}</option> --}}
    {{--                    @foreach ($skills as $skill) --}}
    {{--                    <option value="{{$skill}}" {{isset($arrSkill[$skill]) ? 'selected' : ''}}>{{$skill}}</option> --}}
    {{--                    @endforeach --}}
    {{--                </select> --}}
    {{--            </div> --}}

    {{--            <div class="item-filter item-filter-single"> --}}
    {{--                <label>{{$arrLang['thoigiandilamdukien']}}</label> --}}
    {{--                <select style="width: 100%" --}}
    {{--                        class="select-2-single-choice item-search-job-v2" name="candidate_est_timetowork" > --}}
    {{--                    <option value=" ">{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}</option> --}}
    {{--                    @foreach ($candidateStatus as $k => $item) --}}
    {{--                    <option value="{{$k}}" {{isset($_GET['candidate_est_timetowork']) && $_GET['candidate_est_timetowork'] == $k ? 'selected' : ''}}>{{$item}}</option> --}}
    {{--                    @endforeach --}}
    {{--                </select> --}}
    {{--            </div> --}}

    {{--            <div class="item-filter item-filter-multiple"> --}}
    {{--                <label>{{$arrLang['linhvuc']}}</label> --}}
    {{--                <?php
    $arrCareer = [];
    if (isset($_GET['career'])) {
        $arrCareer = array_flip($_GET['career']);
    }
    ?> --}}
    {{--                <select style="width: 100%" --}}
    {{--                        class="select2-custom select2-style select2-job custom-select-2-single-v2 item-search-job-v2 select-2-multiple-choice" name="career[]" multiple> --}}
    {{--                    @foreach ($career as $k => $item) --}}
    {{--                        <option @if ($k == 0) disabled @endif value="{{$k}}" {{isset($arrCareer[$k]) ? 'selected' : ''}}>{{$item}}</option> --}}
    {{--                    @endforeach --}}
    {{--                </select> --}}
    {{--            </div> --}}

    {{--        </div> --}}

    {{--        <div class="text-right footer-modal-filter"> --}}
    {{--            <a href="{{route('job-index')}}" class="button-reset">{{ config('settings.' . app()->getLocale() . '.home_job.datlai') }}</a> --}}
    {{--            <button class="button-submit btn-search-boloc">{{ config('settings.' . app()->getLocale() . '.home_job.hienthi') }} --}}
    {{--                <span id="count-filter">{{$arrSubmitCv->total()}}</span> {{ config('settings.' . app()->getLocale() . '.home_job.ketqua') }}</button> --}}
    {{--        </div> --}}

    {{--            <input type="text" name="search" value="{{isset($_GET['search']) ? $_GET['search'] : ''}}" hidden> --}}
    {{--            <input type="text" name="page" value="{{isset($_GET['page']) ? $_GET['page'] : ''}}" hidden> --}}
    {{--        </form> --}}
    {{--    </div> --}}

    <div class="modal right fade " id="modal-discuss" tabindex="-1" role="dialog" aria-labelledby="left_modal">
        <div id="main-modal-sell-sv" class="modal-dialog " role="document">
            <div class="modal-content">
                <div class="close" data-bs-dismiss="modal"></div>
                <div class="row-modal">
                    <div class="col-modal col-main">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                Thảo luận
                            </h5>
                        </div>
                        <div id="wrap-list-discuss" class="modal-body">
                            <div class="list-discuss" id="list-discuss">

                            </div>
                            <div class="list-discuss" id="list-schedule-interview">

                            </div>
                            <div class="list-discuss" id="ctv-reject">

                            </div>
                        </div>
                        <div class="action-modal-onboard">
                            <a data-toggle="open-site-col" data-type="onboard" href="javascript:void(0)"
                                class="btn-setup-onboard">Đặt lịch onboard
                                <img src="{{ asset2('frontend/asset/images/cv-bought/icon-calendar.svg') }}"></a>
                        </div>
                        <div class="action-modal">
                            <a data-toggle="open-site-col" data-type="interview" href="javascript:void(0)"
                                class="btn-setup-interview">Đặt lịch phỏng
                                vấn <img src="{{ asset2('frontend/asset/images/cv-bought/icon-calendar.svg') }}"></a>
                        </div>
                        <div class="modal-footer-fixed modal-footer text-center footer-modal-righ">

                            <form id="form-discuss">
                                <div class="wrap-input">
                                    <input name="submit_id" id="submit_id" type="hidden">
                                    <input name="comment" placeholder="Viết phản hồi của bạn tại đây"></input>
                                    <button type="submit" class="bnt-comment"></button>
                                    <span style="color: red" id="ruleError"></span>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-modal col-schedule-interview">
                        <form class="wrap-form" id="form-schedule-interview">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <a data-toggle="open-site-col" href="javascript:void(0)">
                                        <img src="{{ asset2('frontend/asset/images/cv-on-sale/arrow-left.svg') }}">
                                    </a>
                                    <span>Đặt lịch phỏng vấn</span>
                                </h5>
                            </div>
                            <div class="modal-body">
                                <div class="form-schedule-interview">
                                    <div id="datepicker"></div>
                                    <input name="date" value="{{ date('d/m/Y') }}" type="hidden">
                                    <input name="name" value="" type="hidden">
                                    <input name="submit_id" value="" type="hidden">
                                    <input name="type_of_sale" value="" type="hidden">
                                    <div class="group-input-time">
                                        <label id="label-time-interview">Giờ phỏng vấn:</label>
                                        <div class="input-time">
                                            <input id="input-h" name="hour" class="input-h" type="text"
                                                placeholder="Giờ">
                                            <span>:</span>
                                            <input id="input-m" name="minute" class="input-m" type="text"
                                                placeholder="phút">
                                        </div>
                                    </div>
                                    <div class="group-input-address">
                                        <label id="label-address-interview">Địa chỉ phỏng vấn:</label>
                                        <textarea placeholder="Nhập địa chỉ" name="address" id="schedule-address"></textarea>
                                    </div>
                                    <div class="group-input-address mt-4">
                                        <label id="label-phone-interview">Số điện thoại liên hệ phỏng vấn: </label>
                                        <input placeholder="Nhập số điện thoại" name="phone" id="schedule-phone"
                                            class="input-phone"></input>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer-fixed text-center footer-modal-righ">
                                <button type="button" class="btn-popup" data-toggle="reset-form-schedule-interview">Đặt
                                    lại</button>
                                <button type="submit" class="btn-popup">Xác nhận</button>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade modal-bg" id="modal-confirm-interview" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <form method="post" action="{{ route('employer-schedule-interview-submit') }}">
                    <div class="modal-header justify-content-center">
                        Xác nhận [<span class="title-type-of-sale">Đặt lịch phỏng vấn</span>]
                    </div>
                    <div class="modal-body">
                        <div class="description">
                            Bạn có chắc chắn <span class="content-type-of-sale">đặt lịch phỏng vấn</span> ứng viên <span
                                class="name" id="content-name-interview"></span>
                        </div>
                        <div class="content-interview">
                            <div class="item-interview">
                                <div class="label">Ngày:</div>
                                <div class="content" id="content-date-interview"></div>
                            </div>
                            <div class="item-interview">
                                <div class="label">Thời gian:</div>
                                <div class="content" id="content-time-interview">
                                </div>
                            </div>
                            <div class="item-interview">
                                <div class="label">Địa điểm:</div>
                                <div class="content" id="content-address-interview"></div>
                            </div>
                        </div>
                        @csrf
                        <input type="hidden" name="date" id="input-date-interview" value="">
                        <input type="hidden" name="name" id="input-name-interview" value="">
                        <input type="hidden" name="hour" id="input-hour-interview" value="">
                        <input type="hidden" name="minute" id="input-minute-interview" value="">
                        <input type="hidden" name="address" id="input-address-interview" value="">
                        <input type="hidden" name="phone" id="input-phone-interview" value="">
                        <input type="hidden" name="submit_id" id="input-submit_id" value="">
                        <input type="hidden" name="type" id="input-type_of_sale" value="">
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-bs-dismiss="modal" class="btn btn-cancel">Huỷ</button>
                        <button type="submit" class="btn btn-confirm">Xác nhận</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-complain" tabindex="-1" role="dialog" aria-labelledby="left_modal">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content">
                <form id="form-complain" action="{{ route('employer-complain-submit') }}" method="post"
                    enctype="multipart/form-data">
                    @csrf
                    <div class="modal-header">
                        Khiếu nại
                    </div>
                    <div class="modal-body">
                        <div class="name">
                            Ứng viên: <span id="modal-complain-name"></span>
                        </div>

                        <div class="wrap-input-comment">
                            <div class="input-comment">
                                <textarea name="content" placeholder="Nội dung báo cáo"></textarea>
                            </div>
                        </div>

                        <div class="wrap-input-image" data-toggle="dropfile">
                            <div class="label-input-image">
                                Thêm hình ảnh liên quan
                            </div>
                            <div class="group-input-image">
                                <div class="group-before-input">
                                    <img src="{{ asset2('frontend/asset/images/candidate/icon-upload-image.svg') }}">
                                    <div class="text-input-image">
                                        Drag and drop a file here or click
                                    </div>
                                </div>
                                <div class="group-render-input-image" style="display: none">
                                    <img id="image-render" src="">
                                </div>
                            </div>
                        </div>
                        <input type="file" id="image_complain" name="image_complain" style="display: none;"
                            accept="image/png, image/gif, image/jpeg" />
                        {{--                        <div class="wrapper-checkbox"> --}}
                        {{--                            <label class="checkbox-report" data-toggle="report"> --}}
                        {{--                                <input name="report" type="checkbox"> --}}
                        {{--                                <span class="checkmark"></span> --}}
                        {{--                                Khiếu nại CV này? <span class="color-red">*</span> --}}
                        {{--                            </label> --}}
                        {{--                        </div> --}}
                    </div>
                    <div class="modal-footer">
                        <a class="btn-footer btn-cancel" href="">Huỷ</a>
                        <button class="btn-footer btn-success" type="submit">Xác nhận</button>
                    </div>
                    <input type="hidden" name="submit_cv_id" value="">
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade modal-deposit" id="modal-deposit" tabindex="-1" aria-hidden="true" data-backdrop="static"
        data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content detail-cv-before-buy">

            </div>
        </div>
    </div>

    <div class="modal fade modal-bg" id="modal-buy-success" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header justify-content-center title-notify">
                    Bạn đã mua CV ứng viên thành công
                </div>
                <div class="modal-body">
                    <div class="description">
                        Hãy theo dõi trạng thái của Cv ứng viên trong danh sách CV đã mua
                    </div>
                    <img src="{{ asset2('frontend/asset/images/cv-on-sale/image-sell-success.svg') }}">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel">Đóng</button>
                    <button type="button" class="btn btn-reload">Xem lại</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade modal-bg" id="modal-confirm-candidate-cancel-interview" tabindex="-1"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <form method="post" action="{{ route('cancel-interview-submit') }}">
                    <div class="modal-header justify-content-center">
                        Huỷ phỏng vấn
                    </div>
                    <div class="modal-body">
                        <div class="content">
                            <p>
                                Bạn có chắc huỷ lời mời ứng tuyển của ứng viên <span class="name"></span>
                            </p>
                            <p>
                                Trước khi huỷ lời mời, hãy chắc chắn bạn đã xem xét kỹ thông tin của ứng viên và đã đưa ra
                                quyết định phù hợp.
                            </p>
                            <p>Nếu ứng viên này không phù hợp, hãy cùng khám phá những ứng viên tiềm năng khác trên nền tảng
                                của chúng tôi.</p>
                        </div>
                        @csrf
                        <input type="hidden" value="" name="submit_cv_id">
                    </div>
                    <div class="modal-footer">
                        <button type="button" data-bs-dismiss="modal" class="btn btn-cancel">Quay lại</button>
                        <button type="submit" class="btn btn-confirm">Xác nhận</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal for Rejecting CV -->
    <div class="modal fade" id="rejectCvModal" tabindex="-1" role="dialog" aria-labelledby="rejectCvModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title " id="rejectCvModalLabel"><strong>Lý do loại CV</strong></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        onclick="$('#rejectCvModal').modal('hide')">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="rejectCvForm">
                        <input type="hidden" id="rejectCvId" name="id">
                        <div class="form-group">
                            {{-- <label for="rejectReason">Lý do loại CV:</label> --}}
                            <textarea class="form-control" id="rejectReason" name="assessment" rows="3" required
                                placeholder="Nhập lý do loại CV"></textarea>
                            <label id="rejectReason-error" class="error" for="rejectReason" style="display: none;">Vui
                                lòng nhập lý do loại CV</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        onclick="$('#rejectCvModal').modal('hide')">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="submitRejectCv()">Xác nhận</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('before_styles')
    <style>
        .select2-container .select2-selection--single {
            height: 48px;
        }

        .form-search-status {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            height: 40px;
            padding-right: 6px;
        }

        #dashboard-list-recruitment .content-dashboard-list-recruitment {
            padding: 0 24px;
        }
    </style>
@endpush



@section('scripts')
    <script>
        function showRejectCvModal(id) {
            $('#rejectCvId').val(id);
            $('#rejectCvModal').modal('show');
        }

        function submitRejectCv() {
            let id = $('#rejectCvId').val();
            let assessment = $('#rejectReason').val();
            $('#rejectReason-error').hide();

            if (!assessment) {
                $('#rejectReason-error').show();
                // alert('Vui lòng nhập lý do loại CV');
                // $.toast({
                //     title: 'Error',
                //     content: 'Vui lòng nhập lý do loại CV',
                //     type: 'error',
                //     delay: 5000
                // });
                return;
            }

            let url = '{{ route('ajax-recuriter-reject-cv', ':id') }}';
            url = url.replace(':id', id);

            $.ajax({
                url: url,
                type: 'POST',
                data: {
                    assessment: assessment,
                    _token: '{{ csrf_token() }}'
                },
                success: function(res) {
                    if (res.success == 1) {
                        $.toast({
                            content: 'Đã loại CV',
                            type: 'success',
                            delay: 5000
                        });
                        $('#rejectCvModal').modal('hide');
                        location.reload();
                    } else {
                        $.toast({
                            title: 'Error',
                            content: 'Lỗi khi loại CV',
                            type: 'error',
                            delay: 5000
                        });
                    }
                }
            });
        }

        function showPricePopup(id) {
            let url = '{{ route('ajax-get-submit-price-popup', ':id') }}'
            url = url.replace(':id', id);
            $.ajax({
                method: 'get',
                url: url,
                success: function(res) {
                    $('.detail-cv-before-buy').html(res);
                    $("#modal-deposit").modal("show");
                }
            });
        }
        $(document).ready(function() {

            $(document).on('click', '.btn-cancel', function() {
                $('.modal').modal('hide');
            })
            $(document).on('click', '.btn-reload', function() {
                location.reload();
            });

            $('#status_recruitment').change(function() {
                $('#status_recruitment_val').val($(this).val());
                $('#form-search').submit();
            });

            $(document).on('click', '.btn-pay', function() {
                // let id = $("input[name='detail_cv_item']").val();
                let id = $(this).data('id');
                // alert(id);
                // return true;
                // let job = $("#job").val();
                let thisClick = $(this);
                $.ajax({
                    url: '{{ route('ajax-payment-submit-cv') }}',
                    type: 'POST',
                    data: {
                        id: id,
                        // job: job,
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(result) {
                        if (result.success == true) {
                            // let message = 'Bạn đã mua CV ứng viên thành công'
                            // $('.title-notify').html(message);
                            $("#modal-deposit").modal("hide");
                            $("#modal-buy-success").modal('show')
                        } else {
                            let message = 'Lỗi: không thể thanh toán mua ứng viên'
                            $.toast({
                                title: 'Error',
                                content: message,
                                type: 'error',
                                delay: 5000
                            });
                            // $('.title-notify-error').html(message);
                            // $("#modal-sell-error").modal('show')
                        }
                    },
                    beforeSend: function() {
                        console.log(thisClick)
                        toggleLoadingForm(thisClick)
                    },
                    complete: function() {
                        toggleLoadingForm(thisClick)
                    },
                })
            });
            // $(document).on('click', '.detail-cv', function() {
            //     let id = $(this).data('id')
            //     showPricePopup(id);
            // });
            $('#status').select2({
                minimumResultsForSearch: Infinity
            });
            //$("#modal-change-status").modal('show');
            // $("#modal-candidate-introduction-details").modal('show');
            $('#gotoPage').keyup(function(e) {
                let page = $(this).val();
                let lastPage = $('.paginate_total').data('value')
                if (e.keyCode == 13) {
                    if (page <= lastPage && page >= 1) {
                        $("input[name=page]").val(page);
                        $('#form-search').submit();
                    }
                }
            });

            $('[data-toggle="reset-form-schedule-interview"]').click(function() {
                $('#input-h').val('');
                $('#input-m').val('');
                $('#schedule-address').val('');
                $('#datepicker').datepicker("setDate", new Date());
                $("#form-schedule-interview").find('[name="date"]').val($('#datepicker').val());
            })
            $(".date-filter").datepicker({
                dateFormat: 'dd/mm/yy',
            });

            $('body').on('click', '[data-toggle="open-site-col"]', function() {
                let typeOfSale = $(this).data('type');
                if (typeOfSale === 'onboard') {
                    $("#form-schedule-interview .modal-title span").html('Đặt lịch onboard');
                    $("#label-time-interview").html('Giờ onboard');
                    $("#label-address-interview").html('Địa chỉ onboard');
                    $('#form-schedule-interview [name="type_of_sale"]').val(typeOfSale);
                    $("#datepicker").datepicker("option", "maxDate", null);
                }
                if (typeOfSale === 'interview') {
                    $("#form-schedule-interview .modal-title span").html('Đặt lịch phỏng vấn');
                    $("#label-time-interview").html('Giờ phỏng vấn:');
                    $("#label-address-interview").html('Địa chỉ phỏng vấn:');
                    $("#label-phone-interview").html('Số điện thoại liên hệ phỏng vấn:');
                    $('#form-schedule-interview [name="type_of_sale"]').val(typeOfSale)
                    $("#datepicker").datepicker("option", "maxDate", $(this).data('maxDate'));
                }
                $("#main-modal-sell-sv").toggleClass('open-col-site');
            });

            $('[data-toggle="open-complain"]').click(function() {
                let thisClick = $(this);
                $('#form-complain [name="submit_cv_id"]').val(thisClick.data('id'));
                $('#modal-complain-name').html(thisClick.data('name'));
                $("#modal-complain").modal('show');
            })

            $("#datepicker").datepicker({
                dateFormat: 'dd/mm/yy',
                minDate: 0,
                // maxDate: maxSelectableDate,
                onSelect: function(dateText) {
                    $("#form-schedule-interview").find('[name="date"]').val(this.value);
                }
            });


            $('[data-toggle="dropfile"]').on("dragover", function(e) {
                e.preventDefault();
                e.stopPropagation();
            })

            $('[data-toggle="dropfile"]').on("drop", function(e) {
                e.preventDefault();
                e.stopPropagation();
                $("#image_complain").prop("files", e.originalEvent.dataTransfer.files).trigger('change');
            });

            $('#image_complain').change(function() {
                const file = this.files[0];
                if (file) {
                    $('.group-before-input').hide();
                    $('.group-render-input-image').show();
                    let reader = new FileReader();
                    reader.onload = function(event) {
                        $('#image-render').attr('src', event.target.result);
                    }
                    reader.readAsDataURL(file);
                }
            });

            $('[data-toggle="dropfile"]').click(function() {
                $('#image_complain').trigger('click');
            })

            $("#form-complain").validate({
                ignore: [],
                rules: {
                    content: {
                        required: true,
                    },
                    image_complain: {
                        extension: "png|jpeg|jpg"
                    }
                },
                messages: {
                    content: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    image_complain: {
                        extension: '{{ __('frontend/validation.mimes') }}'
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.attr('name') === 'content') {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else if (elem.attr('name') === 'image_complain') {
                        element = $('.wrap-input-image .group-input-image');
                        error.insertAfter(element);
                    } else if (elem.attr('name') === 'report') {
                        element = elem.parent();
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    if (!this.beenSubmitted) {
                        this.beenSubmitted = true;
                        form.submit();
                    }
                }

            });
            $("#form-schedule-interview").validate({
                ignore: [],
                rules: {
                    address: {
                        required: true,
                    },
                    phone: {
                        required: true,
                    },
                    hour: {
                        required: true,
                    },
                    minute: {
                        required: true,
                    },
                },
                messages: {
                    address: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    phone: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    hour: {
                        required: 'Bạn chưa nhập giờ',
                    },
                    minute: {
                        required: 'Bạn chưa nhập phút',
                    },

                },
                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    elem.addClass(errorClass);
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    elem.removeClass(errorClass);
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.hasClass("input-h")) {
                        element = elem.parents('.input-time');
                        error.insertAfter(element);
                    }
                    if (elem.hasClass("input-m")) {
                        element = elem.parents('.input-time');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    let formScheduleInterview = $("#form-schedule-interview");
                    let typeOfSale = formScheduleInterview.find('[name="type_of_sale"]').val();
                    let text = '';
                    let url = '';
                    // console.log(typeOfSale);
                    if (typeOfSale === 'onboard') {
                        text = 'đặt lịch onboard';
                        url = '{{ route('employer-schedule-onboard-submit') }}';
                    }
                    if (typeOfSale === 'interview') {
                        text = 'đặt lịch phỏng vấn';
                        url = '{{ route('employer-schedule-interview-submit') }}';
                    }
                    $("#modal-confirm-interview .content-type-of-sale").html(text);
                    $("#modal-confirm-interview .title-type-of-sale").html(text);

                    $("#content-date-interview").html(formScheduleInterview.find('[name="date"]')
                        .val());
                    $("#content-address-interview").html(formScheduleInterview.find('[name="address"]')
                        .val());
                    $("#content-name-interview").html(formScheduleInterview.find('[name="name"]')
                        .val());
                    $("#content-time-interview").html(formScheduleInterview.find('[name="hour"]')
                        .val() + ':' + formScheduleInterview.find('[name="minute"]').val());


                    $("#input-date-interview").val(formScheduleInterview.find('[name="date"]').val());
                    $("#input-name-interview").val(formScheduleInterview.find('[name="name"]').val());
                    $("#input-address-interview").val(formScheduleInterview.find('[name="address"]')
                        .val());
                    $("#input-phone-interview").val(formScheduleInterview.find('[name="phone"]')
                        .val());
                    $("#input-hour-interview").val(formScheduleInterview.find('[name="hour"]').val());
                    $("#input-minute-interview").val(formScheduleInterview.find('[name="minute"]')
                        .val());
                    $("#input-submit_id").val(formScheduleInterview.find(
                        '[name="submit_id"]').val());
                    $("#input-type_of_sale").val(typeOfSale);

                    $("#modal-confirm-interview form").attr('action', url)
                    $("#modal-confirm-interview").modal('show');
                }
            });

            $('[data-toggle="open-setup-interview"]').click(function() {
                let id = $(this).data('id');
                detailSubmit(id, 'setup-interview');
            });
            $('[data-toggle="open-setup-onboard"]').click(function() {
                let id = $(this).data('id');
                detailSubmit(id, 'setup-onboard');
            });
            $('[data-toggle="view-discuss"]').click(function() {
                let id = $(this).data('id');
                detailSubmit(id)
            })
            @if (!empty($_GET['discuss']))
                detailSubmit({{ $_GET['discuss'] }})
            @endif

            function detailSubmit(id, next_action = '') {
                let urlGetDataDetailCvSelling = '{{ route('ajax-get-data-detail-submit-cv', ':id') }}'
                urlGetDataDetailCvSelling = urlGetDataDetailCvSelling.replace(':id', id);
                $.ajax({
                    url: urlGetDataDetailCvSelling,
                    type: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(result) {
                        let statusRecruitment = result.status;
                        let typeOfSale = result.bonus_type;
                        let name = result.warehouse_cv.candidate_name;
                        let inputDate = new Date();
                        if (result.history_waiting_setup_interview) {
                            let inputDateString = result.history_waiting_setup_interview.created_at;
                            inputDate = new Date(inputDateString);
                        }
                        inputDate.setDate(inputDate.getDate() + 30);
                        let day = inputDate.getDate();
                        let month = inputDate.getMonth() + 1;
                        let year = inputDate.getFullYear();
                        let maxDateBook = ('0' + day).slice(-2) + '/' + ('0' + month).slice(-2) + '/' +
                            year;

                        openDiscuss(result.id, name, statusRecruitment, typeOfSale, maxDateBook);
                        if (next_action === 'setup-interview') {
                            $('.btn-setup-interview').trigger('click');
                        }
                        if (next_action === 'setup-onboard') {
                            $('.btn-setup-onboard').trigger('click');
                        }
                    },
                    error: function(res) {
                        $.toast({
                            title: 'Error',
                            content: res.responseJSON.error,
                            type: 'error',
                            delay: 5000
                        });
                    }
                })
            }

            function openDiscuss(id, name, statusRecruitment, typeOfSale, maxDateBook) {
                $("#list-discuss").html('');
                $("#modal-discuss").modal('show');
                let url = '{{ route('employer-discusses-submit', ':id') }}'
                url = url.replace(':id', id);
                $("#submit_id").val(id);
                const statusRecruitmentShowBook = [3];
                const typeOfSaleShowBook = ['interview', 'onboard'];
                if (maxDateBook) {
                    // $("#datepicker").datepicker("option", "maxDate", maxDateBook);
                    $('[data-toggle="open-site-col"][data-type="interview"]').data("maxDate", maxDateBook);
                }
                if (statusRecruitmentShowBook.includes(statusRecruitment) && typeOfSaleShowBook.includes(
                        typeOfSale)) {
                    $('#modal-discuss .action-modal').show();
                } else {
                    $('#modal-discuss .action-modal').hide();
                }

                const statusRecruitmentShowBookOnboard = [11];
                const typeOfSaleShowOnboard = ['onboard'];
                if (statusRecruitmentShowBookOnboard.includes(statusRecruitment) && typeOfSaleShowOnboard.includes(
                        typeOfSale)) {
                    $('#modal-discuss .action-modal-onboard').show();
                } else {
                    $('#modal-discuss .action-modal-onboard').hide();
                }

                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        let html = '';
                        let htmlScheduleInterview = '';
                        let htmlCancelInterview = '';
                        if (res.discuss.length > 0) {
                            for (let i = 0; i < res.discuss.length; i++) {
                                let item = res.discuss[i];
                                if (item.ntd_id > 0 && item.employer) {
                                    html += '<div class="item-discuss item-discuss-ntd">' +
                                        '<div class="header"><span>Nhà tuyển dụng #' + item.employer
                                        .id + '</span> ' +
                                        item.date_hour + '</div>' +
                                        '<div class="content">' + item.comment + '</div>' +
                                        '</div>';
                                } else if (item.ctv_id > 0 && item.rec) {
                                    html += '<div class="item-discuss">' +
                                        '<div class="header"><span>Cộng tác viên #' + item.rec.id +
                                        '</span> ' + item
                                        .date_hour + '</div>' +
                                        '<div class="content">' + item.comment + '</div>' +
                                        '</div>';
                                }
                            }

                        }

                        if (res.book.length > 0) {
                            for (let i = 0; i < res.book.length; i++) {
                                let item = res.book[i];
                                if (item.ntd_id > 0 && item.employer) {
                                    htmlScheduleInterview +=
                                        '<div class="item-discuss item-discuss-ntd">' +
                                        '<div class="header">' + item.date_hour +
                                        '<span> Nhà tuyển dụng #' + item.employer.id + '</div>' +
                                        '<div class="content">Đặt lịch phỏng vấn:<br>' +
                                        '<b>' + item.name + '</b><br>' +
                                        '<b>' + item.date_book_format + ',' + item.time_book_format +
                                        '</b><br>' +
                                        'tại địa chỉ <b>' + item.address + '</b><br>' +
                                        'Số điện thoại: <b>' + item.phone + '</b>' +
                                        '</div>' +
                                        '</div>';
                                }
                            }

                        }

                        if (res.onboard.length > 0) {
                            for (let i = 0; i < res.onboard.length; i++) {
                                let item = res.onboard[i];
                                if (item.ntd_id > 0 && item.employer) {
                                    htmlScheduleInterview +=
                                        '<div class="item-discuss item-discuss-ntd">' +
                                        '<div class="header">' + item.date_hour +
                                        '<span> Nhà tuyển dụng #' + item.employer.id + '</div>' +
                                        '<div class="content">Đặt lịch onboard:<br>' +
                                        '<b>' + item.name + '</b><br>' +
                                        '<b>' + item.date_book_format + ',' + item.time_book_format +
                                        '</b><br>' +
                                        'tại <b>' + item.address + '</b>' +
                                        '</div>' +
                                        '</div>';
                                }
                            }

                        }

                        if (res.messageReject) {
                            let messageReject = res.messageReject;
                            htmlCancelInterview += '<div class="item-discuss message-reject">' +
                                '<div class="header"><span>Cộng tác viên #' + String(res.submitCv.rec
                                    .id) + '</span>' +
                                messageReject.date_hour + '</div>' +
                                '<div class="content">Ứng viên đã từ chối, vui lòng đặt lại lịch phỏng vấn.</div>' +
                                '<div class="btn-bottom-message-reject">' +
                                '<a href="javascript:void(0)" data-name="' + messageReject.name +
                                '" data-id="' + res.submitCv.id +
                                '" data-toggle="cancel-interview" class="cancel-interview">Huỷ phỏng vấn</a>' +
                                '<a href="javascript:void(0)" data-toggle="open-site-col" data-type="interview" class="re-book-interview">Đặt lại lịch phỏng vấn</a>' +
                                '</div>' +
                                '</div>';

                        }

                        $("#list-discuss").html(html);
                        $("#list-schedule-interview").html(htmlScheduleInterview);
                        $("#ctv-reject").html(htmlCancelInterview);
                        $("#form-schedule-interview").find('[name="name"]').val(name);
                        $("#form-schedule-interview").find('[name="submit_id"]').val(
                            id);
                    },
                    error: function(xhr, status, error) {

                    },
                });
            }

            $("#form-discuss").submit(function(e) {
                e.preventDefault();
                $(this).find('[type="submit"]').prop('disabled', true);
                let comment = $(this).find('[name="comment"]').val();
                let submit_id = $(this).find('[name="submit_id"]').val();
                if (comment) {
                    console.log(comment);
                    $.ajax({
                        method: 'POST',
                        url: '{{ route('employer-send-discusses-submit') }}',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: {
                            'comment': comment,
                            'submit_id': submit_id,
                        },
                        success: function(res) {
                            if (res.success == true) {
                                $("#form-discuss").find('[name="comment"]').val('');
                                let html = '<div class="item-discuss  item-discuss-ntd">' +
                                    '<div class="header"><span>Nhà tuyển dụng #' + res.data
                                    .employer.id +
                                    '</span> ' + res.data.date_hour + '</div>' +
                                    '<div class="content">' + res.data.comment + '</div>' +
                                    '</div>';
                                $("#list-discuss").append(html);
                                $.toast({
                                    title: 'Success',
                                    content: 'Phản hồi thành công',
                                    type: 'success',
                                    delay: 300
                                });
                                $('#wrap-list-discuss').scrollTop($('#wrap-list-discuss').prop(
                                    'scrollHeight'));
                            } else {
                                $.toast({
                                    title: 'Error',
                                    content: 'Phản hồi Thất bại',
                                    type: 'error',
                                    delay: 300
                                });
                            }

                        },
                        complete: function(data) {
                            $("#form-discuss").find('[type="submit"]').prop('disabled', false);
                        }
                    })


                } else {
                    $(this).find('[type="submit"]').prop('disabled', false);
                }
            })

            $("input[name='search']").keyup(function(e) {
                if (e.keyCode == 13) {
                    let val = $(this).val();
                    $('#form-search').submit();
                }
            });

            $('.detail_submit_cv_popup').click(function() {
                let id = $(this).data('id');
                let url = '{{ route('employer-submitcv-detail', ':id') }}';
                url = url.replace(':id', id);
                $('.detail-id').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#detail-submit-cv').html(res);
                        $("#modal-candidate-introduction-details").modal('show');
                    },
                });
            });

            $('.detail_cv').click(function() {
                let id = $('#idDetailCv').val();
                let url = '{{ route('employer-submitcv-detail', ':id') }}';
                url = url.replace(':id', id);
                $('.detail-id').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#detail-submit-cv').html(res);
                        $("#modal-candidate-introduction-details").modal('show');
                    },
                });
            });

            $('.closeDetailCV').click(function() {
                $("#modal-change-status").modal('hide');
                $("#modal-candidate-introduction-details").modal('hide');
            });

            $(document).on('click', '.show-iframe', function() {
                let url = $(this).data('url');
                $(".iframe").attr('src', url);
                $("#modal-candidate-introduction-details").modal('hide');
                $("#modal-cv").modal('show');

                let detailId = $('.detail-id').val();
            });

            $(document).on('click', '.back-header', function() {
                let id = $('.detail-id').val();
                let url = '{{ route('employer-submitcv-detail', ':id') }}';
                url = url.replace(':id', id);
                $('.detail-id').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#detail-submit-cv').html(res);
                        $("#modal-cv").modal('hide');
                        $("#modal-candidate-introduction-details").modal('show');
                    },
                });
            });

            $(document).on('click', '.btn-cancel', function() {
                $("#modal-candidate-introduction-details").modal('hide');
                // $("#modal-deposit").modal('hide');
            });


            $('#status').select2({
                minimumResultsForSearch: Infinity
            });
            // $('[data-toggle="change-status"]').click(function (){
            $(document).on('click', '.change-status', function() {
                let id = $(this).data('id');
                updateStatus(id);
            })

            @if (!empty($_GET['update-status']))
                updateStatus({{ $_GET['update-status'] }})
            @endif

            function updateStatus(id) {
                let url = '{{ route('employer-view-popup-change-status-submit', ':id') }}'
                url = url.replace(':id', id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    success: function(res) {
                        $("#modal-change-status .modal-content").html(res);
                        $('#status').select2({
                            minimumResultsForSearch: Infinity,
                            dropdownParent: "#modal-change-status .modal-content",
                        });
                        $('#status').change(function() {
                            if ($(this).val() == 10 || $(this).val() == 12 || $(this).val() ==
                                17) {
                                $('#assessment').attr('required', 'required');
                                $('#assessment_require').removeClass('d-none');
                            } else {
                                $('#assessment').removeAttr('required');
                                $('#assessment_require').addClass('d-none');
                            }
                        });
                        $("#modal-change-status").modal('show');
                        $('.detail-id').val(id);
                    }
                });
            }

            //bật popup cap nhat trang thai: load data của 1 record
            $(document).on('click', '.update_status_submitcv', function() {
                $('.error').remove();
                let id = $(this).data('id');
                let url = '{{ route('employer-submitcv-status-detail', ':id') }}';
                url = url.replace(':id', id);
                $('#idDetailCv').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (res) {
                            //load status by bonus type job: cv or onboard

                            $("#status").html('');
                            var select = document.getElementById("status");
                            $.each(res.status_show, function(key, value) {
                                var option = document.createElement("option");
                                option.text = value;
                                option.value = key;

                                select.appendChild(option);
                            });
                            //end load
                            $("#assessment").text(res.assessment);
                            $("#status").val(res.status);
                            $('.detail-id').val(id);

                            $('#submitcv_candidate_name').val(res.submit_cv_meta
                                .candidate_name);

                            if (res.status == 0 || res.status == 1) {
                                // var date = formatDate(new Date(res.date_change_status.replace(/-/g, "/")), res.status)
                                var date = formatDateWithoutStatus(new Date(res
                                    .expire_date_change_status.replace(/-/g, "/")))
                                // console.log(res.date_change_status);
                                console.log(date);
                                // var time = formatAMPM(new Date(res.date_change_status))
                                var time = '00:00AM'
                                $('#time-warning').html(time)
                                $('#date-warning').html(date)
                                $('#text-warning').css("display", "block");
                            } else {
                                $('#text-warning').css("display", "none");
                            }

                            $("#status").val(res.status).trigger('change');

                            $("#modal-change-status").modal('show');
                        }
                        //delete cookie
                        $.cookie("submitcvid", null, {
                            path: '/'
                        });
                        $.removeCookie('submitcvid', {
                            path: '/'
                        });
                        $.removeCookie('submitcvid', {
                            path: '/employer'
                        });
                    },
                });
            });
            //cap nhat trang thai: submit
            // $(document).on('click', '.button-confirm', function() {
            //     $('.error').remove();
            //     let url = '{{ route('employer-submitcv-status-change') }}';
            //     let id = $('.detail-id').val();
            //     let assessment = $('#assessment').val();
            //     let status = $('#status').val();
            //     $.ajax({
            //         method: 'POST',
            //         url: url,
            //         data: {
            //             id,
            //             status,
            //             assessment
            //         },
            //         headers: {
            //             'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            //         },
            //         success: function(res) {
            //             console.log(res);
            //             if (res.error != '' && res.error != undefined) {
            //                 $('<label id="name-error" class="error" for="name">' + res.error +
            //                     '</label>').insertAfter("#status");
            //             }
            //             if (res.error_assessment != '' && res.error_assessment != undefined) {
            //                 $('<label id="name-error" class="error" for="name">' + res
            //                     .error_assessment + '</label>').insertAfter("#assessment");
            //             }
            //             if (res.error == '' && res.error_assessment == '') location.reload();
            //         },
            //     });
            // });
            //

            const submitCvId = $.cookie('submitcvid');

            function showPopupWithSubmitId() {
                if (submitCvId) {
                    let id = submitCvId;
                    let url = '{{ route('employer-submitcv-status-detail', ':id') }}';
                    url = url.replace(':id', id);
                    $('#idDetailCv').val(id);
                    $.ajax({
                        method: 'GET',
                        url: url,
                        async: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(res) {
                            if (res) {
                                //load status by bonus type job: cv or onboard

                                $("#status").html('');
                                var select = document.getElementById("status");
                                $.each(res.status_show, function(key, value) {
                                    var option = document.createElement("option");
                                    option.text = value;
                                    option.value = key;

                                    select.appendChild(option);
                                });
                                //end load
                                $("#assessment").text(res.assessment);
                                $("#status").val(res.status);
                                $('.detail-id').val(id);
                                $('#submitcv_candidate_name').val(res.submit_cv_meta.candidate_name);

                                if (res.status == 0 || res.status == 1) {
                                    // var date = formatDate(new Date(res.date_change_status.replace(/-/g, "/")), res.status)
                                    var date = formatDateWithoutStatus(new Date(res
                                        .expire_date_change_status.replace(/-/g, "/")))
                                    // console.log(date);
                                    // var time = formatAMPM(new Date(res.date_change_status))
                                    var time = '00:00AM'
                                    $('#time-warning').html(time)
                                    $('#date-warning').html(date)
                                    $('#text-warning').css("display", "block");
                                } else {
                                    $('#text-warning').css("display", "none");
                                }

                                $("#status").val(res.status).trigger('change');

                                $("#modal-change-status").modal('show');

                                //delete cookie
                                $.cookie("submitcvid", null, {
                                    path: '/'
                                });
                                $.removeCookie('submitcvid', {
                                    path: '/'
                                });
                                $.removeCookie('submitcvid', {
                                    path: '/employer'
                                });
                            }
                            //delete cookie
                            //delete cookie
                            $.cookie("submitcvid", null, {
                                path: '/'
                            });
                            $.removeCookie('submitcvid', {
                                path: '/'
                            });
                            $.removeCookie('submitcvid', {
                                path: '/employer'
                            });
                        },
                    });
                }
            }

            $("body").click(function() {
                if ($("#modal-change-status").is(":visible")) {
                    //delete cookie
                    $.cookie("submitcvid", null, {
                        path: '/'
                    });
                    $.removeCookie('submitcvid', {
                        path: '/'
                    });
                    $.removeCookie('submitcvid', {
                        path: '/employer'
                    });
                }
            });

            function formatDateWithoutStatus($date) {
                var result = new Date($date);
                let date, month, year;

                date = result.getDate();
                month = result.getMonth() + 1;
                year = result.getFullYear();

                date = date
                    .toString()
                    .padStart(2, '0');

                month = month
                    .toString()
                    .padStart(2, '0');

                return `${date}/${month}/${year}`;
            }

            function formatDate(inputDate, status) {
                var result = new Date(inputDate);
                if (status == 0) {
                    //thu 3-4-5-6 lay du lieu 5 ngay truoc
                    //chu nhat - thu 2 lay du lieu 3 ngay truoc
                    //thu 7 lay du lieu 4 ngay truoc
                    var addDay = 4;

                    var numDay = result.getDay();

                    if (numDay == 0 || numDay == 1) {
                        addDay = 4;
                    } else if (numDay == 2 || numDay == 3 || numDay == 4 || numDay == 5) {
                        addDay = 6;
                    } else if (numDay == 6) {
                        addDay = 5;
                    }
                } else {
                    //chu nhat -> thu 5 lay du lieu 21 ngay truoc
                    //thu 6 lay du lieu 23 ngay truoc
                    //thu 7 lay du lieu 22 ngay truoc
                    var addDay = 16;

                    var numDay = result.getDay();

                    if (numDay == 0 || numDay == 1 || numDay == 2 || numDay == 3 || numDay == 4) {
                        addDay = 22;
                    } else if (numDay == 5) {
                        addDay = 24;
                    } else if (numDay == 6) {
                        addDay = 23;
                    }
                }
                result.setDate(result.getDate() + addDay);

                let date, month, year;

                date = result.getDate();
                month = result.getMonth() + 1;
                year = result.getFullYear();

                date = date
                    .toString()
                    .padStart(2, '0');

                month = month
                    .toString()
                    .padStart(2, '0');

                return `${date}/${month}/${year}`;
            }

            function formatAMPM(date) {
                var hours = date.getHours();
                var minutes = date.getMinutes();
                var ampm = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12;
                hours = hours ? hours : 12; // the hour '0' should be '12'
                minutes = minutes < 10 ? '0' + minutes : minutes;
                var strTime = hours + ':' + minutes + ' ' + ampm;
                return strTime;
            }

            showPopupWithSubmitId();

            const rightFix = '-500px';
            $(".button-open-filter-modal").click(function() {
                let right = '25';
                if (!$(this).hasClass('open')) {
                    $(this).addClass('open');
                    $("#modal-filter-v2").css('right', right + 'px')
                } else {
                    $(this).removeClass('open');
                    $("#modal-filter-v2").css('right', rightFix)
                }
            })
            $('.close-modal-filter').click(function() {
                $(".button-open-filter").removeClass('open');
                $("#modal-filter-v2").css('right', rightFix)
            });
            jQuery(function($) {
                $.fn.select2.amd.require([
                    'select2/selection/single',
                    'select2/selection/placeholder',
                    'select2/selection/allowClear',
                    'select2/dropdown',
                    'select2/dropdown/search',
                    'select2/dropdown/attachBody',
                    'select2/utils'
                ], function(SingleSelection, Placeholder, AllowClear, Dropdown, DropdownSearch,
                    AttachBody, Utils) {

                    var SelectionAdapter = Utils.Decorate(
                        SingleSelection,
                        Placeholder
                    );

                    SelectionAdapter = Utils.Decorate(
                        SelectionAdapter,
                        AllowClear
                    );

                    let select2singles = $('.select-2-single-choice');
                    select2singles.select2({
                        dropdownCssClass: "select2-single-desktop-select2-results-drop",
                        placeholder: '{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}',
                        selectionAdapter: SelectionAdapter,
                        minimumResultsForSearch: -1,
                        allowClear: true,
                        templateResult: function(data, container) {
                            let option = $(data.element);
                            let value = '';
                            if (option.length > 0) {
                                let select = option.parent();
                                value = select.val();
                            }

                            let checked = '';
                            if (data.id === value) {
                                checked = 'checked';
                            }

                            var id = 'state' + data.id;

                            let htmlCheckBox = '';

                            htmlCheckBox =
                                '<div class="row row-select-2-single-choice">' +
                                '<div class="col-md-10">' + data.text + '</div> ' +
                                '<div class="col-md-2 text-right">' +
                                '<div class="radio-select-2-single-choice">' +
                                '<input ' + checked + ' value="' + data.id +
                                '" type="checkbox">' +
                                '<span class="checkmark"></span>' +
                                '</div> ' +
                                '</div> ';

                            var $res = $("<label class='item-select-2-single-choice'>" +
                                htmlCheckBox + "</label>", {
                                    id: id
                                });

                            return $res;

                        },
                        templateSelection: function(data) {
                            return data.text;
                        },
                    }).on('select2:open', function() {
                        // $('.select2-container').last().addClass('content-drop-select-2-mobile');
                    })

                });
            });

            jQuery(function($) {
                $.fn.select2.amd.require([
                    'select2/selection/single',
                    'select2/selection/placeholder',
                    'select2/selection/allowClear',
                    'select2/dropdown',
                    'select2/dropdown/search',
                    'select2/dropdown/attachBody',
                    'select2/utils'
                ], function(SingleSelection, Placeholder, AllowClear, Dropdown, DropdownSearch,
                    AttachBody, Utils) {
                    var SelectionAdapter = Utils.Decorate(
                        SingleSelection,
                        Placeholder
                    );

                    let select2multiple = $('.select-2-multiple-choice');
                    select2multiple.select2({
                        dropdownCssClass: "select2-multiple-desktop-select2-results-drop",
                        placeholder: '{{ config('settings.' . app()->getLocale() . '.home_job.tatca') }}',
                        minimumResultsForSearch: -1,
                        allowClear: true,
                        maximumSelectionLength: 3,
                        templateResult: function(data) {
                            let values = select2multiple.val();
                            if (!data.id) {
                                return data.text;
                            }
                            let checked = '';
                            if (values.includes(data.id)) {
                                checked = 'checked';
                            }

                            var id = 'state' + data.id;
                            let htmlCheckBox = '';
                            htmlCheckBox =
                                '<div class="row row-company-desktop-select2">' +
                                '<div class="col-md-10">' + data.text + '</div> ' +
                                '<div class="col-md-2 text-right">' +
                                '<div class="checkbox-single-desktop-select2">' +
                                '<input ' + checked + ' value="' + data.id +
                                '" type="checkbox">' +
                                '<span class="checkmark"></span>' +
                                '</div> ' +
                                '</div> ';

                            var $res = $(
                                "<label class='item-company-desktop-select2'>" +
                                htmlCheckBox + "</label>", {
                                    id: id
                                });
                            return $res;
                        },
                    }).on('select2:open', function() {
                        // $('.select2-container').last().addClass('content-drop-select-2-mobile');
                    })
                    $('.item-filter-multiple').find('.select2-search__field').prop('readonly',
                        true);

                });
            })


            $("input[name='inpSearch']").on("keyup change", function(e) {
                $("input[name='search']").val($(this).val());
                if (e.keyCode == 13) {
                    $('#form-search').submit();
                }
            })

            $('.item-search-job-v2').change(function() {
                let data = $("#form-search").serialize();
                $.ajax({
                    method: 'get',
                    url: '{{ route('submitcv-count-search-ajax') }}',
                    data: data,
                    success: function(res) {
                        $("#count-filter").html(res);
                    }
                });
            });
            $('body').on('click', '[data-toggle="cancel-interview"]', function() {
                let that = $(this);
                $("#modal-confirm-candidate-cancel-interview .name").html(that.data('name'));
                $('#modal-confirm-candidate-cancel-interview [name="submit_cv_id"]').val(that.data('id'));
                $("#modal-confirm-candidate-cancel-interview").modal('show');
            });
        });
    </script>
@endsection
