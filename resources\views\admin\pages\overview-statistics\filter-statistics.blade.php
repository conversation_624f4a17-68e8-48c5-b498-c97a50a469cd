<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Bảng thống kê tổng quan</h4>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="statistics-table">
                        <thead>
                            <tr>
                                <th>Kỳ</th>
                                <th>Companies</th>
                                <th>Collaborators</th>
                                <th>Users</th>
                                <th>Total CV</th>
                                <th>Job is running</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($statistics) > 0)
                                @foreach($statistics as $stat)
                                    <tr>
                                        <td class="period-cell">{{ $stat['period'] }}</td>
                                        <td class="number-cell">{{ number_format($stat['companies']) }}</td>
                                        <td class="number-cell">{{ number_format($stat['collaborators']) }}</td>
                                        <td class="number-cell">{{ number_format($stat['users']) }}</td>
                                        <td class="number-cell">{{ number_format($stat['total_cv']) }}</td>
                                        <td class="number-cell">{{ number_format($stat['job_is_running']) }}</td>
                                    </tr>
                                @endforeach
                                
                                {{-- Tính tổng --}}
                                <tr style="background-color: #fff3cd; font-weight: bold;">
                                    <td class="period-cell">Tổng cộng</td>
                                    <td class="number-cell">{{ number_format(array_sum(array_column($statistics, 'companies'))) }}</td>
                                    <td class="number-cell">{{ number_format(array_sum(array_column($statistics, 'collaborators'))) }}</td>
                                    <td class="number-cell">{{ number_format(array_sum(array_column($statistics, 'users'))) }}</td>
                                    <td class="number-cell">{{ number_format(array_sum(array_column($statistics, 'total_cv'))) }}</td>
                                    <td class="number-cell">{{ number_format(array_sum(array_column($statistics, 'job_is_running'))) }}</td>
                                </tr>
                            @else
                                <tr>
                                    <td colspan="6" class="text-center">Không có dữ liệu</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@if(count($statistics) > 0)
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Biểu đồ thống kê</h4>
            </div>
            <div class="card-body">
                <div id="overviewChart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Dữ liệu cho biểu đồ
        var chartData = {
            periods: @json(array_column($statistics, 'period')),
            companies: @json(array_column($statistics, 'companies')),
            collaborators: @json(array_column($statistics, 'collaborators')),
            users: @json(array_column($statistics, 'users')),
            totalCv: @json(array_column($statistics, 'total_cv')),
            jobsRunning: @json(array_column($statistics, 'job_is_running'))
        };

        // Khởi tạo biểu đồ
        var chartDom = document.getElementById('overviewChart');
        var myChart = echarts.init(chartDom);
        
        var option = {
            title: {
                text: 'Thống kê tổng quan theo thời gian'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['Companies', 'Collaborators', 'Users', 'Total CV', 'Jobs Running']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: chartData.periods
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: 'Companies',
                    type: 'line',
                    data: chartData.companies,
                    itemStyle: { color: '#5470c6' }
                },
                {
                    name: 'Collaborators',
                    type: 'line',
                    data: chartData.collaborators,
                    itemStyle: { color: '#91cc75' }
                },
                {
                    name: 'Users',
                    type: 'line',
                    data: chartData.users,
                    itemStyle: { color: '#fac858' }
                },
                {
                    name: 'Total CV',
                    type: 'line',
                    data: chartData.totalCv,
                    itemStyle: { color: '#ee6666' }
                },
                {
                    name: 'Jobs Running',
                    type: 'line',
                    data: chartData.jobsRunning,
                    itemStyle: { color: '#73c0de' }
                }
            ]
        };

        myChart.setOption(option);
        
        // Responsive
        window.addEventListener('resize', function() {
            myChart.resize();
        });
    });
</script>
@endif
