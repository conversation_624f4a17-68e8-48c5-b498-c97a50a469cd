<?php

namespace App\Repositories;

use App\Models\Company;
use Illuminate\Support\Facades\DB;

class CompanyRepository extends BaseRepository
{
    const MODEL = Company::class;

    public function getListCompany($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc', $columns = ['*'])
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }
        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }

        /*if (!empty($orders)){
            foreach ($orders as $key => $order){
                $query->orderBy($key,$order);
            }
        }*/
        $query->orderBy($order_by, $sort);

        // Load admin relationship
        $query->with('admin');

        if (isset($params['search'])) {
            $query->where('id', 'like', '%' . $params['search'] . '%')
                ->orWhere('name', 'like', '%' . $params['search'] . '%');
        }

        if ($paginate) {
            return $query->paginate($limit, $columns, $pageName = 'page', $page);
        } else {
            return $query->select($columns)->get();
        }
    }

    public function total($params = array())
    {
        $query = $this->query();
        foreach ($params as $key => $param) {
            switch ($key) {
                case 'after_date':
                    $query->where('created_at', '>=', $param);
                    break;
                default:
                    break;
            }
        }
        $query->where('is_active', config('constant.active'));
        $query->where('is_real', 1); // Chỉ lấy dữ liệu thật
        return $query->count();
    }

    /**
     * @return mixed
     */
    function getListByHome()
    {

        return $this->query()
            ->select('id', 'name', 'slug', 'logo')
            ->where('home', 1)
            ->where('is_active', 1)
            ->get();
    }

    function getListForSelect()
    {
        return $this->query()
            ->select(['id', 'name'])
            ->where('is_active', 1)
            ->get();
    }

    public function getListByTypePriority($priority = 0, $paginate = false, $params = [], $limit = 10)
    {
        $query = $this->query();

        if ($priority) {
            $query->where('priority', $priority);
        }

        if (isset($params['career'])) {
            $careers = explode(',', $params['career']);
            $query->where(function ($query) use ($careers) {
                foreach ($careers as $career) {
                    $query->orWhereRaw("CONCAT(',', career, ',') like '%," . $career . ",%'");
                }
            });
        }

        if (isset($params['address'])) {
            $address = explode(',', $params['address']);
            $query->where(function ($query) use ($address) {
                foreach ($address as $addr) {
                    $query->orWhere('address', 'like', '%' . $addr . '%');
                }
            });
        }
        if (isset($params['name'])) {
            $name = $params['name'];
            $query->where('name', 'like', '%' . $name . '%');
        }

        if (isset($params['status'])) {
            $query->whereHas('jobs', function ($query) use ($params) {
                $query->where('status', '=', 1);
            });
        }
        if (isset($params['skill'])) {
            $query->whereHas('jobs', function ($query) use ($params) {
                // $query->where('status', '=', 1);
                $query->where('job.skills', 'like', '%' . $params['skill'] . '%');
            });
        }

        if (isset($params['sort'])) {
            if ($params['sort'] == 'a-z') {
                $query->orderBy('name', 'asc');
            } else if ($params['sort'] == 'z-a') {
                $query->orderBy('name', 'desc');
            } else if ($params['sort'] == 'quymo') {
                $query->orderBy('scale', 'desc');
            }
        }

        $query->where('home', config('constant.is_home'));

        $query->with('jobs');

        if ($paginate) {
            $query = $query->paginate($limit);
        } else {
            $query = $query->get();
        }

        return $query;
    }

    public function getListCompanyAnalysis($params)
    {
        $query = $this->query()->select(
            'companies.id as id',
            'companies.name as name',
            'companies.slug as slug',
            'companies.address as address',
            'companies.logo as logo',
            'companies.banner as banner',
            'companies.scale as scale',
            'companies.website as website',
            'companies.about as about',
            'companies.home as home',
            'companies.mst as mst',
            'companies.priority as priority',
            'companies.career as career',
            DB::raw("(select count(*) from job where job.company_id = companies.id) as count_job"),
            DB::raw("(select count(*) from submit_cvs where submit_cvs.company_id = companies.id) as count_cv"),
            DB::raw("(select count(*) from job where job.company_id = companies.id and job.status = 1 and is_active = 1) as count_job_active"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'Premium') as premium"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'New') as new"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'Hot') as hot")
        );

        if (isset($params['search'])) {
            $query->where('name', 'like', '%' . $params['search'] . '%');
        }

        if (isset($params['career'])) {
            $careers = explode(',', $params['career']);
            $query->where(function ($query) use ($careers) {
                foreach ($careers as $career) {
                    $query->orWhereRaw("CONCAT(',', career, ',') like '%," . $career . ",%'");
                }
            });
        }

        if (isset($params['address'])) {
            $address = explode(',', $params['address']);
            $query->where(function ($query) use ($address) {
                foreach ($address as $addr) {
                    $query->orWhere('address', 'like', '%' . $addr . '%');
                }
            });
        }

        if (isset($params['status'])) {
            $query->whereHas('jobs', function ($query) use ($params) {
                $query->where('status', '=', 1);
            });
        }

        $query->where('home', config('constant.is_home'));

        $query->with('jobs');

        if (isset($params['sort'])) {
            if ($params['sort'] == 'a-z') {
                $query->orderBy('name', 'asc');
            } else if ($params['sort'] == 'z-a') {
                $query->orderBy('name', 'desc');
            } else if ($params['sort'] == 'quymo') {
                $query->orderBy('scale', 'desc');
            }
        }

        $query = $query->paginate(config('constant.paginate_10'));

        return $query;
    }

    public function detailWithSlug($slug)
    {
        $query = $this->query()->select(
            'companies.*',
            DB::raw("(select count(*) from job where job.company_id = companies.id) as count_job"),
            DB::raw("(select count(*) from submit_cvs where submit_cvs.company_id = companies.id) as count_cv"),
            DB::raw("(select count(*) from job where job.company_id = companies.id and job.status = 1 and is_active = 1) as count_job_active"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'Premium') as premium"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'New') as new"),
            DB::raw("(select count(*) from job inner join job_meta on job.id = job_meta.job_id where job.company_id = companies.id and job_meta.priority = 'Hot') as hot")
        );

        $query->where('slug', $slug);
        // $query->where('home', config('constant.is_home'))->where('slug', $slug);

        $query->with('jobs');

        $query = $query->first();

        return $query;
    }

    public function getListCompanyPluck()
    {
        return $this->query()->pluck('name', 'id')->toArray();
    }

    public function numberRecruitingCompany($fromDate = null, $toDate = null)
    {
        $query = $this->query();
        if ($fromDate && $toDate) {
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        }
        $totalRecruiting = $query->whereHas('jobs', function ($query) {
            $query->where('status', config('constant.active'))
                ->where('is_active', config('constant.active'));
        })->count();

        $totalStopRecruiting = $query->whereHas('jobs', function ($query) {
            $query->where('status', config('constant.inActive'))
                ->where('is_active', config('constant.active'));
        })->count();

        return [
            'totalRecruiting' => $totalRecruiting,
            'totalStopRecruiting' => $totalStopRecruiting
        ];
    }
    public function findBySlug($slug)
    {
        return $this->query()->where('slug', $slug)->first();
    }
}
