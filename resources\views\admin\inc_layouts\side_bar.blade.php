<!--aside open-->
<aside class="app-sidebar">
    <div class="app-sidebar__logo">
        <a class="header-brand" href="{{ route('dashboard') }}">
            <img src="{{ asset2('backend/images/recland.jpg') }}" class="header-brand-img desktop-lgo">
            <img src="{{ asset2('backend/images/recland.jpg') }}" class="header-brand-img dark-logo">
            <img src="{{ asset2('backend/images/recland.jpg') }}" class="header-brand-img mobile-logo">
            <img src="{{ asset2('backend/images/recland.jpg') }}" class="header-brand-img darkmobile-logo">
        </a>
    </div>
    <div class="app-sidebar3">
        <div class="app-sidebar__user">
            <div class="dropdown user-pro-body text-center">
                <div class="user-pic">
                    <img src="{{ asset2('backend/assets/images/users/16.jpg') }}" alt="user-img"
                        class="avatar-xxl rounded-circle mb-1">
                </div>
                <div class="user-info">
                    <h5 class=" mb-2">{{ \Auth::guard('admin')->user()->name }}</h5>
                    <span class="text-muted app-sidebar__user-name text-sm">Admin</span>
                </div>
            </div>
        </div>
        <ul class="side-menu">
            <li class="slide">
                <a class="side-menu__item" href="{{ route('dashboard') }}">
                    <i class="feather feather-home sidemenu_icon"></i>
                    <span class="side-menu__label">Dashboard</span>
                </a>
            </li>
            <li class="slide @if (in_array(Route::currentRouteName(), [
                    'employer.create',
                    'employer.edit',
                    'company.create',
                    'company.edit',
                    'collaborator.create',
                    'collaborator.edit',
                    'job.create',
                    'job.edit',
                    'cv.create',
                    'cv.edit',
                    'submit-cv.edit',
                ])) is-expanded @endif">
                <a class="side-menu__item" data-toggle="slide" href="#">
                    <i class="feather  feather-users sidemenu_icon"></i>
                    <span class="side-menu__label">Client board</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul class="slide-menu @if (in_array(Route::currentRouteName(), [
                        'employer.create',
                        'employer.edit',
                        'company.create',
                        'company.edit',
                        'collaborator.create',
                        'collaborator.edit',
                        'job.create',
                        'job.edit',
                        'cv.create',
                        'cv.edit',
                    ])) open @endif">
                    <li><a href="{{ backpack_url('companies') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['company.create', 'company.edit'])) active @endif">Công
                            ty</a></li>
                    <li><a href="{{ backpack_url('employers') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['employer.create', 'employer.edit'])) active @endif">Nhà
                            tuyển dụng</a></li>
                    <li><a href="{{backpack_url('jobs-management')}}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['job.create', 'job.edit'])) active @endif">Job</a>
                    </li>
                    <li><a href="{{ route('new-collaborator.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['new-collaborator.create', 'collaborator.edit'])) active @endif">Cộng
                            tác viên</a></li>
                    <li><a href="{{ backpack_url('warehouse-cv') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['cv.create', 'cv.edit'])) active @endif">Kho
                            CV</a></li>
                    <li><a href="{{ route('submit-cv.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['submit-cv.edit'])) active @endif">Ứng
                            viên Apply</a></li>
                    <li><a href="{{ backpack_url('submit-cv-discuss') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['submit-cv-discuss.index'])) active @endif">Thảo
                            luận ứng tuyển</a>
                    </li>
                    <li><a href="{{ backpack_url('job-comment') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['job-comment.index'])) active @endif">Hỏi
                            đáp job</a></li>
                    <li><a href="{{ route('statistical.index') }}" class="slide-item">Thống kê ứng tuyển</a></li>
                    {{-- @if (backpack_user()->hasPermissionTo('admin.employer-lookup.index')) --}}
                    <li><a href="{{ route('admin.employer-lookup.index') }}" class="slide-item">Tra cứu thông tin
                            nhà tuyển dụng</a></li>
                    <li><a href="{{ route('admin.collaborator-lookup.index') }}" class="slide-item">Tra cứu thông tin
                            cộng tác viên</a></li>
                    {{-- @endif --}}
                </ul>
            </li>
            <li
                class="slide @if (in_array(Route::currentRouteName(), ['user.create', 'user.edit', 'role.create', 'role.edit'])) is-expanded @endif">
                <a class="side-menu__item" data-toggle="slide" href="#">
                    <i class="feather  feather-settings sidemenu_icon"></i>
                    <span class="side-menu__label">Setting board</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['user.create', 'user.edit', 'role.create', 'role.edit'])) open @endif">
                    <li><a href="{{ route('user.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['user.create', 'user.edit'])) active @endif">User
                            Admin</a></li>
                    <li><a href="{{ route('role.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['role.create', 'role.edit'])) active @endif">Role</a>
                    </li>
                    <li><a href="{{ route('seo.index') }}" class="slide-item">Config SEO</a></li>
                    <li><a href="{{ route('setting.edit') }}" class="slide-item">Setting</a></li>
                </ul>
            </li>
            <li
                class="slide @if (in_array(Route::currentRouteName(), ['post.create', 'post.edit'])) is-expanded @endif">
                <a class="side-menu__item" data-toggle="slide" href="#">
                    <i class="feather  feather-clipboard sidemenu_icon"></i>
                    <span class="side-menu__label">CMS</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['post.create', 'post.edit'])) open @endif">
                    <li><a href="{{ route('post.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['post.create', 'post.edit'])) active @endif">Blog</a>
                    </li>
                    <li><a href="{{ route('category.index') }}" class="slide-item">Danh mục bài viết</a></li>
                    <li><a href="{{ route('skill.index') }}" class="slide-item">Skill</a></li>
                    <li><a href="{{ route('banner.index') }}" class="slide-item">Banner</a></li>
                    <li><a href="{{ route('testimonial.index') }}" class="slide-item">Testimonials</a></li>
                    <li><a href="{{ route('information-contacts.index') }}" class="slide-item">Quản lý thông tin liên
                            hệ</a></li>
                    <li><a href="{{ route('top-ctv.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['top-ctv.edit'])) active @endif">Top
                            cộng tác viên</a>
                    </li>
                    {{-- <li><a href="{{route('album-aboutUs.index')}}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['album-aboutUs.edit'])) active @endif">Album
                            About Us</a></li> --}}
                    <li><a href="{{ backpack_url('album-about-us') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['album-about-us'])) active @endif">Album
                            About Us</a></li>
                </ul>
            </li>
            <li
                class="slide @if (in_array(Route::currentRouteName(), ['post.create', 'post.edit'])) is-expanded @endif">
                <a class="side-menu__item" data-toggle="slide" href="#">
                    <i class="feather  feather-clipboard sidemenu_icon"></i>
                    <span class="side-menu__label">Payment</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['post.create', 'post.edit'])) open @endif">
                    <li><a href="{{ route('payment.index') }}" class="slide-item">Giới thiệu ứng viên</a></li>
                </ul>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['wallet-transaction.index'])) open @endif">
                    <li><a href="{{ route('wallet-transaction.index') }}" class="slide-item">Lịch sử giao dịch ví</a>
                    </li>
                </ul>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['admin.wallet-topup.index'])) open @endif">
                    <li><a href="{{ route('admin.wallet-topup.index') }}" class="slide-item">Nạp tiền vào ví</a>
                    </li>
                </ul>
            </li>
            <li class="slide">
                <a class="side-menu__item" href="{{ route('authorize.index') }}">
                    <i class="feather feather-home sidemenu_icon"></i>
                    <span class="side-menu__label">Ủy quyền</span>
                </a>
            </li>
            <li
                class="slide @if (in_array(Route::currentRouteName(), ['cv.edit', 'submit-cv.edit', 'luot-ban.show'])) is-expanded @endif">
                <a class="side-menu__item @if (in_array(Route::currentRouteName(), ['cv.edit', 'submit-cv.edit', 'luot-ban.show'])) active @endif"
                    data-toggle="slide" href="#">
                    <i class="feather  feather-users sidemenu_icon"></i>
                    <span class="side-menu__label">Quản lý CV bán</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['cv-selling.index', 'luot-ban.index'])) open @endif">
                    <li><a href="{{ route('cv-selling.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), [])) active @endif">CV đang đăng
                            bán</a>
                    </li>
                    <li><a href="{{ route('luot-ban.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['luot-ban.show'])) active @endif">Lượt
                            bán CV</a></li>
                </ul>
            </li>
            <li class="slide">
                <a class="side-menu__item" href="{{ route('report.index') }}">
                    <i class="feather feather-home sidemenu_icon"></i>
                    <span class="side-menu__label">Quản lý báo cáo</span>
                </a>
            </li>
            <li
                class="slide @if (in_array(Route::currentRouteName(), ['survey-statistical.index', 'overview-statistics.index', 'job-statistics.index'])) is-expanded @endif">
                <a class="side-menu__item @if (in_array(Route::currentRouteName(), ['survey-statistical.index', 'overview-statistics.index', 'job-statistics.index'])) active @endif"
                    data-toggle="slide" href="#">
                    <i class="feather  feather-users sidemenu_icon"></i>
                    <span class="side-menu__label">Thống kê</span><i class="angle fa fa-angle-right"></i>
                </a>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['transaction-list.index'])) open @endif">
                    <li><a href="{{ route('transaction-list.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['transaction-list.index'])) active @endif">Danh
                            sách giao dịch</a>
                    </li>
                </ul>
                <ul
                    class="slide-menu @if (in_array(Route::currentRouteName(), ['survey-statistical.index', 'overview-statistics.index', 'job-statistics.index'])) open @endif">
                    <li><a href="{{ route('survey-statistical.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['survey-statistical.index'])) active @endif">Thống
                            kê khảo sát</a>
                    </li>
                    <li><a href="{{ route('overview-statistics.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['overview-statistics.index'])) active @endif">Thống
                            kê tổng quan</a>
                    </li>
                    <li><a href="{{ route('job-statistics.index') }}"
                            class="slide-item @if (in_array(Route::currentRouteName(), ['job-statistics.index'])) active @endif">Thống
                            kê Job</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</aside>
<!--aside closed-->