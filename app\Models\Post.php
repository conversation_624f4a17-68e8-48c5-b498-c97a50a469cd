<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

class Post extends BaseModel
{
    use HasFactory;

    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = ['url_banner_img', 'date_format'];

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    public function postMeta()
    {
        return $this->hasOne(PostMeta::class, 'post_id', 'id');
    }

    public function postSeo()
    {
        return $this->hasOne(PostSeo::class, 'post_id', 'id');
    }

    public function getUrlBannerImgAttribute()
    {
        return gen_url_file_s3($this->banner_img, '', false);
    }

    public function getDateFormatAttribute()
    {
        $date = Carbon::createFromFormat('Y-m-d', $this->date)->format('d/m/Y');
        return $date;
    }

    public function getNextAttribute()
    {
        return static::where('id', '>', $this->id)->orderBy('id', 'asc')->first();
    }

    public function getPreviousAttribute()
    {
        return static::where('id', '<', $this->id)->orderBy('id', 'desc')->first();
    }

    public function getUrlBannerDetailImgAttribute()
    {
        return gen_url_file_s3($this->banner_detail_img, '', false);
    }

    public function getUrlBannerViewAttribute()
    {
        return gen_url_file_s3($this->url_banner_detail_img, 'frontend/asset/images/new/default-image.png');
    }


}
