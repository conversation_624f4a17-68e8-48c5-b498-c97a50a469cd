<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;

class CheckEmployerEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => ['required', 'email', new CheckEmailRule(config('constant.role.employer'), null, 1)],
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'email'    => __('message.email'),
        ];
    }
}
