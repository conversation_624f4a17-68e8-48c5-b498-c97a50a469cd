<?php

namespace App\Services\Frontend;

use App\Helpers\Common;
use App\Models\JobTop;
use App\Models\SkillMain;
use App\Notifications\RegisterJob;
use App\Notifications\UpdateJob;
use App\Repositories\JobMetaRepository;
use App\Repositories\JobRepository;
use App\Repositories\JobSeoRepository;
use App\Repositories\SkillRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;

class JobService
{

    protected $jobRepository;
    protected $jobSeoRepository;
    protected $jobMetaRepository;
    protected $skillRepository;

    public function __construct(
        JobRepository     $jobRepository,
        JobSeoRepository  $jobSeoRepository,
        JobMetaRepository $jobMetaRepository,
        SkillRepository   $skillRepository,
    ) {
        $this->jobRepository = $jobRepository;
        $this->jobSeoRepository = $jobSeoRepository;
        $this->jobMetaRepository = $jobMetaRepository;
        $this->skillRepository = $skillRepository;
    }

    public function findBySlug($slug)
    {
        return $this->jobRepository->findBySlug($slug);
    }

    public function findById($id)
    {
        return $this->jobRepository->find($id);
    }

    public function findBySlugAllExpire($slug, $employerId)
    {
        return $this->jobRepository->findBySlugAllExpire($slug, $employerId);
    }

    public function getRelateBySkill($skills, $take = 18)
    {
        if (!$skills) {
            return null;
        }
        $skills = explode(',', $skills);
        return $this->jobRepository->getRelateBySkill($skills, $take);
    }

    //Home Page CTV
    public function getJobCtv($limitJob, $skill = null)
    {
        return $this->jobRepository->getJobCtv($limitJob, $skill);
    }

    //Home Page CTV
    public function getJobCtvCollection($limitJob, $skill = null)
    {
        return $this->jobRepository->getJobCtvCollection($limitJob, $skill);
    }

    //Home page Job
    public function getJobSearch($params)
    {
        return $this->jobRepository->getJobSearch($params);
    }

    public function countJobSearch($params)
    {
        return $this->jobRepository->countJobSearch($params);
    }


    public function getTotalJob($job)
    {
        return $this->jobRepository->getTotalJobWithCompany($job->company_id);
    }

    public function getJobAjax($params)
    {
        return $this->jobRepository->getJobWithKeyword($params);
    }

    public function getJobEmployer($params, $userId)
    {
        return $this->jobRepository->getJobEmployer($params, $userId);
    }

    public function getTopSkill($limit = 8)
    {
        return $this->jobRepository->getTopSkill($limit);
    }

    public function getLatestJobs($limit = 5)
    {
        return $this->jobRepository->getLatestJobs($limit);
    }

    public function totalJob($params)
    {
        return $this->jobRepository->totalJob($params);
    }

    public function createJob($params)
    {
        $user = auth('client')->user();
        $isActive = config('constant.inActive');
        $remote = isset($params['remote']) ? config('job.remote.on') : config('job.remote.off');
        $urgent = isset($params['urgent']) ? config('job.urgent.on') : config('job.urgent.off');
        $skill_name = '';
        if (in_array($params['career'], config('job.it_career'))) { // IT Career
            $skill_name = SkillMain::where('id', $params['skill'])->first()->name_vi;
        } else {
            $skill_name = JobTop::where('id', $params['skill'])->first()->name_vi;
        }


        $data = [
            'name'            => $params['name'],
            'slug'            => Common::buildSlug($params['name']),
            'company_id'      => $user->company_id,
            'employer_id'     => $user->id,
            'expire_at'       => Carbon::createFromFormat('d/m/Y', $params['expire_at'])->format('Y-m-d'),
            'vacancies'       => $params['vacancies'],
            'type'            => $params['type'],
            'urgent'          => $urgent,
            'remote'          => $remote,
            'career'          => $params['career'],
            'rank'            => $params['rank'],
            'skill_id'        => $params['skill'],
            'bonus'           => $params['bonus'],
            'bonus_type'      => $params['bonus_type'],
            'skills'          => $skill_name,
            'address'         => json_encode(array_values($params['address'])),
            'jd_description'  => $params['jd_description'],
            'jd_request'      => $params['jd_request'],
            'jd_welfare'      => $params['jd_welfare'],
            'salary_min'      => $params['salary_min'],
            'salary_max'      => $params['salary_max'],
            'salary_currency' => $params['salary_currency'],
            'is_active'       => $isActive,
        ];
        // dd($data);

        if (isset($params['file_jd']) && is_file($params['file_jd'])) {
            $data['file_jd'] = FileServiceS3::getInstance()->uploadToS3($params['file_jd'], config('constant.sub_path_s3.job'));
        }

        $job = $this->jobRepository->create($data);
        //create seo
        $dataSeo = [
            'job_id'     => $job->id,
            'title_vi'   => $params['name'],
            'title_en'   => $params['name'],
            'keyword_vi' => $skill_name,
            'keyword_en' => $skill_name,
        ];

        $this->jobSeoRepository->create($dataSeo);
        //create meta
        $dataMeta = [
            'job_id'   => $job->id,
            'priority' => config('job.priority.2'),
        ];

        $this->jobMetaRepository->create($dataMeta);
        if ($job) {
            $user->notify(new RegisterJob($user));
        }

        return $job;
    }
    public function createJobApi($params)
    {
        $isActive = config('constant.inActive');
        $remote = isset($params['remote']) ? config('job.remote.on') : config('job.remote.off');
        $urgent = isset($params['urgent']) ? config('job.urgent.on') : config('job.urgent.off');
        $skill_name = '';
        if (in_array($params['career'], config('job.it_career'))) { // IT Career
            $skill_name = SkillMain::where('id', $params['skill'])->first()->name_vi;
        } else {
            $skill_name = JobTop::where('id', $params['skill'])->first()->name_vi;
        }

        if (! isset($params['expire_at']) || empty($params['expire_at'])) {
            $params['expire_at'] = Carbon::now()->addDays(30)->format('Y-m-d');
        }

        $data = [
            'name'            => $params['name'],
            'slug'            => Common::buildSlug($params['name']),
            'company_id'      => $params['company_id'],
            'employer_id'     => $params['employer_id'],
            'expire_at'       => Carbon::createFromFormat('Y-m-d', $params['expire_at'])->format('Y-m-d'),
            'vacancies'       => $params['vacancies'],
            'type'            => $params['type'],
            'urgent'          => $urgent,
            'remote'          => $remote,
            'career'          => $params['career'],
            'rank'            => $params['rank'],
            'skill_id'        => $params['skill'],
            'bonus'           => $params['bonus'],
            'bonus_type'      => $params['bonus_type'],
            'skills'          => $skill_name,
            'address'         => json_encode(array_values($params['address'])),
            'jd_description'  => $params['jd_description'],
            'jd_request'      => $params['jd_request'],
            'jd_welfare'      => $params['jd_welfare'],
            'salary_min'      => $params['salary_min'],
            'salary_max'      => $params['salary_max'],
            'salary_currency' => $params['salary_currency'],
            'is_active'       => $isActive,
        ];

        $job = $this->jobRepository->create($data);
        //create seo
        $dataSeo = [
            'job_id'     => $job->id,
            'title_vi'   => $params['name'],
            'title_en'   => $params['name'],
            'keyword_vi' => $skill_name,
            'keyword_en' => $skill_name,
        ];

        $this->jobSeoRepository->create($dataSeo);
        //create meta
        $dataMeta = [
            'job_id'   => $job->id,
            'priority' => config('job.priority.2'),
        ];

        $this->jobMetaRepository->create($dataMeta);
        $job->setMeta('source', 'api');

        return $job;
    }

    public function detailService($id, $user = null)
    {
        return $this->jobRepository->findByUser($id, $user);
    }

    public function updateJob($id, $params)
    {
        $user = auth('client')->user();
        $job = $this->detailService($id);

        $remote = isset($params['remote']) ? config('job.remote.on') : config('job.remote.off');
        $urgent = isset($params['urgent']) ? config('job.urgent.on') : config('job.urgent.off');
        $skill_name = '';
        if (in_array($params['career'], config('job.it_career'))) { // IT Career
            $skill_name = SkillMain::where('id', $params['skill'])->first()->name_vi;
        } else {
            $skill_name = JobTop::where('id', $params['skill'])->first()->name_vi;
        }

        $data = [
            'name'            => $params['name'],
            'slug'            => $job->name != $params['name'] ? Common::buildSlug($params['name']) : $job->slug,
            'expire_at'       => Carbon::createFromFormat('d/m/Y', $params['expire_at'])->format('Y-m-d'),
            'vacancies'       => $params['vacancies'],
            'type'            => $params['type'],
            'urgent'          => $urgent,
            'remote'          => $remote,
            'career'          => $params['career'],
            'rank'            => $params['rank'],
            'skill_id'        => $params['skill'],
            'bonus'           => $params['bonus'],
            'bonus_type'      => $params['bonus_type'],
            'skills'          => $skill_name,
            'address'         => json_encode(array_values($params['address'])),
            'jd_description'  => $params['jd_description'],
            'jd_request'      => $params['jd_request'],
            'jd_welfare'      => $params['jd_welfare'],
            'salary_min'      => $params['salary_min'],
            'salary_max'      => $params['salary_max'],
            'salary_currency' => $params['salary_currency'],
            'status'          => $params['status'],
            // 'is_active'       => $params['is_active'],
        ];

        if (isset($params['file_jd']) && is_file($params['file_jd'])) {
            $data['file_jd'] = FileServiceS3::getInstance()->uploadToS3($params['file_jd'], config('constant.sub_path_s3.job'));
        }

        $job = $this->jobRepository->update($id, [], $data);
        if ($job) {
            $user->notify(new UpdateJob($user));
        }
        return $job;
    }

    public function getSkill($name = null)
    {
        return $this->skillRepository->getArrName($name);
    }

    public function getListAll(array $params = [])
    {
        return $this->jobRepository->getListAll($params);
    }


    public function calculateBonusForCtv($job_bonus, $bonus_type)
    {
        $job_bonus = intval($job_bonus);

        switch ($bonus_type) {
            case 'cv':
                return 0.40 * $job_bonus; // 40% giá bán
            case 'interview':
                return 0.30 * $job_bonus; // 30% giá bán
            case 'onboard':
                return 0.45 * $job_bonus;
                // if ($job_bonus < 15000000) {
                //     return 0.50 * $job_bonus; // 50% lương gross ứng viên
                // } elseif ($job_bonus >= 15000000 && $job_bonus < 35000000) {
                //     return 0.55 * $job_bonus; // 55% lương gross ứng viên
                // } elseif ($job_bonus >= 35000000) {
                //     return 0.60 * $job_bonus; // 60% lương gross ứng viên
                // }
                break;
            default:
                return 0; // Trường hợp không xác định
        }
        return 0;
    }
}
