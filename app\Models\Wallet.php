<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use OwenIt\Auditing\Contracts\Auditable;

class Wallet extends BaseModel implements Auditable
{
    use HasFactory, Notifiable;
    use \OwenIt\Auditing\Auditable;

    protected $table = 'wallets';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $audit_tags = [];

    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }
    public function generateTags(): array
    {
        return $this->audit_tags;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Lấy lịch sử giao dịch của ví
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function transactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Trừ tiền từ ví
     *
     * @param float $amount Số tiền cần trừ
     * @param string $note Ghi chú về giao dịch
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan đến giao dịch
     * @return bool
     * @throws \Exception
     */
    public function subtractAmount(
        float $amount,
        $transactionable = null,
        string $note = 'Trừ tiền từ ví',
        ?string $type = null,
    ): bool {
        if ($amount <= 0) {
            throw new \Exception('Số tiền không hợp lệ');
        }

        if ($this->amount < $amount) {
            throw new \Exception('Số dư không đủ');
        }

        $oldAmount = $this->amount;
        $this->amount -= $amount;

        // Lưu thay đổi vào ví
        $saved = $this->save();

        if ($saved) {
            // Ghi log giao dịch
            WalletTransaction::createTransaction(
                $this,
                -$amount, // Số âm để thể hiện việc trừ tiền
                $note,
                $type,
                $transactionable
            );
        }

        return $saved;
    }

    public function getAmountStrAttribute()
    {
        return number_format($this->amount, 0, ',', '.') . ' VNĐ';
    }

    /**
     * Cộng tiền vào ví
     *
     * @param float $amount Số tiền cần cộng
     * @param string $note Ghi chú về giao dịch
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan đến giao dịch
     * @return bool
     * @throws \Exception
     */
    public function addAmount(
        float $amount,
        $transactionable = null,
        string $note = 'Cộng tiền vào ví',
        ?string $type = null,
    ): bool {
        if ($amount <= 0) {
            throw new \Exception('Số tiền không hợp lệ');
        }

        $this->amount += $amount;

        // Lưu thay đổi vào ví
        $saved = $this->save();

        if ($saved) {
            // Ghi log giao dịch
            WalletTransaction::createTransaction(
                $this,
                $amount, // Số dương để thể hiện việc cộng tiền
                $note,
                $type,
                $transactionable
            );
        }

        return $saved;
    }


    /**
     * Trừ tiền từ ví
     *
     * @param float $amount Số tiền cần trừ
     * @param string $note Ghi chú về giao dịch
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan đến giao dịch
     * @return bool
     * @throws \Exception
     */
    public function subtractPrice(
        float $price,
        $transactionable = null,
        string $note = 'Trừ tiền từ ví',
        ?string $type = null,
    ): bool {
        if ($price <= 0) {
            throw new \Exception('Số tiền không hợp lệ');
        }

        if ($this->price < $price) {
            throw new \Exception('Số dư không đủ');
        }

        $oldAmount = $this->price;
        $this->price -= $price;

        // Lưu thay đổi vào ví
        $saved = $this->save();

        if ($saved) {
            // Ghi log giao dịch
            WalletTransaction::createTransactionPrice(
                $this,
                -$price, // Số âm để thể hiện việc trừ tiền
                $note,
                $type,
                $transactionable
            );
        }

        return $saved;
    }
    /**
     * Cộng tiền vào ví
     *
     * @param float $amount Số tiền cần cộng
     * @param string $note Ghi chú về giao dịch
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan đến giao dịch
     * @return bool
     * @throws \Exception
     */
    public function addPrice(
        float $price,
        $transactionable = null,
        string $note = 'Cộng tiền vào ví',
        ?string $type = null,
    ): bool {
        if ($price <= 0) {
            throw new \Exception('Số tiền không hợp lệ');
        }

        $this->price += $price;

        // Lưu thay đổi vào ví
        $saved = $this->save();

        if ($saved) {
            // Ghi log giao dịch
            WalletTransaction::createTransactionPrice(
                $this,
                $price, // Số dương để thể hiện việc cộng tiền
                $note,
                $type,
                $transactionable
            );
        }

        return $saved;
    }
}
