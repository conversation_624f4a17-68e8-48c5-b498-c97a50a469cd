<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailRejectInterView extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $wareHouseCvSellingBuyBook;
    protected $wareHouseCvSellingBuy;

    public function __construct($employer,$wareHouseCvSellingBuyBook,$wareHouseCvSellingBuy)
    {
        $this->employer = $employer;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position = '';
        $type = '';

        $companyName = $this->employer->name;
        $employerName = $this->employer->name;
        $timeInterview = $this->wareHouseCvSellingBuyBook->date_time_book_format;
        $address = $this->wareHouseCvSellingBuyBook->address;

        if ($this->wareHouseCvSellingBuy->wareHouseCvSelling){
            $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        }

        if (!empty($this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
                $position = $this->wareHouseCvSellingBuy->job->name;
            }
        }
        $link = route('employer-cv-bought',['discuss' => $this->wareHouseCvSellingBuy->id]);

        return (new MailMessage)
            ->view('email.emailRejectInterView', [
                'employerName' => $employerName,
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type,
                'timeInterview' => $timeInterview,
                'address' => $address,
                'link' => $link,
            ])
            ->subject('[RECLAND] Đặt lại lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);
    }


}
