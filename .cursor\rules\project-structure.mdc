---
description:
globs:
alwaysApply: false
---
# <PERSON><PERSON><PERSON> Trúc Dự Án RecLand

RecLand là một ứng dụng web Laravel quản lý tuyển dụng và CV ứng viên. Dưới đây là cấu trúc dự án:

## Th<PERSON> mục ch<PERSON>

- [app/](mdc:app/) - Chứa core business logic của ứng dụng
  - [Http/Controllers/](mdc:app/Http/Controllers/) - Controllers xử lý requests
  - [Models/](mdc:app/Models/) - Models Eloquent định nghĩa các entity
  - [Services/](mdc:app/Services/) - Services xử lý business logic
  - [Repositories/](mdc:app/Repositories/) - Repositories cho data access layer
  
- [routes/](mdc:routes/) - Đị<PERSON> nghĩa routes của ứng dụng
  - [web.php](mdc:routes/web.php) - Routes frontend
  - [api.php](mdc:routes/api.php) - Routes API
  - [admin.php](mdc:routes/admin.php) - Routes admin panel
  
- [resources/](mdc:resources/) - Assets và views
  - [views/](mdc:resources/views/) - Blade templates
  - [js/](mdc:resources/js/) - JavaScript files
  - [css/](mdc:resources/css/) - CSS files
  
- [database/](mdc:database/) - Migrations và seeders
  - [migrations/](mdc:database/migrations/) - Database migrations
  - [seeders/](mdc:database/seeders/) - Database seeders
  
- [config/](mdc:config/) - Files cấu hình ứng dụng

- [tests/](mdc:tests/) - Tests cho ứng dụng

- [storage/](mdc:storage/) - Files tạm và uploads

- [memory_bank/](mdc:memory_bank/) - Lưu trữ workflow và ghi nhớ quá trình phát triển

## Workflow

Dự án theo mô hình MVC của Laravel với các lớp bổ sung:
1. Routes định nghĩa endpoints
2. Controllers xử lý requests
3. Services chứa business logic
4. Repositories tương tác với database
5. Models định nghĩa cấu trúc dữ liệu
