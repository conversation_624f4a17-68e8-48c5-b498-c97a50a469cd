# RecLand - <PERSON><PERSON><PERSON> trúc Code

## Cấu trúc thư mục ch<PERSON>h

```
Recland/
├── app/                    # Application logic
│   ├── Console/           # Artisan commands
│   ├── Http/              # Controllers, Middleware, Requests
│   ├── Models/            # Eloquent models
│   ├── Services/          # Business logic services
│   ├── Repositories/      # Data access layer
│   ├── Jobs/              # Queue jobs
│   ├── Notifications/     # Email/notification classes
│   └── Traits/            # Reusable traits
├── bootstrap/             # Framework bootstrap files
├── config/                # Configuration files
├── database/              # Migrations, seeders, factories
├── document/              # Project documentation
│   ├── SRS/              # Software Requirements Specification
│   └── HTML_2023/        # Frontend design templates
├── helpers/               # Helper functions
├── lang/                  # Language files
├── public/                # Web accessible files
├── resources/             # Views, assets, lang files
├── routes/                # Route definitions
├── storage/               # File storage, logs, cache
├── tests/                 # Test files
└── vendor/                # Composer dependencies
```

## App Structure

### Controllers
- `Admin/` - Admin panel controllers (Backpack)
- `Frontend/` - Frontend controllers
- `Api/` - API controllers

### Models
- Eloquent models cho database tables
- Relationships và business logic
- Scopes và accessors/mutators

### Services
- `Admin/` - Admin business logic
- `Frontend/` - Frontend business logic
- Separation of concerns

### Repositories
- Data access layer
- Database query abstraction
- Interface-based design

### Jobs
- Background job processing
- Email sending
- Data processing tasks

### Notifications
- Email notifications
- In-app notifications
- Multi-channel notifications

## Key Files

### Configuration
- `composer.json` - PHP dependencies
- `package.json` - JavaScript dependencies
- `webpack.mix.js` - Asset compilation
- `phpunit.xml` - Testing configuration

### Helpers
- `helpers/const.php` - Constants
- `helpers/function.php` - Global helper functions

### Routes
- `routes/web.php` - Web routes
- `routes/api.php` - API routes
- `routes/admin.php` - Admin routes (if exists)

## Database Structure

### Core Tables
- `users` - User accounts (all roles)
- `companies` - Company profiles
- `jobs` - Job postings
- `warehouse_cvs` - CV warehouse
- `submit_cvs` - CV submissions
- `warehouse_cv_sellings` - CV marketplace
- `wallets` - User wallets
- `transactions` - Financial transactions

### Supporting Tables
- `categories` - Job categories
- `skills` - Skills master data
- `bonuses` - Commission structure
- `notifications` - System notifications
- `email_logs` - Email tracking

## Naming Conventions

### Files & Classes
- Controllers: `PascalCase` + `Controller` suffix
- Models: `PascalCase` (singular)
- Services: `PascalCase` + `Service` suffix
- Jobs: `PascalCase` (descriptive action)

### Database
- Tables: `snake_case` (plural)
- Columns: `snake_case`
- Foreign keys: `table_id` format

### Variables & Methods
- Variables: `camelCase`
- Methods: `camelCase`
- Constants: `UPPER_SNAKE_CASE`

## Architecture Patterns

### Repository Pattern
- Data access abstraction
- Interface-based contracts
- Service layer integration

### Service Layer
- Business logic encapsulation
- Controller thin layer
- Reusable components

### Job Queue Pattern
- Background processing
- Email sending
- Heavy computations

### Notification Pattern
- Multi-channel notifications
- Template-based emails
- Event-driven notifications