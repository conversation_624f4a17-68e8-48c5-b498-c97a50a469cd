<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class InviteUser extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $emailInvite;
    protected $role;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $emailInvite, $role)
    {
        $this->user = $user;
        $this->emailInvite = $emailInvite;
        $this->role = $role;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = route('verify-employer-invite', [
            'token' => $this->user->token
        ]);
        return (new MailMessage)
            ->action('Invite User', url($url))
            ->view('email.inviteUser', [
                'emailInvite' => $this->emailInvite,
                'email'       => $this->user->email,
                'company'     => $this->user->company->name,
                'role'        => $this->role,
                'url'         => $url,
                'title'       => 'THƯ MỜI GIA NHẬP'
            ])
            ->subject('[HRI RECLAND] [THƯ MỜI GIA NHẬP]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [];
    }
}
