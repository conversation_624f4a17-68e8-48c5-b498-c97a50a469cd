<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToAdminSubmit extends Mailable
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function content(): Content
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $position      = $this->submitCv->job->name;
        $link           = route('submit-cv.edit', ['id' => $this->submitCv->id]);
        return new Content(
            view: 'email.changeStatusCancelOnboardToAdminSubmit',
            with: [
                'candidateName' => $candidateName,
                'position'      => $position,
                'companyName'   => $companyName,
                'link'          => $link,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $position = '';
        $type = $this->submitCv->bonus_type;
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $message->subject('[Ủy quyền] Thông báo ứng viên ' . $candidateName . ' đã “Cancel Onboard” vị trí ' . $position);
        return $this;
    }
}
