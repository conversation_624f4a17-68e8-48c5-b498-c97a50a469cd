<?php

namespace App\Repositories;

use App\Models\LevelBySkillMain;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LevelBySkillMainRepository extends BaseRepository
{
    const MODEL = LevelBySkillMain::class;

    public function getByGroup($params)
    {
        $query = $this->query();

        if (isset($params['group'])) {
            $query->where('group', $params['group']);
        }

        return $query->get();
    }
}
