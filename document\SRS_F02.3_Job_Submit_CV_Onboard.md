# SRS - F02.3: Submit CV - Giai đoạn Onboard

**<PERSON><PERSON><PERSON> bản:** 6.0
**Ngày:** 2025-07-08
**<PERSON><PERSON><PERSON> gi<PERSON>:** Gemini

---

### 1. <PERSON><PERSON> tả

Đây là giai đoạn cuối của luồng ứng tuyển. <PERSON>hi ứng viên chính thức nhận việc, NTD sẽ cập nhật trạng thái, dẫn đến việc hệ thống trừ phí Onboard (bằng **Point**) từ NTD và trả hoa hồng cuối cùng (bằng **Price**) cho CTV (nếu có). Logic được điều phối bởi `SubmitCvService` và Job `PayOnboardSubmit`.

### 2. Đ<PERSON>i tượng tham gia

-   **Nhà tuyển dụng (NTD):** Người cập nhật trạng thái, tr<PERSON> phí.
-   **<PERSON><PERSON><PERSON> tác viên (CTV):** Ng<PERSON>ời nhận hoa hồng.
-   **<PERSON><PERSON> thống:** Tự động hóa thanh toán.

### 3. <PERSON><PERSON><PERSON><PERSON> kiện tiên quyết

-   Bản ghi `submit_cvs` đang ở trạng thái **3 (Pass Interview)**.
-   Ví của NTD có đủ số dư Point cho phí Onboard.
-   `job->bonus_type` được cấu hình là 'onboard'.

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (Pass phỏng vấn):** NTD cập nhật trạng thái ứng viên thành **8 (Pass phỏng vấn)** hoặc **10 (Fail phỏng vấn)**. Service chỉ cập nhật trạng thái và ghi log lịch sử.

2.  **Bước 2 (Bắt đầu thử việc - `Trial work`):** NTD cập nhật trạng thái thành **14 (Trial work - Thử việc)**. Hành động này kích hoạt một chuỗi xử lý trong `SubmitCvService` (hoặc Job `ChangeToTrailWorkSubmit` nếu được dispatch):

    -   **a. Kiểm tra số dư ví NTD:** Hệ thống sẽ kiểm tra số dư ví của NTD để đảm bảo đủ tiền thanh toán phần còn lại của phí "Onboard" (90% giá trị, vì 10% có thể đã được đặt cọc trước đó).
    -   **b. Xử lý ghi nợ:** Nếu số dư không đủ, hệ thống sẽ ghi nhận khoản nợ vào bảng `SubmitCvPaymentDebit` cho NTD. NTD sẽ nhận được chuỗi thông báo nhắc nhở thanh toán nợ (email nhắc nhở sau 24 giờ, sau đó lặp lại từ ngày thứ 10 đến ngày thứ 15).
    -   **c. Trừ tiền từ ví NTD (nếu đủ):** Nếu số dư đủ, hệ thống sẽ trừ 90% giá trị "Onboard" từ ví của NTD. Giao dịch trừ tiền sẽ được ghi lại chi tiết trong `SubmitCvHistoryPayment`.
    -   **d. Cập nhật trạng thái và lịch sử:** Trạng thái của `SubmitCv` sẽ được cập nhật thành `14` ("Thử việc"). Lịch sử thay đổi trạng thái sẽ được ghi lại.
    -   **e. Thông báo:** Admin sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc (nếu CV thuộc "authority"). CTV sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc.
    -   **f. Nhắc nhở hết hạn thử việc:** Hệ thống sẽ gửi email nhắc nhở NTD về việc sắp hết hạn thử việc của ứng viên, bắt đầu từ ngày thứ 55 đến ngày thứ 59 của giai đoạn thử việc.
    -   **g. Tự động chuyển trạng thái "Tuyển dụng thành công":** Nếu NTD không thay đổi trạng thái của ứng viên sau 67 ngày kể từ ngày bắt đầu thử việc, hệ thống sẽ tự động cập nhật trạng thái thành `16` ("Tuyển dụng thành công"). Job `SuccessRecuitmentSubmit` sẽ được kích hoạt sau 67 ngày.
    -   **h. Lên lịch thanh toán hoa hồng cho CTV theo giai đoạn (Job `PayOnboardSubmit`):**
        -   **Lần 1 (15%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 30 ngày kể từ ngày bắt đầu thử việc để thanh toán 15% hoa hồng cho CTV.
        -   **Lần 2 (10%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 45 ngày kể từ ngày bắt đầu thử việc để thanh toán 10% hoa hồng cho CTV.
        -   **Lần 3 (75%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 67 ngày kể từ ngày bắt đầu thử việc để thanh toán 75% hoa hồng còn lại cho CTV (khi ứng viên đã ký hợp đồng chính thức).

3.  **Bước 3 (Job `PayOnboardSubmit` thực thi):**

    -   **a. Kiểm tra điều kiện:** Job `PayOnboardSubmit` được worker thực thi. Nó kiểm tra lại `status` của `submit_cv` phải là 14 hoặc 16 và `status_complain` phải hợp lệ (không có khiếu nại).
    -   **b. Xử lý thanh toán:** Nếu hợp lệ, job gọi phương thức `payOnboardCtv` trong `SubmitCvService`. Service này sẽ:
        -   Cộng phần trăm hoa hồng tương ứng vào ví **Price** của CTV (tính trên tổng giá trị hoa hồng). _Lưu ý: Nếu `submitCv->authorize > 0`, tổng hoa hồng CTV nhận được sẽ là 20% của `submitCv->bonus_point`, và các phần trăm trên sẽ được tính trên 20% đó._
        -   Ghi bản ghi tương ứng vào `payin_months` và `wallet_transactions`.

4.  **Bước 4 (Kết thúc):** Quy trình được xem là hoàn tất. `status_payment` trong `submit_cvs` được cập nhật thành **4 (NTD Đã thanh toán)** khi NTD thanh toán đủ hoặc **3 (Hoàn tiền)** nếu có hoàn tiền.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

-   **5.1. Ứng viên Fail phỏng vấn (Status 10) hoặc Từ chối Offer (Status 12):**

    -   **Nguyên nhân:** NTD cập nhật trạng thái thành 10 hoặc 12.
    -   **Xử lý:** Service cập nhật trạng thái và dispatch job **`DepositRefundRejectOfferSubmit`** để **hoàn lại phí Interview** đã trừ trước đó cho NTD. Quy trình kết thúc.

-   **5.2. Ứng viên Thất bại thử việc (Status 17):**

    -   **Nguyên nhân:** NTD cập nhật trạng thái thành 17 sau khi đã ở trạng thái 14 (Trial Work).
    -   **Xử lý:** Service cập nhật trạng thái và ghi log. Hệ thống sẽ hoàn lại một phần phí Onboard cho NTD dựa trên thời gian thử việc (100% cho 0-30 ngày, 70% cho 31-60 ngày, 50% cho 61-67 ngày). Khoản phí 10% đã trả ở Bước 3 sẽ **không** được hoàn lại. Không có khoản phí nào khác bị trừ.

-   **5.3. Lỗi không đủ Point:**

    -   **Nguyên nhân:** Tại thời điểm job `PayOnboardSubmit` thực thi (Bước 3 hoặc 5), ví của NTD không đủ Point.
    -   **Xử lý:** Phương thức trong `WalletService` sẽ ném ra Exception. Job `PayOnboardSubmit` sẽ thất bại. Hệ thống cần có cơ chế theo dõi (failed jobs) để Admin có thể thấy lỗi, thông báo cho NTD nạp tiền và thực hiện chạy lại job theo cách thủ công.

-   **5.4. NTD khiếu nại trước khi Job thực thi:**

    -   **Nguyên nhân:** NTD cập nhật trạng thái thành công (ví dụ: 14), job `PayOnboardSubmit` được dispatch. Nhưng trước khi worker thực thi job, NTD vào hệ thống và tạo khiếu nại (`status_complain` = 1).
    -   **Xử lý:** Tại **Bước 3a** hoặc **5a**, job `PayOnboardSubmit` sẽ kiểm tra `status_complain` và thấy có khiếu nại. Job sẽ kết thúc ngay lập tức mà không xử lý thanh toán, ngăn việc trả tiền cho CTV khi đang có tranh chấp.

-   **5.5. CTV hủy ứng tuyển:**
    -   **Mô tả:** CTV chủ động hủy ứng tuyển thông qua giao diện quản lý.
    -   **Khi nào xảy ra:**
        -   Trong giai đoạn chờ onboard (`status` = `Offering` - 11 hoặc `Waitingonboard` - 13)
        -   Hoặc trong giai đoạn thử việc (`status` = `Trialwork` - 14)
        -   Nhưng chưa thanh toán hoàn tất cho CTV (`status_payment_ctv` = 0)
    -   **Điều kiện:**
        -   `submit_cv->status` ∈ [11, 13, 14] (Offering, Waitingonboard, Trialwork)
        -   `submit_cv->status_payment_ctv` = 0 (chưa thanh toán CTV)
        -   `submit_cv->can_cancel_apply` = true
    -   **Xử lý:**
        -   Controller: `RecController@candidateCancelSubmitCv`
        -   Service: `SubmitCvService@candidateCancelSubmitWithRefund`
        -   Cập nhật `status` thành `2` (CandidateCancelApply)
        -   Hoàn tiền cho NTD theo giai đoạn:
            -   Nếu status = 11 hoặc 13: Hoàn 100% phí Onboard đã trả
            -   Nếu status = 14: Hoàn tiền theo thời gian thử việc (100%/70%/50% tùy thời điểm)
        -   Hủy tất cả các Job `PayOnboardSubmit` đã được dispatch
        -   Ghi log lịch sử trạng thái
        -   Gửi thông báo cho NTD về việc hủy ứng tuyển và hoàn tiền
        -   Controller trả về HTTP `200 OK` với thông báo thành công

### 6. Yêu cầu phi chức năng

-   **Tính toàn vẹn:** Toàn bộ các bước thanh toán và cập nhật ở Bước 3 phải nằm trong một Database Transaction.
-   **Độ tin cậy:** Job `PayOnboardSubmit` phải được giám sát để đảm bảo hoa hồng luôn được trả cho CTV một cách đáng tin cậy sau khi NTD đã bị trừ phí.

### 7. Mô hình dữ liệu liên quan

-   **Models:** `SubmitCv`, `SubmitCvHistoryStatus`, `Job`, `Wallet`, `WalletTransaction`, `SubmitCvHistoryPayment`, `SubmitCvOnboard`, `SubmitCvPaymentDebit`.
-   **Repositories:** `SubmitCvRepository`, `SubmitCvHistoryStatusRepository`, `SubmitCvHistoryPaymentRepository`, `SubmitCvOnboardRepository`, `SubmitCvPaymentDebitRepository`.
-   **Services:** `SubmitCvService`, `WalletService`.
-   **Controller:** `RecController` (Frontend).

### 8. Các Job liên quan (Background Processes)

-   **`ChangeToRejectOfferSubmit`**

    -   **Mô tả:** Xử lý khi ứng viên từ chối lời mời làm việc (Reject Offer) trong luồng Job Submit. Job này cập nhật trạng thái của `submit_cvs` và dispatch job hoàn tiền.
    -   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `submit_cvs` thành **12 (Reject Offer)**.
    -   **Happy Path:** Job nhận các tham số `submitCvId`, `onboardId`, và `user`. Nếu `submit_cv->status` là 20 (trạng thái nội bộ trước khi chuyển sang Reject Offer), job sẽ:
        -   Cập nhật trạng thái của `onboard` (liên quan đến `SubmitCvOnboardRepository`) thành 2.
        -   Ghi audit tag cho hành động này.
        -   Cập nhật `status` của `submit_cvs` thành **12 (Reject Offer)**.
        -   Ghi log lịch sử trạng thái vào `submit_cvs_history_status`.
        -   Gửi thông báo cho NTD (`EmailRejectOnboardSubmit`) thông qua đối tượng `employer` liên quan đến `submitCv`.
        -   Nếu `submit_cv->authorize > 0`, gửi thông báo cho Admin (`ChangeStatusCancelOnboardToAdminSubmit`).
        -   Nếu `submit_cv->authorize == 0`, gửi thông báo cho CTV (`ChangeStatusCancelOnboardToRecSubmit`).
        -   Dispatch job **`DepositRefundRejectOfferSubmit`** với độ trễ (48 giờ) để hoàn tiền cho NTD.
    -   **Sad Path / Xử lý lỗi:** Nếu `submit_cv->status` không phải là 20, job sẽ không thực hiện hành động nào.

-   **`PayOnboardSubmit`**

    -   **Mô tả:** Xử lý việc thanh toán hoa hồng Onboard cho CTV trong luồng Job Submit, theo các phần trăm được chỉ định (15%, 10%, 75%).
    -   **Khi nào được dispatch:** Khi `submit_cv->status` thay đổi sang 14 (Trial work) hoặc 16 (Success Recruitment).
    -   **Happy Path:** Nếu `submit_cv->status` là 14 hoặc 16 VÀ `submit_cv->status_complain` là 0 (Không khiếu nại) hoặc 5 (Admin từ chối khiếu nại), job sẽ gọi `SubmitCvService->payOnboardCtv()` để cộng tiền vào ví CTV và ghi log giao dịch cho phần trăm được chỉ định.
        -   **Mức thanh toán:**
            -   **Đợt 1 (15%):** Dispatched sau 30 ngày sau "Trial work".
            -   **Đợt 2 (10%):** Dispatched sau 45 ngày sau "Trial work".
            -   **Đợt 3 (75%):** Dispatched sau 67 ngày sau "Trial work" và ký hợp đồng chính thức.
        -   **Lưu ý:** Nếu `submitCv->authorize > 0`, tổng hoa hồng CTV nhận được sẽ là 20% của `submitCv->bonus_point`, và các phần trăm trên sẽ được tính trên 20% đó.
    -   **Sad Path / Xử lý lỗi:** Nếu `submit_cv->status` không hợp lệ hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thanh toán. Lỗi trong quá trình thanh toán sẽ khiến job thất bại và có thể được thử lại.

-   **`DepositRefundRejectOfferSubmit`**

    -   **Mô tả:** Hoàn lại Point cho NTD khi một giao dịch bị hủy hoặc từ chối trong luồng Job Submit.
    -   **Khi nào được dispatch:** Khi `submit_cv->status` thay đổi sang các trạng thái hủy/từ chối (ví dụ: 2, 4, 9, 12, 15).
    -   **Happy Path:** Job tìm các bản ghi thanh toán trước đó (`submitCvHistoryPaymentRepository->finBySubmitTypeStatus`), đánh dấu chúng là đã hoàn tiền, tạo các bản ghi log hoàn tiền mới, cập nhật `submit_cv->status_payment` thành 2 (Hoàn cọc), ghi audit tag, và cộng Point vào ví của NTD thông qua `WalletService`.
    -   **Sad Path / Xử lý lỗi:** Nếu không tìm thấy bản ghi thanh toán nào để hoàn tiền hoặc nếu đã được hoàn tiền trước đó, job sẽ không thực hiện hành động hoàn tiền. Lỗi trong quá trình hoàn tiền sẽ khiến job thất bại và có thể được thử lại.

-   **`SuccessRecuitmentSubmit`**
    -   **Mô tả:** Tự động cập nhật trạng thái ứng viên thành "Tuyển dụng thành công" nếu NTD không cập nhật sau một khoảng thời gian nhất định.
    -   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `submit_cvs` thành **14 (Trial work)**.
    -   **Happy Path:** Job nhận tham số `submitCvId`. Nếu `submit_cv->status` là 14 ("Trial work") và không có khiếu nại hoặc khiếu nại đã được giải quyết (ngụ ý đã quá 67 ngày kể từ ngày bắt đầu thử việc do job được dispatch với độ trễ 67 ngày), job sẽ cập nhật `status` thành 16 ("Success Recruitment"), `status_payment` thành 4, ghi audit tag, ghi log lịch sử trạng thái, và gửi thông báo cho Admin (nếu ủy quyền) hoặc CTV.
    -   **Sad Path / Xử lý lỗi:** Nếu `status` không phải là 14 hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thay đổi trạng thái.
