<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SubmitCvDiscuss;
use App\Models\SubmitCvBook;
use App\Models\Discuss;
use App\Models\SubmitCvOnboard;

class RecreateDiscussModel extends Command
{
    protected $signature = 'model:recreate-discuss';
    protected $description = 'Recreate Discuss models from SubmitCvDiscuss and SubmitCvBook models';

    public function handle()
    {
        $this->info('Starting to recreate Discuss models...');

        // Xử lý SubmitCvDiscuss
        $this->processSubmitCvDiscuss();

        // Xử lý SubmitCvBook
        $this->processSubmitCvBook();

        // Xử lý SubmitCvOnboard
        $this->processSubmitCvOnboard();
    }

    private function processSubmitCvDiscuss()
    {
        // Get all SubmitCvDiscuss records
        $submitCvDiscusses = SubmitCvDiscuss::all();
        $count = 0;

        $this->output->progressStart($submitCvDiscusses->count());

        foreach ($submitCvDiscusses as $submitCvDiscuss) {
            try {
                $discuss = $submitCvDiscuss->createDiscuss();
                if ($discuss) {
                    $count++;
                }
                $this->output->progressAdvance();
            } catch (\Exception $e) {
                $this->error("Error processing SubmitCvDiscuss ID {$submitCvDiscuss->id}: {$e->getMessage()}");
            }
        }

        $this->output->progressFinish();
        $this->info("Successfully recreated {$count} Discuss models from SubmitCvDiscuss");
    }

    private function processSubmitCvBook()
    {
        $submitCvBooks = SubmitCvBook::all();
        $count = 0;

        $this->output->progressStart($submitCvBooks->count());

        foreach ($submitCvBooks as $submitCvBook) {
            try {
                $discuss = $submitCvBook->createDiscuss();
                if ($discuss) {
                    $count++;
                }
                $this->output->progressAdvance();
            } catch (\Exception $e) {
                $this->error("Error processing SubmitCvBook ID {$submitCvBook->id}: {$e->getMessage()}");
            }
        }

        $this->output->progressFinish();
        $this->info("Successfully recreated {$count} Discuss models from SubmitCvBook");
    }

    private function processSubmitCvOnboard()
    {
        $submitCvOnboards = SubmitCvOnboard::all();
        $count = 0;

        $this->output->progressStart($submitCvOnboards->count());

        foreach ($submitCvOnboards as $submitCvOnboard) {
            try {
                $discuss = $submitCvOnboard->createDiscuss();
                if ($discuss) {
                    $count++;
                }
                $this->output->progressAdvance();
            } catch (\Exception $e) {
                $this->error("Error processing SubmitCvOnboard ID {$submitCvOnboard->id}: {$e->getMessage()}");
            }
        }

        $this->output->progressFinish();
        $this->info("Successfully recreated {$count} Discuss models from SubmitCvOnboard");
    }
}
