<?php

namespace App\Repositories;

use App\Models\WareHouseCv;
use App\Models\WareHouseCvSelling;
use App\Models\WareHouseCvSellingEvaluate;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WareHouseCvSellingRepository extends BaseRepository
{
    const MODEL = WareHouseCvSelling::class;
    private $million = 1000000;

    public function getCvSellingByUser($userId, $params, $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_5');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        $query->whereHas('wareHouseCv', function ($q) use ($userId, $params) {
            $q->where('user_id', $userId);
        });

        if (isset($params['search'])) {
            //            $query->where('candidate_name', 'like', '%' . $params['search'] . '%');
            $query->whereHas('wareHouseCv', function ($q) use ($userId, $params) {
                $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
            });
        }

        if (isset($params['status']) && $params['status'] != 'all') {
            $status = explode(',', $params['status']);
            $query->where(function ($q) use ($status) {
                foreach ($status as $k => $v) {
                    if ($v == 'all') {
                        break;
                    }
                    if ($v == 0) {
                        $q->orWhere('status', 0);
                    }
                    if ($v == 1) {
                        $q->orWhere('status', 1);
                    }
                    if ($v == 2) {
                        $q->orWhere(function ($q1) {
                            $q1->where('authority', 1)->where('status', 2);
                        });
                    }
                    if ($v == 3) {
                        $q->orWhere(function ($q1) {
                            $q1->where('authority', 3)->where('status', 2);
                        });
                    }
                }
            });
        }

        $query->with('wareHouseCv');

        //so luot mua
        $query->with(['wareHouseCvSellingBuy', 'wareHouseCvSellingQa' => function ($query) {
            $query->where('status', 0);
        }]);
        //0 dang ban, 1 huy dang ban
        //        $query->where(function ($query) {
        //            $query->whereIn('status', [0,2]);
        //        });
        /*$query->where(function ($query) {
            $query->whereIn('status', [0,2])
                ->orWhere(function ($query) {
                    $query->where('status', 1)->where('authority', 3);
                });
        });*/

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findCvSellingByUser($id, $userId)
    {
        $query = $this->query()
            ->where('id', $id)
            ->whereHas('wareHouseCv', function ($q) use ($userId) {
                $q->where('user_id', $userId);
            })->first();
        return $query;
    }

    public function getCvSellingInMarket($params)
    {
        //company NTD login
        $user = auth('client')->user();
        $company = $user->type == config('constant.role.employer') ? $user->company_id : null;

        $query = $this->query()->select('warehouse_cv_sellings.*');

        //        $query->join('warehouse_cvs', 'warehouse_cvs.id', '=', 'warehouse_cv_sellings.warehouse_cv_id');
        $query->join(
            DB::raw('
                (select warehouse_cv_sellings.id, max(warehouse_cv_sellings.id) as max_id from warehouse_cv_sellings
                inner join warehouse_cvs on warehouse_cvs.id = warehouse_cv_sellings.warehouse_cv_id
                group by warehouse_cvs.candidate_mobile, type_of_sale) as t1
            '),
            function ($join) {
                $join->on('warehouse_cv_sellings.id', '=', 't1.max_id');
            }
        );

        $query->whereHas('wareHouseCv', function ($q) use ($params) {
            if (isset($params['search'])) {
                $q->where(function ($q1) use ($params) {
                    $q1->where('candidate_name', 'like', '%' . $params['search'] . '%')
                        ->orWhere('candidate_job_title', 'like', '%' . $params['search'] . '%');
                });
            }
            $q->whereNotNull('cv_private');

            if (isset($params['type'])) {
                if (!(in_array(0, $params['type']) && in_array(1, $params['type']))) {
                    $types = [30, 31];
                    $q->where(function ($q1) use ($types, $params) {
                        if (in_array(0, $params['type'])) {
                            foreach ($types as $k => $type) {
                                if ($k == 0) {
                                    $q1->whereRaw("CONCAT(',', career, ',') like '%," . $type . ",%'");
                                    continue;
                                }
                                $q1->orWhereRaw("CONCAT(',', career, ',') like '%," . $type . ",%'");
                            }
                        } else {
                            foreach ($types as $k => $type) {
                                $q1->whereRaw("CONCAT(',', career, ',') not like '%," . $type . ",%'");
                            }
                        }
                    });
                }
            }

            if (isset($params['career'])) {
                $careers = explode(',', $params['career']);
                $q->where(function ($q1) use ($careers) {
                    foreach ($careers as $career) {
                        $q1->whereRaw("CONCAT(',', career, ',') like '%," . $career . ",%'");
                    }
                });
            }

            if (isset($params['year_of_experience'])) {
                $q->where('year_experience', $params['year_of_experience']);
            }

            if (isset($params['rank'])) {
                $q->where('rank', $params['rank']);
            }

            if (isset($params['time_to_work'])) {
                $q->where('candidate_est_timetowork', $params['time_to_work']);
            }

            if (isset($params['skill'])) {
                $q->where('main_skill', 'like', '%' . $params['skill'] . '%');
            }
        });

        if (isset($params['min']) && isset($params['max'])) {
            $min = $params['min'] * $this->million;
            $max = $params['max'] * $this->million;
            $query->where('warehouse_cv_sellings.candidate_salary_expect', '>=', $min)
                ->where('warehouse_cv_sellings.candidate_salary_expect', '<=', $max);
        }

        if (isset($params['cv_saved']) && $params['cv_saved'] == 1) {
            $query->whereHas('wareHouseCvSaveWithUser');
        }

        if (isset($params['type_of_sale'])) {
            $query->where('warehouse_cv_sellings.type_of_sale', $params['type_of_sale']);
        }

        if (isset($company)) {
            $query->where(function ($q) use ($company) {
                $q->whereRaw("CONCAT(',', warehouse_cv_sellings.exclude_company, ',') not like '%," . $company . ",%'")
                    ->orWhereNull('warehouse_cv_sellings.exclude_company');
            });
        }
        //0 đăng bán, 1 hủy đăng bán
        $query->where("warehouse_cv_sellings.status", 0);

        $query->with(['wareHouseCv', 'wareHouseCvSellingBuyNtd']);
        //        $query->groupBy('mobile', 'type_of_sale');

        if (isset($params['sort']) && isset($params['sort_name'])) {
            if ($params['sort_name'] == 'new') {
                $sort = $params['sort'] == 1 ? 'desc' : 'asc';
                $query->orderBy('updated_at', $sort);
            } else if ($params['sort_name'] == 'price') {
                $sort = $params['sort'] == 1 ? 'desc' : 'asc';
                $query->orderBy('price', $sort);
            } else if ($params['sort_name'] == 'by') {
                if ($params['sort'] == 0) {
                    $query->orderByDesc(WareHouseCv::select('year_experience')->whereColumn('warehouse_cvs.id', 'warehouse_cv_sellings.warehouse_cv_id'));
                } else if ($params['sort'] == 1) {
                    $query->orderByDesc(WareHouseCvSellingEvaluate::select(DB::raw('avg(star) as star'))->whereColumn('warehouse_cv_selling_evaluates.warehouse_cv_selling_id', 'warehouse_cv_sellings.id'));
                } else if ($params['sort'] == 2) {
                    $query->orderBy(WareHouseCvSellingEvaluate::select(DB::raw('avg(star) as star'))->whereColumn('warehouse_cv_selling_evaluates.warehouse_cv_selling_id', 'warehouse_cv_sellings.id'));
                } else if ($params['sort'] == 3) {
                    $query->orderBy('candidate_salary_expect', 'asc');
                } else if ($params['sort'] == 4) {
                    $query->orderBy('candidate_salary_expect', 'desc');
                }
            }
        } else {
            // tắt tạm order theo ngành nghề của cty
            if (isset($user->company) && $user->company->career && false) {
                $careers = explode(',', $user->company->career);
                $raw = 'case';
                foreach ($careers as $k => $career) {
                    $raw .= " when concat(',', career, ',') like '%," . $career . ",%' then " . $k + 1;
                }
                $raw .= ' else ' . count($careers) + 1 . ' end';
                $query->orderBy(WareHouseCv::select(DB::raw($raw))->whereColumn('warehouse_cvs.id', 'warehouse_cv_sellings.warehouse_cv_id'))
                    ->orderBy('updated_at', 'desc');
                // $query->orderBy('updated_at', 'desc');
            } else {
                $query->orderBy('updated_at', 'desc');
            }
        }

        $query = $query->paginate(config('constant.paginate_10'));
        return $query;
    }

    public function findWithWareHouseCvId($wareHouseCvId)
    {
        return $this->query()->where('warehouse_cv_id', $wareHouseCvId)->first();
    }

    public function findCvWithType($params)
    {
        return $this->query()->where('warehouse_cv_id', $params['warehouse_cv_id'])->where('type_of_sale', $params['type'])->whereIn('status', [0, 2])->first();
    }

    public function getCvSeeMore($email, $mobile, $id)
    {
        //company NTD login
        $user = auth('client')->user();
        if (!$user) {
            return [];
        }
        $company = $user->type == config('constant.role.employer') ? $user->company_id : null;

        $query = $this->query();
        $query->select(
            'warehouse_cv_sellings.*',
            'warehouse_cvs.candidate_name as candidate_name',
        );

        $query->join('warehouse_cvs', 'warehouse_cvs.id', '=', 'warehouse_cv_sellings.warehouse_cv_id');
        $query->where('warehouse_cvs.candidate_mobile', $mobile);
        $query->where('warehouse_cv_sellings.id', '!=', $id);
        $query->where("warehouse_cv_sellings.status", 0);

        if (isset($company)) {
            $query->where(function ($q) use ($company) {
                $q->whereRaw("CONCAT(',', warehouse_cv_sellings.exclude_company, ',') not like '%," . $company . ",%'")
                    ->orWhereNull('warehouse_cv_sellings.exclude_company');
            });
        }

        return $query->get();
    }

    public function getCvAnother($skill, $type, $id)
    {
        //company NTD login
        $user = auth('client')->user();
        if (!$user) {
            return [];
        }
        $company = $user->type == config('constant.role.employer') ? $user->company_id : null;

        $query = $this->query();
        $query->select(
            'warehouse_cv_sellings.*',
            'warehouse_cvs.candidate_name as candidate_name',
        );

        $query->join('warehouse_cvs', 'warehouse_cvs.id', '=', 'warehouse_cv_sellings.warehouse_cv_id');
        $query->where('warehouse_cv_sellings.skill', $skill)->where('warehouse_cv_sellings.type_of_sale', $type)->where('warehouse_cv_sellings.id', '!=', $id);
        $query->where("warehouse_cv_sellings.status", 0);

        if (isset($company)) {
            $query->where(function ($q) use ($company) {
                $q->whereRaw("CONCAT(',', warehouse_cv_sellings.exclude_company, ',') not like '%," . $company . ",%'")
                    ->orWhereNull('warehouse_cv_sellings.exclude_company');
            });
        }

        return $query->get();
    }

    public function findWithId($id)
    {
        return $this->query()->where('id', $id)->with('wareHouseCv')->first()->toArray();
    }

    public function getMaxSalary()
    {
        return $this->query()->where('status', 0)->max('candidate_salary_expect');
    }

    //CV đang đăng bán

    public function total($params = array())
    {
        $query = $this->query();
        foreach ($params as $key => $param) {
            switch ($key) {
                case 'after_date':
                    $query->where('created_at', '>=', $param);
                    break;
                case 'user_id':
                    $query->where('user_id', $param);
                    break;
                default:
                    break;
            }
        }
        $query->where('authority', 1); //chờ duyệt
        return $query->count();
    }

    public function getListCvSellingAll($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])) {
            $query->offset($params['start']);
        }

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('candidate_name', 'like', '%' . $params['search'] . '%');
        }
        //        $query->where('status', 0);//0 dang ban, 1 huy dang ban

        if (isset($params['filter'])) {
            $query->whereHas('warehouseCv', function ($query) use ($params) {
                if (isset($params['filter']['user_id'])) {
                    $query->where('user_id', $params['filter']['user_id']);
                }
            });

            if (isset($params['filter']['candidate_email_phone'])) {
                $query->where(function ($query) use ($params) {
                    $query->where('candidate_email', 'LIKE', '%' . $params['filter']['candidate_email_phone'] . '%')
                        ->orWhere('candidate_mobile', 'LIKE', '%' . $params['filter']['candidate_email_phone'] . '%');
                });
            }

            if (isset($params['filter']['authority']) && $params['filter']['authority'] >= 0) {
                $query->where('authority', $params['filter']['authority']);
            }

            if (isset($params['filter']['status']) && $params['filter']['status'] >= 0) {
                $query->where('status', $params['filter']['status']);
            }

            if (isset($params['filter']['start_buy'])) {
                $startBuy = Carbon::createFromFormat('d/m/Y', $params['filter']['start_buy'])->startOfDay();
                $query->where('created_at', '>=', $startBuy);
            }

            if (isset($params['filter']['end_buy'])) {
                $endBuy = Carbon::createFromFormat('d/m/Y', $params['filter']['end_buy'])->endOfDay();
                $query->where('created_at', '<=', $endBuy);
            }
        }

        $query->with(['warehouseCv']);
        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function statisticalByMonth($fromDate = null, $toDate = null)
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();

        return $query->where('status', 0)
            ->where('is_real', 1) // Chỉ lấy dữ liệu thật
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('COUNT(id) as total, DATE_FORMAT(created_at, "%m") as month')
            ->groupByRaw('DATE_FORMAT(created_at, "%m")')
            ->orderByRaw('DATE_FORMAT(created_at, "%m")')
            ->get();
    }
}