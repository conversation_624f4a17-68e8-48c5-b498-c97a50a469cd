<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class HtmlController extends Controller
{

    public function homeNtd()
    {
        return view('frontend.pages.html.home-ntd');
    }

    public function homeCtv(){
        return view('frontend.pages.html.home-ctv');
    }

    public function listJob(){
        return view('frontend.pages.html.list-job');
    }

    public function listNew(){
        return view('frontend.pages.html.list-new');
    }

    public function noJob(){
        return view('frontend.pages.html.no-job');
    }

    public function newDetail(){
        return view('frontend.pages.html.new-detail');
    }

    public function profileCtv(){
        return view('frontend.pages.html.profile-ctv');
    }

    public function dashboardCtv(){
        return view('frontend.pages.html.dashboard-ctv');
    }

    public function candidateIntroductionManager(){
        return view('frontend.pages.html.candidate-introduction-manager');
    }

    public function dashboardStorage(){
        return view('frontend.pages.html.dashboard-storage');
    }

    public function dashboardStorageEdit(){
        return view('frontend.pages.html.dashboard-storage-edit');
    }

    public function dashboardListJob(){
        return view('frontend.pages.html.dashboard-list-job');
    }

    public function dashboardEditJob(){
        return view('frontend.pages.html.dashboard-edit-job');
    }

    public function dashboardListRecruitment(){
        return view('frontend.pages.html.dashboard-list-recruitment');
    }

    public function dashboardInfoCompany(){
        return view('frontend.pages.html.dashboard-info-company');
    }

    public function aboutUs(){
        return view('frontend.pages.html.about-us');
    }

    public function privacyPolicy(){
        return view('frontend.pages.html.privacy-policy');
    }

    public function contactUs(){
        return view('frontend.pages.html.contact-us');
    }

    public function email(){
        return view('frontend.pages.html.email');
    }

    public function searchCompany(){
        return view('frontend.pages.html.search-company');
    }

    public function searchCompany2(){
        return view('frontend.pages.html.search-company2');
    }

    public function detailCompany(){
        return view('frontend.pages.html.detail-company');
    }

    public function memberManager(){
        return view('frontend.pages.html.detail-manager');
    }

    public function listMember(){
        return view('frontend.pages.html.list-member');
    }

    public function employerRegister(){
        return view('frontend.pages.html.employer-register');
    }

    public function listCandidate(){
        return view('frontend.pages.html.candidates');
    }

    public function cvOnSale(){
        return view('frontend.pages.html.cv-on-sale');
    }

    public function cvSales(){
        return view('frontend.pages.html.cv-sale');
    }

    public function cvBought(){
        return view('frontend.pages.html.cv-bought');
    }

    public function walletDetail(){
        return view('frontend.pages.html.wallet-detail');
    }

    public function approveCandidate(){
        return view('frontend.pages.html.approve-candidate');
    }

    public function walletRec(){
        return view('frontend.pages.html.wallet-rec');
    }

    public function walletEmployer(){
        return view('frontend.pages.html.wallet-employer');
    }

    public function historyPayment(){
        return view('frontend.pages.html.history-payment');
    }

}
