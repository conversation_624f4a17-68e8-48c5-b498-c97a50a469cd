<?php

namespace App\Services\Frontend;


use App\Repositories\BonusRepository;
use App\Repositories\UserRepository;
use App\Repositories\WalletRepository;

class BonusService
{

    protected $bonusRepository;
    protected $userRepository;
    protected $walletRepository;

    public function __construct(BonusRepository $bonusRepository, UserRepository $userRepository, WalletRepository $walletRepository)
    {
        $this->bonusRepository = $bonusRepository;
        $this->userRepository = $userRepository;
        $this->walletRepository = $walletRepository;
    }

    public function addBonus($userId, $money, $moneyKpi, $month, $year)
    {
        $paymentStatus = 0;
        if ($money == 0 && $moneyKpi == 0) $paymentStatus = 1;
        $bonus = $this->bonusRepository->getBonusByUser($userId, $month, $year);
        $wallet = $this->walletRepository->findByUser($userId);
        if ($bonus) {
            //Cộng vào Ví tổng của CTV
            $price = 0;
            if ($bonus->money < $money)          $price = $money - $bonus->money;
            if ($bonus->money_kpi < $moneyKpi)   $price += $moneyKpi - $bonus->money_kpi;

            if ($price > 0) {
                // $wallet->price = $wallet->price + $price;
                $wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wallet->id]);
                $wallet->addPrice($price, $wallet, 'Cộng bonus', 'add_bonus');
                // $wallet->save();
            }

            $bonus->update(['money' => $money, 'money_kpi' => $moneyKpi, 'payment_status' => $paymentStatus]);
        } else {
            $this->bonusRepository->create([
                'month' => $month,
                'year' => $year,
                'user_id' => $userId,
                'money' => $money,
                'money_kpi' => $moneyKpi,
                'payment_status' => $paymentStatus
            ]);
            //Cộng vào Ví tổng của CTV
            $price = 0;
            if ($money > 0)      $price = $money;
            if ($moneyKpi > 0)   $price += $moneyKpi;
            if ($price > 0) {
                // $wallet->price = $wallet->price + $price;
                $wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wallet->id]);
                $wallet->addPrice($price, $wallet, 'Cộng bonus', 'add_bonus');
                // $wallet->save();
            }
        }
    }

    public function kpiLeader($referral_code, $money, $month, $year)
    {
        $userList = $this->userRepository->getWithDefineReferral($referral_code);
        $moneyKpi = 0;
        if (count($userList) > 0) {
            $team = self::recursive($userList, $month, $year);
        } else {
            $team = 0;
            return 0;  //không có team thì không có kpi
        }
        // $money = $money + $team;
        // Chỉ tính tiền của member để tính KPI thôi, không tính tiền của bản thân leader
        $money = $team;
        //>= 150M  & <= 200M thi dc 5% của phần vượt
        if ($money >= config('constant.kpi.0.min') && $money <= config('constant.kpi.0.max')) {
            $moneyKpi = ($money - config('constant.kpi.0.min')) * config('constant.kpi.0.bonus');
        }
        //> 200M => 7M
        if ($money > config('constant.kpi.1.min') && $money < config('constant.kpi.1.max')) {
            $moneyKpi = config('constant.kpi.1.bonus');
        }
        //> 300M => 10M
        if ($money > config('constant.kpi.2.more')) {
            $moneyKpi = config('constant.kpi.2.bonus');
        }

        return $moneyKpi;
    }
    //cap1
    public function recursive($userList, $month, $year)
    {
        $team = 0;
        foreach ($userList as $key => $value) {
            //cap1
            $bonus = $this->bonusRepository->getBonusByUser($value->id, $month, $year);
            $team = $team + (!empty($bonus) ? $bonus->money : 0);
            //cap23
            $team23 = self::recursiveChild($value->referral_define, $month, $year);
            $team = $team + $team23;
        }

        return $team;
    }
    //cap 2, 3
    public function recursiveChild($referral_code, $month, $year)
    {
        $team23 = 0;
        $userList2 = $this->userRepository->getWithDefineReferral($referral_code);
        if (count($userList2) > 0) {
            foreach ($userList2 as $key => $value) {
                $bonus = $this->bonusRepository->getBonusByUser($value->id, $month, $year);
                $team23 = $team23 + (!empty($bonus) ? $bonus->money : 0);
                //cap3
                $userList3 = $this->userRepository->getWithDefineReferral($value->referral_define);
                if (count($userList3) > 0) {
                    foreach ($userList3 as $key1 => $value1) {
                        $bonus2 = $this->bonusRepository->getBonusByUser($value1->id, $month, $year);
                        $team23 = $team23 + (!empty($bonus2) ? $bonus2->money : 0);
                    }
                }
            }
        }

        return $team23;
    }
}
