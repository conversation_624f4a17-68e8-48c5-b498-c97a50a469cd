<?php

namespace App\Services\Frontend;

use App\Jobs\CandidateCancelInterviewSubmitCv;
use App\Jobs\RecSumPointSubmit;
use App\Jobs\RejectRecruitmentSubmit;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerIntroduceCandidate;
use App\Notifications\RecApplyJobNotiSendAdmin;
use App\Notifications\RecCancelCv;
use App\Notifications\RecIntroduceCandidate;
use App\Notifications\AuthorizeCv;
use App\Notifications\RecNewCv;
use App\Notifications\RecUpdateCv;
use App\Notifications\SendQaToRec;
use App\Notifications\SubmitCvSuccessSendMailCandidate;
use App\Notifications\UpdateStatusCv;
use App\Repositories\JobRepository;
use App\Repositories\SubmitCvMetaRepository;
use App\Repositories\UserRepository;
use App\Repositories\WareHouseCvRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingQaRepository;
use App\Repositories\WareHouseCvSellingRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class WareHouseSubmitCvService
{
    protected $wareHouseCvRepository;
    protected $submitCvRepository;
    protected $jobRepository;
    protected $userRepository;
    protected $submitCvMetaRepository;
    protected $wareHouseCvSellingRepository;
    protected $wareHouseCvSellingQaRepository;
    protected $submitCvHistoryStatusRepository;
    protected $submitCvHistoryPaymentRepository;
    protected $submitCvService;

    public function __construct(
        WareHouseCvRepository  $wareHouseCvRepository,
        SubmitCvRepository     $submitCvRepository,
        JobRepository          $jobRepository,
        SubmitCvHistoryPaymentRepository $submitCvHistoryPaymentRepository,
        UserRepository         $userRepository,
        SubmitCvMetaRepository $submitCvMetaRepository,
        WareHouseCvSellingRepository $wareHouseCvSellingRepository,
        WareHouseCvSellingQaRepository $wareHouseCvSellingQaRepository,
        SubmitCvHistoryStatusRepository $SubmitCvHistoryStatusRepository,
        SubmitCvService $submitCvService,
    ) {
        $this->submitCvHistoryStatusRepository  = $SubmitCvHistoryStatusRepository;
        $this->wareHouseCvRepository            = $wareHouseCvRepository;
        $this->submitCvRepository               = $submitCvRepository;
        $this->jobRepository                    = $jobRepository;
        $this->userRepository                   = $userRepository;
        $this->submitCvMetaRepository           = $submitCvMetaRepository;
        $this->wareHouseCvSellingRepository     = $wareHouseCvSellingRepository;
        $this->wareHouseCvSellingQaRepository   = $wareHouseCvSellingQaRepository;
        $this->submitCvHistoryPaymentRepository = $submitCvHistoryPaymentRepository;
        $this->submitCvService                  = $submitCvService;
    }

    public function find($id)
    {
        return $this->submitCvRepository->find($id);
    }
    public function saveData($params)
    {
        $user = auth('client')->user();

        try {
            //Tab Thêm mới CV
            if (isset($params['tab']) && $params['tab'] == 'themmoi_cv') {

                //----Tạo cv: warehouse_cvs----
                $paramWareHouseCvs = [
                    //lấy user đang login type rec
                    'user_id' => $user->id,
                    //Họ và tên
                    'candidate_name' => isset($params['candidate_name']) ? $params['candidate_name'] : '',
                    //So dien thoai
                    'candidate_mobile' => isset($params['candidate_mobile']) ? $params['candidate_mobile'] : '',
                    //Email
                    'candidate_email' => isset($params['candidate_email']) ? $params['candidate_email'] : '',
                    //Vị trí công việc
                    'candidate_job_title' => isset($params['candidate_job_title']) ? $params['candidate_job_title'] : '',
                    //mức lương mong muôn
                    'candidate_salary_expect' => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                    'candidate_currency' => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                    //Link portfolio
                    'candidate_portfolio' => isset($params['candidate_portfolio']) ? $params['candidate_portfolio'] : '',
                ];

                if (isset($params['cv_private']) && is_file($params['cv_private'])) {
                    $paramWareHouseCvs['cv_private'] = FileServiceS3::getInstance()->uploadToS3($params['cv_private'], config('constant.sub_path_s3.cv'));
                }

                if (isset($params['cv_public']) && is_file($params['cv_public'])) {
                    $paramWareHouseCvs['cv_public'] = FileServiceS3::getInstance()->uploadToS3($params['cv_public'], config('constant.sub_path_s3.cv'));
                }

                if (isset($params['slug'])) {
                    $jobDetail = $this->jobRepository->findBySlug($params['slug']);
                } elseif (isset($params['job'])) {
                    $jobDetail = $this->jobRepository->find($params['job']);
                }

                $wareHouseCvs = $this->wareHouseCvRepository->create($paramWareHouseCvs);
                //----Giơi thieu ứng viên submit_cvs----
                if ($wareHouseCvs) {
                    $paramSubmitCvs = [
                        //md5(user_id dang login . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id)
                        'code' => md5($user->id . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id),
                        //lấy user đang login type rec
                        'user_id' => $user->id,
                        //lấy từ màn hình job_detail hoặc job_search mà click hiển thị popup
                        'job_id' => isset($jobDetail->id) ? $jobDetail->id : 0,
                        'company_id' => isset($jobDetail->company_id) ? $jobDetail->company_id : 0,
                        'bonus_type' => isset($jobDetail->bonus_type) ? $jobDetail->bonus_type : 0,
                        'bonus' => isset($jobDetail->bonus) ? $jobDetail->bonus : 0,
                        'incentive' => isset($jobDetail->incentive) ? $jobDetail->incentive : 0,
                        'bonus_currency' => isset($jobDetail->bonus_currency) ? $jobDetail->bonus_currency : 0,
                        'bonus_self_apply' => isset($jobDetail->bonus_self_apply) ? $jobDetail->bonus_self_apply : 0,
                        'bonus_self_apply_incentive' => isset($jobDetail->bonus_self_apply_incentive) ? $jobDetail->bonus_self_apply_incentive : 0,
                        'bonus_self_apply_currency' => isset($jobDetail->bonus_self_apply_currency) ? $jobDetail->bonus_self_apply_currency : 0,
                        //mức lương mong muôn,
                        'candidate_salary_expect' => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                        'candidate_currency' => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                        'warehouse_cv_id' => $wareHouseCvs->id,
                        //click xác nhận $params['status'] = 'pending-review'
                        'status' => array_search($params['status'], config('constant.submit_cvs_status')),
                        //Ở màn hình https://v2.recland.co/job/cong-viec-moi-binh-test-kYwC0XuY click vào Giới thiệu ứng viên => 0, Tự ứng tuyển => 1
                        'is_self_apply' => isset($params['is_self_apply']) ? $params['is_self_apply'] : 0,
                        //ngày dự kiến đi làm
                        'expected_date' => isset($params['expected_date']) ? Carbon::createFromFormat('d/m/Y', $params['expected_date'])->format('Y-m-d') : null,
                        'is_active' => config('constant.active'),
                        'date_change_status' => Carbon::now()->format('Y-m-d H:i:s'),
                        'payment_fee' => $jobDetail->payment_fee,
                    ];
                    $submitCv = $this->submitCvRepository->create($paramSubmitCvs);

                    $paramWareHouseCvs['warehouse_cv_id'] = $wareHouseCvs->id;
                    $paramWareHouseCvs['submit_cv_id'] = $submitCv->id;
                    $this->submitCvMetaRepository->create($paramWareHouseCvs);
                }
            } elseif (isset($params['tab']) && $params['tab'] == 'chontukho') {
                $id = isset($params['ware_house_cv']) ? $params['ware_house_cv'] : 1;
                $wareHouseCvs = $this->wareHouseCvRepository->find($id);
                if (isset($params['slug'])) {
                    $jobDetail = $this->jobRepository->findBySlug($params['slug']);
                } elseif (isset($params['job'])) {
                    $jobDetail = $this->jobRepository->find($params['job']);
                }
                $paramSubmitCvs = [
                    //md5(user_id dang login . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id)
                    'code' => md5($user->id . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id),
                    //lấy user đang login type rec
                    'user_id' => $user->id,
                    //lấy từ màn hình job_detail hoặc job_search mà click hiển thị popup
                    'job_id' => isset($jobDetail->id) ? $jobDetail->id : 0,
                    'company_id' => isset($jobDetail->company_id) ? $jobDetail->company_id : 0,
                    'bonus_type' => isset($jobDetail->bonus_type) ? $jobDetail->bonus_type : 0,
                    'bonus' => isset($jobDetail->bonus) ? $jobDetail->bonus : 0,
                    'incentive' => isset($jobDetail->incentive) ? $jobDetail->incentive : 0,
                    'bonus_currency' => isset($jobDetail->bonus_currency) ? $jobDetail->bonus_currency : 0,
                    'bonus_self_apply' => isset($jobDetail->bonus_self_apply) ? $jobDetail->bonus_self_apply : 0,
                    'bonus_self_apply_incentive' => isset($jobDetail->bonus_self_apply_incentive) ? $jobDetail->bonus_self_apply_incentive : 0,
                    'bonus_self_apply_currency' => isset($jobDetail->bonus_self_apply_currency) ? $jobDetail->bonus_self_apply_currency : 0,
                    //mức lương mong muôn,
                    'candidate_salary_expect' => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                    'candidate_currency' => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                    'warehouse_cv_id' => $wareHouseCvs->id,
                    //Click Luu nháp: $params['status'] = 'draft', click xác nhận $params['status'] = 'pending-review'
                    'status' => array_search(ucfirst($params['status']), config('constant.submit_cvs_status')),
                    //Ở màn hình https://v2.recland.co/job/cong-viec-moi-binh-test-kYwC0XuY click vào Giới thiệu ứng viên => 0, Tự ứng tuyển => 1
                    'is_self_apply' => isset($params['is_self_apply']) ? $params['is_self_apply'] : 0,
                    //ngày dự kiến đi làm
                    'expected_date' => isset($params['expected_date']) ? Carbon::createFromFormat('d/m/Y', $params['expected_date'])->format('Y-m-d') : null,
                    'is_active' => config('constant.active'),
                    'date_change_status' => Carbon::now()->format('Y-m-d H:i:s'),
                    'payment_fee' => $jobDetail->payment_fee,
                ];

                $submitCv = $this->submitCvRepository->create($paramSubmitCvs);

                $paramWareHouseCvs = [
                    'user_id' => $user->id,
                    'candidate_name' => $wareHouseCvs->candidate_name,
                    'candidate_mobile' => $wareHouseCvs->candidate_mobile,
                    'candidate_email' => $wareHouseCvs->candidate_email,
                    'candidate_job_title' => $wareHouseCvs->candidate_job_title,
                    'candidate_salary_expect' => $wareHouseCvs->candidate_salary_expect,
                    'candidate_portfolio' => $wareHouseCvs->candidate_portfolio,
                    'candidate_currency' => $wareHouseCvs->candidate_currency,
                    'cv_public' => $wareHouseCvs->cv_public,
                    'cv_private' => $wareHouseCvs->cv_private,
                    'assessment' => $wareHouseCvs->assessment,
                    'main_skill' => $wareHouseCvs->main_skill,
                    'year_experience' => $wareHouseCvs->year_experience,
                ];

                $paramWareHouseCvs['warehouse_cv_id'] = $wareHouseCvs->id;
                $paramWareHouseCvs['submit_cv_id'] = $submitCv->id;

                $this->submitCvMetaRepository->create($paramWareHouseCvs);
            }

            if (strtolower($params['status']) != strtolower(config('constant.submit_cvs_status.7'))) {

                //send noti ctv
                $user->notify(new RecIntroduceCandidate($jobDetail, $wareHouseCvs, $user));

                //send noti ntd
                $employer = $this->userRepository->find($jobDetail->employer_id);

                if (!\Str::contains($employer->email, 'fake.dev')) {
                    $employer->notify(new EmployerIntroduceCandidate($jobDetail, $wareHouseCvs, $user, $employer));
                }
            }

            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function paymentSubmitCv($submitCvId)
    {
        // try {
        DB::beginTransaction();
        $submitCv = $this->submitCvRepository->find($submitCvId);

        if (!$submitCv) {
            return false;
        }
        $user = auth('client')->user();

        if (!$user) {
            return false;
        }

        $status_payment = config('constant.status_payments_revert.waiting_payment');
        $type_of_submit = $submitCv->bonus_type;
        if ($submitCv->bonus_type == 'onboard' || $submitCv->bonus_type == 'interview') {
            $statusRecruitment = config('constant.status_recruitment_revert.Waitingsetupinterview'); //3
        }
        if ($submitCv->bonus_type == 'cv') {
            $statusRecruitment = config('constant.status_recruitment_revert.BuyCVdatasuccessfull'); //18
        }
        $submitCv->update([
            'status' => $statusRecruitment,
        ]);

        //update wallet user
        //onboard: users.total = users.total - số tiền đặt cọc (10 % warehouse_cv_sellings.price)
        //cv, interview: users.total = users.total - warehouse_cv_sellings.price
        $point = $submitCv->bonus_type == 'onboard' ? 0.1 * $submitCv->bonus_ntd : $submitCv->bonus_ntd;
        $percent = $submitCv->bonus_type == 'onboard' ? 10 : 100;
        //check so point của NTD neu ko đủ thi dừng
        if ($point > $user->wallet->amount) return false;
        //trừ point của NTD
        $remainingPoint = $user->wallet->amount - $point;
        // $user->wallet->amount = $remainingPoint;
        $user->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
        $user->wallet->subtractAmount($point, $submitCv, 'Thanh toán tiền mua Cv Submit', 'payment_submit_cv');

        if ($submitCv->bonus_type == 'onboard' || $submitCv->bonus_type == 'interview') {
            // Sau 7 ngày không đặt lịch PV thì coi như NTD từ chối tuyển dụng (ko hoàn tiền)
            CandidateCancelInterviewSubmitCv::dispatch($submitCv->id)->delay(now()->addDays(7));
        }
        // $user->wallet->save();
        //ghi log mua
        $this->submitCvHistoryPaymentRepository->create([
            'user_id'      => $submitCv->employer->id,
            'submit_cv_id' => $submitCv->id,
            'type'         => 0,                                              //0 trừ tiền, 1 hoàn tiền
            'percent'      => $percent,                                       //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
            'type_of_sale' => $submitCv->bonus_type,
            'amount'       => $point,
            'balance'      => $remainingPoint,
            'status'       => 0
        ]);
        $historyStatus = $this->submitCvHistoryStatusRepository->create([
            'user_id'            => $submitCv->employer->id,
            'submit_cvs_id'      => $submitCv->id,
            'status_recruitment' => $statusRecruitment,
            'candidate_name'     => $submitCv->warehouseCv->candidate_name,
            'type'               => 'employer',
            'authority'          => $submitCv->authorize
        ]);

        //sau 7 ngày mà trạng thái tuyển dụng :
        //1. status_recruitment = 18 => 'Buy CV data successfull'
        //2. không bị NTD khiếu nại, hoặc khiếu nại thất bại: CTV-> Admin từ chối thì cộng tiền trả CTV status_complain Khác 4, 3 CTV xác nhận
        // Thì thực hiện thanh toán cho CTV
        if ($submitCv->bonus_type == 'cv') {
            // cong tien cho CTV sau 7 ngay khong co khieu nai
            RecSumPointSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(7 * 24 * 60));
        } else {
            // set queue check status_recruitment sau 48h
            // RejectRecruitmentSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(48 * 60));
        }

        DB::commit();
        // } catch (\Exception $e) {
        //     DB::rollBack();
        //     Log::info('error log: ', [
        //         'content buy: ' => $e->getMessage()
        //     ]);
        // }

        return true;
    }

    public function candidateIntroduction($params)
    {
        $user = auth('client')->user();

        try {

            $mainSkill = [];
            if (!empty($params['skills'])) {
                foreach ($params['skills'] as $skill) {
                    if (isset($skill['name'])) {
                        $mainSkill[$skill['name']] = isset($skill['description']) ? $skill['description'] : null;
                    }
                }
            }
            if (isset($params['slug'])) {
                $jobDetail = $this->jobRepository->findBySlug($params['slug']);
            } elseif (isset($params['job'])) {
                $jobDetail = $this->jobRepository->find($params['job']);
            }
            // $status_payment = config('constant.status_payments_revert.waiting_payment');
            $status_payment = null;
            $type_of_submit = $jobDetail->bonus_type;
            // if ($jobDetail->bonus_type == 'onboard' || $jobDetail->bonus_type == 'interview') {
            //     $statusRecruitment = config('constant.status_recruitment_revert.Waitingcandidateconfirm'); //1
            //     // $statusRecruitment = config('constant.status_recruitment_revert.WaitingPayment'); //21
            // }elseif ($jobDetail->bonus_type == 'cv') {
            //     // $statusRecruitment = config('constant.status_recruitment_revert.BuyCVdatasuccessfull'); //18
            //     $statusRecruitment = config('constant.status_recruitment_revert.WaitingPayment'); //21
            // }
            $statusRecruitment = config('constant.status_recruitment_revert.Waitingcandidateconfirm'); //1

            $bonus_for_ctv = $jobDetail->bonus_for_ctv;

            //Tab Thêm mới CV
            if (isset($params['tab']) && $params['tab'] == 'themmoi_cv') {

                //----Tạo cv: warehouse_cvs----
                $paramWareHouseCvs = [
                    //lấy user đang login type rec
                    'user_id' => $user->id,
                    //Họ và tên
                    'candidate_name' => isset($params['candidate_name']) ? $params['candidate_name'] : '',
                    //So dien thoai
                    'candidate_mobile' => isset($params['candidate_mobile']) ? $params['candidate_mobile'] : '',
                    //Email
                    'candidate_email' => isset($params['candidate_email']) ? $params['candidate_email'] : '',
                    //Vị trí công việc
                    'candidate_job_title' => isset($params['candidate_job_title']) ? $params['candidate_job_title'] : '',
                    //mức lương mong muôn
                    'candidate_salary_expect'    => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                    'candidate_salary_expect_to' => isset($params['candidate_salary_expect_to']) ? $params['candidate_salary_expect_to'] : 0,
                    'candidate_currency'         => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                    //Link portfolio
                    'candidate_portfolio'      => isset($params['candidate_portfolio']) ? $params['candidate_portfolio'] : '',
                    'assessment'               => isset($params['assessment']) ? $params['assessment'] : '',
                    'year_experience'          => isset($params['year_experience']) ? $params['year_experience'] : null,
                    'career'                   => implode(',', $params['career']),
                    'rank'                     => isset($params['rank']) ? $params['rank'] : null,
                    'candidate_est_timetowork' => isset($params['candidate_est_timetowork']) ? $params['candidate_est_timetowork'] : null,
                    'main_skill'               => json_encode($mainSkill),
                ];

                if (isset($params['cv_private'])) {
                    $paramWareHouseCvs['cv_private'] = $params['cv_private'];
                }

                if (isset($params['cv_public'])) {
                    $paramWareHouseCvs['cv_public'] = $params['cv_public'];
                }

                $wareHouseCvs = $this->wareHouseCvRepository->create($paramWareHouseCvs);
                //----Giơi thieu ứng viên submit_cvs----
                if ($wareHouseCvs) {

                    $paramSubmitCvs = [
                        //md5(user_id dang login . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id)
                        'code' => md5($user->id . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id),
                        //lấy user đang login type rec
                        'user_id' => $user->id,
                        //lấy từ màn hình job_detail hoặc job_search mà click hiển thị popup
                        'job_id'                     => isset($jobDetail->id) ? $jobDetail->id : 0,
                        'company_id'                 => isset($jobDetail->company_id) ? $jobDetail->company_id : 0,
                        'bonus_type'                 => isset($jobDetail->bonus_type) ? $jobDetail->bonus_type : 0,
                        'bonus'                      => $bonus_for_ctv,
                        'incentive'                  => isset($jobDetail->incentive) ? $jobDetail->incentive : 0,
                        'bonus_currency'             => isset($jobDetail->bonus_currency) ? $jobDetail->bonus_currency : 0,
                        'bonus_self_apply'           => isset($jobDetail->bonus_self_apply) ? $jobDetail->bonus_self_apply : 0,
                        'bonus_self_apply_incentive' => isset($jobDetail->bonus_self_apply_incentive) ? $jobDetail->bonus_self_apply_incentive : 0,
                        'bonus_self_apply_currency'  => isset($jobDetail->bonus_self_apply_currency) ? $jobDetail->bonus_self_apply_currency : 0,
                        //mức lương mong muôn,
                        'candidate_salary_expect'    => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                        'candidate_salary_expect_to' => isset($params['candidate_salary_expect_to']) ? $params['candidate_salary_expect_to'] : 0,
                        'candidate_currency'         => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                        'warehouse_cv_id'            => $wareHouseCvs->id,
                        //click xác nhận $params['status'] = 'pending-review'
                        // 'status' => config('constant.submit_cvs_status_value')['pending-confirm'],
                        'status'         => $statusRecruitment,
                        'status_payment' => $status_payment,
                        //Ở màn hình https://v2.recland.co/job/cong-viec-moi-binh-test-kYwC0XuY click vào Giới thiệu ứng viên => 0, Tự ứng tuyển => 1
                        'is_self_apply' => isset($params['is_self_apply']) ? $params['is_self_apply'] : 0,
                        //ngày dự kiến đi làm
                        'expected_date'            => isset($params['expected_date']) ? Carbon::createFromFormat('d/m/Y', $params['expected_date'])->format('Y-m-d') : null,
                        'is_active'                => config('constant.active'),
                        'date_change_status'       => Carbon::now()->format('Y-m-d H:i:s'),
                        'payment_fee'              => $jobDetail->payment_fee,
                        'year_experience'          => isset($params['year_experience']) ? $params['year_experience'] : null,
                        'rank'                     => isset($params['rank']) ? $params['rank'] : null,
                        'candidate_est_timetowork' => isset($params['candidate_est_timetowork']) ? $params['candidate_est_timetowork'] : null,
                        'career'                   => implode(',', $params['career']),
                        'confirm_token'            => Str::random(16),
                    ];
                    if (!empty($params['authority']) && $params['authority'] == 'on') {
                        $paramSubmitCvs['authorize'] = 1;
                        $paramSubmitCvs['confirm_candidate'] = null;
                    } else {
                        $paramSubmitCvs['confirm_candidate'] = Carbon::now()->addDay(2);
                    }
                    $submitCv = $this->submitCvRepository->create($paramSubmitCvs);


                    $paramWareHouseCvs['warehouse_cv_id'] = $wareHouseCvs->id;
                    $paramWareHouseCvs['submit_cv_id'] = $submitCv->id;
                    $this->submitCvMetaRepository->create($paramWareHouseCvs);
                }
            } elseif (isset($params['tab']) && $params['tab'] == 'chontukho') {
                if (!isset($params['ware_house_cv']) || $params['ware_house_cv'] == 0) {
                    throw new \Exception('Dữ liệu không tồn tại');
                }
                $id = isset($params['ware_house_cv']) ? $params['ware_house_cv'] : 0;
                $wareHouseCvs = $this->wareHouseCvRepository->find($id);
                if (isset($params['slug'])) {
                    $jobDetail = $this->jobRepository->findBySlug($params['slug']);
                } elseif (isset($params['job'])) {
                    $jobDetail = $this->jobRepository->find($params['job']);
                }
                $paramSubmitCvs = [
                    //md5(user_id dang login . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id)
                    'code' => md5($user->id . $jobDetail->id . $jobDetail->company_id . $wareHouseCvs->id),
                    //lấy user đang login type rec
                    'user_id' => $user->id,
                    //lấy từ màn hình job_detail hoặc job_search mà click hiển thị popup
                    'job_id'                     => isset($jobDetail->id) ? $jobDetail->id : 0,
                    'company_id'                 => isset($jobDetail->company_id) ? $jobDetail->company_id : 0,
                    'bonus_type'                 => isset($jobDetail->bonus_type) ? $jobDetail->bonus_type : 0,
                    'bonus'                      => $bonus_for_ctv,
                    'incentive'                  => isset($jobDetail->incentive) ? $jobDetail->incentive : 0,
                    'bonus_currency'             => isset($jobDetail->bonus_currency) ? $jobDetail->bonus_currency : 0,
                    'bonus_self_apply'           => isset($jobDetail->bonus_self_apply) ? $jobDetail->bonus_self_apply : 0,
                    'bonus_self_apply_incentive' => isset($jobDetail->bonus_self_apply_incentive) ? $jobDetail->bonus_self_apply_incentive : 0,
                    'bonus_self_apply_currency'  => isset($jobDetail->bonus_self_apply_currency) ? $jobDetail->bonus_self_apply_currency : 0,
                    //mức lương mong muôn,
                    'candidate_salary_expect'    => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                    'candidate_salary_expect_to' => isset($params['candidate_salary_expect_to']) ? $params['candidate_salary_expect_to'] : 0,
                    'candidate_currency'         => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                    'warehouse_cv_id'            => $wareHouseCvs->id,
                    //Click Luu nháp: $params['status'] = 'draft', click xác nhận $params['status'] = 'pending-review'
                    // 'status' => config('constant.submit_cvs_status_value')['pending-confirm'],
                    'status'         => $statusRecruitment,
                    'status_payment' => $status_payment,
                    //Ở màn hình https://v2.recland.co/job/cong-viec-moi-binh-test-kYwC0XuY click vào Giới thiệu ứng viên => 0, Tự ứng tuyển => 1
                    'is_self_apply' => isset($params['is_self_apply']) ? $params['is_self_apply'] : 0,
                    //ngày dự kiến đi làm
                    'expected_date'            => isset($params['expected_date']) ? Carbon::createFromFormat('d/m/Y', $params['expected_date'])->format('Y-m-d') : null,
                    'is_active'                => config('constant.active'),
                    'date_change_status'       => Carbon::now()->format('Y-m-d H:i:s'),
                    'payment_fee'              => $jobDetail->payment_fee,
                    'year_experience'          => isset($params['year_experience']) ? $params['year_experience'] : null,
                    'rank'                     => isset($params['rank']) ? $params['rank'] : null,
                    'candidate_est_timetowork' => isset($params['candidate_est_timetowork']) ? $params['candidate_est_timetowork'] : null,
                    'career'                   => implode(',', $params['career']),
                    'confirm_token'            => Str::random(16),
                ];


                if (!empty($params['authority']) && $params['authority'] == 'on') {
                    $paramSubmitCvs['authorize'] = 1;
                    $paramSubmitCvs['confirm_candidate'] = null;
                } else {
                    $paramSubmitCvs['confirm_candidate'] = Carbon::now()->addDay(2);
                }

                $submitCv = $this->submitCvRepository->create($paramSubmitCvs);

                $paramWareHouseCvs = [
                    'user_id'                 => $user->id,
                    'candidate_name'          => isset($params['candidate_name']) ? $params['candidate_name'] : '',
                    'candidate_mobile'        => isset($params['candidate_mobile']) ? $params['candidate_mobile'] : '',
                    'candidate_email'         => isset($params['candidate_email']) ? $params['candidate_email'] : '',
                    'candidate_job_title'     => isset($params['candidate_job_title']) ? $params['candidate_job_title'] : '',
                    'candidate_salary_expect' => isset($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : 0,
                    'candidate_portfolio'     => isset($params['candidate_portfolio']) ? $params['candidate_portfolio'] : '',
                    'candidate_currency'      => isset($params['candidate_currency']) ? $params['candidate_currency'] : 'VND',
                    'assessment'              => isset($params['assessment']) ? $params['assessment'] : 'VND',
                    'main_skill'              => json_encode($mainSkill),
                    'year_experience'         => isset($params['year_experience']) ? $params['year_experience'] : null,
                ];
                $paramWareHouseCvs['cv_private'] = $wareHouseCvs->cv_private;
                $paramWareHouseCvs['cv_public'] = $wareHouseCvs->cv_public;
                $paramWareHouseCvs['warehouse_cv_id'] = $wareHouseCvs->id;
                $paramWareHouseCvs['submit_cv_id'] = $submitCv->id;

                $this->submitCvMetaRepository->create($paramWareHouseCvs);
            }

            if (isset($submitCv) && $submitCv) {
                //Log thay đổi trang thái
                $historyStatus = $this->submitCvHistoryStatusRepository->create([
                    'user_id'            => $user->id,
                    'submit_cvs_id'      => $submitCv->id,
                    'status_recruitment' => $statusRecruitment,
                    'type'               => 'rec',
                    'authority'          => isset($paramSubmitCvs['authorize']) && $paramSubmitCvs['authorize'] == 1 ? 1 : 0
                ]);

                // comment lai, khi nao mo CV thi moi chay doan code nay (khi chuyen trang thai "cho setup lich phong van")
                // if (false) {

                //sau 7 ngày mà trạng thái tuyển dụng :
                //1. status_recruitment = 18 => 'Buy CV data successfull'
                //2. không bị NTD khiếu nại, hoặc khiếu nại thất bại: CTV-> Admin từ chối thì cộng tiền trả CTV status_complain Khác 4, 3 CTV xác nhận
                // Thì thực hiện thanh toán cho CTV
                // if ($jobDetail->bonus_type == 'cv') {
                //     // RecSumPointSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(7 * 24 * 60));
                // } else {
                //     // set queue check status_recruitment sau 48h
                //     RejectRecruitmentSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(48 * 60));
                //     // neu la interview hoac onboard thi gui mail cho Candidate
                //     Mail::to($wareHouseCvs->candidate_email)->send(mailable: new SubmitCvSuccessSendMailCandidate($submitCv, $jobDetail));
                // // }
                // }
            }

            if (isset($paramSubmitCvs['authorize']) && $paramSubmitCvs['authorize'] == 1) {
                //gui thông báo:
                $user->notify(new AuthorizeCv());
            }

            // if (isset($submitCv) && !(isset($paramSubmitCvs['authorize']) && $paramSubmitCvs['authorize'] == 1)) {
            // send noti candidate
            // $wareHouseCvs->notify(new ConfirmSupportJob($jobDetail, $wareHouseCvs, $user));
            // }
            // neu la interview hoac onboard thi gui mail cho Candidate
            // if ($jobDetail->bonus_type == 'interview' || $jobDetail->bonus_type == 'onboard') {
            Mail::to($wareHouseCvs->candidate_email)->send(new SubmitCvSuccessSendMailCandidate($submitCv, $jobDetail));

            // Dat job tu reject sau 48h neu Ung vien khong xac nhan
            RejectRecruitmentSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(48 * 60));
            // }

            // if (strtolower($params['status']) != strtolower(config('constant.submit_cvs_status.7'))) {
            if (strtolower($params['status']) == 'pending-confirm') {
                //send noti ctv
                // $user->notify(instance: new RecIntroduceCandidate($jobDetail, $wareHouseCvs, $user));

                //send noti ntd
                $employer = $this->userRepository->find($jobDetail->employer_id);

                if (!\Str::contains($employer->email, 'fake.dev')) {
                    // $employer->notify(new EmployerIntroduceCandidate($jobDetail, $wareHouseCvs, $user, $employer));
                }
            }

            $email_admin = config('settings.global.email_admin');
            Notification::route('mail', $email_admin)->notify(new RecApplyJobNotiSendAdmin($jobDetail, $wareHouseCvs, $user));



            return true;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }


    public function getWareHouseCvByCtv($userId, $keyWord = '')
    {
        return $this->wareHouseCvRepository->getWareHouseCvByCtv($userId, $keyWord);
    }

    public function getWareHouseCvNotSelling($userId, $keyWord = '')
    {
        return $this->wareHouseCvRepository->getWareHouseCvNotSelling($userId, $keyWord);
    }

    public function getWareHouseCvById($userId, $id)
    {
        return $this->wareHouseCvRepository->getWareHouseCvById($userId, $id);
    }

    public function getTotalWareHouseCvByUserId($userId)
    {
        return $this->wareHouseCvRepository->total(['user_id' => $userId]);
    }

    /**
     * @param $condition
     *
     * @return mixed
     * Pending - approved - reject a nhé
     */
    public function countSubmitcvByCondition($condition)
    {
        return $this->submitCvRepository->total($condition);
    }

    public function getNewCv($userId)
    {
        $data = $this->submitCvRepository->getListSubmitCv(
            [
                'user_id' => $userId,
                'size' => 20,
            ],
            [],
            false,
            'created_at'
        );
        $data->load('warehouseCv')->load('company');
        return $data;
    }

    public function getNewCvByEmployerId($employerId)
    {
        $data = $this->submitCvRepository->getListSubmitCv(
            [
                'employer_id' => $employerId,
                'size' => 20,
            ],
            [],
            false,
            'created_at'
        );
        $data->load('warehouseCv')->load('company');
        return $data;
    }

    public function statisticalFrontend($userId)
    {
        $status = config('constant.submit_cvs_status');
        unset($status[config('constant.submit_cvs_status_value.draft')]);
        unset($status[config('constant.submit_cvs_status_value.admin-review')]);
        unset($status[config('constant.submit_cvs_status_value.admin-rejected')]);
        $statistical = [];
        $now = \Carbon\Carbon::now();
        $end = $now->lastOfMonth()->format('d');
        foreach ($status as $key => $value) {
            $itemStatistical = $this->submitCvRepository->statisticalByStatus($userId, $key);
            $dayValue = [];
            for ($i = 1; $i <= (int)$end; $i++) {
                $item = $itemStatistical->where('day', $i);
                if ($item->count() > 0) {
                    $dayValue[$i] = $item->first()->total;
                } else {
                    $dayValue[$i] = 0;
                }
            }
            $statistical[$value] = $dayValue;
        }
        return $statistical;
    }

    public function statisticalEmployerFrontend($userId)
    {
        $status = config('constant.submit_cvs_status');
        unset($status[config('constant.submit_cvs_status_value.draft')]);
        unset($status[config('constant.submit_cvs_status_value.admin-review')]);
        unset($status[config('constant.submit_cvs_status_value.admin-rejected')]);
        unset($status[config('constant.submit_cvs_status_value.pending-confirm')]);
        unset($status[config('constant.submit_cvs_status_value.candidate-rejected')]);
        $statistical = [];
        $now = \Carbon\Carbon::now();
        $end = $now->lastOfMonth()->format('d');
        foreach ($status as $key => $value) {
            $itemStatistical = $this->submitCvRepository->statisticalJobByStatus($userId, $key);
            $dayValue = [];
            for ($i = 1; $i <= (int)$end; $i++) {
                $item = $itemStatistical->where('day', $i);
                if ($item->count() > 0) {
                    $dayValue[$i] = $item->first()->total;
                } else {
                    $dayValue[$i] = 0;
                }
            }
            $statistical[$value] = $dayValue;
        }
        return $statistical;
    }


    /**
     * @param $params
     *
     * @return mixed
     * Search danh sach gioi thieu ung vien
     */
    public function getListSubmitCvByJobSearch($params)
    {
        return $this->submitCvRepository->getListSubmitCvByJobSearch($params);
    }

    public function cancelSubmitCvByIdUserStatus($id, $userId)
    {
        try {
            $checkSubmitCv = $this->submitCvRepository->getSubmitCvByIdUserStatus($id, $userId);
            if ($checkSubmitCv && in_array($checkSubmitCv->status, array_intersect_key(config('constant.submit_cvs_status_value'), array_flip(['pending-review', 'pending-confirm', 'admin-review'])))) {
                $checkSubmitCv->status = config('constant.submit_cvs_status_value.cancel');
                if ($checkSubmitCv->save()) {
                    //=> có thông báo cho nhà tuyển dụng là đã hủy ứng viên nay
                    //=> email cho chính CTV
                    //=> dùng riêng template gửi email

                    //send mail and notify to rec
                    $rec = $this->userRepository->find($userId);
                    $rec->notify(new RecCancelCv($rec));
                }
                return $checkSubmitCv;
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function getSubmitCvByIdUserStatus($id, $userId)
    {
        try {
            $checkSubmitCv = $this->submitCvRepository->getSubmitCvByIdUserStatus($id, $userId);
            if ($checkSubmitCv) {
                return $checkSubmitCv;
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function getWarehousecvByIdUser($id, $userId)
    {
        try {
            $checkWarehousecv = $this->wareHouseCvRepository->getWarehousecvByIdUser($id, $userId);
            if ($checkWarehousecv) {
                return $checkWarehousecv;
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    //get all qa by selling
    public function getWarehousecvQa($id, $userId)
    {
        try {
            return $this->wareHouseCvSellingQaRepository->getAllWithCvSelling($id);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    //delete
    public function deleteWarehousecvQa($id, $userId)
    {
        try {
            $qa = $this->wareHouseCvSellingQaRepository->find($id);
            $parent = $this->wareHouseCvSellingQaRepository->find($qa->parent_id);
            $parent->status = 0;
            $parent->save();
            return $this->wareHouseCvSellingQaRepository->delete($id);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    //send
    public function sendQa($data, $client)
    {
        try {
            $save['user_id'] = $client->id;
            $save['parent_id'] = $data['id'];
            $save['warehouse_cv_selling_id'] = $data['warehouse_cv_selling_id'];
            $save['comment'] = $data['feedback'];
            $save['status'] = 1;
            $parent = $this->wareHouseCvSellingQaRepository->find($data['id']);
            if ($parent) {
                $parent->status = 1;
                $parent->save();
            }

            //send mail NTD
            $cvSelling = $this->wareHouseCvSellingRepository->find($data['warehouse_cv_selling_id']);
            $parent->user->notify(new SendQaToRec($parent->user, $cvSelling, $save));

            return $this->wareHouseCvSellingQaRepository->create($save);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }
    //comment
    public function comment($data, $client)
    {
        try {
            $save['user_id'] = $client->id;
            $save['warehouse_cv_selling_id'] = $data['warehouse_cv_selling_id'];
            $save['comment'] = $data['comment'];
            $save['status'] = 1;

            return $this->wareHouseCvSellingQaRepository->create($save);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * @param $params
     *
     * @return mixed
     * Search danh sach gioi thieu ung vien
     */
    public function getListWarehouseCvSearch($params)
    {
        return $this->wareHouseCvRepository->getListWarehouseCvSearch($params);
    }

    public function countWareHouseCvSearch($params)
    {
        $params['user_id'] = auth('client')->user()->id;
        return $this->wareHouseCvRepository->countWareHouseCvSearch($params);
    }

    /**
     * @param $params
     *
     * @return mixed
     * Search danh sach cv đa ứng tuyển vao 1 job
     */
    public function getListSubmitCvSearch($params)
    {
        return $this->submitCvRepository->getListSubmitCvSearch($params);
    }

    public function getListJobByTeam($params)
    {
        return $this->submitCvRepository->getListJobByTeam($params);
    }

    /**
     * @param $params
     *
     * @return mixed
     * Search danh sach Ứng viên đã ứng tuyển vào job của NTD
     * /employer/submitcv
     */
    public function getListSubmitByNtd($params)
    {
        return $this->submitCvRepository->getListSubmitByNtd($params);
    }

    public function countSubmitCvSearch($params)
    {
        $params['user_id'] = auth('client')->user()->id;
        return $this->submitCvRepository->countSubmitCvSearch($params);
    }

    public function saveWarehouseCvModal($params, $id = 0)
    {
        $arrSkillDescription = [];
        if (isset($params['skills'])) {
            foreach ($params['skills'] as $k => $v) {
                if (isset($v['name'])) {
                    $arrSkillDescription[$v['name']] = isset($v['description']) ? $v['description'] : null;
                }
            }
        }

        $data = [
            'user_id'                    => $params['user_id'],
            'candidate_name'             => !empty($params['candidate_name']) ? $params['candidate_name'] : '',
            'candidate_email'            => !empty($params['candidate_email']) ? $params['candidate_email'] : '',
            'candidate_mobile'           => !empty($params['candidate_mobile']) ? $params['candidate_mobile'] : '',
            'assessment'                 => !empty($params['assessment']) ? $params['assessment'] : '',
            'candidate_portfolio'        => !empty($params['candidate_portfolio']) ? $params['candidate_portfolio'] : '',
            'candidate_job_title'        => !empty($params['candidate_job_title']) ? $params['candidate_job_title'] : '',
            'candidate_currency'         => !empty($params['candidate_currency']) ? $params['candidate_currency'] : '',
            'candidate_salary_expect'    => !empty($params['candidate_salary_expect']) ? $params['candidate_salary_expect'] : '',
            'candidate_salary_expect_to' => !empty($params['candidate_salary_expect_to']) ? $params['candidate_salary_expect_to'] : '',
            'year_experience'            => !empty($params['year_experience']) ? $params['year_experience'] : '',
            'candidate_address'          => !empty($params['candidate_address']) ? $params['candidate_address'] : '',
            'candidate_location'         => !empty($params['candidate_location']) ? json_encode($params['candidate_location']) : '',
            'main_skill'                 => json_encode($arrSkillDescription),
            'career'                     => !empty($params['career']) ? implode(',', $params['career']) : '',
            'candidate_formwork'         => !empty($params['candidate_formwork']) ? $params['candidate_formwork'] : '',
            'candidate_est_timetowork'   => !empty($params['candidate_est_timetowork']) ? $params['candidate_est_timetowork'] : '',
            'rank'                       => !empty($params['rank']) ? $params['rank'] : null,
        ];
        if (isset($params['cv_private'])) {
            $data['cv_private'] = $params['cv_private'];
        }

        if (isset($params['cv_public'])) {
            $data['cv_public'] = $params['cv_public'];
        }

        if ($id) {
            $wareHouseCv = $this->wareHouseCvRepository->find($id);
            $old_cv_private = $wareHouseCv->cv_private;
            $old_cv_public = $wareHouseCv->cv_public;
            if ($wareHouseCv) {
                $this->wareHouseCvRepository->update($id, [], $data);
                $this->wareHouseCvRepository->changeCvFile($id, [
                    'old_cv_private' => $old_cv_private,
                    'old_cv_public'  => $old_cv_public,
                    'cv_private'     => isset($params['cv_private']) ? $params['cv_private'] : null,
                    'cv_public'      => isset($params['cv_public']) ? $params['cv_public'] : null,
                ]);
                //send mail and notify
                $rec = $this->userRepository->find($params['user_id']);
                $rec->notify(new RecUpdateCv($rec, $wareHouseCv));
            }

            return $id;
        } else {
            $response = $this->wareHouseCvRepository->create($data);

            //send mail and notify
            $rec = $this->userRepository->find($params['user_id']);
            $rec->notify(new RecNewCv($rec));

            return $response->id;
        }
    }


    public function saveWarehouseCv($params, $id = 0)
    {
        $arrSkillDescription = [];
        if (isset($params['skill']) && $params['description']) {
            foreach ($params['skill'] as $k => $v) {
                if ($v) {
                    $arrSkillDescription[$v] = $params['description'][$k];
                }
            }
        }

        $data = [
            'user_id' => $params['user_id'],
            'candidate_name' => $params['candidate_name'],
            'candidate_email' => $params['candidate_email'],
            'candidate_mobile' => $params['candidate_mobile'],
            'assessment' => $params['assessment'],
            'candidate_portfolio' => $params['candidate_portfolio'],
            'candidate_job_title' => $params['candidate_job_title'],
            'candidate_currency' => $params['candidate_currency'],
            'candidate_salary_expect' => $params['candidate_salary_expect'],
            'candidate_salary_expect_to' => $params['candidate_salary_expect_to'],
            'year_experience' => $params['year_experience'],
            'candidate_address' => $params['candidate_address'],
            'candidate_location' => json_encode($params['candidate_location']),
            'main_skill' => json_encode($arrSkillDescription),
            'career' => implode(',', $params['career']),
            'candidate_formwork' => $params['candidate_formwork'],
            'candidate_est_timetowork' => $params['candidate_est_timetowork'],
        ];
        if (isset($params['cv_private']) && is_file($params['cv_private'])) {
            $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3($params['cv_private'], config('constant.sub_path_s3.cv'));
        }

        if (isset($params['cv_public']) && is_file($params['cv_public'])) {
            $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3($params['cv_public'], config('constant.sub_path_s3.cv'));
        }

        if ($id) {
            $wareHouseCv = $this->wareHouseCvRepository->find($id);
            if ($wareHouseCv) {
                $this->wareHouseCvRepository->update($id, [], $data);

                //send mail and notify
                $rec = $this->userRepository->find($params['user_id']);
                $rec->notify(new RecUpdateCv($rec, $wareHouseCv));
            }

            return $id;
        } else {
            $response = $this->wareHouseCvRepository->create($data);

            //send mail and notify
            $rec = $this->userRepository->find($params['user_id']);
            $rec->notify(new RecNewCv($rec));

            return $response->id;
        }
    }

    public function getDetailSubmitCvByEmployer($id, $employerId)
    {
        try {
            $checkSubmitCv = $this->submitCvRepository->getDetailSubmitCvByEmployer($id, $employerId);
            if ($checkSubmitCv) {
                return $checkSubmitCv;
            }
            return false;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function changeStatusSubmitCvByEmployer($data)
    {
        try {
            $response['error'] = '';
            $response['error_assessment'] = '';
            $id = isset($data['id']) ? $data['id'] : 0;
            $employerId = isset($data['employer_id']) ? $data['employer_id'] : 0;
            $status = isset($data['status']) ? $data['status'] : 0;
            $assessment = isset($data['assessment']) ? $data['assessment'] : '';
            $checkSubmitCv = $this->submitCvRepository->getDetailSubmitCvByEmployer($id, $employerId);
            //dd($checkSubmitCv->job->bonus_type);
            //dd($checkSubmitCv->id);
            if ($checkSubmitCv) {
                //ko thay doi status thi ko thuc hien xu ly gi
                if ($checkSubmitCv->status == $status) return $response;
                //onboard, cancel, reject, fail  => KO thay đổi
                if (
                    $checkSubmitCv->status == config('constant.submit_cvs_status_value.fail-interview') ||
                    $checkSubmitCv->status == config('constant.submit_cvs_status_value.onboarded') ||
                    $checkSubmitCv->status == config('constant.submit_cvs_status_value.rejected') ||
                    $checkSubmitCv->status == config('constant.submit_cvs_status_value.cancel')
                ) {
                    $response['error'] = __('message.status_submit_cv_not_change', ['status' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                    return $response;
                }

                //pending-review chi duoc: reject, accepted
                $arrStatusPending = [
                    config('constant.submit_cvs_status_value.rejected'),
                    config('constant.submit_cvs_status_value.accepted')
                ];

                if ($checkSubmitCv->status == config('constant.submit_cvs_status_value.pending-review') && !in_array($status, $arrStatusPending)) {
                    $response['error'] = __('message.status_submit_cv_not_change_two', ['status1' => config('constant.submit_cvs_status.' . $status), 'status2' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                    return $response;
                }
                //end pending-review chi duoc: reject, accepted

                //accepted chi duoc: reject, pass, fail, cancel
                $arrStatusAccepted = [
                    config('constant.submit_cvs_status_value.rejected'),
                    config('constant.submit_cvs_status_value.pass-interview'),
                    config('constant.submit_cvs_status_value.fail-interview'),
                    config('constant.submit_cvs_status_value.cancel')
                ];

                if ($checkSubmitCv->status == config('constant.submit_cvs_status_value.accepted') && !in_array($status, $arrStatusAccepted)) {
                    $response['error'] = __('message.status_submit_cv_not_change_two', ['status1' => config('constant.submit_cvs_status.' . $status), 'status2' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                    return $response;
                }
                //end accepted chi duoc: reject, pass, fail, cancel
                //JOB bonus_type CV
                if ($checkSubmitCv->job->bonus_type == 'cv') {
                    //pass-interview chi duoc reject
                    if ($checkSubmitCv->status == config('constant.submit_cvs_status_value.pass-interview') && $status != config('constant.submit_cvs_status_value.rejected')) {
                        $response['error'] = __('message.status_submit_cv_not_change_two', ['status1' => config('constant.submit_cvs_status.' . $status), 'status2' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                        return $response;
                    }
                }

                //JOB bonus_type onboard
                if ($checkSubmitCv->job->bonus_type == 'onboard') {
                    //pass-interview chi duoc reject, offering
                    $arrStatusPass = [
                        config('constant.submit_cvs_status_value.rejected'),
                        config('constant.submit_cvs_status_value.offering')
                    ];

                    if ($checkSubmitCv->status == config('constant.submit_cvs_status_value.pass-interview') && !in_array($status, $arrStatusPass)) {
                        $response['error'] = __('message.status_submit_cv_not_change_two', ['status1' => config('constant.submit_cvs_status.' . $status), 'status2' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                        return $response;
                    }

                    //offering chi duoc reject, onboard
                    $arrStatusOffering = [
                        config('constant.submit_cvs_status_value.rejected'),
                        config('constant.submit_cvs_status_value.onboarded')
                    ];

                    if ($checkSubmitCv->status == config('constant.submit_cvs_status_value.offering') && !in_array($status, $arrStatusOffering)) {
                        $response['error'] = __('message.status_submit_cv_not_change_two', ['status1' => config('constant.submit_cvs_status.' . $status), 'status2' => config('constant.submit_cvs_status.' . $checkSubmitCv->status)]);
                        return $response;
                    }
                }

                if ($status == config('constant.submit_cvs_status_value.rejected') || $status == config('constant.submit_cvs_status_value.fail-interview')) {
                    if ($assessment == '') {
                        $response['error_assessment'] = __('message.required');
                        return $response;
                    }
                }
            } else {
                $response['error'] = __('message.data_not_exit');
                return $response;
            }

            $save['status'] = $status;
            $save['date_change_status'] = Carbon::now()->format('Y-m-d H:i:s');
            $save['assessment'] = $assessment;
            $saveSubmitCv = $this->submitCvRepository->update($id, [], $save);
            if ($saveSubmitCv) {
                //TODO
                //send email
                $user = $this->userRepository->find($checkSubmitCv->user_id);
                $user->notify(new UpdateStatusCv($checkSubmitCv, $user));
                return $response;
            }
        } catch (\Exception $e) {
            $response['error'] = $e->getMessage();
            return $response;
        }
    }

    public function getExpiredDateByStatus($date, $status)
    {
        // tuy trang thai ma tinh so ngay het han doi trang thai
        switch ($status) {
            case 1:
                $add_date = 16;
                break;
            default:
                $add_date = config('settings.global.submit_cv_pending_date', 3) + 1;
                break;
        }
        $start_date = Carbon::createFromFormat('Y-m-d H:i:s', $date);
        return $start_date->addWeekdays($add_date)->format('Y-m-d H:i:s');
    }

    public function updatePublicCv($id, $publicCv)
    {
        try {
            $data = [
                'cv_public' => $publicCv
            ];
            $warehouseCv = $this->wareHouseCvRepository->find($id);
            if (!$warehouseCv) {
                throw new \Exception('Id not found');
            }

            $this->wareHouseCvRepository->update($id, [], $data);

            return $warehouseCv;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function updatePrivateCv($id, $privateCv)
    {
        try {
            $data = [
                'cv_private' => $privateCv
            ];
            $warehouseCv = $this->wareHouseCvRepository->find($id);
            if (!$warehouseCv) {
                throw new \Exception('Id not found');
            }

            $this->wareHouseCvRepository->update($id, [], $data);

            return $warehouseCv;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function updatePrivateCvForSubmitCv($id, $privateCv)
    {
        try {
            $data = [
                'cv_private' => $privateCv
            ];
            $submitCv = $this->submitCvRepository->find($id);
            if (!$submitCv) {
                throw new \Exception('Id not found');
            }
            $submitCvMeta = $submitCv->submitCvMeta;

            $this->submitCvMetaRepository->update($submitCvMeta->id, [], $data);

            return $submitCv;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function updateStatusCvById($id, $userId)
    {
        try {
            $data = [
                'status' => config('constant.submit_cvs_status_value.pending-review'),
                'date_change_status' => Carbon::now()->format('Y-m-d H:i:s')
            ];
            $submitCv = $this->submitCvRepository->find($id);
            if (!$submitCv) {
                throw new \Exception('Id not found');
            }

            $this->submitCvRepository->update($id, [], $data);

            return $submitCv;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function checkDuplicateCvForJob($email, $phone, $jobId)
    {
        $wareHouseCv = $this->wareHouseCvRepository->getByEmailPhone($email, $phone);
        if ($wareHouseCv->count() > 0) {
            $groupId = $wareHouseCv->pluck('id')->toArray();
            return $this->countGroupCvInJob($groupId, $jobId) > 0;
        }
        return false;
    }

    public function countGroupCvInJob($groupCvId, $jobId)
    {
        return $this->submitCvRepository->countGroupCvInJob($groupCvId, $jobId);
    }

    public function checkDuplicateCvForCompany($email, $phone, $jobId)
    {
        $job = $this->jobRepository->find($jobId);
        $wareHouseCv = $this->wareHouseCvRepository->getByEmailPhone($email, $phone);
        if ($wareHouseCv->count() > 0) {
            $groupId = $wareHouseCv->pluck('id')->toArray();
            return $this->countGroupCvInJob($groupId, $jobId) > 0;
            // return $this->countGroupCvInCompany($groupId, $job->company_id) > 0;
        }
        return false;
    }

    public function countGroupCvInCompany(array $groupCvId, $companyId)
    {
        return $this->submitCvRepository->countGroupCvInCompany($groupCvId, $companyId);
    }

    public function checkDuplicateCvForJobById($cvId, $jobId)
    {
        return $this->countGroupCvInJob([$cvId], $jobId) > 0;
    }

    public function checkDuplicateCvCompanyJobById($cvId, $jobId)
    {
        return $this->countGroupCvInJob([$cvId], $jobId) > 0;
        // $job = $this->jobRepository->find($jobId);
        // return $this->countGroupCvInCompany([$cvId], $job->company_id) > 0;
    }

    public function findCvWithEmail($email)
    {
        $listIdWareHouseCv = $this->wareHouseCvRepository->getWithEmail($email);
        if (count($listIdWareHouseCv)) {
            $submitCvOnboard = $this->submitCvRepository->getWithIdWareHouse($listIdWareHouseCv);
            if ($submitCvOnboard) {
                return $submitCvOnboard;
            }
        }
        return false;
    }

    public function findCv($cv)
    {
        $listIdWareHouseCv = $this->wareHouseCvRepository->find($cv);
        if ($listIdWareHouseCv) {
            $submitCvOnboard = $this->submitCvRepository->getWithIdWareHouse([$listIdWareHouseCv->id]);
            if ($submitCvOnboard) {
                return $submitCvOnboard;
            }
        }
        return false;
    }

    public function getMarket($params)
    {
        try {
            $result = $this->wareHouseCvSellingRepository->getCvSellingInMarket($params);
            return $result;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getMaxSalary()
    {
        $maxSalary = $this->wareHouseCvSellingRepository->getMaxSalary();
        return $maxSalary / 1000000;
    }

    public function getDetailSubmitCvByRec($id)
    {
        try {
            $user = auth('client')->user();
            $data = $this->submitCvRepository->findByRec($id, $user->id);
            if (!$data) return false;
            $data->load(['warehouseCv', 'employer', 'job', 'submitCvMeta']);
            return $data;
            // $data = $data->toArray();
            // $data['company'] = [];
            // if ($data['ware_house_cv_selling']['exclude_company']) {
            //     foreach (explode(',', $data['ware_house_cv_selling']['exclude_company']) as $companyId) {
            //         $company = $this->companyRepository->find($companyId);
            //         array_push($data['company'], $company->name);
            //     }
            // }

            // $data['level_name'] = '';
            // if (isset($data['ware_house_cv_selling']['ware_house_cv']['career'])) {
            //     $career = isset($data['ware_house_cv_selling']['ware_house_cv']['career']) ? (string) $data['ware_house_cv_selling']['ware_house_cv']['career'] : '';
            //     $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
            //     if ($isIT) {
            //         $level = $this->levelBySkillMainRepository->find($data['ware_house_cv_selling']['level']);
            //     } else {
            //         $level =  $this->levelByJobTopRepository->find($data['ware_house_cv_selling']['level']);
            //     }

            //     $data['level_name'] = $level->name_en;
            // }

            // $data['skill_name'] = '';
            // if ($data['ware_house_cv_selling']['skill']) {
            //     $career = isset($data['ware_house_cv_selling']['ware_house_cv']['career']) ? (string) $data['ware_house_cv_selling']['ware_house_cv']['career'] : '';
            //     $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
            //     if ($isIT) {
            //         $skill = $this->skillMainRepository->find($data['ware_house_cv_selling']['skill']);
            //     } else {
            //         $skill =  $this->jobTopRepository->find($data['ware_house_cv_selling']['skill']);
            //     }

            //     $data['skill_name'] = $skill->name_en;
            // }
            // return $data;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function recruiterRejectCv($id, $assessment = '')
    {
        try {
            $user = auth('client')->user();
            $submitCv = $this->submitCvRepository->find($id);
            if ($submitCv->company_id != $user->company_id) {
                return false;
            }
            if (!$submitCv) return false;
            $submitCv->status = config('constant.status_recruitment_revert.RecruiterRejectCV');
            if ($assessment) {
                $submitCv->employer_feedback = $assessment;
                // if (empty($submitCv->assessment)) {
                //     $submitCv->assessment = $assessment;
                // } else {
                //     $submitCv->assessment = 'Lý do từ chối CV: ' . $assessment . " | \n" . $submitCv->assessment;
                // }
            }
            $submitCv->save();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
