<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailRejectOnboard extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $wareHouseCvSellingBuyBook;
    protected $wareHouseCvSellingBuy;

    public function __construct($employer,$wareHouseCvSellingBuyBook,$wareHouseCvSellingBuy)
    {
        $this->employer = $employer;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position = '';
        $employerName = $this->employer->name;
        $timeInterview = $this->wareHouseCvSellingBuyBook->date_hour;
        if (!empty($this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $link = route('employer-cv-bought',['discuss' => $this->wareHouseCvSellingBuy->id]);
        $linkMarket = route('market-cv');

        return (new MailMessage)
            ->view('email.emailRejectOnboard', [
                'employerName' => $employerName,
                'candidateName' => $candidateName,
                'position' => $position,
                'timeInterview' => $timeInterview,
                'link' => $link,
                'linkMarket' => $linkMarket,
            ])
            ->subject('[RECLAND] Thông báo từ chối offer của ứng viên '.$candidateName.' vị trí '.$position);
    }


}
