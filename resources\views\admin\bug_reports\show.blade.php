@extends('admin.layouts.app')

@section('content')
<div class="page-header">
    <div class="page-leftheader">
        <h4 class="page-title">Chi tiết báo cáo lỗi #{{ $bugReport->id }}</h4>
        <ol class="breadcrumb pl-0">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{{ route('bug-reports.index') }}">Báo cáo lỗi</a></li>
            <li class="breadcrumb-item active" aria-current="page">Chi tiết #{{ $bugReport->id }}</li>
        </ol>
    </div>
    <div class="page-rightheader">
        <a href="{{ route('bug-reports.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle"></i> Thông tin báo cáo
                </h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>ID:</strong></div>
                    <div class="col-sm-9">#{{ $bugReport->id }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Người báo cáo:</strong></div>
                    <div class="col-sm-9">
                        @if($bugReport->user)
                            {{ $bugReport->user->name }} ({{ $bugReport->user->email }})
                        @else
                            <span class="text-muted">Người dùng không tồn tại</span>
                        @endif
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>URL trang lỗi:</strong></div>
                    <div class="col-sm-9">
                        <a href="{{ $bugReport->url }}" target="_blank" class="text-break">
                            {{ $bugReport->url }}
                            <i class="fas fa-external-link-alt ml-1"></i>
                        </a>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Thời gian báo cáo:</strong></div>
                    <div class="col-sm-9">{{ $bugReport->created_at->format('d/m/Y H:i:s') }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Trạng thái:</strong></div>
                    <div class="col-sm-9">
                        <span class="badge {{ $bugReport->status === 'pending' ? 'badge-warning' : 'badge-success' }}">
                            {{ $bugReport->status === 'pending' ? 'Chờ xử lý' : 'Đã xử lý' }}
                        </span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Mô tả lỗi:</strong></div>
                    <div class="col-sm-9">
                        <div class="border p-3 bg-light rounded">
                            {!! nl2br(e($bugReport->description)) !!}
                        </div>
                    </div>
                </div>
                
                @if($bugReport->image_path)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Ảnh minh họa:</strong></div>
                    <div class="col-sm-9">
                        <div class="image-container">
                            <img src="{{ $bugReport->image_url }}" 
                                 alt="Bug Report Image" 
                                 class="img-thumbnail" 
                                 style="max-width: 100%; max-height: 400px; cursor: pointer;"
                                 onclick="openImageModal(this.src)">
                            <br>
                            <small class="text-muted">Click vào ảnh để xem kích thước đầy đủ</small>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs"></i> Thao tác
                </h3>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label for="status">Cập nhật trạng thái:</label>
                    <select id="status" class="form-control">
                        <option value="pending" {{ $bugReport->status === 'pending' ? 'selected' : '' }}>
                            Chờ xử lý
                        </option>
                        <option value="resolved" {{ $bugReport->status === 'resolved' ? 'selected' : '' }}>
                            Đã xử lý
                        </option>
                    </select>
                </div>
                
                <button type="button" class="btn btn-primary btn-block" onclick="updateStatus()">
                    <i class="fas fa-save"></i> Cập nhật trạng thái
                </button>
                
                <hr>
                
                <div class="text-muted">
                    <small>
                        <strong>Lần cập nhật cuối:</strong><br>
                        {{ $bugReport->updated_at->format('d/m/Y H:i:s') }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ảnh minh họa</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Bug Report Image" class="img-fluid">
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function updateStatus() {
    const status = $('#status').val();
    const bugReportId = {{ $bugReport->id }};
    
    $.ajax({
        url: `/admin/bug-reports/${bugReportId}/status`,
        type: 'PATCH',
        data: {
            status: status,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            if (response.success) {
                toastr.success(response.message);
                
                // Update status badge
                const badge = status === 'pending' ? 
                    '<span class="badge badge-warning">Chờ xử lý</span>' :
                    '<span class="badge badge-success">Đã xử lý</span>';
                
                $('.row:has(strong:contains("Trạng thái:")) .col-sm-9').html(badge);
                
                // Update last updated time
                $('.text-muted small').html(
                    '<strong>Lần cập nhật cuối:</strong><br>' + 
                    moment().format('DD/MM/YYYY HH:mm:ss')
                );
            }
        },
        error: function(xhr) {
            toastr.error('Có lỗi xảy ra khi cập nhật trạng thái.');
        }
    });
}

function openImageModal(src) {
    $('#modalImage').attr('src', src);
    $('#imageModal').modal('show');
}
</script>
@endsection

@section('css_custom')
<style>
.text-break {
    word-break: break-all;
}

.image-container img {
    transition: transform 0.2s;
}

.image-container img:hover {
    transform: scale(1.05);
}

#imageModal .modal-body {
    padding: 0;
}

#imageModal img {
    max-width: 100%;
    height: auto;
}
</style>
@endsection
