<?php

namespace App\Notifications;

use App\Models\BugReport;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BugReportNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $bugReport;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(BugReport $bugReport)
    {
        $this->bugReport = $bugReport;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $userName = $this->bugReport->user ? $this->bugReport->user->name : 'Người dùng không xác đị<PERSON>';
        $userEmail = $this->bugReport->user ? $this->bugReport->user->email : '<PERSON><PERSON> không xác định';

        return (new MailMessage)
            ->subject('Báo cáo lỗi mới từ ' . $userName)
            ->greeting('Xin chào Admin!')
            ->line('Có một báo cáo lỗi mới từ người dùng: ' . $userName . ' (' . $userEmail . ')')
            ->line('**URL trang lỗi:** ' . $this->bugReport->url)
            ->line('**Mô tả lỗi:** ' . $this->bugReport->description)
            ->action('Xem chi tiết báo cáo', url('/admin/bug-reports/' . $this->bugReport->id))
            ->line('Vui lòng kiểm tra và xử lý báo cáo này sớm nhất có thể.')
            ->salutation('Trân trọng, Hệ thống RecLand');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'bug_report_id' => $this->bugReport->id,
            'user_name' => $this->bugReport->user ? $this->bugReport->user->name : 'N/A',
            'url' => $this->bugReport->url,
            'description' => $this->bugReport->short_description,
        ];
    }
}
