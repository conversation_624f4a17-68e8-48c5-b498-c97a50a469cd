<?php

namespace App\Http\Middleware;

use App\Services\Admin\PermissionService;
use App\Services\Admin\Toast\Facades\Toast;
use Closure;
use Illuminate\Support\Facades\Auth;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = Auth::guard('admin')->user();
        if ($user->type != config('constant.role.admin')){
            return redirect()->route('login');
        }
        if (PermissionService::checkPermission($request->route()->getName())){
            return $next($request);
        }else{
            Toast::error('Bạn chưa có quyền để thực hiện chức năng này !');
            abort(401, 'Access denied');
        }

    }
}
