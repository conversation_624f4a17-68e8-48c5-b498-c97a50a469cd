<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ChagePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $this->session()->flash('action', 'changePassword');
        return [
            'password'         => 'required|min:6|max:20',
            'password_confirm' => 'required|same:password'
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'password_confirm.same' => __('message.same'),
            'min' => __('message.min'),
            'max' => __('message.max'),
        ];
    }


}
