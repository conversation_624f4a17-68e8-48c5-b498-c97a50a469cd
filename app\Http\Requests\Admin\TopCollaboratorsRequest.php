<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TopCollaboratorsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'rec_name' => 'required|string',
            'amount' => 'required|numeric',
            'increase_percent' => 'nullable|numeric',
            'is_hot' => ['required', 'numeric', 'in:0,1'],
        ];
    }
}
