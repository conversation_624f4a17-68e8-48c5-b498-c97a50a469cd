<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SendQaToRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $cvSelling;
    protected $data;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $cvSelling, $data)
    {
        $this->user = $user;
        $this->cvSelling = $cvSelling;
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->user->name;
        $candidateName = $this->cvSelling->wareHouseCv->candidate_name;
        $position = $this->cvSelling->wareHouseCv->candidate_job_title;
        $comment = $this->data['comment'];
        if ($this->user->type == 'rec') {
            $link = route('rec-cv-selling', ['qa' => $this->cvSelling->id]);
        } else {
            $link = route('market-cv', ['qa' => $this->cvSelling->id]);
        }
        return (new MailMessage)
            ->view('email.send_qa_to_rec_employer', [
                'name' => $name,
                'candidateName' => $candidateName,
                'position' => $position,
                'comment' => $comment,
                'link' => $link,
            ])
            ->subject('[Recland] Bạn nhận được 1 hỏi đáp mới ứng viên ' . $candidateName);
    }

}
