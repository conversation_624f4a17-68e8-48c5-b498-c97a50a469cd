<?php

namespace App\Rules\Admin;

use App\Repositories\UserRepository;
use Illuminate\Contracts\Validation\Rule;
use Hash;
use Illuminate\Support\Facades\Auth;

class CheckCurrentAdminOldPassword implements Rule
{

    public function __construct()
    {

    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

        $user = Auth::guard('admin')->user();
        if (!Hash::check($value, $user->password)){
            return false;
        }
        return true;

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('message.check_old_password');
    }
}
