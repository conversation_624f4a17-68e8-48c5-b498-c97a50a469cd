<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\Api\AboutUsAlbumCollection;
use App\Http\Resources\Frontend\Api\AboutUsAlbumResource;
use App\Http\Resources\Frontend\Api\SubmitCvCollection;
use App\Services\Frontend\AboutUsAlbumService;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Services\Frontend\UserService;
use Illuminate\Http\Request;

class RecController extends Controller
{
    protected $userService;
    protected $wareHouseSubmitCvService;

    public function __construct(UserService $userService, WareHouseSubmitCvService $wareHouseSubmitCvService)
    {
        $this->userService = $userService;
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
    }

    public function applyListInTeam()
    {
        $user = auth('client')->user();
        $rec_id = request()->get('rec_id');
        $member = $this->userService->teams(['perPage' => 1000]);
        $user_ids = array_column($member, 'id');
        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 10);
        $list_user = [];
        foreach ($member as $item) {
            $list_user[$item->id] = ['name' => $item->name . ' (' . $item->referral_define . ')', 'id' => $item->id];
        }

        $params = [
            'user_ids' => $user_ids,
            'perPage' => $perPage,
            'page' => $page,
            'job_id' => request()->get('job_id'),
        ];
        if ($rec_id) {
            $params['user_id'] = $rec_id;
        }
        $params['with_meta'] = true;
        $arrSubmitCv = $this->wareHouseSubmitCvService->getListSubmitCvSearch($params);
        $arrJob = $this->wareHouseSubmitCvService->getListJobByTeam($params);
        return [
            'submit_cvs' => new SubmitCvCollection($arrSubmitCv),
            'jobs' => $arrJob,
            'list_user' => $list_user
        ];
    }

    public function getRecInfo()
    {
        $user = auth('client')->user();
        $rec_id = request()->get('rec_id');
        $rec = $this->userService->find($rec_id);
        return response()->json($rec);
    }
}
