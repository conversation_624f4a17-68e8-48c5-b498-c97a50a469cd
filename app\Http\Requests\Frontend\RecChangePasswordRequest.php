<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use App\Rules\Frontend\CheckCurrentPasswordRule;
use Illuminate\Foundation\Http\FormRequest;

class RecChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $this->session()->flash('action', 'change-password');
        return [
            'current_password' => ['required', new CheckCurrentPasswordRule()],
            'password'         => 'required|min:8|max:20',
            'confirm_password' => 'required|same:password',
        ];
    }

    public function messages()
    {
        return [
            'required'              => __('frontend/validation.required'),
            'min'                   => __('frontend/validation.min', ['min' => 8]),
            'max'                   => __('frontend/validation.max', ['max' => 20]),
            'confirm_password.same' => __('message.same'),
        ];
    }
}
