<?php

namespace App\Http\Requests\Admin;

use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
            {
                return [
                    'name'  => 'required',
                    'email' => ['required','email', new CheckEmailRule(config('constant.role.admin'))],
                    'role'  => 'required',
                ];
            }
            case 'PUT':
            {
                return [
                    'email' => ['required','email', new CheckEmailRule(config('constant.role.admin'), $this->get('id'))],
                    'name'  => 'required',
                    'role'  => 'required',
                ];
            }
            default:
                break;
        }
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'required'      => __('message.required'),
            'role.required' => __('message.select'),
            'email'         => __('message.email')
        ];
    }
}
