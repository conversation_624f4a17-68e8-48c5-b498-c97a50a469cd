# Recland Application Logic

This document outlines the core logic of the Recland application, a recruitment platform that connects employers, collaborators (recruiters), and candidates.

## User Roles

The application has three main user roles:

*   **Collaborators (CTV):** Recruiters who find and submit candidates for jobs.
*   **Employers (NTD):** Companies that post jobs and hire candidates.
*   **Administrators:** Users who manage the platform.

## Core Features

### Job Management

*   Employers can create, edit, and manage job postings.
*   Jobs have various attributes, including title, description, salary, location, and expiration date.
*   The application includes logic for calculating bonuses for collaborators and determining job rankings.

### Candidate Management

*   Collaborators can submit candidates for jobs.
*   The application tracks the status of each candidate (e.g., submitted, interviewing, hired).
*   Employers can view and manage candidates who have been submitted for their jobs.

### User Management

*   The application provides separate registration and login functionality for collaborators and employers.
*   Users can manage their profiles, including contact information and notification settings.
*   The application includes a system for managing user roles and permissions.

### API

*   The application exposes a set of API endpoints for interacting with external services.
*   Some API endpoints require API key authentication.

## Technical Details

*   The application is built with the Laravel framework.
*   It uses a MySQL database to store data.
*   The application uses several third-party packages, including Backpack for administration, Metable for managing metadata, and Auditable for tracking changes to data.