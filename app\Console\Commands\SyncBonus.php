<?php

namespace App\Console\Commands;

use App\Repositories\UserRepository;
use App\Repositories\WalletRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;
use DB;

/**
 * Tính tổng tiền của CTV từ trước đến giờ
 */
class SyncBonus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:syncbonus';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tính tổng tiền của CTV từ trước đến giờ';

    public function __construct()
    {
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->error('Command not run');
        return;
        \Log::info('Command start');
        $userRepo = resolve(UserRepository::class);
        $walletRepo = resolve(WalletRepository::class);
        $numRow = 500;
        $i = 0;
        do {
            $limit = $numRow;
            $offset = $i * $numRow;

            $listUser = $userRepo->getUserRecWithLimitOffset($limit, $offset);
            if ($listUser) {
                foreach ($listUser as $value) {
                    $wallet = $walletRepo->findByUser($value->id);
                    $result = DB::table('bonus')
                        ->selectRaw('SUM(money) as money, SUM(money_kpi) as money_kpi, SUM(price) as price')
                        ->where('user_id', $value->id)
                        ->first();

                    $price = (int)$result->money + (int)$result->money_kpi + (int)$result->price;
                    if ($price > 0) {
                        $wallet->price = $price;
                        $wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $value->id]);
                        $wallet->save();
                    }
                }
            }

            $i++;
        } while (count($listUser) == $numRow);
        \Log::info('Command end');
    }
}
