@import "config";

#page {
    font-family: $font-family;
    overflow: hidden;
    position: relative;
}

img {
    max-width: 100%;
}

.container {
    width: 100%;
    max-width: 1376px;
    margin: 0 auto;
}

.btn-primary {
    background-color: #17677b;
    border: 1px solid #17677b;
    border-radius: 10px;
}

.btn-primary:hover {
    background-color: #079DB2;
    border: 1px solid #079DB2;
    border-radius: 10px;
}

.text-primary {
    color: #17677b;
}

.text-green {
    color: #17677b;
}

.style-group-right {
    display: flex;
    justify-content: right;
    align-items: center;
}

.style-group-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.h-100-percen {
    height: 100%;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.p-12 {
    padding: 12px;
}

.p-14 {
    padding: 14px;
}

.p-16 {
    padding: 16px;
}

.pl-24 {
    padding-left: 24px;
}

.pr-24 {
    padding-right: 24px;
}

.pt-40 {
    padding-top: 40px;
}

.mb-12 {
    margin-bottom: 12px;
}

.mb-44 {
    margin-bottom: 44px;
}

.mb-48 {
    margin-bottom: 48px !important;
}

.mb-16 {
    margin-bottom: 16px;
}

.mb-8 {
    margin-bottom: 8px;
}

.mb-44 {
    margin-bottom: 44px;
}

.full-width {
    width: 100%;
}

.full-height {
    height: 100%;
}

.mb-24 {
    margin-bottom: 24px;
}

.mb-60 {
    margin-bottom: 60px;
}

.mb-6 {
    margin-bottom: 6px;
}

.mr-60 {
    margin-right: 60px;
}

.mb-80 {
    margin-bottom: 80px;
}



/* width */

::-webkit-scrollbar {
    width: 0;
}


/* Track */

::-webkit-scrollbar-track {
    background: #fff;
}


/* Handle */

::-webkit-scrollbar-thumb {
    background: #808285;
}


/* Handle on hover */

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.loading {
    border: 2px solid #f3f3f3;
    border-radius: 50%;
    border-top: 2px solid $color-brand;
    width: 18px;
    height: 18px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.tooltip-inner {
    white-space: nowrap;
    max-width: none;
}

#header {
    background: $bg-header;
    z-index: 99999;

    &.detail-company-header {
        position: fixed;
        top: 0;
        width: 100%;
    }

    .height-header {
        height: $height-header;
    }

    .wapper-logo {
        display: flex;
        justify-items: center;
        align-items: center;
    }

    .header {
        .logo {
            display: flex;
            justify-items: center;
        }
    }

    .main-menu {
        display: flex;
        align-items: center;
        position: relative;
        margin-left: -85px;

        ul {
            li {
                display: inline-block;
                margin-right: 32px;

                a {
                    color: $color-palette4;
                    font-weight: 600;
                    font-size: 18px;
                    line-height: 26px;
                }
            }

            li.active-menu {
                a {
                    color: $color-brand;
                }
            }

            li:last-child {
                margin-right: 0;
            }
        }
    }

    .right-header {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .item-right-header {
            min-width: 183px;
        }

        .item-right-header:last-child {
            margin-right: 0;
            text-align: center;
        }

        .item-right-header.wapper-flag {
            margin-right: 34px;
            min-width: auto;
        }

        .button-login-header {
            border-left: 1px solid $color-secondary1;
            border-right: 1px solid $color-secondary1;
            height: 32px;
            color: $color-palette4;
            font-size: 16px;
            font-weight: 400;
            padding-right: 34px;
            padding-left: 34px;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
                margin-right: 8px;
            }
        }

        .button-header {
            font-style: normal;
            font-weight: 800;
            font-size: 16px;
            line-height: 24px;
            color: $color-palette4;
        }
    }

    .button-menu-mobile {
        position: absolute;
        left: 15px;
        display: none;
    }

    .button-notification-mobile {
        position: absolute;
        right: 15px;
        display: none;

        .wapper-bell span {
            width: 12px;
            height: 12px;
            position: absolute;
            border-radius: 50%;
            background: $color-palette4;
            font-weight: 700;
            font-size: 8px;
            line-height: 100%;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            color: $color-primary1v2;
            right: -1px;
            top: -3px;
        }


        .wapper-list {
            display: none;
            background: $color-st4;
            padding-top: 4px;
            position: absolute;
            right: -15px;
            z-index: 99999;
            box-shadow: 0px 8px 25px rgba(52, 60, 107, 60%);
            border-radius: 4px;
            top: 42px;

            .header {
                color: $color-palette1;
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                padding: 16px 13px;
                background: $color-palette4;
                margin-bottom: 12px;
            }

            .list {
                width: 440px;
                max-width: 100vw;
                background: $color-white;
            }

            .image {
                height: 32px;
                width: 32px;
                padding: 5px;
                border-radius: 50%;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid $color-st1;
            }

            .item {
                padding: 16px;
                display: flex;
                gap: 16px;
                flex-wrap: wrap;
                margin-bottom: 12px;
                background: $color-palette4;
                position: relative;
            }

            .item:hover {
                background: $color-st19;
                box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
            }

            .not-seen:before {
                content: '';
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: $color-primary4;
                position: absolute;
                left: 8px;
                top: 8px;
            }

            .wapper-image {
                padding-top: 5px;
            }

            .group {
                padding-right: 3px;
                height: 510px;
                overflow-y: hidden;
            }

            .content {
                width: calc(100% - 48px);
                font-weight: 400;

                .title {
                    font-size: 14px;
                    line-height: 20px;
                    color: $color-palette1;
                    margin-bottom: 11px;

                    b {
                        font-weight: 600;
                    }
                }

                .time {
                    font-style: normal;
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 22px;
                    color: $color-secondary1;
                }
            }
        }

        .wapper-list:before {
            background: $color-st4;
            content: "";
            width: 10px;
            height: 10px;
            position: absolute;
            top: -4px;
            transform: rotate(45deg);
            right: 22px;
        }

        .wapper-list:hover {
            .group {
                padding-right: 0;
                overflow-y: scroll;
            }
        }


    }

    .bell {
        cursor: pointer;
        width: 24px;
    }

    .notification {
        position: relative;

        img {
            margin-right: 0 !important;
        }

        .number {
            position: absolute;
            top: -5px;
            right: 0px;
            width: 12px;
            height: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: $color-palette4;
            font-size: 8px;
            color: $color-primary1v2;
            border-radius: 50%;
        }

        .wapper-list {
            display: none;
            background: $color-st4;
            padding-top: 4px;
            position: absolute;
            right: -15px;
            z-index: 99999;
            box-shadow: 0px 8px 25px rgba(52, 60, 107, 60%);
            border-radius: 4px;
            top: 35px;

            .header {
                color: $color-palette1;
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                padding: 16px 13px;
                background: $color-palette4;
                margin-bottom: 12px;
            }

            .list {
                width: 440px;
                background: $color-white;
            }

            .image {
                height: 32px;
                width: 32px;
                padding: 5px;
                border-radius: 50%;
                overflow: hidden;
                display: flex;
                justify-content: center;
                align-items: center;
                border: 1px solid $color-st1;
            }

            .item {
                padding: 16px;
                display: flex;
                gap: 16px;
                flex-wrap: wrap;
                margin-bottom: 12px;
                background: $color-palette4;
                position: relative;
            }

            .item:hover {
                background: $color-st19;
                box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
            }

            .not-seen:before {
                content: '';
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: $color-primary4;
                position: absolute;
                left: 8px;
                top: 8px;
            }

            .wapper-image {
                padding-top: 5px;
            }

            .group {
                padding-right: 3px;
                height: 510px;
                overflow-y: hidden;
            }

            .content {
                width: calc(100% - 48px);
                font-weight: 400;

                .title {
                    font-size: 14px;
                    line-height: 20px;
                    color: $color-palette1;
                    margin-bottom: 11px;

                    b {
                        font-weight: 600;
                    }
                }

                .time {
                    font-style: normal;
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 22px;
                    color: $color-secondary1;
                }
            }
        }

        .wapper-list:before {
            background: $color-st4;
            content: "";
            width: 10px;
            height: 10px;
            position: absolute;
            top: -4px;
            transform: rotate(45deg);
            right: 22px;
        }

        .wapper-list:hover {
            .group {
                padding-right: 0;
                overflow-y: scroll;
            }
        }

    }

    .drop-user-info {
        text-align: center;
        font-weight: 400;
        font-size: 18px;
        color: $color-palette4;
        padding-right: 34px;
        background: url("../images/dropdown-info-user.svg") no-repeat;
        background-position: right center;
        white-space: nowrap;
    }

    .wapper-drop-down-user-info {
        position: relative;
    }

    .main-drop-down-user-info {
        position: absolute;
        inset: 7px auto auto 0 !important;
        width: 250px;
        background: $color-palette4;
        box-shadow: 0px 2px 8px rgba(117, 131, 142, 0.04), 0px 16px 24px rgba(52, 60, 68, 0.12);
        border-radius: 8px;
        border: none;
        padding: 2px 0;
        right: 0 !important;
        left: auto !important;

        li {
            border-bottom: 1px solid $color-palette3;

            a {
                display: flex;
                align-items: center;
                padding: 20px 24px;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                color: $color-palette1;

                img {
                    margin-right: 12px;
                }
            }
        }

        li:last-child {
            border-bottom: 0;
        }

    }

    .wapper-flag {
        width: 42px;
    }

    .main-col-last-header {
        position: relative;
        margin-left: -30px;
    }
}

#header.header-logged {
    .item-right-header {
        min-width: 20px;
        margin-right: 34px;
    }

    .item-right-header:last-child {
        margin-right: 0;
    }
}


#mobile-menu {
    position: absolute;
    width: 304px;
    max-width: 100%;
    top: 0;
    z-index: 999;
    transition: 0.2s;
    left: -304px;

    .button-close {
        text-align: right;
        height: 44px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 16px;
        background: $color-brand1;
    }

    .main-mobile-menu {
        background: $color-st21;
        padding-top: 24px;
        padding-bottom: 30px;
    }

    .ava-mobile-menu {
        width: 40px;
        height: 40px;
        overflow: hidden;
        border-radius: 50%;
        margin-bottom: 12px;
    }

    .user-info {
        padding: 12px 16px;
        background: $color-white;
        margin-bottom: 12px;

        .name {
            font-style: normal;
            font-weight: 500;
            font-size: 20px;
            line-height: 24px;
            color: $color-brand1;
            margin-bottom: 6px;
        }

        .position {
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: $color-palette1;
        }
    }

    .item-mobile-menu {
        background: $color-white;
        margin-bottom: 12px;

        img {
            margin-right: 12px;
        }

        ul {
            li {
                a {
                    display: block;
                    padding: 12px 16px;
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 20px;
                    color: $color-secondary1;
                }
            }

            li.active-menu {
                a {
                    color: $color-brand;
                }
            }
        }
    }

    .item-mobile-menu:last-child {
        margin-bottom: 0;
    }

}

#mobile-menu.active {
    left: 0;
}

.group-field label {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: $color-palette2;
    margin-bottom: 8px;
    display: block;
}

.field-item-st1 {
    margin-bottom: 24px;
}

.field-st1 {
    width: 100%;
    border: none;
    padding: 6px 12px;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    border-bottom: 1px solid $color-palette3;
    background: none;
    background-repeat: no-repeat;
    background-position: center left 6px;
}

.field-st1:focus {
    outline: none;
}

.field-st1::-webkit-input-placeholder {
    /* Edge */
    color: $color-palette4v2;
}

.field-st1:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: $color-palette4v2;
}

.field-st1::placeholder {
    color: $color-palette4v2;
}

.field-icon {
    padding-left: 42px;
}

.field-icon-user {
    background-image: url("../images/field-icon-user.svg");
}

.field-icon-lock {
    background-image: url("../images/field-icon-lock.svg");
}

.field-icon-email {
    background-image: url("../images/field-icon-email.svg");
}

.group-field-checkbox {
    display: block;
    position: relative;
    padding-left: 25px;
    margin-bottom: 12px;
    cursor: pointer;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: $color-palette1;
}

.field-checkbox-st1 {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.field-checkbox-st1-checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: $color-white;
    border: 1px solid $color-brand;
    border-radius: 3px;
}

.group-field-checkbox:hover .field-checkbox-st1~.field-checkbox-st1-checkmark {}

.group-field-checkbox .field-checkbox-st1:checked~.field-checkbox-st1-checkmark {
    background-color: $color-brand;
    background: url(../images/icon-checked.svg) no-repeat;
    background-position: center;
    background-size: 76%;
}

.field-checkbox-st1-checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.group-field-checkbox .field-checkbox-st1:checked:checked~.field-checkbox-st1-checkmark:after {
    display: block;
}

.group-field-checkbox .field-checkbox-st1-checkmark:after {
    left: 4px;
    top: 1px;
    width: 4px;
    height: 7px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

.button-st1 {
    font-size: 18px;
    line-height: 26px;
    padding: 10px 0;
    width: 224px;
    background: $bg-button-st1;
    box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
    border-radius: 30px;
    color: $color-palette4;
    border: none;
}

.button-st2 {
    display: inline-block;
    background: $color-palette4;
    box-sizing: border-box;
    box-shadow: 0px 4px 10px rgba(255, 255, 255, 0.4);
    border-radius: 30px;
    color: $color-primary1;
    padding: 10px 80px;
    font-weight: 400;
    font-size: 18px;
    line-height: 26px;
}

.field-password {
    position: relative;
}

.toggle-password {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 8px;
    right: 0;
}

.hide-password {
    background: url("../images/icon-eye.svg") no-repeat;
}

.show-password {
    background: url("../images/icon-eye-slash.svg") no-repeat;
}

.error-message,
label.error {
    color: $color-primary1v2 !important;
    font-weight: 400 !important;
    font-size: 10px !important;
    line-height: 12px !important;
    margin-top: 14px;
    padding-left: 20px;
    position: relative;
    background: url("../images/icon-x.svg") no-repeat;
    background-position: bottom -2px left;
}

.warning-message {
    color: #FBA943 !important;
    font-weight: 400 !important;
    font-size: 10px !important;
    line-height: 12px !important;
    margin-top: 14px;
    position: relative;
}

.btn-close {
    background: url("../images/icon-close.svg") no-repeat;
    background-position: center;
}

.bg-st1 {
    position: relative;
}

.bg-st1:before {
    background-image: url(../images/bg-form-login.svg);
    background-repeat: no-repeat;
    background-position: top 54px right 66px;
    opacity: 0.1;
    width: 100%;
    height: 100%;
    content: '';
    z-index: 9;
    position: absolute;
    top: 0;
    right: 0;
}

.main-bg-st {
    position: relative;
    z-index: 10;
}

.modal-title {
    display: flex;
    font-size: 15px;

    img {
        margin-right: 14px;
    }
}

.modal-header {
    border-bottom: none;
    padding: 24px;
}

.modal-body {
    padding: 25px;
}

.modal-content {
    box-shadow: 0px 6px 20px rgba(255, 255, 255, 0.4);
    background: $color-palette4;
    border: none;
}

.tooltip-custom {
    position: relative;

    p {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .modal-tooltip {
        position: absolute;
        width: fit-content;
        background: $color-st4;
        padding: 17px 18px 15px;
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
        z-index: 9999;
        min-width: 300px;
        border-radius: 4px;
        left: -10px;
        border: 2px solid $color-palette3;
        color: $color-palette1;
        display: none;
        margin-top: 6px;
    }

    .address-tooltip {
        position: relative;
        margin-left: 25px;
        margin-bottom: 12px;
    }

    .address-tooltip:last-child {
        margin-bottom: 0;
    }

    .address-tooltip:before {
        content: "";
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        position: absolute;
        top: 1px;
        left: -25px;
    }

    .modal-tooltip:before {
        content: '';
        width: 8.5px;
        height: 8.5px;
        transform: rotate(45deg);
        background: $color-st4;
        background-repeat: no-repeat;
        position: absolute;
        border: 2px solid $color-palette3;
        z-index: 999;
        top: -6px;
        border-bottom: none;
        border-right: none;
    }
}

.d-inline-block {
    display: inline-block;
}


.text-white {
    color: #fff !important;
}

button.close {
    -webkit-appearance: none;
    background-color: transparent;
    border: 0;
    padding: 0;
    text-shadow: none;
    font-size: 1.40625rem;
    font-weight: 700;
    line-height: 1;
    opacity: .5;
}

.toast-header {
    justify-content: space-between;
    display: none;
}

.toast {
    width: 240px;
    font-family: $font-family;
    margin-bottom: 12px;
}

.toast-body {
    padding: 20px 14px;
    padding-left: 48px;
    background: rgba(60, 68, 85, 0.75);
    box-shadow: 0px 8px 25px rgba(52, 60, 107, 0.6);
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: $color-palette4;
    background-repeat: no-repeat;
    background-position: left 12px center;
}

#toast-container>#toast-wrapper>.toast {
    min-width: 344px;
}

.toast-error .toast-body {
    background-image: url("../images/toast/error.svg");
}

.toast-success .toast-body {
    background-image: url("../images/toast/success.svg");
}

.toast-warning .toast-body {
    background-image: url("../images/toast/warning.svg");
}

.toast {
    background-color: inherit;
}

#modal-forgot-password {

    .modal-dialog {
        max-width: 652px;
    }

    .form-modal-forgot-password {
        max-width: 400px;
        margin: 0 auto;
    }

    .modal-body {
        padding-bottom: 94px;
    }

    .modal-content:before {
        content: '';
        background-image: url(../images/bg-form-login.svg);
        background-repeat: no-repeat;
        opacity: 0.1;
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 100%;
        background-position: top -7px right 25px;
        background-size: 175px;
    }

    .main-modal-content {
        position: relative;
        z-index: 2;
    }
}


#page-login-collaborator {
    background: $bg-login-collaborator;
    padding-top: 50px;
    padding-bottom: 120px;

    .title-form-login-collaborator {
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette4;
        margin-bottom: 48px;

        img {
            position: relative;
            top: -4px;
        }
    }

    .bottom-form-login {
        text-align: center;
        position: relative;
        margin: 0 -127px;

        .item-bottom-form-login {
            padding-left: 24px;
            padding-right: 24px;
            display: inline-block;
            position: relative;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;

            a {
                color: $color-secondary1;
            }
        }

        .item-bottom-form-login:after {
            content: '';
            position: absolute;
            right: -1px;
            top: 3px;
            height: 20px;
            width: 1px;
            background: $color-secondary1;
        }

        .item-bottom-form-login:last-child:after {
            display: none;
        }
    }

    .wapper-form-login {
        background: $color-palette4;

        width: 100%;
        box-shadow: 0px 6px 20px rgba(255, 255, 255, 0.4);
        border-radius: 8px;
        padding: 96px 126px 24px;
        position: relative;

        .button-login-social {
            margin: 0 25px;
        }

        .login-social {
            margin-bottom: 48px;
        }

        .text-form-login-1 {
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-secondary1;
            margin-bottom: 48px;
        }

        .text-forgot-password {
            color: #000000;
            font-weight: 400;
            font-size: 14px;
            text-decoration-line: underline;
        }

        .session-form-login-2 {
            margin-bottom: 48px;
        }

        .button-form-login {
            margin-bottom: 118px;
        }
    }

    .main-wapper-form-login {
        position: relative;
        z-index: 10;
    }

    .wapper-form-login:before {
        background-image: url("../images/bg-form-login.svg");
        background-repeat: no-repeat;
        background-position: top 54px right 66px;
        opacity: 0.1;
        width: 100%;
        height: 100%;
        content: '';
        z-index: 9;
        position: absolute;
        top: 0;
        right: 0;
    }

    .wapper-form-login:after {
        content: '';
        position: absolute;
        bottom: -85px;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 592px;
        max-width: 100%;
        height: 48px;
        background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0.4;
    }

    .right-login-collaborator {
        padding-top: 44px;
    }

    .title-right-login-collaborator {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 36px;
        margin-bottom: 12px;
        color: $color-primary2;
    }

    .description-right-login {
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        margin-bottom: 24px;
    }

    .image-login {
        text-align: center;
        padding-top: 64px;

        img {
            max-width: 527px;
            width: 100%;
        }
    }

    //register
    .wapper-form-register {
        background: $color-palette4;
        width: 100%;
        border-radius: 8px;
        padding: 48px 113px 24px;
        position: relative;
        box-shadow: 0px 6px 20px rgba(255, 255, 255, 0.4);

        .main-wapper-form-register {
            @extend .main-wapper-form-login
        }

        .title-form-register {
            font-style: normal;
            font-weight: 800;
            font-size: 26px;
            line-height: 38px;
            margin-bottom: 48px;
            color: $color-palette1;
        }

        .group-form-register {
            margin-bottom: 48px;
        }

        .button-form-register {
            margin-bottom: 50px;
        }
    }

    .wapper-form-register:before {
        background-image: url("../images/bg-form-login.svg");
        background-repeat: no-repeat;
        background-position: top 54px right 66px;
        opacity: 0.1;
        width: 100%;
        height: 100%;
        content: '';
        z-index: 9;
        position: absolute;
        top: 0;
        right: 0;
    }

    .wapper-form-register:after {
        content: '';
        position: absolute;
        bottom: -85px;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 592px;
        max-width: 100%;
        height: 48px;
        background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0.4;
    }

    .title-right-register-collaborator-from-link {
        font-style: normal;
        font-weight: 600;
        font-size: 42px;
        line-height: 80px;
        color: $color-palette4;
        text-transform: uppercase;

        span {
            color: $color-brand;
        }
    }

    .field-checkbox-register-from-link-checkmark {
        border: 1px solid $color-palette1;
    }

    .field-checkbox-st1:checked~.field-checkbox-register-from-link-checkmark {
        background-color: $color-brand;
        background: url(../images/icon-checked.svg) no-repeat;
        background-position: center;
        background-size: 76%;
    }

    .height-light {
        color: $color-primary1;
    }

    .button-form-register-from-link {
        margin-bottom: 24px;
    }

    .group-field-checkbox-register-from-link-checkmark {
        display: block;
        position: relative;
        padding-left: 25px;
        margin-bottom: 2px;
        cursor: pointer;
        font-size: 14px;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        color: $color-palette1;
    }

    .image-register-from-url {
        padding-top: 64px;

        img {
            width: 644px;
        }
    }

}

#page-login-collaborator.page-register-employer {
    padding-top: 78px;

    .right-login-collaborator {
        padding-top: 140px;
    }

    .text-disable {
        color: $color-st6;
        font-weight: 400;
        font-size: 10px;
        line-height: 12px;
    }
}

#choice-recland-register-link {
    padding-top: 48px;
    padding-bottom: 24px;

    .title {
        font-style: normal;
        font-weight: 700;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;
        margin-bottom: 60px;
        text-align: center;

        span {
            color: $color-brand;
        }
    }

    .icon-choice {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        text-align: center;
    }

    .title-item-choice {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-brand;
        margin-bottom: 16px;
        text-align: center;
    }

    .desc-item-choice {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 28px;
        color: $color-palette1;
        text-align: center;
    }

    .row-choice {
        justify-content: center;
    }
}

#procedure-recland-register-link {
    padding-top: 98px;
    padding-bottom: 96px;
    background: radial-gradient(70.31% 79.66% at 50% 50%, #079DB2 0%, #0A3039 99.96%);

    .title {
        font-weight: 700;
        font-size: 28px;
        line-height: 42px;
        text-transform: uppercase;
        color: $color-palette4;
        text-align: center;
        margin-bottom: 66px;

        span {
            color: $color-palette4;
            padding: 2px 12px;
            background: $color-st6;
        }
    }

    .icon-procedure {
        margin-bottom: 58px;
        text-align: center;
        position: relative;
    }

    .icon-procedure:after {
        width: 46px;
        height: 6px;
        background: $color-st6;
        content: '';
        position: absolute;
        bottom: -24px;
        left: 0;
        right: 0;
        margin: 0 auto;
    }

    .icon-procedure:before {
        background: #61D4EA;
        content: "";
        position: absolute;
        height: 1px;
        width: 86%;
        right: -129px;
        top: 50%;
    }

    .col-procedure:last-child .icon-procedure:before {
        display: none;
    }

    .title-item-procedure {
        text-align: center;
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 27px;
        text-transform: capitalize;
        color: $color-st28;
    }

    .row-procedure {
        --bs-gutter-x: 1.5rem;
        --bs-gutter-y: 0;
        display: flex;
        flex-wrap: wrap;
        margin-top: calc(-1 * var(--bs-gutter-y));
        margin-right: calc(-.5 * var(--bs-gutter-x));
        margin-left: calc(-.5 * var(--bs-gutter-x));
        justify-content: space-around;
    }

    .col-procedure {
        flex: 0 0 auto;
        width: 12%;
    }
}

#page-forgot-password-collaborator {
    background: linear-gradient(270deg, #022D33 0%, #196A80 100%);

    .bg-page-forgot-password-collaborator {
        background-image: url(../images/bg-forgot-password.svg);
        background-repeat: no-repeat;
        background-position: top 35px center;
    }

    .page-forgot-password-collaborator {
        padding-top: 104px;
        padding-bottom: 297px;
    }

    .main-form-forgot-password {
        box-shadow: 0px 6px 20px rgba(255, 255, 255, 0.4);
        background: $color-palette4;
        padding-top: 48px;
        padding-bottom: 76px;
        padding-left: 126px;
        padding-right: 126px;
        max-width: 652px;
        display: block;
        margin: 0 auto;
        border-radius: 8px;
        position: relative;
    }

    .title-form-forgot-password {
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        margin-bottom: 48px;
    }

    .main-form-forgot-password:before {
        background-image: url(../images/bg-form-login.svg);
        background-repeat: no-repeat;
        background-position: top 54px right 66px;
        opacity: 0.1;
        width: 100%;
        height: 100%;
        content: '';
        z-index: 9;
        position: absolute;
        top: 0;
        right: 0;
    }

    .main-form-forgot-password:after {
        content: '';
        position: absolute;
        bottom: -85px;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 592px;
        max-width: 100%;
        height: 48px;
        background: radial-gradient(50% 50% at 50% 50%, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 100%);
        opacity: 0.4;
    }

    .wapper-main-form-forgot-password {
        position: relative;
        z-index: 10;
    }

}

.modal-landing-employee {
    .modal-dialog {
        max-width: 652px;
    }

    .title-modal-login-employee {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        margin-bottom: 48px;
    }

    .btn-close {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .modal-body {
        padding: 48px;
        padding-bottom: 24px;
    }

    .text-forgot-password {
        color: #000000;
        font-weight: 400;
        font-size: 14px;
        text-decoration-line: underline;
    }

    .button-form-login {
        margin-bottom: 242px;
    }

    .bottom-form-login {
        text-align: center;
        position: relative;
        margin: 0 -127px;

        .item-bottom-form-login {
            padding-left: 24px;
            padding-right: 24px;
            display: inline-block;
            position: relative;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;

            a {
                color: $color-secondary1;
            }
        }

        .item-bottom-form-login:after {
            content: '';
            position: absolute;
            right: -1px;
            top: 3px;
            height: 20px;
            width: 1px;
            background: $color-secondary1;
        }

        .item-bottom-form-login:last-child:after {
            display: none;
        }
    }

    .main-form-login-employee {
        max-width: 400px;
        display: block;
        margin: 0 auto;
    }

    .button-form-register {
        margin-bottom: 24px;
    }
}

#footer {
    padding: 48px 0;

    .widget-footer-2 {
        padding-top: 32px;
    }

    .logo-footer {
        img {
            width: 276px;
        }
    }

    .description-footer {
        color: $color-palette11;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
    }

    .social-footer {
        ul {
            li {
                display: inline-block;
                margin-right: 20px;
            }
        }
    }

    .social-mobile {
        display: none;
        text-align: center;
    }

    .title-footer {
        color: $color-palette1;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        margin-bottom: 20px;
    }

    .menu-footer {
        ul {
            li {
                font-size: 18px;
                line-height: 26px;
                font-weight: 600;
                margin-bottom: 10px;
            }
        }

        a {
            color: $color-palette8;

            span {
                color: $color-brand;
            }
        }
    }
}

#copyright {
    text-align: center;
    font-weight: 600;
    font-size: 18px;
    line-height: 26px;
    padding: 35px 0;
    background: linear-gradient(90deg, #065A68 1.56%, #079DB2 52.08%, #065A68 100%);
    color: $color-palette5;
}


#banner {
    background-size: cover;
    background-repeat: no-repeat;
    height: 437px;
    background-position: center;
    position: relative;

    .main-banner {
        background: rgba(0, 28, 43, 0.6);
        padding: 16px 0 12px;
        position: absolute;
        bottom: 0;
        width: 100%;

        .title-banner {
            color: $color-palette5;
            font-weight: 800;
            font-size: 36px;
            line-height: 28px;
            margin-bottom: 12px;
        }

        .small-title-banner {
            color: $color-palette3;
            font-weight: 800;
            font-size: 24px;
            line-height: 36px;
        }

        .line-banner-2 {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .wapper-btn-banner {
            display: flex;
            gap: 8px;
            flex-flow: wrap;
        }


    }

}

.btn-banner-st1 {
    background: $color-st1 url(../images/icon-heart.svg) no-repeat;
    background-position: left 10px center;
    border-radius: 12px;
    font-size: 14px;
    line-height: 20px;
    color: $color-primary3;
    padding: 2px 12px 2px 30px;
    background-size: 12px;
}

.btn-banner-st1.Hot {
    color: $color-brand;
    background: $color-secondary3 url(../images/icon-hot.svg) no-repeat;
    background-size: 12px !important;
    background-position: left 10px center;
    white-space: nowrap;
}

.btn-banner-st1.Premium {
    color: $color-primary1;
    background: $color-secondary4 url(../images/icon-premium.svg) no-repeat;
    background-size: 12px !important;
    background-position: left 10px center;
    white-space: nowrap;

}

.btn-banner-st2 {
    background: $color-st2 url(../images/icon-lightning.svg) no-repeat;
    background-position: left 10px center;
    border-radius: 12px;
    font-size: 14px;
    line-height: 20px;
    color: $color-primary1v2;
    padding: 2px 12px 2px 30px;
    white-space: nowrap;
}

.btn-banner-st3 {
    background: $color-st28 url(../images/icon-bonus-onboard.svg) no-repeat;
    background-position: left 10px center;
    border-radius: 12px;
    font-size: 14px;
    line-height: 20px;
    color: $color-st27;
    padding: 2px 12px 2px 30px;
    white-space: nowrap;
}

.btn-banner-st4 {
    background: $color-st29 url(../images/icon-job-remove.svg) no-repeat;
    background-position: left 10px center;
    border-radius: 12px;
    font-size: 14px;
    line-height: 20px;
    color: $color-primary4;
    padding: 2px 12px 2px 30px;
    white-space: nowrap;
}

.btn-banner-st5 {
    background: $color-st22 url(../images/icon-bonus-interview.svg) no-repeat;
    background-position: left 10px center;
    border-radius: 12px;
    font-size: 14px;
    line-height: 20px;
    color: $color-primary1;
    padding: 2px 12px 2px 30px;
    white-space: nowrap;
}

.list-icon {
    height: 100%;

    ul {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
        height: 100%;

        li {
            a {
                display: block;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: $color-palette5;
                background-position: center;
                background-repeat: no-repeat;
            }

            a.icon-care-active {
                background-color: $color-palette4;
            }

            .icon-heart {
                background-image: url(../images/icon-heart2.svg);
            }

            .icon-heart.icon-care-active {
                background-image: url(../images/icon-heart2-active.svg);
            }

            .icon-bookmark {
                background-image: url(../images/icon-bookmark.svg);
            }

            .icon-bookmark.icon-care-active {
                background-image: url(../images/icon-bookmark-active.svg);
            }

            .icon-share {
                background-image: url(../images/icon-share.svg);
            }

            .icon-download {
                background-image: url(../images/icon-download.svg);
            }
        }
    }
}

#job {
    background: $color-st3;
    padding-bottom: 48px;
}

.list-icon-mobile {
    display: none;
    height: auto;

    ul {
        height: auto;
    }
}

#header-job {
    position: relative;
    height: 168px;

    .expired-time {
        position: absolute;
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-st6;
        text-transform: uppercase;
        background: rgba(52, 52, 52, 0.6);
        backdrop-filter: blur(10px);

        img {
            margin-right: 25px;
        }
    }

    .header-job {
        display: flex;
        justify-content: center;
        padding: 48px 0;
        gap: 48px;
        flex-wrap: wrap;

        .icon-item-header-job {
            background: $color-white;
            box-shadow: inset 2px 2px 2px rgba(0, 0, 0, 0.25), inset -2px -2px 2px rgba(0, 0, 0, 0.25);
            width: 52px;
            height: 52px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
        }

        .image-icon {
            width: 100%;
            height: 100%;
            display: block;
            background-position: center;
            background-size: auto;
            background-repeat: no-repeat;
        }

        .content-item-header-job {
            text-align: center;
            color: $color-palette4;
            width: 216px;

            .line1 {
                font-weight: 800;
                font-size: 16px;
                line-height: 24px;
                text-transform: uppercase;
                margin-bottom: 4px;
            }

            .line2 {
                font-style: normal;
                font-weight: 600;
                font-size: 12px;
                line-height: 16px;
            }
        }

        .item-header-job-st1 {
            transition: 0.1s;
            background: $color-brand;

            .image-icon {
                background-image: url(../images/icon-header-job-1.svg);
            }
        }

        .item-header-job-st1:hover {
            background: $color-palette4;

            .line1 {
                color: $color-secondary1;
            }

            .line2 {
                color: $color-brand;
            }

            .icon-item-header-job {
                background: linear-gradient(45deg, #065A68 1.56%, #079DB2 52.08%, #065A68 100%);
                box-shadow: 0px 0px 15px rgba(224, 255, 245, 0.4);
            }

            .image-icon {
                background-image: url(../images/icon-header-job-hover-1.svg);
            }
        }

        .item-header-job-st2 {
            transition: 0.1s;
            background: $color-brand1;

            .image-icon {
                background-image: url(../images/icon-header-job-2.svg);
            }
        }

        .item-header-job-st2:hover {
            background: $color-palette4;

            .line1 {
                color: $color-secondary1;
            }

            .line2 {
                color: $color-brand;
            }

            .icon-item-header-job {
                background: linear-gradient(45deg, #065A68 1.56%, #079DB2 52.08%, #065A68 100%);
                box-shadow: 0px 0px 15px rgba(224, 255, 245, 0.4);
            }

            .image-icon {
                background-image: url(../images/icon-header-job-hover-2.svg);
            }
        }

        .disable-button {
            cursor: not-allowed !important;
        }

        .item-header-job {
            display: flex;
            gap: 13px;
            justify-content: center;
            align-items: center;
            padding: 10px 16px;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            width: 313px;
            max-width: 100%;
            cursor: pointer;
        }
    }
}

#header-job.header-job-scroll {
    .header-job {
        position: fixed;
        width: auto;
        display: flex;
        flex-direction: column;
        right: 50px;
        bottom: 15vh;
        justify-content: end;
        align-items: end;
        z-index: 10;
        box-shadow: unset;

        @media only screen and (max-width: $mobile-width) {
            align-items: center;
            gap: 16px;
            bottom: 0;
            display: flex;
            width: 100%;
            justify-content: center;
            margin: 0 auto;
            padding: 16px 0 35px 0;
            background: white;
            left: 0;
            right: 0;
            box-shadow: 0px -4px 6px rgb(0 0 0 / 25%);
            border-radius: 8px 8px 0px 0px;
            flex-direction: row;
        }

    }

    .content-item-header-job {
        display: none;

        @media only screen and (max-width: $mobile-width) {
            display: block;
        }
    }

    .header-job {
        gap: 16px;
    }

    .item-header-job {
        width: 72px;
        padding: 10px 10px;
    }

    .item-header-job-st1 {
        transition: 0s;
    }

    .item-header-job-st2 {
        transition: 0s;
    }

    .item-header-job-st1:hover {
        width: 313px;

        .content-item-header-job {
            display: block;
        }
    }

    .item-header-job-st2:hover {
        width: 313px;

        .content-item-header-job {
            display: block;
        }
    }
}

.show-info-job-mobile {
    text-align: center;
    margin-bottom: 20px;
    display: none;

    .open {
        img {
            transform: rotate(3.142rad);
        }
    }
}

#main-job-detail {
    margin-bottom: 48px;

    .content-job {
        background: $color-white;
        border-radius: 6px;
        height: 100%;
        padding: 24px;

        .item-content-job {
            border-bottom: 1px solid $color-palette3;
            margin-bottom: 24px;
            padding-bottom: 24px;
        }

        .item-content-job:last-child {
            border-bottom: 0;
            margin-bottom: 0;
        }

        .title-content-job {
            margin-bottom: 15px;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            color: $color-palette2;
        }

        .edit-content-job {
            color: $color-secondary1;
            font-size: 16px;

            ul {
                padding-left: 35px;
                list-style: disc;
            }

            p,
            li {
                line-height: 24px;
                margin-bottom: 16px;
            }

            p:last-child,
            li:last-child {
                margin-bottom: 0;
            }
        }

        .title-skill {
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            color: $color-palette2;
            padding-bottom: 11px;
            border-bottom: 1px solid $color-palette3;
            margin-bottom: 12px;
        }

        .sidebar-skill-mobi {
            display: none;
        }
    }

    .sidebar-job {
        background: $color-white;

        .title-sidebar {
            padding: 23px 24px 12px;
            color: $color-palette2;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            border-bottom: 1px solid $color-palette3;
            margin-bottom: 12px;
        }

        .list-info-job {
            padding-left: 78px;
            padding-right: 24px;
        }

        .item-info-job {
            position: relative;
            margin-bottom: 16px;

            .line1 {
                color: $color-palette7;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                margin-bottom: 6px;
            }

            .line2 {
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: $color-secondary1;

                sup,
                sub {
                    font-size: 10px;
                }
            }
        }

        .item-info-job:last-child {
            margin-bottom: 0;
        }

        .item-info-job:before {
            content: '';
            background-repeat: no-repeat;
            width: 20px;
            height: 20px;
            position: absolute;
            top: 1px;
            left: -30px;
        }

        .icon-bag:before {
            background-image: url(../images/icon-bag.svg);
        }

        .icon-user:before {
            background-image: url(../images/icon-user2.svg);
        }

        .icon-level:before {
            background-image: url(../images/icon-level.svg);
        }

        .icon-address:before {
            background-image: url(../images/icon-address.svg);
        }

        .icon-calendar:before {
            background-image: url(../images/icon-calendar.svg);
        }

        .icon-clock:before {
            background-image: url(../images/icon-clock.svg);
        }

        .icon-money:before {
            background-image: url(../images/icon-money.svg);
        }

        .icon-money-hand:before {
            background-image: url(../images/icon-money-hand.svg);
        }

        .icon-number-user:before {
            background-image: url(../images/icon-number-user.svg);
        }

        .job-skill {
            padding: 0 12px 12px;
            display: flex;
            gap: 12px;

            span {
                font-style: normal;
                font-weight: 600;
                font-size: 12px;
                line-height: 22px;
                color: $color-primary1;
                padding: 2px 12px;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 12px;
                background: $color-palette4;
            }
        }


    }


}

#detail-company {
    margin-bottom: 90px;

    .detail-company {
        //padding: 24px;
        background: $color-white;
        border-radius: 6px;

        .item-detail-company {
            border-bottom: 1px solid $color-palette3;
            padding-bottom: 27px;
            margin-bottom: 24px;
        }

        .item-detail-company:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .title-detail-company {
            color: $color-palette2;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            margin-bottom: 15px;
        }

        .main-detail-company {
            padding: 0 24px;

            .logo-company {
                max-width: 115px;

                img {
                    max-width: 100%;
                }
            }

            .info-company {
                display: flex;
                gap: 24px;
            }

            .main-info-company {

                width: calc(100% - 115px);
            }

            .title-main-info-company {
                font-style: normal;
                font-weight: 800;
                font-size: 20px;
                line-height: 28px;
                color: $color-palette7;
                margin-bottom: 15px;

                a {
                    font-weight: 600;
                    font-size: 16px;
                    color: $color-primary1;
                }
            }

            .line-info-company {
                font-size: 18px;
                line-height: 26px;
                color: $color-secondary1;

                .label-list-info-company {
                    position: relative;
                    color: $color-palette7;
                }

                .label-list-info-company:before {
                    content: '';
                    position: absolute;
                    left: -25px;
                    top: 2px;
                    width: 20px;
                    height: 20px;
                    background-repeat: no-repeat;
                    background-position: center;
                }

                .label-list-info-company-link:before {
                    background-image: url(../images/icon-link.svg);
                }

                .label-list-info-company-address:before {
                    background-image: url(../images/icon-address.svg);
                }

                .label-list-info-company-number-user:before {
                    background-image: url(../images/icon-number-user.svg);
                }
            }

            .wapper-list-info-company {
                padding: 0 25px;
            }

            .row-line-info-company {
                margin-bottom: 16px;
            }

            .row-line-info-company:last-child {
                margin-bottom: 0;
            }


        }


        .content-company {
            font-style: normal;
            font-size: 16px;
            line-height: 28px;
            color: $color-secondary1;
            overflow: hidden;
            height: 84px
        }

        .show-more {
            font-size: 16px;
            line-height: 28px;
            text-decoration-line: underline;
            color: $color-primary1;
            display: none;
        }

    }

    .show-detail-company {
        padding: 24px;
    }
}


#with-recland {
    background-image: url(../images/bg-with-recland.svg);
    background-repeat: no-repeat;

    .wapper-recland-slider {
        position: relative;
        margin: 0 -30px;
    }

    .title-st1 {
        color: $color-palette1;
        font-size: 28px;
        font-weight: 800;
        line-height: 1.5;
        margin-bottom: 18px;

        span {
            color: $color-brand;
        }
    }

    .row-with-recland {
        margin-left: -15px;
        margin-right: -15px;

        .col-with-recland {
            padding-left: 15px;
            padding-right: 15px;
            margin-bottom: 30px;
        }
    }

    .item-with-recland {
        display: flex;
        background: #fff;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        border-radius: 6px;
        transition: 0.2s;
        height: 100%;

        .logo-item-with-recland {
            position: relative;
            margin-right: 12px;
            width: 74px;
            @extend .style-group-center;

            img {
                max-width: 100%;
            }
        }

        .logo-item-with-recland:after {
            content: '';
            opacity: 0.4;
            border: 1px solid $color-palette3;
            width: 0;
            height: 68px;
            position: absolute;
            right: -12px;
        }

        .main-item-with-recland {
            padding-left: 12px;
            width: calc(100% - 74px);

            .row-line-item-with-recland {
                margin-right: 0 -8px;

                .col-item-with-recland {
                    padding: 0 8px;
                }
            }

            .title-item-with-recland {
                font-weight: 800;
                font-size: 14px;
                line-height: 22px;
                color: $color-palette7;
            }

            .item-with-recland-st {
                display: flex;
                align-items: center;
                font-size: 12px;

                img {
                    margin-right: 6px;
                    width: 16px;
                }

                sup {
                    font-size: 10px;
                    position: relative;
                    top: -5px;
                    right: -2px;
                }
            }

            .item-with-recland-st1 {
                color: $color-brand;
            }

            .item-with-recland-st2 {
                color: $color-primary1;
            }
        }
    }

    .item-with-recland:hover {
        box-shadow: 0px 10px 26px rgba(112, 131, 187, 0.45);
    }

    .item-with-recland-slider {
        padding: 30px 30px 0;
    }

    .with-recland-slider {
        position: relative;

    }

    .owl-theme {
        .owl-nav {
            [class*='owl-'] {
                width: 48px;
                height: 48px;
                background-image: url(../images/icon-arrow.svg);
                background-repeat: no-repeat;
                margin: 0 24px;
            }

            [class*='owl-']:hover {
                background-image: url(../images/icon-arrow-hover.svg);
                background-color: initial;
            }

            button.owl-next {
                -webkit-transform: scaleX(-1);
                transform: scaleX(-1);
            }
        }
    }
}


#home-ntd {}

#banner-ntd {
    overflow: hidden;

    .wapper-main-banner {
        position: absolute;
        z-index: 99;
        width: 100%;
    }

    .main-banner-ntd {}

    .owl-dots {
        position: absolute;
        bottom: 27px;
        width: 100%;

    }

    .owl-carousel {
        .owl-item {
            img {
                //height: 748px;
            }
        }
    }

    .owl-theme .owl-dots .owl-dot span {
        width: 18px;
        height: 18px;
        background: $color-st8;
    }

    .owl-theme .owl-dots .owl-dot.active span {
        background: $color-primary1;
    }

    .content {
        height: 100%;
        position: relative;

        .slogan {
            margin-bottom: 80px;
            padding-left: 36px;
        }

        .line {
            font-weight: 600;
            font-size: 42px;
            line-height: 80px;
            color: $color-brand1;

            span {
                color: $color-brand;
            }
        }

        .line2 {
            margin-bottom: 44px;
            font-weight: 600;
            font-size: 22px;
            line-height: 28px;
            color: $color-secondary1;
        }

        .line-button {
            padding-left: 36px;
            bottom: 134px;
            position: absolute;
        }

        .button-ntd {
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            color: $color-palette4;
            border: none;
            height: 56px;
            min-width: 200px;
            padding: 15px;
            box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
            border-radius: 8px;
        }

        .button-ntd-st1 {
            background: linear-gradient(90deg, #056777 0%, #079DB2 100%);
        }

        .button-ntd-st2 {
            background: linear-gradient(90deg, #FD8F3B 0%, #FDC498 100%);
        }
    }

}

#choose-ntd {
    padding-top: 100px;
    padding-bottom: 100px;

    .title {
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;

        span {
            color: $color-brand;
        }
    }

    .description {
        max-width: 1023px;
        text-align: center;
        margin-left: auto;
        margin-right: auto;
        font-weight: 400;
        font-size: 20px;
        line-height: 36px;
        color: $color-palette1;
    }

    .list-choose {
        padding-right: 20px;
        position: relative;
        left: -55px;

        .image-item-choose {
            width: 118px;
        }

        .content-item-choose {
            padding-left: 24px;
            width: calc(100% - 130px);

            .title-item-choose {
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand1;
                margin-bottom: 18px;
            }

            .description-item-choose {
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 28px;
                color: $color-secondary1;
            }
        }

        li {
            display: -webkit-box;
            display: -moz-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-flex-flow: row wrap;
            flex-flow: row wrap;
            align-items: center;
        }
    }

    .col-img {
        display: flex;
        align-items: center;

        .main-col-img {
            position: relative;

        }
    }
}

#company-width {
    padding-top: 154px;
    padding-bottom: 55px;
    background: linear-gradient(180deg, rgba(5, 103, 119, 0.5) 0%, rgba(255, 255, 255, 0) 61.46%);

    .header-company-width {
        padding-top: 96px;
        padding-bottom: 175px;
    }

    .title-company-width {
        display: inline-block;
        position: relative;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;
        text-align: center;
        margin-left: 280px;

        span {
            color: $color-brand;
        }
    }

    .title-company-width:before {
        content: '';
        background-image: url(../images/ntd-image-title-company-width.svg);
        background-repeat: no-repeat;
        width: 296px;
        height: 403px;
        position: absolute;
        left: -320px;
        top: -140px;
    }

    .item-slider-company-width {
        flex-wrap: wrap;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        margin-left: -32px;
        margin-right: -32px;
    }

    .item-logo-company-width {
        text-align: center;
        height: 80px;
        display: flex;
        justify-content: center;
        align-items: center;

        img {
            //max-width: 100%;
            width: auto;
            margin: 0 auto;
            max-width: 120px;
            max-height: 80px;
        }
    }

    .company-width-slider-1 {
        margin-bottom: 96px;
    }

    .company-width-slider-2 {
        margin-bottom: 60px;
    }

    .owl-theme {
        .owl-nav {
            [class*='owl-'] {
                width: 48px;
                height: 48px;
                background-image: url(../images/icon-arrow.svg);
                background-repeat: no-repeat;
                margin: 0 24px;
            }

            [class*='owl-']:hover {
                background-image: url(../images/icon-arrow-hover.svg);
                background-color: initial;
            }

            button.owl-next {
                -webkit-transform: scaleX(-1);
                transform: scaleX(-1);
            }
        }
    }

    .owl-nav-custom {
        text-align: center;

        [class*=owl-] {
            width: 48px;
            height: 48px;
            background-image: url(../images/icon-arrow.svg);
            background-repeat: no-repeat;
            margin: 0 24px;
            border: none;
            border-radius: 50%;
        }

        [class*=owl-]:hover {
            background-image: url(../images/icon-arrow-hover.svg);
            background-color: initial;
        }

        [class*=owl-]:active {
            background-image: url(../images/icon-arrow-hover.svg);
            background-color: initial;
        }

        button.owl-next {
            -webkit-transform: scaleX(-1);
            transform: scaleX(-1);
        }
    }

}

#procedure-ntd {
    //background-color: $color-st5;
    padding-top: 80px;
    padding-bottom: 182px;
    background-image: url(../images/ntd-bg-procedure.svg);
    background-repeat: no-repeat;
    background-position: center;
    background-color: $color-st7;

    .title-procedure-ntd {
        font-weight: 700;
        font-size: 28px;
        line-height: 42px;
        text-transform: uppercase;
        color: $color-brand1;
        margin-bottom: 95px;

        span {
            color: $color-st7;
            background: $color-st6;
            padding: 3px 9px;
        }
    }

    .image-item-procedure-ntd {
        margin-bottom: 34px;
        position: relative;
    }

    .image-item-procedure-ntd:before {
        background: $color-brand1;
        content: "";
        position: absolute;
        height: 2px;
        width: 38%;
        right: -51px;
        top: 50%;
    }

    .col-item-procedure-ntd:last-child {
        .image-item-procedure-ntd:before {
            display: none;
        }
    }

    .title-item-procedure-ntd {
        height: 90px;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        text-transform: capitalize;
        color: $color-palette1;
        position: relative;
        margin: 0 auto;
        margin-bottom: 54px;
        max-width: 160px;
    }

    .title-item-procedure-ntd:after {
        content: '';
        width: 46px;
        height: 6px;
        background: $color-st6;
        position: absolute;
        bottom: -30px;
        margin: 0 auto;
        left: 0;
        right: 0;
    }

    .description-item-procedure-ntd {
        font-style: normal;
        font-weight: 300;
        font-size: 14px;
        line-height: 26px;
        color: $color-palette1;
        max-width: 160px;
        opacity: 0.8;
        margin: 0 auto;
    }
}

#customer-ctv {
    padding-top: 48px;
    padding-bottom: 118px;

    .title-customer-ctv {
        color: $color-brand;
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        margin-bottom: 100px;
        text-align: center;
    }

    .owl-stage {
        display: flex;
    }

    .slider {
        margin: 20px auto;
        text-align: center;
        padding: 20px;
        color: white;

        .slide {
            padding: 0;
            height: 100%;

            .child-element {
                //transition: all .2s ease;
                padding: 15px;
                margin: 30px 0;
                transition: all .5s;
                height: 100%;
            }
        }

        .slick-active {
            .child-element {
                transform: scale(0.5);
                margin: 0;
                max-width: initial;
                height: 100%;
            }
        }

        .slick-active.slick-center {
            .child-element {
                transform: scale(1);
                margin: 0;
            }
        }

        .item-customer-ctv {
            border-radius: 16px;
            padding-top: 50px;
            padding-bottom: 15px;
            padding-left: 15px;
            padding-right: 15px;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0px 2px 4px #056777;
            height: 90%;

            .avatar {
                width: 86px;
                padding: 5px;
                border-radius: 50%;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                background: $color-white;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                margin: 0 auto;
                overflow: hidden;

                img {
                    border-radius: 50%;
                    height: 76px;
                    width: 76px;
                }
            }

            .description {
                font-style: normal;
                font-weight: 400;
                font-size: 20px;
                line-height: 40px;
                text-align: center;
                color: $color-secondary1;
                margin-bottom: 32px;
            }

            .name {
                font-style: normal;
                font-weight: 800;
                font-size: 20px;
                line-height: 36px;
                color: $color-palette1;
                margin-bottom: 12px;
            }

            .position {
                font-style: normal;
                font-weight: 600;
                font-size: 24px;
                line-height: 36px;
                text-align: center;
                color: $color-brand1;
            }
        }

    }

}


#banner-ctv {
    position: relative;
    overflow: hidden;

    .slider-ctv {
        img {
            //object-fit: cover;
            //height: 748px;
            width: 100%;
            margin: 0 auto;
        }
    }

    .owl-dots {
        position: absolute;
        bottom: 27px;
        width: 100%;

    }

    .owl-theme .owl-dots .owl-dot span {
        width: 18px;
        height: 18px;
        background: $color-st8;
    }

    .owl-theme .owl-dots .owl-dot.active span {
        background: $color-primary1;
    }

    .search-box {
        background: linear-gradient(114.28deg, rgba(255, 255, 255, 0.6) 0%, rgba(196, 196, 196, 0.2) 100%);
        box-shadow: 0px 4px 24px -1px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(40px);
        border-radius: 30px;
        padding: 2px;
        position: absolute;
        bottom: 107px;
        left: 80px;
        z-index: 9;
    }

    .main-form {
        border-radius: 30px;
        width: 602px;
        height: 72px;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .wapper-search-form {
        padding: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
    }

    .wapper-search-form {
        height: 100%;

        input {
            width: calc(100% - 248px);
            height: 100%;
            border: none;
            outline: none;
            padding-left: 52px;
            padding-right: 10px;
            color: $color-palette4;
            background: url("../images/ctv/icon-search.svg") no-repeat;
            background-position: left 22px center;
        }

        input::placeholder {
            color: $color-palette4;
        }

        button {
            width: 248px;
            background: $color-brand1;
            box-shadow: 0px 2px 10px 2px rgba(255, 255, 255, 0.2);
            border-radius: 30px;
            border: none;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            color: $color-palette4;
            height: 100%;
        }
    }
}

#ecosystem-ctv {
    padding-top: 48px;

    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;
        margin-bottom: 16px;
    }

    .description {
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 56px;
    }
}

#chance-ctv {

    background-image: url(../images/ctv/bg-change.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    padding-top: 48px;

    .title {
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;
        margin-bottom: 16px;
    }

    .description {
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 24px;
    }

    .col-chance-ctv {
        margin-bottom: 48px;
        padding-left: 24px;
        padding-right: 24px;
    }

    .row-chance-ctv {
        margin-left: -24px;
        margin-right: -24px;
    }

    .item-slider-change-ctv {
        padding: 24px 50px;
        padding-bottom: 0;
    }

    .wapper-slider-change-ctv {
        position: relative;
        margin-right: -24px;
        margin-left: -24px;
    }

    .item-change-ctv {
        transition: 0.1s;
        position: relative;
        background: rgba(255, 255, 255, 0.8);
        padding: 16px;
        box-shadow: 0px 4px 16px rgba(129, 155, 234, 0.4);
        border-radius: 12px;
        text-align: center;

        img {
            display: inline-block;
            max-height: 60px;
            width: auto;
        }

        .header-item-change-ctv {
            color: $color-palette3;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;

            span {
                color: $color-palette9;
            }

            img {
                position: relative;
                top: -1px;
            }
        }

        .logo {
            height: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .title-item-change-ctv {
            font-style: normal;
            font-weight: 800;
            font-size: 20px;
            line-height: 28px;
            color: $color-palette2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 56px;
        }

        .line-price {
            font-weight: 800;
            font-size: 16px;
            line-height: 24px;
            min-height: 24px;
            margin-bottom: 12px;
            text-align: left;
            gap: 24px;
            display: flex;
            justify-content: center;

            span {
                //padding-left: 24px;
                font-size: 16px;
            }

            sup {
                text-transform: lowercase;
                font-weight: 600;
            }
        }

        .line-price-st1 {
            color: $color-brand;
        }

        .line-price-st2 {
            color: $color-brand1;
        }

        .footer-item-change-ctv {
            display: flex;
            padding-top: 12px;
            justify-content: space-between;

            .btn {
                padding: 8px 12px;
                text-align: center;
                color: $color-palette9;
                background: $color-palette4;
                box-shadow: 0px 2px 4px rgba(4, 69, 80, 0.15);
                border-radius: 30px;
                min-width: 50px;
                font-size: 12px;
            }
        }

        .image-icon-ctv {
            position: absolute;
            top: 10px;
            left: -19px;
            max-width: 60px;
        }
    }

    .item-change-ctv:hover {
        box-shadow: 0px 8px 25px rgba(52, 60, 107, 0.6);
    }

    .owl-theme {
        .owl-nav {
            [class*='owl-'] {
                width: 48px;
                height: 48px;
                background-image: url(../images/icon-arrow.svg);
                background-repeat: no-repeat;
                margin: 0 24px;
            }

            [class*='owl-']:hover {
                background-image: url(../images/icon-arrow-hover.svg);
                background-color: initial;
            }

            [class*='owl-']:active {
                background-image: url(../images/icon-arrow-hover.svg);
                background-color: initial;
            }

            button.owl-next {
                -webkit-transform: scaleX(-1);
                transform: scaleX(-1);
            }
        }
    }
}

#choose-ctv {
    padding-top: 58px;
    padding-bottom: 48px;


    .title {
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;
        margin-bottom: 60px;

        span {
            color: $color-brand;
        }
    }

    .col-image {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .list-choose {
        position: relative;
        margin-left: -15px;

        .image-item-choose {
            width: 130px;
        }

        .content-item-choose {
            padding-left: 15px;
            width: calc(100% - 130px);

            .title-item-choose {
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand;
                margin-bottom: 16px;
            }

            .description-item-choose {
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 28px;
                color: $color-palette1;
                padding: 0 15px;
            }
        }

        li {
            display: -webkit-box;
            display: -moz-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            -webkit-flex-flow: row wrap;
            flex-flow: row wrap;
            align-items: center;
        }
    }

}

#procedure-ctv {
    position: relative;

    .image-bg {
        img {
            width: 100%;
        }
    }

    .content-procedure-ctv {
        padding-top: 48px;
        ;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
    }

    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-brand1;

        span {
            color: $color-brand;
        }
    }
}

#count-ctv {
    padding-top: 30px;
    padding-bottom: 200px;
    background: $color-palette4;

    .header-count-ctv {
        margin-bottom: 30px;
    }

    .icon-count {
        margin-bottom: 15px;
    }

    .number-count {
        font-style: normal;
        font-weight: 800;
        font-size: 58px;
        line-height: 80px;
        color: $color-brand1;
        margin-bottom: 12px;
    }

    .text-count {
        font-style: normal;
        font-weight: 600;
        font-size: 32px;
        line-height: 44px;
        color: $color-secondary1;
    }
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-family: $font-family;
}

.select2-container .select2-selection--single {
    border-radius: 8px;
    font-family: $font-family;
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    background: $color-white ;
    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);

}

.select2-container--default .select2-search--dropdown .select2-search__field {
    height: 40px;
    background: $color-palette4;
    outline: none;
}

.select2-search--dropdown {
    height: 45px;
    display: flex;
}

.select2-container--open .select2-dropdown--below {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.select2-results__option {
    font-family: $font-family;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    font-family: $font-family;
}

//header search job
.item-multiple-select2 {
    line-height: 41px;
    height: 47px;

    select {
        display: none;
    }

    .select2-container {
        line-height: 41px;
        height: 45px;
    }

    .select2-container .select2-selection--multiple {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        min-height: 55px;
        user-select: none;
        -webkit-user-select: none;
    }

    .select2-container--default .select2-selection--multiple {
        border: none;
        cursor: text;
        padding-bottom: 5px;
        padding-right: 5px;
        position: relative;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0px 4px 4px rgba(51, 51, 51, 4%), 0px 4px 16px rgba(51, 51, 51, 8%);
        z-index: 9;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        background-color: #fff;
        border: none;
        color: #495057;
        outline: 0;
    }

    .select2-container--default .select2-selection--multiple {
        background: $color-white url("../images/list-new/arrow-select.svg") no-repeat;
        background-position: center right 15px;
    }

    .select2-container--default .select2-search--inline .select2-search__field {
        height: 50px;
        position: absolute;
        line-height: 49px;
        font-family: $font-family;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        color: $color-palette7;
        padding-left: 12px;
    }

    .select2-selection__rendered {
        line-height: 20px !important;
    }

    .select2-container .select2-selection--single {
        height: 20px !important;
    }

    .select2-selection__arrow {
        height: 20px !important;
    }

    .select2-container .select2-selection--multiple .select2-selection__rendered {
        display: list-item;
        position: absolute;
        top: 11px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        padding-left: 10px;
        padding-right: 20px;
        border-radius: 15px;
        background: $color-white;
        border-color: $color-palette3;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        left: auto;
        right: 0;
        color: $color-brand;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__clear {
        display: none;
    }
}

#wapper-serch-job-mobile {
    display: none;
}

#search-job-group {
    padding: 24px 0;
    background: rgba(220, 220, 220, 0.25);

    .filter-search {
        background: none;
        border: none;
        height: 100%;
    }

    .field-search-job {
        width: 100%;
        background: $color-white url("../images/search-job/search-job.svg") no-repeat;
        background-position: left 16px center;
        padding-left: 44px;
        border: none;
        outline: none;
        box-shadow: 0px 4px 4px rgba(51, 51, 51, 0.04), 0px 4px 16px rgba(51, 51, 51, 0.08);
        border-radius: 8px;
        height: 56px;
    }
}

/* The switch - the box around the slider */
.checkbox-remote {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #000000;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 12px;

    input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    /* The slider */
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 20px;
        width: 20px;
        left: 3px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
        top: 2px;
    }

    input:checked+.slider {
        background-color: $color-brand;
    }

    input:focus+.slider {
        box-shadow: 0 0 1px $color-brand;
    }

    input:checked+.slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }
}

#search-job-group {
    .wapper-last-form {
        display: flex;
        align-items: center;
        height: 100%;
        position: relative;
        margin-left: -50px;
        justify-content: space-between;
    }

    .button-search-job {
        button {
            background: $color-primary1;
            box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
            border-radius: 30px;
            width: 164px;
            height: 56px;
            border: none;
            color: $color-palette4;
        }
    }

    .main-select {
        padding-right: 50px;
    }

    .modal-filter {
        z-index: 9;
        width: 600px;
        position: absolute;
        height: revert;
        right: 0;
        background: #fff;
        padding: 24px;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 25%);
        margin-right: 320px;
        top: 80px;
        border-radius: 4px;

        label {
            font-style: normal;
            font-weight: bold;
            font-size: 14px;
            line-height: 20px;
            display: flex;
            align-items: center;
            color: $color-palette1;
            margin-bottom: 6px;
        }

        .item-filter {
            margin-bottom: 24px;
        }
    }
}

#search-job-group-mobile {
    padding: 24px 0;
    background: rgba(255, 233, 208, 0.25);

    .group-select {
        white-space: nowrap;
        overflow-x: scroll;
        margin-bottom: 12px;
        padding-bottom: 5px;
    }

    .group-select::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }

    .item-select {
        margin-right: 25px;
        display: inline-block;
    }

    .text-search-group {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-secondary1;
        padding-bottom: 13px;
        border-bottom: 1px solid $color-palette3;
        margin-bottom: 18px;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: $color-palette7;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered,
    .select2-container .select2-selection--single,
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 14px;
        color: $color-palette7;
        padding: 5px 15px 5px 5px;
        display: inline;
        background: none;
        box-shadow: none;
    }

    .select2-container .select2-selection--single {
        border: 1px solid $color-palette4v2;
        border-radius: 50px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 100%;
    }

    .select2-container {
        display: inline;
        height: 30px;
        line-height: 30px;
    }

    //.switch {
    //    height: 17px;
    //    width: 34px;
    //    .slider:before{
    //        width: 13px;
    //        height: 13px;
    //    }
    //    input:checked + .slider:before {
    //        transform: translateX(15px);
    //    }
    //}


    .group-radio {
        width: 100%;

        input[type="checkbox"] {
            display: none;

            &:checked {
                +.box {
                    color: $color-palette4;
                    background: $color-brand1;
                }
            }
        }

        .box {
            min-width: 76px;
            padding: 0 15px;
            height: 40px;
            text-align: center;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            background: $color-white;
            margin-right: 16px;
            border: 1px solid $color-palette3;
            border-radius: 8px;
            font-size: 12px;

            &:active {}

            span {

                &:before {
                    display: block;
                    opacity: 0;
                    transition: all 300ms ease-in-out;
                    font-weight: normal;
                    color: $color-palette1;
                }
            }
        }
    }

    .checkbox-remote {
        font-size: 12px;
        line-height: 16px;
    }

    .group-select-bottom {
        display: flex;
        gap: 45px;
    }

    .field-checkbox-st1-checkmark {
        background-color: unset;
    }

    .text-search-mobile-on-change {
        position: relative;
        margin-right: 24px;
    }

    .text-search-mobile-on-change:before {
        content: '';
        width: 4px;
        height: 4px;
        background: $color-palette1;
        position: absolute;
        right: -12px;
        top: 8px;
        border-radius: 50%;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow b {
        top: 58%;
    }

    .button-reset {
        a {
            min-width: 76px;
            padding: 0 15px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            cursor: pointer;
            justify-content: center;
            align-items: center;
            color: $color-palette4;
            background: $color-brand1;
            border: 1px solid $color-brand1;
            border-radius: 8px;
            font-size: 12px;
            display: inline-block;
        }

    }
}

.select2-search-mobile {
    width: calc(100vw - 30px) !important;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;

    .select2-container--default .select2-selection--single .select2-selection__clear {
        display: none;
    }

    .select2-container--default .select2-results>.select2-results__options {
        border-radius: 8px !important;
    }

    max-width: 100vw;

    .select2-results {
        border-radius: 8px;
        overflow: hidden;
    }
}

.content-drop-select-2-mobile {
    left: 15px !important;
}

.input-checkbox-item-career-mobile {
    margin-right: 15px;
}

.input-checkbox-item-career-mobile-checkmark {
    position: absolute;
    top: 1px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: $color-white;
    border: 1px solid $color-brand;
    border-radius: 3px;
}

.input-checkbox-item-career-mobile {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.group-input-checkbox-item-career-mobile .input-checkbox-item-career-mobile:checked~.input-checkbox-item-career-mobile-checkmark {
    background-color: $color-brand;
    background: url(../images/icon-checked.svg) no-repeat;
    background-position: center;
    background-size: 76%;
}

.group-input-checkbox-item-career-mobile .input-checkbox-item-career-mobile:disabled~.input-checkbox-item-career-mobile-checkmark {
    background: rgba(220, 220, 220, 0.25);
    border: 1px solid rgba(220, 220, 220, 0.25);
}

.group-input-checkbox-item-career-mobile {
    font-style: normal;
    font-weight: bold;
    font-size: 14px;
    line-height: 20px;
    align-items: center;
    color: $color-palette1;
    margin-bottom: 6px;
    position: relative;
    height: 16px;
    width: 16px;
    margin-right: 25px;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    background-color: initial;
    color: initial;
}

.multiple-select2-mobile {
    .select2-selection__clear {
        display: none;
    }
}

.select2-container--open:has(> .select2-search-mobile-custom-only) {
    left: 15px !important;
}


#search-job-group-mobile-layout2 {
    padding: 17px 0;
    position: relative;

    .field-search-job {
        padding-left: 38px;
        padding-right: 15px;
        padding-top: 5px;
        padding-bottom: 5px;
        border: none;
        border-bottom: 1px solid $color-secondary1;
        outline: none;
        width: 100%;
        background: $color-white url("../images/search-job/search-job.svg") no-repeat;
        background-position: left 5px center;
        background-size: 10px;
    }

    .field-item-mobile {
        margin-bottom: 18px;
    }

    select {
        display: none;
    }

    .select2-container {
        line-height: 30px;
        height: 30px;
    }

    .select2-container .select2-selection--multiple {
        box-sizing: border-box;
        cursor: pointer;
        display: block;
        min-height: 30px;
        user-select: none;
        -webkit-user-select: none;
    }

    .select2-container--default .select2-selection--multiple {
        border: none;
        cursor: text;
        padding-bottom: 5px;
        padding-right: 5px;
        position: relative;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: none;
        z-index: 9;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        background-color: #fff;
        border: none;
        color: #495057;
        outline: 0;
    }

    .select2-container--default .select2-selection--multiple {
        background-position: center right 10px;
    }

    .select2-container--default .select2-search--inline .select2-search__field {
        height: 30px;
        position: absolute;
        line-height: 30px;
        font-family: $font-family;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        color: $color-palette7;
        padding-left: 12px;
    }

    .select2-selection__rendered {
        line-height: 20px !important;
    }

    .select2-container .select2-selection--single {
        height: 20px !important;
    }

    .select2-selection__arrow {
        height: 20px !important;
    }

    .select2-container .select2-selection--multiple .select2-selection__rendered {
        display: list-item;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        padding-left: 10px;
        padding-right: 20px;
        border-radius: 15px;
        background: $color-white;
        border-color: $color-palette3;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        left: auto;
        right: 9px;
        border-right: 0;
        padding: 0;
        color: $color-brand;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__clear {
        display: none;
    }

    .item-multiple-select2-mobile {
        padding-left: 24px;
        border-bottom: 1px solid $color-secondary1;
        padding-bottom: 4px;
        background: url("../images/search-job/icon-search-address-mobile.svg") no-repeat;
        background-position: left 5px center;
    }

    .search-job-group-mobile-layout2 {
        padding-left: 26px;
    }

    .back-search-mobile {
        position: absolute;
        top: 23px;
        left: 12px;
    }
}

.select2-address-mobile {
    top: 15px;
}

#search-job-group-v2 {
    padding: 24px 0;
    background: $color-st25;
    margin-bottom: 27px;

    .row-search-job {
        display: flex;
        justify-content: space-between;
        gap: 24px;
    }

    .button-open-filter {
        width: 148px;
        height: 56px;
        padding: 16px 35px;
        text-align: left;
        color: $color-palette1;
        background: url("../images/search-job/icon-button-open-filter.svg") no-repeat $color-palette4;
        background-position: right 35px center;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: none;
    }

    .button-search {
        width: 148px;
        height: 56px;
        padding: 10px 10px;
        background: $color-primary1;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: none;
        color: $color-palette4;
    }

    .button-row-search-job {
        display: flex;
        gap: 24px;
        width: 320px;
    }

    .group-input-search-job {
        width: calc(100% - 320px);
    }

    .field-search-job {
        width: 100%;
        background: $color-white url(../images/search-job/search-job.svg) no-repeat;
        background-position: left 16px center;
        padding-left: 44px;
        border: none;
        outline: none;
        box-shadow: 0px 4px 4px rgba(51, 51, 51, 4%), 0px 4px 16px rgba(51, 51, 51, 8%);
        border-radius: 8px;
        height: 56px;
    }
}

#modal-filter-v2 {
    width: 478px;
    //height: 841px;
    position: absolute;
    top: 194px;
    background: $color-white;
    z-index: 9;
    background: $color-palette4;
    box-shadow: -10px 0px 26px rgba(112, 131, 187, 0.45);
    border-radius: 8px;
    right: -500px;
    transition: 0.5s;

    .header-modal-filter {
        color: $color-palette1;
        font-style: normal;
        font-weight: 800;
        font-size: 18px;
        line-height: 26px;
        padding: 15px 24px;
        border-bottom: 1px solid $color-palette3;
    }

    .content-modal-filter {
        padding: 20px 24px;
        border-bottom: 1px solid $color-palette3;
    }


    .group-radio {

        margin-bottom: 24px;
        width: 100%;

        input[type="checkbox"] {
            display: none;

            &:checked {
                +.box {
                    color: $color-palette4;
                    background: $color-brand1;
                }
            }
        }

        .box {
            min-width: 120px;
            padding: 0 15px;
            height: 40px;
            text-align: center;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            background: $color-white;
            margin-right: 24px;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;

            &:active {}

            span {

                &:before {
                    font-size: 1.2em;
                    display: block;
                    opacity: 0;
                    transition: all 300ms ease-in-out;
                    font-weight: normal;
                    color: $color-palette1;
                }
            }
        }
    }

    .item-filter {
        margin-bottom: 24px;

        label {
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-st26;
            margin-bottom: 6px;
            display: block;
        }
    }

    .select2-container .select2-selection--single {
        width: 100%;
        height: 46px;
        background: $color-palette4;
        /* #4 Effect / Shadow 6 */
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__clear {
        display: none;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 44px;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        color: $color-palette7;
    }

    #select2-salary-desktop-select2-container {
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        border-radius: 8px;
        padding-left: 12px;
    }

    .select2-selection__arrow {
        display: none;
    }

    .select2-container--default .select2-results__option--selected {
        background: $color-white;
    }

    .select2-container--open {
        #select2-salary-desktop-select2-container {
            background-color: $color-brand1;
            background-image: url(../images/search-job/arrow-up.svg);
            background-repeat: no-repeat;
            color: $color-palette4;
        }
    }

    .select2-container--below {
        #select2-salary-desktop-select2-container {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .select2-container--above {
        #select2-salary-desktop-select2-container {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
    }


    #select2-rank-desktop-select2-container {
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        border-radius: 8px;
        padding-left: 12px;
    }

    .select2-container--open {
        #select2-rank-desktop-select2-container {
            background-color: $color-brand1;
            background-image: url(../images/search-job/arrow-up.svg);
            background-repeat: no-repeat;
            color: $color-palette4;
        }
    }

    .select2-container--below {
        #select2-rank-desktop-select2-container {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .select2-container--above {
        #select2-rank-desktop-select2-container {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
    }

    #select2-bonus-type-desktop-select2-container {
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        border-radius: 8px;
        padding-left: 12px;
    }

    .select2-container--open {
        #select2-bonus-type-desktop-select2-container {
            background-color: $color-brand1;
            background-image: url(../images/search-job/arrow-up.svg);
            background-repeat: no-repeat;
            color: $color-palette4;
        }
    }

    .select2-container--below {
        #select2-bonus-type-desktop-select2-container {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .select2-container--above {
        #select2-bonus-type-desktop-select2-container {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
    }


    #select2-bonus-desktop-select2-container {
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        border-radius: 8px;
        padding-left: 12px;
    }

    .select2-container--open {
        #select2-bonus-desktop-select2-container {
            background-color: $color-brand1;
            background-image: url(../images/search-job/arrow-up.svg);
            background-repeat: no-repeat;
            color: $color-palette4;
        }
    }

    .select2-container--below {
        #select2-bonus-desktop-select2-container {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .select2-container--above {
        #select2-bonus-desktop-select2-container {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
    }


    #select2-type-desktop-select2-container {
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        border-radius: 8px;
        padding-left: 12px;
    }

    .select2-container--open {
        #select2-type-desktop-select2-container {
            background-color: $color-brand1;
            background-image: url(../images/search-job/arrow-up.svg);
            background-repeat: no-repeat;
            color: $color-palette4;
        }
    }

    .select2-container--below {
        #select2-type-desktop-select2-container {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    .select2-container--above {
        #select2-type-desktop-select2-container {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
    }


    .select2-search__field {
        height: 46px;
        position: absolute;
        line-height: 46px;
        font-family: "Manrope", sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        color: $color-palette7;
        padding-top: 0;
        padding-left: 0;
        margin-left: 0;
        margin-top: 0;
    }

    .select2-search__field::-webkit-input-placeholder {
        /* Edge */
        color: $color-palette7;
    }

    .select2-search__field:-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: $color-palette7;
    }

    .select2-search__field::placeholder {
        color: $color-palette7;
    }

    .select2-selection--multiple {
        width: 100%;
        height: 46px;
        background-color: $color-palette4;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
        border-radius: 8px;
        border: none;
        padding-bottom: 0;
        background-image: url(../images/list-new/arrow-select.svg);
        background-repeat: no-repeat;
        background-position: center right 15px;
        padding-left: 12px;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        border: none;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__clear {
        display: none;
    }

    .select2-container .select2-selection--multiple .select2-selection__rendered {
        position: absolute;
        height: 100%;
        display: flex;
        align-items: center;
        flex-flow: row wrap;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        padding-left: 10px;
        padding-right: 20px;
        border-radius: 15px;
        height: 21px;
        background: $color-white;
        border-color: $color-palette3;
        margin-top: 0;
        margin-left: 0;
        margin-right: 5px;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
        left: auto;
        right: 9px;
        border-right: 0;
        padding: 0;
        color: $color-brand;
    }

    .footer-modal-filter {
        display: flex;
        justify-content: flex-end;
        padding: 22px 24px;

        .button-submit {
            min-width: 159px;
            padding: 0 5px;
            height: 46px;
            border: 1px solid $color-brand1;
            background: $color-brand1;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            color: $color-white;
            margin-left: 24px;
        }

        .button-reset {
            width: 120px;
            padding: 0 5px;
            height: 46px;
            border: 1px solid $color-brand1;
            background: $color-white;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            color: $color-brand1;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .close-modal-filter {
        background-image: url(../images/search-job/close-modal-filter.svg);
        width: 32px;
        height: 32px;
        position: absolute;
        right: 24px;
    }

}

#select2-salary-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}

.select2-salary-desktop-select2-results-drop {
    max-width: 478px;
}

.item-salary-desktop-select2 {
    width: 100%;
    display: block;
}

#select2-rank-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}

.select2-rank-desktop-select2-results-drop {
    max-width: 478px;
}

.item-rank-desktop-select2 {
    width: 100%;
    display: block;
}

#select2-bonus-type-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}

.select2-bonus-type-desktop-select2-results-drop {
    max-width: 478px;
}

.item-bonus-type-desktop-select2 {
    width: 100%;
    display: block;
}

#select2-bonus-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}

.select2-bonus-desktop-select2-results-drop {
    max-width: 478px;
}

.item-bonus-desktop-select2 {
    width: 100%;
    display: block;
}


#select2-type-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}

.select2-type-desktop-select2-results-drop {
    max-width: 478px;
}

.item-type-desktop-select2 {
    width: 100%;
    display: block;
}

#select2-company-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}


.select2-company-desktop-select2-results-drop {
    max-width: 478px;
}

.item-company-desktop-select2 {
    width: 100%;
    display: block;
}

#select2-career-desktop-select2-results {
    max-width: 478px;
    overflow-x: hidden;

    .select2-results__option--selected {
        background: $color-white;
    }

    .select2-results__option {
        height: 40px;
    }

    .select2-results__option:hover {
        background: $color-palette4;
        box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
    }
}


.select2-career-desktop-select2-results-drop {
    max-width: 478px;
}

.item-career-desktop-select2 {
    width: 100%;
    display: block;
}



//#select2-company-desktop-select2-container {
//    ul{
//        line-height: 20px !important;
//        display: contents;
//        position: absolute;
//        top: 13px;
//        li{
//            display: inline-block;
//            padding-left: 10px;
//            padding-right: 20px;
//            border-radius: 15px;
//            background: $color-white;
//            border:1px solid $color-palette3;
//            cursor: default;
//            font-size: 13px;
//            position: relative;
//            .main-text-item-selected{
//
//            }
//            .button-close{
//                color: $color-brand;
//                right: 0;
//                position: absolute;
//                top: -1px;
//                padding: 0 5px;
//                z-index: 99;
//                cursor: pointer;
//            }
//        }
//
//    }
//}



.radio-single-desktop-select2 {
    display: block;
    position: relative;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .checkmark {
        position: absolute;
        top: 3px;
        right: 4px;
        height: 15px;
        width: 15px;
        border-radius: 50%;
        background-color: $color-white;
        border: 1px solid $color-palette1;
    }

    input:checked~.checkmark {
        background-color: $color-white;
        border: 1px solid $color-st6;
    }

    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    .checkmark:after {
        top: 2.2px;
        left: 2.2px;
        width: 8.75px;
        height: 8.75px;
        border-radius: 50%;
        background: $color-st6;
    }
}

.checkbox-single-desktop-select2 {
    display: block;
    position: relative;
    cursor: pointer;
    font-size: 22px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .checkmark {
        position: absolute;
        top: 3px;
        right: 4px;
        height: 15px;
        width: 15px;
        border-radius: 30%;
        background-color: $color-white;
        border: 1px solid $color-palette1;
    }

    input:checked~.checkmark {
        background-color: $color-white;
        border: 1px solid $color-st6;
        background-image: url("../images/search-job/icon-checked-checkbox.svg");
        background-repeat: no-repeat;
        background-position: center;
    }

    input:checked~.checkmark:after {
        display: block;
    }

    .checkmark:after {
        top: 2.2px;
        left: 2.2px;
        width: 8.75px;
        height: 8.75px;
        border-radius: 30%;
        background: $color-st6;
    }
}

#main-col-job {
    //padding-right: 12px;
    position: relative;
    margin-right: -22px;

    .header-col-job {
        margin-bottom: 20px;
    }

    .number-job {
        img {
            margin-right: 12px;
        }

        span {
            font-weight: bold;
            margin-right: 6px;
        }

        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        display: flex;
        align-items: center;
        color: $color-palette1;
    }

    .order-job {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: $color-palette7;
        position: relative;
        padding-right: 28px;
    }

    .order-job:after {
        content: '';
        position: absolute;
        background: url("../images/search-job/arrow-drop.svg") no-repeat;
        width: 9px;
        height: 5px;
        background-position: center;
        right: 10px;
        top: 8px;
    }

    .item-col-job {
        display: flex;
        align-items: center;
        border-bottom: 1px solid $color-palette3;
        padding: 12px 12px;
        border-radius: 6px;
        margin-bottom: 16px;
        border-right: 2px solid $color-white;
        cursor: pointer;
        transition: 0.3s;
    }

    .item-col-job:hover {
        border-right: 2px solid $color-primary2;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
    }

    .item-col-job.active {
        border-right: 2px solid $color-primary2;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
    }

    .list-col-job {
        margin-bottom: 80px;
        max-height: 980px;
        overflow-y: hidden;
        //padding-right: 17px;
        padding-left: 4px;
        position: relative;
        margin-left: -4px;
        padding-top: 5px;
        margin-top: -5px;

        .logo {
            max-width: 50px;
            min-width: 50px;
            text-align: center;
        }
    }

    .list-col-job:hover {
        //padding-right: 14px;
        overflow-y: scroll;
    }

    .main-item-job {
        padding-left: 12px;
        width: calc(100% - 50px);

        .title-job {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-palette1;
            margin-bottom: 6px;
        }

        .company-job {
            font-style: normal;
            font-weight: 800;
            font-size: 12px;
            line-height: 16px;
            color: $color-secondary1;
            margin-bottom: 6px;
        }

        .address {
            font-style: normal;
            font-weight: 600;
            font-size: 12px;
            line-height: 22px;
            color: $color-secondary1;

            img {
                margin-right: 5px;
                position: relative;
                top: -1px;
            }
        }

        .btn-banner-st1,
        .btn-banner-st2,
        .btn-banner-st3,
        .btn-banner-st4,
        .btn-banner-st5 {
            font-size: 10px;
        }

        .salary-job {
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            color: $color-secondary1;
            margin-bottom: 6px;

            img {
                margin-right: 5px;
                position: relative;
                top: -1px;
            }

            span {
                color: $color-st6;
            }

            sup {
                font-size: 10px;
                color: $color-st6;
            }
        }

        .bonus-job {
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
            color: $color-secondary1;
            margin-bottom: 6px;

            img {
                margin-right: 5px;
                position: relative;
                top: -1px;
            }

            span {
                color: $color-st6;
            }

            sup {
                font-size: 10px;
                color: $color-st6;
            }
        }
    }

    .row-bottom-job {
        display: flex;
        justify-content: space-between;
    }

    .bottom-col-job {
        display: flex;
        justify-content: center;
        align-items: center;

        .paginate {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 25px;

            .paginate_prev,
            .paginate_next {
                width: 25px;
                height: 25px;
                background-image: url(../images/icon-arrow.svg);
                background-size: 100%;
                background-repeat: no-repeat;
                cursor: pointer;
            }

            .paginate_prev:hover,
            .paginate_next:hover {
                background-image: url(../images/icon-arrow-hover.svg);
            }

            .paginate_next {
                transform: scaleX(-1);
            }

            .number {
                padding: 0 26px;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
            }
        }

        .goto {
            padding: 0 25px;
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-palette1;

            input {
                color: $color-palette1;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                width: 56px;
                text-align: center;
                height: 50px;
            }
        }

    }

    .btn-col-bottom-job-mobile {
        display: none;
    }
}

#main-search-job {
    margin-bottom: 60px;

    #banner {
        height: 287px;
        border-radius: 6px;
        overflow: hidden;

        .main-banner {
            padding: 12px 24px;

            .title-banner {
                font-weight: 800;
                font-size: 20px;
                line-height: 28px;
                margin-bottom: 6px;
            }

            .small-title-banner {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
            }
        }
    }

    #detail-company {
        .detail-company {
            .main-detail-company {
                .line-info-company {
                    .label-list-info-company {
                        font-size: 16px;
                    }
                }
            }
        }
    }

    .back-mobile {
        align-items: center;
        background-color: transparent;
        border: none;
        color: $color-st14;
        font-size: 14px;
        font-weight: 400;
        height: 50px;
        line-height: 18px;
        padding: 0 15px;
        width: 100%;
        display: none;
    }

    #content-main-search-job {
        max-width: 862px;
        margin-left: auto;
    }
}

#list-info-job-search-job {
    padding-left: 34px;
    margin-bottom: 60px;

    .item-info-job {
        position: relative;
        margin-bottom: 16px;
        width: 33.333333%;
        padding-right: 40px;

        .line1 {
            color: $color-palette7;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            margin-bottom: 6px;
        }

        .line2 {
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-secondary1;

            sup,
            sub {
                font-size: 10px;
            }
        }
    }

    .item-info-job:last-child {
        margin-bottom: 0;
    }

    .item-info-job:before {
        content: '';
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
        position: absolute;
        top: 1px;
        left: -30px;
    }

    .icon-bag:before {
        background-image: url(../images/icon-bag.svg);
    }

    .icon-user:before {
        background-image: url(../images/icon-user2.svg);
    }

    .icon-level:before {
        background-image: url(../images/icon-level.svg);
    }

    .icon-address:before {
        background-image: url(../images/icon-address.svg);
    }

    .icon-calendar:before {
        background-image: url(../images/icon-calendar.svg);
    }

    .icon-clock:before {
        background-image: url(../images/icon-clock.svg);
    }

    .icon-money:before {
        background-image: url(../images/icon-money.svg);
    }

    .icon-money-hand:before {
        background-image: url(../images/icon-money-hand.svg);
    }

    .icon-number-user:before {
        background-image: url(../images/icon-number-user.svg);
    }

    .icon-number-remote:before {
        background-image: url(../images/icon-remote.svg);
    }

    .job-skill {
        padding: 0 12px 12px;
        display: flex;
        gap: 12px;

        span {
            font-style: normal;
            font-weight: 600;
            font-size: 12px;
            line-height: 22px;
            color: $color-primary1;
            padding: 2px 12px;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 12px;
            background: $color-palette4;
        }
    }

    ul {
        display: flex;
        flex-wrap: wrap;
    }

    .item-info-job {}

}

#wapper-content-job {
    background: $color-white;
    border-radius: 6px;
    height: 100%;

    .main-content-job {
        margin-bottom: 60px;
    }

    .item-content-job {
        border-bottom: 1px solid $color-palette3;
        margin-bottom: 24px;
        padding-bottom: 24px;
    }

    .item-content-job:last-child {
        border-bottom: 0;
        margin-bottom: 0;
    }

    .job-content-skill {
        border-bottom: 1px solid $color-palette3 !important;
    }

    .title-content-job {
        margin-bottom: 15px;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette2;
    }

    .edit-content-job {
        color: $color-secondary1;
        font-size: 16px;

        ul {
            padding-left: 35px;
            list-style: disc;
        }

        p,
        li {
            line-height: 24px;
            margin-bottom: 16px;
        }

        p:last-child,
        li:last-child {
            margin-bottom: 0;
        }
    }

    .main-list-job-content {
        margin-bottom: 0;
    }
}

.banner-search-job img {
    max-width: 100%;
}

#list-new {
    background: $color-st10;

    .line-1-col-hot-new {
        margin-bottom: 12px;

        .category {
            padding: 6px 12px;
            font-weight: 700;
            font-size: 12px;
            line-height: 14px;
            color: $color-palette4;
            background: $color-st9;
            display: inline-block;
            margin-right: 17px;
        }

        .date {
            display: inline-block;
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 14px;
            color: $color-secondary1;

            img {
                margin-right: 7px;
                position: relative;
                top: -1px;
                width: auto;
                display: inherit;
            }
        }
    }

    .title-banner {
        font-style: normal;
        font-weight: 800;
        font-size: 18px;
        line-height: 26px;
        color: $color-secondary1;
        margin-bottom: 17px;
    }
}

.item-skill-job {
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 22px;
    color: $color-primary1;
    padding: 2px 12px;
    box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
    border-radius: 12px;
    background: $color-palette4;
}

#hot-new {
    padding: 48px 0;
    $shadow-item: 0px 2px 6px rgba(121, 132, 165, 0.25);

    .col-hot-new-1 {
        background: $color-white;
        margin-right: 27px;
        height: 100%;
        box-shadow: $shadow-item;
        border-radius: 4px;
        overflow: hidden;

        .image-col-hot-new-1 {
            height: 210px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .main-col-hot-new-1 {
            padding: 24px;
        }

        .title {
            font-style: normal;
            font-weight: 800;
            font-size: 18px;
            line-height: 32px;
            color: $color-palette1;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }

    }

    .col-hot-new-2 {
        .image-main-hot-new-2 {
            height: 360px;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        position: relative;
        margin-left: -13px;
        margin-right: 99px;

        .main-hot-new-2 {
            box-shadow: $shadow-item;
            border-radius: 4px;
            overflow: hidden;
            position: relative;

        }

        .wapper-content-hot-new-2 {
            background: rgba(15, 10, 34, 0.5);
            padding: 24px;
            position: absolute;
            bottom: 0;
            width: 100%;

            .title {
                font-style: normal;
                font-weight: 800;
                font-size: 18px;
                line-height: 32px;
                color: $color-palette4;
            }
        }

        .date {
            color: $color-palette4;
        }

    }

    .col-hot-new-3 {
        position: relative;
        margin-left: -84px;

        .item-col-hot-new-3 {
            position: relative;
            margin-bottom: 24px;
            box-shadow: $shadow-item;
            border-radius: 4px;
            overflow: hidden;

            .content {
                position: absolute;
                top: 0;
                width: 100%;
                height: 100%;
                background: rgba(7, 5, 16, 0.5);
                padding: 31px 21px;

                .title {
                    font-weight: 800;
                    font-size: 18px;
                    line-height: 32px;
                    color: $color-palette4;
                }
            }

            .date {
                color: $color-palette4;
            }

            .image-main-hot-new-2 {
                height: 168px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .item-col-hot-new-3:last-child {
            margin-bottom: 0;
        }

    }

}

#tags-new {

    .tags-new {
        padding: 32px 24px;
        background: $color-white;
    }

    .header-tab-new {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        position: relative;
        margin-bottom: 32px;

        .title {
            position: relative;
            font-style: normal;
            font-weight: 800;
            font-size: 28px;
            line-height: 36px;
            color: $color-palette1;

            span {
                background: $color-white;
                position: relative;
                z-index: 1;
                padding-right: 48px;
            }

        }

        .tabs-title {
            position: relative;
            z-index: 1;
            background: #fff;
            padding-left: 48px;
            padding-top: 2px;

            li {
                margin-right: 48px;
            }

            a {
                font-style: normal;
                font-weight: 800;
                font-size: 18px;
                line-height: 26px;
                color: $color-secondary1;
            }

        }
    }

    .header-tab-new:before {
        content: "";
        width: 99%;
        position: absolute;
        border-bottom: 3px solid $color-palette3;
        top: 15px;
        right: 0;
    }

    .main-tab {
        .item-tab {
            .image-item-tab {
                margin-bottom: 22px;

                img {
                    width: 100%;
                    height: 200px;
                }
            }

            .title {
                font-style: normal;
                font-weight: 800;
                font-size: 18px;
                line-height: 32px;
                color: $color-palette1;
            }
        }

        .row-tab {
            margin-left: -17px;
            margin-right: -17px;

            .col-tab {
                padding-left: 17px;
                padding-right: 17px;
            }
        }
    }
}

#col-post-video {
    height: 100%;

    .col-post-video {
        height: 100%;
        background: $color-white;
        padding: 32px 24px;
        position: relative;
        margin-right: -20px;
    }

    .header-post-video {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        margin-bottom: 32px;
        color: $color-palette1;
    }

    .video-item {
        .thumb-video-item {
            position: relative;
            margin-bottom: 28px;

            iframe {
                height: 200px;
            }
        }

        .thumb-video-item:before {
            //content: '';
            //background: url("../images/list-new/icon-video.svg") no-repeat;
            //width: 48px;
            //height: 48px;
            //position: absolute;
            //top: 16px;
            //right: 16px;
        }

        .title {
            font-style: normal;
            font-weight: 800;
            font-size: 18px;
            line-height: 32px;
            color: $color-palette1;
        }
    }

    .col-item-video {
        margin-bottom: 48px;
    }

    .col-item-video:last-child {
        margin-bottom: 0;
    }
}

#col-trending-video {
    .col-trending-video {
        background: $color-white;
        padding: 32px 24px;
        position: relative;
        margin-left: 20px;

        .header-trending-video {
            font-style: normal;
            font-weight: 800;
            font-size: 28px;
            line-height: 36px;
            color: $color-palette1;
            position: relative;
            margin-bottom: 32px;

            span {
                padding-right: 48px;
                background: $color-white;
                position: relative;
                z-index: 1;
            }
        }

        .header-trending-video:before {
            content: '';
            border-bottom: 3px solid $color-palette3;
            position: absolute;
            top: 17px;
            width: 90%;
            left: 20px;
        }

        .image-big-video {
            position: relative;

            iframe {
                height: 629px;
            }

            img {
                width: 100%;
            }
        }

        .image-big-video:before {
            //content: '';
            //background: url("../images/list-new/icon-video.svg") no-repeat;
            //background-size: 100%;
            //width: 68px;
            //height: 68px;
            //position: absolute;
            //top: 90px;
            //left: 20px;
        }

        .wapper-big-video {
            position: relative;
        }

        .content-big-video {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 24px 20px;

            .category {
                background: $color-primary1;
                border-radius: 12px;
            }

            .date {
                color: $color-white;
            }

            .title {
                color: $color-palette4;
                font-style: normal;
                font-weight: 800;
                font-size: 18px;
                line-height: 32px;
            }
        }
    }
}

#social-new {
    padding: 32px 24px 48px;
    background: $color-white;
    margin-bottom: 17px;

    .header-social-new {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
        margin-bottom: 32px;
        position: relative;

        span {
            padding-right: 48px;
            background: $color-white;
            position: relative;
        }
    }

    .header-social-new:before {
        content: "";
        border-bottom: 3px solid $color-palette3;
        position: absolute;
        top: 17px;
        width: 80%;
        left: 20px;
    }

    .slider-social-new {
        .item {
            position: relative;
        }

        .thumb-social-item {}

        .icon-social {
            width: 40px;
            position: absolute;
            top: 12px;
            left: 12px;
        }

        .owl-nav {
            position: absolute;
            right: 50px;
            top: -78px;
        }

        .owl-nav [class*='owl-'] {
            width: 24px;
            height: 24px;
            background: $color-palette5;
            font-size: 20px;
            color: $color-secondary1;
        }
    }
}

.slider-advertisement {
    img {
        max-height: 100px;
        width: auto !important;
        margin: 0 auto;
    }
}

#list-recent-new {
    .list-recent-new {
        padding: 32px 24px;
        background: $color-white;

        .header {

            span {}

            .title {
                position: relative;
                font-style: normal;
                font-weight: 800;
                font-size: 28px;
                line-height: 36px;
                color: $color-palette1;
                margin-bottom: 32px;

                span {
                    padding-right: 48px;
                    position: relative;
                    z-index: 1;
                    background: $color-white;
                }
            }

            .title:before {
                content: "";
                border-bottom: 3px solid $color-palette3;
                position: absolute;
                top: 17px;
                width: 80%;
                left: 20px;
            }
        }

    }

    .main-list-recent-new {
        margin-bottom: 30px;

        .item-recent-new {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 24px;

            .thumbnail {
                width: 300px;
                height: 200px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .content {
            padding-top: 22px;
            width: calc(100% - 300px);
            padding-left: 24px;

            .title {
                font-style: normal;
                font-weight: 800;
                font-size: 18px;
                line-height: 32px;
                color: $color-palette1;
                margin-bottom: 12px;
            }

            .description {
                font-style: normal;
                font-size: 14px;
                line-height: 22px;
                color: $color-secondary1;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .bottom-list-new {
        display: flex;
        align-items: center;

        .paginate {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-right: 25px;

            .paginate_prev,
            .paginate_next {
                width: 25px;
                height: 25px;
                background-image: url(../images/icon-arrow.svg);
                background-size: 100%;
                background-repeat: no-repeat;
                cursor: pointer;
            }

            .paginate_prev:hover,
            .paginate_next:hover {
                background-image: url(../images/icon-arrow-hover.svg);
            }

            .paginate_next {
                transform: scaleX(-1);
            }

            .number {
                padding: 0 26px;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
            }
        }

        .goto {
            padding: 0 25px;
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-palette1;

            input {
                color: $color-palette1;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                width: 56px;
                text-align: center;
                height: 50px;
            }
        }
    }
}

#group-new-list {
    padding-bottom: 48px;
}

#main-no-job {
    .main-no-job {
        padding-bottom: 300px;
    }

    .line-1 {
        font-style: normal;
        font-weight: 400;
        font-size: 38px;
        line-height: 52px;
        color: $color-secondary1;
    }

    .line-2 {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 48px;
        text-align: center;
        color: $color-secondary1;
    }
}


.widget-social-count {
    padding: 32px 24px;
    background: $color-white;

    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
        margin-bottom: 32px;
    }

    ul {
        li {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: $color-secondary1;
            padding: 12px;
            background: $color-st11;

            .number {
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
            }

            .icon {
                margin-right: 17px;
            }
        }
    }

}

.widget-most-poplular {
    padding: 32px 16px;
    background: $color-white;

    .header {
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        //display: flex;
        //justify-content: space-between;
        margin-bottom: 32px;

        .nav {
            display: flex;
            justify-content: space-between;
        }

        .title {
            color: $color-palette3;
        }

        .active {
            .title {
                color: $color-primary1v2;
            }

        }

        .description {
            color: $color-palette3;
        }
    }

    .tab-content {
        ul {
            li {
                display: flex;
                gap: 16px;
                margin-bottom: 24px;

                .content {
                    width: calc(100% - 76px);

                    .title {
                        font-style: normal;
                        font-weight: 800;
                        font-size: 14px;
                        line-height: 22px;
                        color: $color-palette1;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }

                .thumbnail {
                    width: 76px;
                    height: 76px;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }
            }

            li:last-child {
                margin-bottom: 0;
            }
        }
    }

}

.widget-banner {
    text-align: center;

    img {
        max-width: 100%;
        text-align: center;
        margin-bottom: 24px;
    }
}

.widget-banner-text-link {
    position: relative;

    .image {
        text-align: center;
        width: 100%;

        img {
            width: 100%;
        }
    }

    .content {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        padding: 24px;

        .title {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-white;
            margin-bottom: 16px;
        }

        .button {
            font-weight: 600;
            font-size: 14px;
            line-height: 16px;
            padding: 8px 12px;
            display: inline-block;
            box-shadow: 0px 0px 1px rgba(117, 131, 142, 0.04), 0px 2px 4px rgba(52, 60, 68, 0.16);
            border-radius: 8px;
            color: $color-palette4;
            background: $color-primary1v2;
        }
    }
}

#new-detail {
    background: $color-st10;
}

#col-content-new {
    .line-1-col-hot-new {
        margin-bottom: 25px;

        .category {
            padding: 6px 12px;
            font-weight: 700;
            font-size: 12px;
            line-height: 14px;
            color: $color-palette4;
            background: $color-primary1;
            display: inline-block;
            margin-right: 17px;
            border-radius: 12px;
        }

        .date {
            display: inline-block;
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 14px;
            color: $color-secondary1;

            img {
                margin-right: 7px;
                position: relative;
                top: -1px;
            }
        }
    }

    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
    }

    .er-meta-author {
        display: -webkit-flex;
        display: -moz-flex;
        display: -ms-flex;
        display: -o-flex;
        display: flex;
        align-items: center;
        padding-top: 20px;

        .er-author {

            img {
                margin-right: 17px;
            }

            span {
                color: $color-secondary1;

                span {
                    color: $color-palette1;
                    font-weight: 700;
                }
            }
        }

        .er-meta-list {
            ul {
                margin: 0;
                padding: 0;
                list-style-type: none;

                li {
                    img {
                        position: relative;
                        top: -2px;
                    }

                    display: inline-block;
                    margin-left: 24px;
                    padding-left: 24px;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 16px;
                    line-height: 24px;
                    color: $color-secondary1;
                    border-left: 1px solid #DDDDDD;
                }
            }
        }
    }
}

#main-new-detail {
    padding-left: 14px;

    b,
    strong {
        font-weight: bolder !important;
    }

    .edit {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 30px;
        color: $color-black;
        border-bottom: 1px solid $color-palette3;
        margin-bottom: 24px;

        p {
            margin-bottom: 24px;
        }

        .video-container {
            position: relative;
            padding-bottom: 56.25%;
            /* 16:9 */
            height: 0;
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    .banner {
        padding-bottom: 24px;
        border-bottom: 1px solid $color-palette3;

        .title-banner {
            font-style: normal;
            font-weight: 800;
            font-size: 18px;
            line-height: 26px;
            color: $color-secondary1;
            margin-bottom: 17px;
        }

        img {
            max-width: 100%;
        }
    }

    .er-social-share-tag {
        padding-top: 24px;
        padding-bottom: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid $color-palette3;

        .er-tag {
            margin: 0;
            padding: 0;
            list-style-type: none;

            ul {
                li {
                    display: inline-block;
                    margin-right: 9px;
                    color: $color-palette1;

                    a {
                        background: $color-st12;
                        color: $color-palette1;
                        display: inline-block;
                        line-height: 30px;
                        padding: 0 20px;
                        font-size: 12px;
                        font-weight: 500;
                    }
                }
            }
        }

        .er-social ul {
            margin: 0;
            padding: 0;
            list-style-type: none;

            li {
                display: inline-block;
                margin-right: 9px;

                a {
                    display: inline-block;
                    color: #D3D3D3;
                    font-size: 18px;
                    margin-left: 24px;
                }
            }
        }
    }

    .same-new {
        margin-bottom: 30px;
        border-bottom: 1px solid $color-palette3;
        padding-bottom: 24px;

        .item-same-new {
            position: relative;
        }

        .thumb-save-new {
            img {
                width: 100%;
            }
        }

        .title-save-new {
            font-style: normal;
            font-weight: 800;
            font-size: 16px;
            line-height: 24px;
            color: $color-white;
            position: absolute;
            bottom: 16px;
            width: 100%;
        }
    }


    .er-blog-post-prev-next {
        padding-top: 20px;
        padding-bottom: 13px;
        position: relative;
        border-bottom: 1px solid $color-palette3;

        .er-post-bars {
            position: absolute;
            left: 50%;
            top: 23px;
            transform: translateX(-50%);
        }

        .er-post-prev-next {
            width: 40%;

            span {
                font-style: normal;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                color: $color-palette1;
                display: block;
                margin-bottom: 4px;
            }

            .er-title {
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 20px;
                color: $color-palette1;
            }
        }
    }


}

#banner-new {
    height: 500px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

#col-content-new {
    background: $color-white;
    padding: 48px 40px;
    position: relative;
    margin-top: -106px;
}

#wapper-new-detail {
    padding-bottom: 190px;
}

#sidebar-new-detail {
    padding-top: 64px;

    .widget-most-poplular {
        .line-1-col-hot-new {
            margin-bottom: 12px;

            .date {
                display: inline-block;
                font-style: normal;
                font-weight: 500;
                font-size: 12px;
                line-height: 14px;
                color: $color-secondary1;

                img {
                    margin-right: 7px;
                    position: relative;
                    top: -1px;
                }
            }
        }

    }

    .widget-social-count {
        background: none;

        ul {
            li {
                background: $color-white;
            }
        }
    }
}

.widget-post-video {
    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
        margin-bottom: 32px;
    }

    .image {
        margin-bottom: 20px;
        position: relative;
    }

    .image:before {
        content: "";
        background: url(../images/list-new/icon-video.svg) no-repeat;
        width: 48px;
        height: 48px;
        position: absolute;
        bottom: 16px;
        left: 16px;
    }

    .line-1-col-hot-new {
        margin-bottom: 12px;

        .category {
            padding: 6px 12px;
            font-weight: 700;
            font-size: 12px;
            line-height: 14px;
            color: $color-palette4;
            background: $color-st9;
            display: inline-block;
            margin-right: 17px;
        }

        .date {
            display: inline-block;
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 14px;
            color: $color-secondary1;

            img {
                margin-right: 7px;
                position: relative;
                top: -1px;
            }
        }
    }

    .title-video {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        margin-bottom: 3px;
        color: $color-palette1;
    }

    .auth {
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: $color-secondary1;

        span {
            color: $color-palette1;
        }
    }
}

#social-main-new-detail {
    ul {
        li {
            margin-bottom: 24px;

            .item {
                display: flex;
                gap: 16px;

                .content {
                    .number {
                        font-style: normal;
                        font-weight: 800;
                        font-size: 16px;
                        line-height: 24px;
                        color: $color-palette1;
                    }

                    .type {
                        font-style: normal;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 24px;
                        color: $color-secondary1;
                    }
                }
            }
        }
    }
}

.modal-job {
    .btn-close {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .modal-dialog {
        max-width: 652px;
        min-height: 706px;
    }

    .modal-body {
        min-height: 610px;
        padding: 48px 74px;
        background-image: url(../images/bg-form-login-v2.svg);
        background-repeat: no-repeat;
        background-position: top 48px right 48px;
    }

    .title {
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        margin-bottom: 58px;
        color: $color-palette1;
    }

    .header-tab {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 48px;

        li {
            width: 50%;

            a {
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                display: block;
                text-align: center;
                color: $color-st13;
                padding: 10px 0;
                border-bottom: 2px solid $color-palette4;

                span {
                    position: relative;
                }

                span:before {
                    content: "";
                    position: absolute;
                    width: 24px;
                    height: 24px;
                    left: -30px;
                    top: 2px;
                }
            }

            .from-storage {
                span:before {
                    background: url("../images/search-job/icon-tab-1.svg") no-repeat;
                }
            }

            .new {
                span:before {
                    background: url("../images/search-job/icon-tab-2.svg") no-repeat;
                }
            }

            a.active {
                border-bottom: 2px solid $color-brand;
                color: $color-brand1;
            }

        }
    }

    .item-form {
        margin-bottom: 48px;

        label {
            margin-bottom: 8px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;

            span {
                color: $color-primary1v2;
            }

            span.type {
                color: $color-brand1;
            }
        }

        label.error {
            font-weight: 400;
            font-size: 10px;
            line-height: 12px;
        }
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered,
    .select2-container .select2-selection--single,
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        line-height: 44px;
        height: 44px;
    }

    .select2-container .select2-selection--single .select2-selection__rendered {
        padding: 0 12px;
    }

    .select2-container {
        line-height: 44px;
        height: 44px;
    }

    .select2-container .select2-selection--single {
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        font-size: 14px;
    }

    .field-item {
        display: block;
        width: 100%;
        background: $color-palette4;
        line-height: 44px;
        height: 44px;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        -webkit-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        -webkit-appearance: none;
        border-radius: 8px;
        border: none;
        padding: 0 12px;
        outline: none;
    }

    .fc-datepicker {
        background-image: url("../images/search-job/icon-calenda.svg");
        background-repeat: no-repeat;
        background-position: center right 12px;
    }

    .bottom-form {
        display: flex;
        justify-content: space-between;

        button {
            width: 224px;
            max-width: 45%;
            height: 46px;
            background: $color-palette4;
            color: $color-brand1;
            border: 1px solid $color-brand1;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
            border-radius: 30px;
            font-size: 18px;
        }

        button.btn-save {
            background: $color-brand1;
            color: $color-palette4;
        }

        button.btn-save-clone {
            background: $color-brand1;
            color: $color-palette4;
        }
    }

    .step {
        margin-bottom: 48px;

        ul {
            display: flex;
            gap: 100px;
            justify-content: center;

            li {
                position: relative;
            }

            li:after {
                content: "";
                height: 0;
                width: 100px;
                position: absolute;
                border: 1px dashed $color-st14;
                top: 12px;
                left: 27px;
            }

            li:last-child:after {
                display: none;
            }
        }

        .step-item {
            width: 27px;
            height: 27px;
            display: block;
            background-repeat: no-repeat;
            background-position: center;
        }

        .step-item1 {
            background-image: url("../images/search-job/icon-step1.svg");
        }

        .step-item2 {
            background-image: url("../images/search-job/icon-step2.svg");
        }

        .step-item2.active {
            background-image: url("../images/search-job/icon-step2-active.svg");
        }

        .step-item3 {
            background-image: url("../images/search-job/icon-step3.svg");
        }

        .step-item3.active {
            background-image: url("../images/search-job/icon-step3-active.svg");
        }
    }

    .form-step {
        display: none;
    }

    .form-step.active {
        display: block;
    }

    .file-browser-mask {
        display: block;
        width: 100%;
        background: $color-palette4;
        line-height: 44px;
        height: 44px;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        border-radius: 8px;
        border: none;
        padding: 0 12px;
        outline: none;
        padding-left: 110px;
        color: $color-st15;
    }

    .input-file {
        position: relative;
        border-radius: 6px;

        .btn-file {
            width: 86px;
            height: 28px;
            background: $color-primary1;
            box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
            border-radius: 6px;
            color: $color-st16;
            font-size: 14px;
            line-height: 20px;
            padding: 4px 16px;
            position: absolute;
            top: 8px;
            left: 12px;
        }

        .file-browser-mask {
            box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
        }
    }

    .error-message,
    label.error {
        background: url(../images/job/icon-error.svg) no-repeat;
        font-weight: 400;
        font-size: 14px !important;
        line-height: 1.5 !important;
        background-position: top 2px left;
    }
}

.modal-open {
    .select2-container.select2-container--default.select2-container--open {
        z-index: 9999999;
    }
}

.ui-datepicker {
    background-color: #fff;
    border: 1px solid #e9ebfa;
    border-radius: 4px;
    box-shadow: 0 16px 18px rgba(6, 10, 48, 0.1);
    display: none;
    font-size: inherit;
    margin: 1px 0 0;
    padding: 10px;
    width: auto !important;
    font-family: $font-family;
}

.ui-datepicker .ui-datepicker-header {
    align-items: center;
    background-color: transparent;
    border: 0;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    color: #473b52;
    display: flex;
    font-size: 12px;
    font-weight: 500;
    justify-content: space-between;
    letter-spacing: 1px;
    padding: 0 0 5px;
    position: relative;
    text-transform: uppercase;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next,
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
    color: #6c757d;
    text-indent: -99999px;
    top: 1px;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next:before,
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
    font-family: FontAwesome;
    font-size: 16px;
    position: absolute;
    text-indent: 0;
    top: -4px;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next:focus:before,
.ui-datepicker .ui-datepicker-header .ui-datepicker-next:hover:before,
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:focus:before,
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:hover:before {
    color: #353a40;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
    order: 3;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next:before {
    content: "";
    right: 5px;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
    content: "";
    left: 5px;
}

.ui-datepicker .ui-datepicker-header .ui-datepicker-next-hover,
.ui-datepicker .ui-datepicker-header .ui-datepicker-prev-hover {
    background-color: transparent;
    border: 0;
    color: #495057;
    cursor: pointer;
    top: 1px;
}

.ui-datepicker .ui-datepicker-title {
    color: #36f;
}

.ui-datepicker .ui-datepicker-calendar {
    background-color: transparent;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
    margin: 0;
}

.ui-datepicker .ui-datepicker-calendar th {
    color: #98a1b5;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 6px 10px;
    text-transform: uppercase;
}

.ui-datepicker .ui-datepicker-calendar td {
    background-color: #f6f7fb;
    border: 1px solid #e9ebfa;
    padding: 0;
    text-align: right;
}

.ui-datepicker .ui-datepicker-calendar td.ui-datepicker-other-month .ui-state-default {
    color: #ced1da;
}

.ui-datepicker .ui-datepicker-calendar td a,
.ui-datepicker .ui-datepicker-calendar td span {
    background-color: #fff;
    border: 0;
    border-radius: 1px;
    color: #263871;
    display: block;
    font-size: 12px;
    font-weight: 400;
    padding: 6px 10px;
    transition: all 0.2s ease-in-out;
}

.ui-datepicker .ui-datepicker-calendar td a:hover {
    background-color: #f0f2f7;
    color: #473b52;
}

.ui-datepicker .ui-datepicker-calendar .ui-datepicker-today a {
    background-color: #36f;
    color: #fff;
}

@media (max-width: 320px) {
    .ui-datepicker .ui-datepicker-calendar th {
        letter-spacing: normal;
        padding: 4px 0;
    }
}

.ui-datepicker-multi .ui-datepicker-group {
    float: left;
    padding-right: 15px;
    width: auto;
}

.ui-datepicker-multi .ui-datepicker-group .ui-datepicker-title {
    margin: auto;
}

.ui-datepicker-multi .ui-datepicker-group .ui-datepicker-prev:before {
    left: 10px;
}

.ui-datepicker-multi .ui-datepicker-group .ui-datepicker-next:before {
    right: 10px;
}

.ui-datepicker-multi .ui-datepicker-group table {
    margin: 0;
}

.ui-datepicker-multi .ui-datepicker-group-last {
    padding-right: 0;
}

.ui-datepicker-inline {
    border-radius: 12px;
    max-width: 270px;
}

.custom-month-year-only {
    .ui-datepicker-calendar {
        display: none;
    }

    .ui-widget {
        font-size: .7em;
    }

    .ui-datepicker-prev {
        display: none;
    }

    .ui-datepicker-next {
        display: none;
    }

    .ui-datepicker-header select.ui-datepicker-month,
    .ui-datepicker-header select.ui-datepicker-year {
        width: 150px;
        height: 30px;
        border: none;
        margin: 0 10px;
        padding: 0 12px;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
    }
}

#wapper-page-ctv {
    position: relative;
    display: flex;
    flex-wrap: wrap;

    //overflow: hidden;
    .sidebar {
        //transition: 0.4s;
        min-height: 800px;
        width: 292px;
        background: $color-white;
        border-right: 1px solid $color-st17;
        box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.05);
        border-radius: 0px;
        padding: 24px 10px;
        background-image: url("../images/dashboard-ctv/image-bottom-sidebar.svg");
        background-repeat: no-repeat;
        background-position: bottom 24px center;
        position: relative;
    }

    .header-sidebar {
        text-align: center;
        padding-bottom: 24px;
        border-bottom: 1px solid $color-palette3;

        .avatar {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;
            margin-bottom: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .name {
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            text-align: center;
            color: $color-st18;
            margin-bottom: 6px;
            word-break: break-all;
        }

        .price {
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: $color-secondary1;
        }
    }

    .menu {
        li {
            display: block;

            a {
                display: block;
                align-items: center;
                padding: 10px 15px;
                padding-right: 0;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-palette1;
                border-radius: 5px;
            }

            .icon {
                display: inline-block;
                width: 24px;
                height: 24px;
                margin-right: 12px;
                background-position: center;
                background-repeat: no-repeat;
                position: relative;
                top: 4px;
            }

            a.active {
                color: $color-palette4;
                background: $color-brand1;

                .icon-home {
                    background-image: url("../images/dashboard-ctv/icon-menu-home-active.svg");
                }

                .icon-user {
                    background-image: url("../images/dashboard-ctv/icon-menu-user-active.svg");
                }

                .icon-storage {
                    background-image: url("../images/dashboard-ctv/icon-menu-storage-active.svg");
                }

                .icon-square-link {
                    background-image: url("../images/dashboard-ctv/icon-menu-square-link-active.svg");
                }

                .icon-brief-case {
                    background-image: url("../images/dashboard-ctv/icon-brief-case-active.svg");
                }

                .icon-checked {
                    background-image: url("../images/dashboard-ctv/icon-checked-active.svg");
                }

                .icon-print {
                    background-image: url("../images/dashboard-ctv/icon-print-active.svg");
                }
            }

            .icon-home {
                background-image: url("../images/dashboard-ctv/icon-menu-home.svg");
            }

            .icon-user {
                background-image: url("../images/dashboard-ctv/icon-menu-user.svg");
            }

            .icon-storage {
                background-image: url("../images/dashboard-ctv/icon-menu-storage.svg");
            }

            .icon-square-link {
                background-image: url("../images/dashboard-ctv/icon-menu-square-link.svg");
            }

            .icon-brief-case {
                background-image: url("../images/dashboard-ctv/icon-brief-case.svg");
            }

            .icon-checked {
                background-image: url("../images/dashboard-ctv/icon-checked.svg");
            }

            .icon-print {
                background-image: url("../images/dashboard-ctv/icon-print.svg");
            }

        }
    }

    .page-ctv {
        //transition: 0.4s;
        background: rgba(255, 255, 255, 0.15);
        width: calc(100% - 292px);
    }

    .toggle-sidebar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: $color-palette4;
        box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        bottom: 40px;
        right: 16px;
    }

    .text {
        height: 100px;
    }
}

.avatar-default {
    background: $color-brand1;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-style: normal;
    font-weight: 600;
    font-size: 28px;
    color: $color-palette4;
    text-transform: uppercase;
}

#wapper-page-ctv.deactivate {
    .sidebar {
        width: 126px;
    }

    .page-ctv {
        width: calc(100% - 126px);
    }

    .price {
        display: none;
    }

    .text {
        display: none;
    }

    .menu {
        li {
            a {
                text-align: center;
                padding-right: 15px;
            }

            .icon {
                display: block;
                width: 48px;
                margin: 0 auto;
                top: 0;
            }
        }
    }

    .toggle-sidebar {
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
    }
}


#page-user-profile {
    .header-company-info {
        text-align: center;
        padding: 24px 0;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        background: $color-palette4;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        position: relative;

        #back-to-company {
            position: absolute;
            left: 28px;
            top: 22px;
        }

    }

    .main-form {
        padding: 60px 48px;

        .wapper-form {
            margin-bottom: 32px;
        }

        .title-group {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-palette1;
            margin-bottom: 25px;

            .button-edit {
                border: none;
                background: none;
                margin-left: 20px;
            }
        }

        .wapper-group-avatar {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto;
            margin-bottom: 24px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto;
            margin-bottom: 24px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            img {
                width: 100%;
            }
        }

        .icon-edit-ava {
            position: absolute;
            top: 0;
            right: -15px;
        }

        .drop-edit-ava {
            width: 210px;
            background: $color-white;
            border: 1px solid $color-st20;
            border-radius: 8px;
            padding: 0;
            transform: translate3d(-100px, 31px, 0px) !important;
            box-shadow: 0px 2px 4px rgba(117, 131, 142, 0.04), 0px 8px 16px rgba(52, 60, 68, 0.15);

            li {
                a {
                    display: block;
                    padding: 13px 15px;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20px;
                    color: $color-palette1;

                    .icon {
                        margin-right: 15px;
                    }
                }

                border-bottom:1px solid $color-palette3;
            }

            li:last-child {
                border-bottom: none;
            }
        }

        .drop-edit-ava:before {
            content: '';
            width: 14px;
            height: 14px;
            background: $color-white;
            position: absolute;
            transform: rotate(45deg);
            top: -8px;
            left: 0;
            right: 0;
            margin: 0 auto;
            border: 1px solid $color-palette3;
            border-bottom: none;
            border-right: none;
        }

        .group-item-field {
            label {
                display: block;
                font-style: normal;
                font-weight: 600;
                font-size: 14px;
                line-height: 22px;
                color: $color-palette1;
                margin-bottom: 8px;

                span {
                    color: $color-primary1v2;
                }
            }

            .item-form {
                border-radius: 8px;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            }

            .item-field {
                background-color: $color-white;
                display: block;
                height: 40px;
                width: 100%;
                border-radius: 8px;
                border: none;
                outline: none;
                padding: 0 16px;
                font-size: 14px;
                line-height: 22px;
                color: $color-palette1;
            }

            .item-field:disabled {
                background-color: $color-palette4;
            }

            .icon-clone {
                background-image: url("../images/dashboard-ctv/icon-clone.svg");
                background-repeat: no-repeat;
                background-position: right 16px center;
            }

            .image-item-field {
                height: 219px;
                width: 100%;
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    max-width: 100%;
                    max-height: 100%;
                    width: auto;
                }
            }

            .upload-image {
                background-image: url("../images/dashboard-ctv/bg-upload-image.svg");
                background-repeat: no-repeat;
                background-position: center;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered,
            .select2-container .select2-selection--single,
            .select2-container--default .select2-selection--single .select2-selection__arrow {
                line-height: 40px;
                height: 40px;
                border: none;
                background: $color-palette4;
                border-radius: 8px;
            }

            .select2-container {
                line-height: 40px;
                height: 40px;
            }

        }

        .multiple-select2 {
            .select2-selection--multiple {
                width: 100%;
                height: 46px;
                background-color: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                border: none;
                padding-bottom: 0;
                background-image: url(../images/list-new/arrow-select.svg);
                background-repeat: no-repeat;
                background-position: center right 15px;
                padding-left: 12px;
            }

            .select2-container--default.select2-container--focus .select2-selection--multiple {
                border: none;
            }

            .select2-container--default .select2-selection--multiple .select2-selection__clear {
                display: none;
            }

            .select2-container .select2-selection--multiple .select2-selection__rendered {
                position: absolute;
                height: 100%;
                display: flex;
                align-items: center;
                flex-flow: row wrap;
            }

            .select2-container--default .select2-selection--multiple .select2-selection__choice {
                padding-left: 10px;
                padding-right: 20px;
                border-radius: 15px;
                height: 21px;
                background: $color-white;
                border-color: $color-palette3;
                margin-top: 0;
                margin-left: 0;
                margin-right: 5px;
                display: flex;
                align-items: center;
            }

            .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
                left: auto;
                right: 9px;
                border-right: 0;
                padding: 0;
                color: $color-brand;
                font-size: 18px;
                line-height: 20px;
            }

            .select2-search__field {
                display: none;
            }

            .select2-selection__choice__display {
                color: $color-palette1;
                line-height: 21px;
            }
        }

        .group-gallery {
            margin-bottom: 32px;

            label {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-secondary1;
                margin-bottom: 8px;
                display: block;
            }

            .row-gallery {
                display: flex;
                gap: 24px;

                .frame {
                    position: relative;
                }

                .btn-x,
                .btn-y {
                    background-image: url("../images/dashboard-ctv/icon-x.svg");
                    background-repeat: no-repeat;
                    background-position: center;
                    width: 16px;
                    height: 16px;
                    position: absolute;
                    top: 7px;
                    right: 7px;
                }

                .col-gallery {
                    width: 158px;
                    height: 88px;
                    position: relative;
                    overflow: hidden;
                    border-radius: 8px;
                    background: rgba(255, 255, 255, 0.15);

                    img {
                        object-fit: cover;
                        width: 100%;
                        height: 100%;
                    }

                }

                .add-item-gallery {
                    background-color: $color-palette4;
                    background-image: url("../images/dashboard-ctv/bg-upload-item-gallery.svg");
                    background-repeat: no-repeat;
                    background-position: center;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                }

                .add-item-image {
                    background-color: $color-palette4;
                    background-image: url("../images/dashboard-ctv/bg-upload-image-item-gallery.svg") !important;
                    background-repeat: no-repeat;
                    background-position: center;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                }

            }

        }


        .item-col-form {
            margin-bottom: 24px;
        }

        .item-button {
            width: 224px;
            height: 46px;
            padding: 10px;
            text-align: center;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 25%);
            border-radius: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .destroy-form {
            color: $color-primary1v2;
            border: 1px solid $color-primary1v2;
        }

        .save-form {
            background: $color-brand1;
            color: $color-palette4;
            border: 1px solid $color-brand1;
        }

        .bottom-form {
            display: flex;
            gap: 48px;
            justify-content: flex-end;
        }

        .fc-datepicker {
            background-image: url("../images/search-job/icon-calenda.svg");
            background-repeat: no-repeat;
            background-position: center right 12px;
        }
    }

    .header-tab {
        li {
            position: relative;

            a {
                display: block;
                width: 250px;
                max-width: 100%;
                padding: 8px 15px;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
                border-bottom: 2px solid $color-palette3;
                text-align: center;
            }

            .icon {
                width: 16px;
                height: 16px;
                display: inline-block;
                background-repeat: no-repeat;
                background-position: center;
                position: relative;
                margin-right: 10px;
                top: 1px;
            }

            .icon-change-password {
                background-image: url("../images/dashboard-ctv/icon-tab-password.svg");
            }

            .icon-company {
                background-image: url("../images/dashboard-ctv/icon-company.svg");
            }

            .icon-change-profile {
                background-image: url("../images/dashboard-ctv/icon-change-profile.svg");
            }

            a.active {
                color: $color-brand1;
                border-bottom: 2px solid $color-brand;

                .icon-change-password {
                    background-image: url("../images/dashboard-ctv/icon-tab-password-active.svg");
                }

                .icon-company {
                    background-image: url("../images/dashboard-ctv/icon-company-active.svg");
                }

                .icon-change-profile {
                    background-image: url("../images/dashboard-ctv/icon-change-profile-active.svg");
                }
            }

        }

        li:after {
            content: '';
            width: 1px;
            height: 32px;
            position: absolute;
            background: $color-palette3;
            right: 0;
            top: 6px;
        }

        li:last-child:after {
            display: none;
        }
    }

    .form-change-password {
        max-width: 537px;
    }

    .item-form-change-password {
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: $color-palette1;

        label {
            padding-top: 10px;
            margin-bottom: 10px;
        }

        .show-password {
            background-image: url("../images/dashboard-ctv/icon-hide-pass.svg");
        }

        .hide-password {
            background-image: url("../images/dashboard-ctv/icon-show-pass.svg");
        }

        .toggle-password {
            width: 24px;
            height: 24px;
            position: absolute;
            top: 10px;
            right: 12px;
        }
    }

    #content-tab-password {
        .item-button {
            width: 148px;
        }
    }

    #content-tab-user {
        .item-button {
            width: 148px;
        }
    }

    #modal-addvideo {
        .modal-footer {
            border-top: none;
        }

        .modal-dialog {
            width: 592px;
            max-width: 100%;
        }

        .modal-content {
            padding-top: 48px;
            padding-bottom: 56px;
            background-image: url(../images/member-manager/bg-modal.svg);
            background-repeat: no-repeat;
            background-position: right 62px top -12px;
        }

        .modal-header {
            font-style: normal;
            font-weight: 800;
            font-size: 26px;
            line-height: 38px;
            color: $color-palette1;
            text-align: center;
            padding: 0;
            margin-bottom: 48px;
        }

        .modal-body {
            padding: 0 48px;

            .item-modal-form {
                margin-bottom: 24px;

                label {
                    font-style: normal;
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    display: block;
                    color: $color-palette1;
                    margin-bottom: 8px;
                }

                input {
                    height: 44px;
                    width: 100%;
                    border: none;
                    background: $color-palette4;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    border-radius: 8px;
                    padding: 0 12px;
                    outline: none;
                }
            }

            p {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-palette1;
                margin-bottom: 48px;
            }
        }

        .modal-footer {
            padding: 0;
            justify-content: center;
            gap: 48px;

            .btn {
                width: 224px;
                height: 46px;
                margin: 0;
                border-radius: 30px;
                border: 1px solid $color-primary1v2;
                color: $color-primary1v2;
            }

            .btn:focus {
                box-shadow: none;
            }

            .btn-save {
                background: $color-brand1;
                border: 1px solid $color-brand1;
                color: $color-palette4;
            }
        }
    }
}

#dashboard-collaborator {
    padding: 48px 24px;

    .layout1 {
        margin-bottom: 50px;
    }
}

.card {
    border: none;
    background: $color-palette4;
    box-shadow: 0px 4px 10px rgba(222, 223, 225, 0.5);
    border-radius: 10px;
    padding: 16px;

    .icon1 {
        display: flex;
        align-items: center;
    }

    .title-number {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: $color-secondary1;
        margin-bottom: 16px;
    }

    .count-number {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;

        sup {
            font-size: 12px;
        }
    }

}

.card-type2 {
    padding: 24px;

    .header {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 26px;
    }
}

.card-type3 {
    padding: 24px 12px;

    .header {
        font-style: normal;
        font-weight: 800;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 24px;
    }

    .list-new-cv {
        height: 541px;
        overflow-y: hidden;
    }

    .list-new-cv:hover {
        overflow-y: scroll;
    }

    // Quick Access Menu Styles
    .quick-access-menu {
        .quick-access-item {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        .quick-access-link {
            display: flex;
            align-items: center;
            padding: 16px 12px;
            background: $color-palette4;
            border: 1px solid $color-palette3;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;

            &:hover {
                background: $color-palette5;
                border-color: $color-brand1;
                text-decoration: none;
                transform: translateY(-2px);
                box-shadow: 0px 4px 12px rgba(121, 132, 165, 0.15);
            }
        }

        .quick-access-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: $color-brand1;
            border-radius: 8px;
            margin-right: 12px;
            flex-shrink: 0;

            img {
                width: 20px;
                height: 20px;
                filter: brightness(0) invert(1);
            }
        }

        .quick-access-content {
            flex: 1;
        }

        .quick-access-title {
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            color: $color-palette1;
            margin-bottom: 4px;
        }

        .quick-access-desc {
            font-style: normal;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: $color-secondary1;
        }
    }
}

.list-new-cv {
    .item-new-cv {
        padding: 12px;
        border-bottom: 1px dashed rgba(121, 125, 136, 0.15);
        margin-bottom: 12px;

        .date {
            display: inline-block;
            padding: 6px 22px;
            background: $color-brand1;
            border-radius: 10px;
            font-weight: 600;
            font-size: 10px;
            line-height: 12px;
            margin-right: 12px;
            color: $color-palette4;
        }

        .date-time {
            font-weight: 400;
            font-size: 10px;
            line-height: 12px;
            color: $color-secondary1;
            margin-bottom: 6px;
        }

        .job-name {
            font-style: normal;
            font-weight: 600;
            font-size: 12px;
            line-height: 22px;
            color: $color-palette1;
            margin-bottom: 6px;
        }

        .name {
            color: $color-secondary1;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
        }
    }
}

#col-detail-job {
    .back-job-mobile {
        display: none;
    }
}


.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden
}

.slider-3d {
    height: 500px;
    margin: 30px auto;
}

.slider-3d ul li {
    border: 1px solid #fff;
    overflow: initial !important;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #a39c9c;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0px 2px 4px #056777;
    position: relative;
}

.slider-3d .content {
    padding: 110px 88px 0;
}

.slider-3d ul li .item {
    transition: 0.8s;
    transform: scale(0.6) translate(0, -90px);
}

.slider-3d ul li.active .item {
    transform: scale(1);
}

.slider-3d .avatar {
    overflow: hidden;
    border-radius: 50%;
    margin: 0 auto;
    border: 1px solid #ddd;
    padding: 8px;
    width: 180px;
    height: 180px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
    position: absolute;
    top: -88px;
    left: 0;
    right: 0;
}

.slider-3d .avatar .img {
    border-radius: 50%;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.slider-3d .avatar img {
    object-fit: cover;
    width: 100%;
    min-height: 100%;
}

.slider-3d .active .content p {
    font-size: 20px;
    margin-bottom: 32px;
    line-height: 40px;
    max-height: 120px;
}

.slider-3d .content p {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 25px;
    text-align: center;
    color: $color-secondary1;
    margin-bottom: 20px;
    max-height: 75px;
    overflow: hidden;
}

.slider-3d .active h3 {
    font-size: 20px;
    line-height: 36px;
    margin-bottom: 12px;
}

.slider-3d h3 {
    font-style: normal;
    font-weight: 800;
    font-size: 18px;
    line-height: 30px;
    color: $color-palette1;
    margin-bottom: 5px;
    overflow: hidden;

}

.slider-3d h3 a {
    color: $color-palette1;
    display: block
}

.slider-3d .active span {
    font-size: 24px;
    line-height: 36px;
}

.slider-3d span {
    color: $color-brand1;
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    text-align: center;
}

.slider-3d ul li::before {
    content: '';
    width: 68px;
    height: 48px;
    background: url("../images/icon-quote.svg") no-repeat;
    position: absolute;
    top: 0px;
    left: 0px;
    background-size: 100%;
    background-position: center;
    transform: scale(0.6);
    transition: 0.5s;
}

.slider-3d ul li.active::before {
    transform: scale(1);
    top: 25px;
    left: 25px;
}

.slider-3d ul li::after {
    content: '';
    width: 68px;
    height: 48px;
    background: url("../images/icon-quote.svg") no-repeat;
    position: absolute;
    right: 0px;
    bottom: 0px;
    background-size: 100%;
    background-position: center;
    transform: scale(0.6) rotate(180deg);
    transition: 0.5s;
}

.slider-3d ul li.active::after {
    transform: scale(1) rotate(180deg);
    right: 25px;
    bottom: 25px;
}

.slider-3d li.active::before,
.slider-3d li.active::after {
    color: $color-brand1;
}

.left,
.right {
    cursor: pointer;
    padding: 20px;
    //font-size: 40px;
    transform: translateY(-50%);
    color: $color-brand1;
}

.left:hover,
.right:hover {
    opacity: 1;
}

#customer-slider {
    width: 100% !important;
}

.slider-3d {
    margin-bottom: 0 !important;
}

@media (max-width: 1319px) {
    .slider-3d .content {
        padding: 110px 10px 0;
    }

    .slider-3d .avatar {
        width: 100px;
        height: 100px;
        top: -58px;
    }

    .slider-3d .content {
        padding: 80px 10px 0;
    }

    .slider-3d .content p {
        line-height: 1;
        margin-bottom: 0;
        font-size: 14px;
    }

    .slider-3d h3 {
        line-height: 1.5 !important;
    }

    .slider-3d .active .content p {
        line-height: 1.5;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .slider-3d .active h3 a {
        font-size: 16px;
    }

    .slider-3d .active span {
        font-size: 14px;
    }
}

@media (max-width: 992px) {
    .slider-3d {
        height: 300px;
    }

    .slider-3d .active .content {
        padding: 50px 25px 0;
    }

    .slider-3d .content {
        padding: 10px 20px 0;
    }

    .slider-3d .active .content p {
        font-size: 15px;
        margin-bottom: 10px;
        line-height: 24px;
        max-height: 72px;
    }

    .slider-3d .active h3 {
        font-size: 17px;
        line-height: 24px;
        max-height: 24px;
        overflow: hidden;
        margin-bottom: 5px;
    }

    .slider-3d h3 {
        font-size: 13px;
        line-height: 20px;
        max-height: 20px;
        overflow: hidden;
        margin-bottom: 0;
    }

    .slider-3d .content p {
        font-size: 11px;
        line-height: 20px;
        max-height: 40px;
        margin-bottom: 5px;
    }

    .slider-3d span {
        font-size: 12px;
    }

    .slider-3d .active span {
        font-size: 16px;
        line-height: 24px;
    }

    .slider-3d li {
        opacity: 0.5;
    }

    .slider-3d ul li.active {
        background: rgba(255, 255, 255, 0.95);
        opacity: 1;
    }

    .left,
    .right {
        opacity: 1;
    }

    .slider-3d .avatar {
        width: 60px;
        height: 60px;
        top: -58px
    }

    .slider-3d li.active .avatar {
        width: 100px;
        height: 100px;
    }

    .slider-3d li.active::before,
    .slider-3d li.active::after {
        font-size: 25px;
    }

    .customer-slider-mobile {
        margin: 0 auto !important;
    }

    .left,
    .right {
        font-size: 30px;
    }

    .slider-3d ul li .item {
        transition: 0.8s;
        transform: scale(0.6) translate(0, -10px);
    }

    .slider-3d ul li::after {
        width: 30px;
        height: 24px;
    }

    .slider-3d ul li::before {
        width: 30px;
        height: 24px;
    }

    .slider-3d ul li.active::after {
        right: 8px;
        bottom: 8px;
    }

    .slider-3d ul li.active::before {
        left: 8px;
        top: 8px;
    }
}

@media (max-width: 480px) {

    .left,
    .right {
        font-size: 25px;
        margin-top: -30px;
    }

    .slider-3d {
        height: 200px;
    }

    .slider-3d .avatar {
        width: 40px;
        height: 40px;
        top: -20px;
        padding: 3px;
    }

    .slider-3d li.active .avatar {
        width: 70px;
        height: 70px;
        top: -45px
    }

    .slider-3d span {
        display: none;
    }

    .slider-3d .active span {
        display: block;
        font-size: 10px;
        line-height: 1
    }

    .slider-3d .active .content {
        padding: 30px 20px 0;
    }

    .slider-3d .content p {
        max-height: 30px;
        line-height: 15px;
        margin-bottom: 0;
    }

    .slider-3d .active .content p {
        font-size: 10px;
        margin-bottom: 0;
        line-height: 12px
    }

    .slider-3d .active h3 {
        font-size: 10px;
        margin-bottom: 0
    }

    .slider-3d li.active::before,
    .slider-3d li.active::after {
        font-size: 14px;
    }

    .slider-3d ul li::after {
        width: 15px;
        height: 15px;
    }

    .slider-3d ul li::before {
        width: 15px;
        height: 15px;
    }

    .slider-3d ul li .item {
        transform: scale(0.6) translate(0, -22px);
    }

    .slider-3d .content {
        padding: 25px 20px 0;
    }
}


#candidate-introduction-manager {
    padding-bottom: 48px;

    .header {
        padding: 24px 44px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        background: $color-st12;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);

        .search {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 18px;

            .input-search {
                position: relative;

                input {
                    width: 600px;
                    background: $color-palette4 url("../images/search-job/search-job.svg") no-repeat;
                    background-position: left 16px center;
                    box-shadow: 0px 4px 4px rgba(51, 51, 51, 0.04), 0px 4px 16px rgba(51, 51, 51, 0.08);
                    border-radius: 8px;
                    height: 56px;
                    border: none;
                    padding-left: 44px;
                    padding-right: 14px;
                    outline: none;
                }
            }

            .button-search {
                border: none;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette4;
                padding: 10px 24px 10px 56px;
                background: linear-gradient(90deg, #056777 0%, #079DB2 100%);
                position: relative;
            }

            .button-search:before {
                content: '';
                background: url("../images/dashboard-ctv/icon-search.svg") no-repeat;
                width: 24px;
                height: 24px;
                position: absolute;
                left: 24px;
            }

            .button-search-modify {
                padding: 10px 24px 10px 24px;

                &:before {
                    display: none;
                }
            }

            .button-open-popup {
                width: 48px;
                height: 100%;
                position: absolute;
                background: red;
                right: 0;
                background: url("../images/dashboard-ctv/icon-filter.svg") no-repeat;
                background-position: center;
            }
        }

        .button-header {
            width: 200px;
            max-width: 100%;
            padding-top: 5px;

            a {
                display: block;
                width: 100%;
                font-style: normal;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand1;
                background: $color-palette4;
                border: 1px solid $color-brand1;
                padding: 10px;
                box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
                border-radius: 8px;
                text-align: center;
            }
        }
    }

    .group-item {
        padding: 0 44px;

        .item {
            padding: 24px;
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 32px;
            background: $color-palette4;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 4px;
            position: relative;
            transition: 0.2s;

            .ava {
                width: 90px;
                height: 90px;
                border-radius: 50%;
                overflow: hidden;
            }

            .content {
                max-width: calc(100% - 106px);
                width: 100%;

                .title-item {
                    font-style: normal;
                    font-weight: 400;
                    font-size: 10px;
                    line-height: 12px;
                    color: $color-secondary1;
                    margin-bottom: 12px;
                }

                .name {
                    font-style: normal;
                    font-weight: 800;
                    font-size: 18px;
                    line-height: 26px;
                    color: $color-brand1;
                    margin-bottom: 12px;
                }

                .list-info {
                    margin-bottom: 2px;

                    ul {
                        li {
                            width: 210px;
                            display: inline-flex;
                            margin-right: 15px;
                            color: $color-palette1;
                            position: relative;
                            padding-left: 24px;
                            margin-bottom: 10px;
                            word-break: break-word;
                            line-height: 1.5;

                            .text-over {
                                display: -webkit-box;
                                -webkit-line-clamp: 1;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }

                            .candidate-currency {
                                max-width: 160px;
                            }

                            .icon {
                                width: 20px;
                                height: 20px;
                                display: inline-block;
                                margin-right: 6px;
                                position: absolute;
                                left: 0;
                                top: -1px;
                            }

                            a {
                                color: $color-primary1;
                                text-decoration: underline;
                            }

                            sub {
                                font-size: 10px;
                                position: relative;
                                bottom: -6px;
                                left: 2px;
                            }
                        }

                        li:last-child {
                            margin-right: 0;
                        }
                    }
                }

                .list-info-2 {
                    margin-bottom: 0;
                }

            }

            .status-group {
                text-align: center;
                position: absolute;
                right: 24px;
                top: 24px;
                width: 146px;
                height: 70%;

                .authorize {
                    position: absolute;
                    bottom: 0;
                    color: #7984A5;
                    font-weight: 600;
                    font-size: 10px;
                    line-height: 12px;
                    display: block;
                    text-align: center;
                    width: 100%;
                }

                .status {
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 20px;
                    margin-bottom: 30px;
                }

                .status.pending-review {
                    color: $color-st6;
                }

                .status.onboarded {
                    color: $color-primary4;
                }

                .status.cancel {
                    color: $color-primary1v2;
                }

                .button {
                    a {
                        display: block;
                        padding: 8px 8px;
                        background: $color-brand1;
                        font-style: normal;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 16px;
                        color: $color-palette4;
                        border: 1px solid $color-brand1;
                        border-radius: 30px;
                    }
                }

                .button.button-draft {
                    a {
                        background: $color-white;
                        color: $color-brand1;
                    }
                }
            }

            .status-action-icon {
                text-align: center;
                position: absolute;
                right: 24px;
                top: 24px;
                height: 70%;

                .sell-new-icon {
                    position: absolute;
                    width: 48px;
                    right: -21px;
                    top: -46px;
                    z-index: 10;
                }

                .button {
                    text-align: right;
                    margin-bottom: 24px;

                    .button-sale {
                        padding: 8px 12px 8px 34px;
                        background: linear-gradient(90deg, #056777 0%, #079DB2 100%);
                        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                        border-radius: 8px;
                        display: inline-block;
                        color: #FCFCFE;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 16px;
                        position: relative;
                    }

                    .button-sale:before {
                        background-image: url("../images/dashboard-ctv/icon-fly.svg");
                        background-repeat: no-repeat;
                        content: '';
                        width: 14px;
                        height: 14px;
                        position: absolute;
                        top: 8px;
                        left: 8px;
                    }
                }

                ul {
                    li {
                        display: inline-block;
                        margin-right: 32px;

                        .icon {
                            display: block;
                            width: 35px;
                            height: 35px;
                            border-radius: 50%;
                            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                            color: $color-palette4;
                            background-position: left -17px center;
                        }

                        .icon:hover {
                            background-position: right -20px center;
                        }

                        .icon-download {
                            background-image: url("../images/dashboard-ctv/icon-group-1.svg");
                        }

                        .icon-user-download {
                            background-image: url("../images/dashboard-ctv/icon-group-2.svg");
                            background-position: left -13px center;
                        }

                        .icon-user-download:hover {
                            background-position: right -22px center;
                        }

                        .icon-pen {
                            background-image: url("../images/dashboard-ctv/icon-group-3.svg");
                            background-position: left -14px center;
                        }

                        .icon-pen:hover {
                            background-position: right -22px center;
                        }

                        .icon-sales {
                            background-image: url("../images/dashboard-ctv/icon-group-4.svg");
                            background-position: left -19px center;
                        }

                        .icon-sales:hover {
                            background-position: right -27px center;
                        }
                    }

                    li:last-child {
                        margin-right: 0;
                    }
                }
            }

        }

        .item:hover {
            box-shadow: 0px 10px 26px rgba(112, 131, 187, 0.45);
        }
    }

    .select-option-group {
        padding: 12px 48px;
        text-align: right;

        .select2-container,
        .select2-container--default .select2-selection--single .select2-selection__rendered,
        .select2-container .select2-selection--single,
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            line-height: 32px;
            height: 32px;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            color: $color-palette1;
            padding-right: 0;
            text-align: left;
        }

        .select2-container {
            width: 152px !important;
        }

        .select2-container--default .select2-results>.select2-results__options {
            max-height: fit-content;
            overflow-y: hidden;
        }

        .select2-container--default .select2-selection--multiple {
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            border-radius: 8px;
            border: none;
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            display: none;
        }

        .select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
        .select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        .select2-container--default .select2-selection--single .select2-selection__placeholder {
            color: $color-palette1;
        }
    }

    #form-search {
        position: relative;
    }

}

.select2-status-dropdown {
    width: 215px !important;
    left: auto !important;
    right: -153px;
    top: 12px;
    border: none !important;
    border-radius: 8px !important;
    overflow: hidden;

    .select2-results__options {
        max-height: unset !important;
        box-shadow: 0px 2px 8px rgba(117, 131, 142, 0.04), 0px 16px 24px rgba(52, 60, 68, 0.12);
    }

    .select2-container--open .select2-dropdown {}

    input {
        margin-right: 7px;
    }

    .select2-results__option {
        padding: 12px;
    }

    .select-status-0 {
        color: $color-st6;
    }

    .select-status-1 {
        color: $color-primary3;
    }

    .select-status-2 {
        color: $color-secondary1;
    }

    .select-status-3 {
        color: $color-primary1;
    }

    .select-status-4 {
        color: $color-st23;
    }

    .select-status-5 {
        color: $color-primary4;
    }

    .select-status-6 {
        color: $color-primary1v2;
    }
}

.group-paginate {
    display: flex;
    justify-content: center;
    align-items: center;

    .paginate {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 25px;

        .paginate_prev,
        .paginate_next {
            width: 25px;
            height: 25px;
            background-image: url(../images/icon-arrow.svg);
            background-size: 100%;
            background-repeat: no-repeat;
            cursor: pointer;
        }

        .paginate_prev:hover,
        .paginate_next:hover {
            background-image: url(../images/icon-arrow-hover.svg);
        }

        .paginate_next {
            transform: scaleX(-1);
        }

        .number {
            padding: 0 26px;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
        }
    }

    .goto {
        padding: 0 25px;
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: $color-palette1;

        input {
            color: $color-palette1;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            width: 56px;
            text-align: center;
            height: 50px;
        }
    }
}


#modal-confirm {
    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-body {
        padding-top: 48px;
        padding-bottom: 56px;

        .header {
            font-style: normal;
            font-weight: 800;
            font-size: 26px;
            line-height: 38px;
            margin-bottom: 48px;
            color: $color-palette1;
            text-align: center;
        }

        .content {
            margin-bottom: 48px;

            p.p-confirm {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-palette2;
                text-align: center;
                margin-bottom: 10px;
            }

            p.p-name {
                text-align: center;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand;
            }

        }

        .footer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 48px;

            .button {
                border: none;
                width: 160px;
                height: 46px;
                font-style: normal;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
                border-radius: 30px;
            }

            .button-back {
                background: $color-palette4;
                color: $color-brand1;
                border: 1px solid $color-brand1;
            }

            .button-confirm {
                background: $color-brand1;
                color: $color-palette4;
                border: 1px solid $color-brand1;
            }

            .button-confirm1 {
                background: $color-brand1;
                color: $color-palette4;
                border: 1px solid $color-brand1;
            }
        }
    }
}

.modal-confirm {
    .modal-dialog {
        width: 652px;
        max-width: 100%;
    }

    .modal-body {
        padding-top: 48px;
        padding-bottom: 56px;

        .header {
            font-style: normal;
            font-weight: 800;
            font-size: 26px;
            line-height: 38px;
            margin-bottom: 48px;
            color: $color-palette1;
            text-align: center;
        }

        .content {
            margin-bottom: 48px;

            p.p-confirm {
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-palette2;
                text-align: center;
                margin-bottom: 10px;
            }

            p.p-name {
                text-align: center;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-brand;
            }

        }

        .footer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 48px;

            .button {
                border: none;
                width: 160px;
                height: 46px;
                font-style: normal;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
                box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
                border-radius: 30px;
            }

            .button-back {
                background: $color-palette4;
                color: $color-brand1;
                border: 1px solid $color-brand1;
            }

            .button-confirm {
                background: $color-brand1;
                color: $color-palette4;
                border: 1px solid $color-brand1;
            }

            .button-confirm1 {
                background: $color-brand1;
                color: $color-palette4;
                border: 1px solid $color-brand1;
            }
        }
    }
}

#footer-v2 {
    padding-top: 25px;
    padding-bottom: 30px;
    background: linear-gradient(180deg, #0A4450 0%, #18697E 100%);
    position: relative;
    border-bottom: 1px solid $color-palette4;

    .footer-v2 {
        position: relative;
        z-index: 9;
    }

    .form-footer {
        max-width: 790px;
        margin: 0 auto;

        .title {
            font-style: normal;
            font-weight: 800;
            font-size: 28px;
            line-height: 36px;
            color: $color-palette4;
            margin-bottom: 10px;
        }

        .description {
            color: $color-palette4;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            margin-bottom: 30px;
        }

        .group-form {
            box-shadow: 0px 4px 15px rgba(129, 155, 234, 0.4);
            background: rgba(255, 255, 255, 0.9);
            padding: 32px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 24px;
            border-radius: 8px;

            .input-search {
                width: 100%;
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 30px;
                outline: none;
                border: none;
                height: 60px;
                padding: 0 12px;
            }

            .btn-search {
                background: $color-brand;
                width: 155px;
                height: 60px;
                box-shadow: 0px 4px 16px rgba(255, 255, 255, 0.45);
                border-radius: 30px;
                border: none;
                color: $color-palette4;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
            }
        }
    }

    .main-footer {
        padding-top: 65px;

        .logo-footer {
            margin-bottom: 32px;
        }

        .description-footer {
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            margin-bottom: 48px;
            color: $color-palette3;
        }

        .social-footer ul li {
            display: inline-block;
            margin-right: 20px;
        }

        .social-footer ul li:last-child {
            margin-right: 0;
        }

        .title-footer {
            color: $color-palette4;
            font-weight: 800;
            font-size: 28px;
            line-height: 36px;
            margin-bottom: 20px;
        }

        .menu-footer {
            a {
                color: $color-palette4;

                span {
                    color: $color-brand;
                }
            }

            ul {
                li {
                    font-size: 18px;
                    line-height: 26px;
                    font-weight: 600;
                    margin-bottom: 10px;
                }
            }
        }
    }

    .social-mobile {
        display: none;
        text-align: center;
    }
}

#footer-v2:before {
    content: '';
    width: 511px;
    height: 326px;
    background: url("../images/bg-footer-1.svg") no-repeat;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.2;
}

#footer-v2:after {
    content: '';
    width: 290px;
    height: 246px;
    background: url("../images/bg-footer-2.svg") no-repeat;
    position: absolute;
    bottom: 0;
    right: 0;
    opacity: 0.2;
}

#copyright-v2 {
    text-align: center;
    font-weight: 600;
    font-size: 18px;
    line-height: 26px;
    padding: 30px 0;
    background: $color-brand1;
    color: $color-palette5;
    position: relative;
}

#modal-candidate-introduction-details {
    .modal-dialog {
        max-width: 864px;
    }

    .modal-body {
        padding: 48px;
        padding-bottom: 16px;
        background-image: url(../images/bg-form-login-v2.svg);
        background-repeat: no-repeat;
        background-position: top 48px right 48px;
    }

    .header {
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        text-align: center;
        color: $color-palette1;
        margin-bottom: 48px;
    }

    .item-form {
        label {
            display: block;
            width: 100%;
            margin-bottom: 8px;
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-secondary1;

            span {
                color: $color-st9;
            }
        }

        input {
            width: 100%;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -moz-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-appearance: none;
            border-radius: 8px;
            background: $color-palette4;
            border: none;
            outline: none;
            height: 44px;
            padding: 0 12px;
            color: $color-secondary1;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        textarea {
            padding: 12px;
            width: 100%;
            height: 88px;
            background: $color-palette4;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -moz-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-appearance: none;
            border-radius: 8px;
            border: none;
            outline: none;
            color: $color-secondary1;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }
    }

    .item-form-btn {
        position: relative;

        .btn-button {
            position: absolute;
            padding: 4px 10px;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: $color-st16;
            background: $color-primary1;
            box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
            border-radius: 6px;
            right: 12px;
            top: 8px;
        }
    }

    .input-unit {
        display: flex;
        flex-wrap: wrap;
        box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
        border-radius: 6px;

        input {
            width: calc(100% - 88px);
            box-shadow: none;
        }

        span {
            width: 88px;
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            display: flex;
            align-items: center;
            color: $color-palette1;
            justify-content: center;
            border-left: 1px solid $color-palette3;
        }
    }

    .last-form {
        label {
            height: 48px;
        }
    }

    .row-form {
        margin-bottom: 18px;
    }

    .btn-cancel {
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 30px;
        height: 46px;
        padding: 0 52px;
        border: none;
        background: $color-brand1;
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
    }
}

#modal-cv {
    .modal-dialog {
        max-width: 652px;

        .modal-content {
            border-radius: 0;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        .modal-body {
            padding: 0;

            .header {
                padding: 12px;

                img {
                    margin-right: 14px;
                    position: relative;
                    top: -1px;
                }
            }

            .content {
                padding: 24px;
                padding-bottom: 48px;
                background: $color-st17;

                iframe {
                    min-height: calc(100vh - 220px);
                }
            }
        }
    }

    .modal-lg {
        max-width: 900px;
    }
}

.header-layout-edit {
    background: $color-palette4;
    padding: 24px 28px;
    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);

    .title-header-layout-edit {
        text-align: center;
    }

    a {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-secondary1;

        img {
            margin-right: 16px;
        }
    }

    .st2 {
        color: $color-palette1;
    }
}

.base-layout-edit {
    .main-form {
        padding: 48px;

        .form-item-switch {
            margin-bottom: 24px;
        }

        .select2-container--default .select2-selection--single .select2-selection__clear {
            display: none;
        }

        .form-item {
            margin-bottom: 24px;

            label {
                display: block;
                font-style: normal;
                font-weight: 600;
                font-size: 16px;
                line-height: 24px;
                color: $color-palette1;
                margin-bottom: 8px;

                span {
                    color: $color-st9;
                }
            }

            input {
                display: block;
                width: 100%;
                height: 44px;
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                border: none;
                outline: none;
                padding: 0 12px;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
            }

            select {
                display: block;
                width: 100%;
                height: 44px;
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                border: none;
                outline: none;
                padding: 0 12px;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
            }

            input::placeholder {
                color: $color-palette4v2;
            }

            input::-webkit-input-placeholder {
                color: $color-palette4v2;
            }

            input::-ms-input-placeholder {
                color: $color-palette4v2;
            }

            textarea {
                display: block;
                width: 100%;
                min-height: 68px;
                background: $color-palette4;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                border-radius: 8px;
                border: none;
                outline: none;
                padding: 10px 12px;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered,
            .select2-container .select2-selection--single,
            .select2-container--default .select2-selection--single .select2-selection__arrow {
                line-height: 44px;
                height: 44px;
                border: none;
                background: $color-palette4;
                border-radius: 8px;
            }

            .select2-container {
                line-height: 44px;
                height: 44px;
            }

            .multiple-select2 {
                .select2-container {
                    line-height: 32px !important;
                }

                textarea.select2-search__field {
                    width: auto !important;
                    display: inline-table !important;
                    min-height: 35px !important;
                    box-shadow: none !important;
                    background: none !important;
                    border-radius: 0 !important;
                }

                .select2-selection--multiple {
                    border: none !important;
                    background: $color-palette4;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                    border-radius: 8px;
                }

                .select2-selection__choice {
                    background: $color-palette4;
                    color: $color-primary1;
                    border: none;
                    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
                    padding-left: 12px;
                    padding-right: 30px;
                    border-radius: 30px;
                }

                .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
                    border: none;
                    right: 5px;
                    left: auto;
                    color: $color-brand;
                }
            }

        }

        .input-unit {
            display: flex;
            flex-wrap: wrap;
            box-shadow: 0px 4px 6px rgba(3, 12, 53, 0.1);
            border-radius: 6px;

            input {
                width: calc(100% - 82px);
                box-shadow: none;
                border-right: 1px solid $color-palette3;
                border-bottom-right-radius: 0;
                border-top-right-radius: 0;
            }

            .select2-unit {
                width: 82px;
            }

            .select2-container .select2-selection--single {
                box-shadow: none;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered,
            .select2-container .select2-selection--single,
            .select2-container--default .select2-selection--single .select2-selection__arrow {
                border: none;
                line-height: 44px;
                height: 44px;
            }

            .select2-container {
                line-height: 44px;
                height: 44px;
            }

            .group-input-unit {
                border-right: 1px solid $color-palette3;
                justify-content: space-between;
                background: $color-palette4;
                display: flex;
                width: calc(100% - 82px);

                .input-unit-item {
                    width: 45%;
                    position: relative;
                }

                .input-unit-item:after {
                    content: '';
                    width: 12px;
                    height: 2px;
                    background: $color-brand1;
                    position: absolute;
                    top: 21px;
                    right: -25px;
                    border-radius: 2px;
                }

                .input-unit-item:last-child:after {
                    display: none;
                }

                input {
                    border-right: none;
                    width: 100%;
                }
            }


        }

        .input-file {
            position: relative;

            .input-group {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                align-items: stretch;
                width: 100%;
            }

            .file-browser-mask {
                display: block;
                width: 100%;
                background: $color-palette4;
                line-height: 44px;
                height: 44px;
                box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
                border-radius: 8px;
                border: none;
                padding: 0 12px;
                outline: none;
                padding-left: 110px;
                color: #32343F;
            }

            .btn-file {
                width: 86px;
                height: 28px;
                background: #079DB2;
                box-shadow: 0px 4px 6px rgba(3, 12, 53, 10%);
                border-radius: 6px;
                color: #FBFBFB;
                font-size: 14px;
                line-height: 20px;
                padding: 4px 16px;
                position: absolute;
                top: 8px;
                left: 12px;
                text-align: center;
            }
        }

        .col-remove-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 44px;
        }

        .group-field-repeater {
            position: relative;
            margin-right: -42px;
        }

        .group-btn {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 48px;

            .btn {
                width: 244px;
                height: 46px;
                display: flex;
                justify-content: center;
                align-items: center;
                box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
                border-radius: 30px;
                border: 1px solid $color-white;
                font-style: normal;
                font-weight: 400;
                font-size: 18px;
                line-height: 26px;
            }

            .btn-reload {
                color: $color-primary1v2;
                border: 1px solid $color-primary1v2;
            }

            .btn-save-disable {
                background: $color-palette3;
                color: $color-palette4;
                border: 1px solid $color-palette3;
            }

            .btn-save {
                background: $color-brand1;
                color: $color-palette4;
                border: 1px solid $color-brand1;
            }
        }

        .input-review {
            display: flex;
            width: 100%;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 25%);
            border-radius: 8px !important;
            overflow: hidden;

            .file-browser-mask {
                box-shadow: none !important;
            }

            .button-review {
                width: 82px;
                background: $color-primary1;
                color: $color-st16;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .ck-custom {
            .cke_top {
                background: $color-white;
                border: none;
                border-bottom: 1px solid $color-palette3;
            }

            .cke_chrome {
                border: 1px solid $color-palette3;
            }
        }

        .item-multiple-select2 {
            textarea {
                display: none;
            }
        }

        .item-multiple-select2 .select2-container .select2-selection--multiple {
            min-height: 46px;
        }

        .item-multiple-select2 .select2-container .select2-selection--multiple .select2-selection__rendered {
            top: 6px;
        }

        .item-multiple-select2 .select2-container--default .select2-selection--multiple {
            background: $color-palette4 url(../images/list-new/arrow-select.svg) no-repeat;
            background-position: center right 15px;
        }

        .row-form {
            border-bottom: 1px solid $color-white;
            margin-bottom: 24px;
        }

    }

    .select2-selection__placeholder {
        color: #C8D1D1 !important;
        //font-style: normal;
        //font-weight: 500;
        font-size: 14px;
    }
}

#dashboard-list-job {
    padding: 50px 24px 90px;

    .layout1 {
        margin-bottom: 24px;
    }

    .status-active {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-brand;
    }

    .status-inactive {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-secondary1;
    }

    .bt-dt {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1;
        background: $color-st22;
    }

    .bt-dt {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1;
        background: $color-st22;
    }

    .bt-dt {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1;
        background: $color-st22;
        padding: 2px 12px;
    }

    .bt-dt {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1;
        background: $color-st22;
        padding: 2px 12px;
        border-radius: 12px;
    }

    .bt-du {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-brand;
        background: $color-secondary3;
        padding: 2px 12px;
        border-radius: 12px;
    }

    .bt-hht {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-secondary1;
        background: $color-palette3;
        padding: 2px 12px;
        border-radius: 12px;
    }

}

.card-table {
    padding: 24px;
    background: $color-white;
    box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
    border-radius: 8px;

    .title {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .input-table {
        input {
            padding: 12px 20px;
            width: 100%;
            height: 50px;
            background: $color-palette4;
            border: 1px solid $color-st12;
            border-radius: 10px;
        }

        .search {
            padding-left: 50px;
            background: $color-palette4 url("../images/dashboard-ctv/search-icon.svg");
            background-repeat: no-repeat;
            background-position: left 20px center;
        }

        .search2 {
            padding-left: 50px;
            background: $color-palette4 url("../images/dashboard-ctv/icon-search2.svg");
            background-repeat: no-repeat;
            background-position: left 20px center;
        }
    }

    .button-add-item {
        display: inline-block;
        padding: 12px 56px 11px 24px;
        box-shadow: 0px 4px 10px rgba(226, 88, 34, 0.2);
        border-radius: 8px;
        background: $color-brand1;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette4;
        background-image: url(../images/dashboard-ctv/icon-add-item.svg);
        background-repeat: no-repeat;
        background-position: right 24px center;
    }

    .header {
        margin-bottom: 18px;
    }

    .content {
        margin-bottom: 42px;

        table {
            width: 100%;

            tr {
                background: $color-palette4;
            }

            tr:has(> .fix-col ul.show) {
                z-index: 999999;
                position: relative;
            }

            ;

            tr:nth-of-type(odd) {
                background: $color-st12;
            }

            tr:nth-child(n+2):hover {
                background: $color-st22 !important;
                box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
                position: relative;
                z-index: 9;
            }

            th {
                height: 48px;
                vertical-align: middle;
                padding: 0 16px;
                border-bottom: 1px solid $color-st12;
                position: relative;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 22px;
                color: $color-secondary1;
            }

            td {
                padding: 5px 16px;
                font-style: normal;
                font-weight: 400;
                font-size: 14px;
                line-height: 40px;
                color: $color-palette1;
            }

            .order-down:before {
                content: '';
                background-image: url(../images/dashboard-ctv/table-arrow-down.svg);
                background-position: 100%;
                width: 10px;
                height: 6px;
                position: absolute;
                right: 20px;
                bottom: 17px;
                background-repeat: no-repeat;
            }

            .order-up:after {
                content: '';
                background-image: url(../images/dashboard-ctv/table-arrow-down.svg);
                background-position: 100%;
                width: 10px;
                height: 6px;
                position: absolute;
                right: 20px;
                top: 17px;
                transform: rotate(3.142rad);
                background-repeat: no-repeat;
            }

            .t-header {
                background: $color-palette4 !important;
            }

            .button-action-table {
                padding: 0;
                width: 40px;
                height: 40px;
                border: none;
                background: url(../images/dashboard-ctv/action-table.svg);
                background-position: center;
            }

            .button-action-table.show {
                background: url(../images/dashboard-ctv/action-table-show.svg);
            }

            .btn-group {
                ul {
                    padding: 0;
                    border: none;
                    background: $color-palette4;
                    box-shadow: 0px 2px 8px rgba(117, 131, 142, 0.04), 0px 16px 24px rgba(52, 60, 68, 0.12);
                    border-radius: 8px;
                    overflow: hidden;
                    width: 170px;
                    z-index: 999999;

                    li {
                        a {
                            img {
                                margin-right: 8px;
                            }

                            display: block;
                            padding: 12px;
                            font-style: normal;
                            font-weight: 400;
                            font-size: 12px;
                            line-height: 16px;
                            color: $color-palette1;
                        }

                        a:hover {
                            background: $color-st22;
                            box-shadow: 0px 2px 12px rgba(112, 131, 187, 0.45);
                        }
                    }
                }
            }

            .status-accepted {
                color: $color-primary3;
            }

            .status-rejected {
                color: $color-secondary1;
            }

            .status-pass-interview {
                color: $color-primary1;
            }

            .status-onboarded {
                color: $color-primary4;
            }

            .status-cancel {
                color: $color-primary1v2;
            }

            .status-fail-interview {
                color: $color-st23;
            }

            .status-pending-review {
                color: $color-brand;
            }

            .status-paid {
                color: $color-brand1;
            }
        }
    }

}

#dashboard-list-recruitment {
    .right-header {
        display: flex;
        gap: 24px;
    }

    .layout1 {
        margin-bottom: 24px;
    }

    #form-search {
        width: 100%;
    }

    .input-table {
        input {
            padding-left: 50px;
            background: $color-palette4 url("../images/dashboard-ctv/search-icon.svg");
            background-repeat: no-repeat;
            background-position: left 20px center;
        }
    }

    .content-dashboard-list-recruitment {
        padding: 50px 24px;
    }

    .card-recruitment {
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
    }

    .content-header-recruitment {
        .title {
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            margin-bottom: 22px;
            color: $color-palette1;

            span {
                color: $color-brand1;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
            }
        }

        .list {
            ul {
                li {
                    display: inline-block;
                    margin-right: 80px;
                    color: $color-secondary1;

                    span {
                        color: $color-palette1;
                    }
                }
            }
        }
    }
}

.modal-change-status {
    .close {
        width: 21px;
        height: 18px;
        background-image: url(../images/dashboard-ctv/back.svg);
        background-repeat: no-repeat;
        position: absolute;
        top: 52px;
        left: 30px;
    }

    .next-step {
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        position: absolute;
        color: $color-brand1;
        top: 52px;
        right: 30px;
    }

    #text-warning {
        margin-bottom: 16px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1v2;
        padding-left: 20px;
        background-image: url(../images/dashboard-ctv/waning.svg);
        background-position: top 3px left;
        background-repeat: no-repeat;
    }

    .modal-dialog {
        max-width: 864px;
    }

    .modal-body {
        padding: 48px;
        background-image: url(../images/bg-form-login-v2.svg);
        background-repeat: no-repeat;
        background-position: top 48px right 48px;
    }

    .header {
        text-align: center;
        font-style: normal;
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        color: $color-palette1;
        margin-bottom: 48px;
    }

    .modal-content {
        background: $color-palette4;
        box-shadow: 0px 6px 20px rgba(255, 255, 255, 0.4);
        border-radius: 8px;
    }

    #select2-status-container[title="Pending review"] {
        color: $color-brand;
    }

    #select2-status-container[title="Accepted"] {
        color: $color-primary3;
    }

    #select2-status-container[title="Rejected"] {
        color: $color-secondary1;
    }

    #select2-status-container[title="Pass-interview"] {
        color: $color-primary1;
    }

    #select2-status-container[title="Fail-interview"] {
        color: $color-st23;
    }

    .select2-results__option--disabled {
        display: none;
    }
}

.main-form-popup {
    margin-bottom: 24px;

    .item-form {
        margin-bottom: 24px;

        label {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: $color-palette1;
            display: block;
            margin-bottom: 8px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered,
        .select2-container .select2-selection--single,
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            line-height: 44px;
            height: 44px;
        }

        .select2-container {
            line-height: 44px;
            height: 44px;
        }

        textarea {
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -moz-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-appearance: none;
            border-radius: 8px;
            background: $color-palette4;
            width: 100%;
            min-height: 132px;
            padding: 11px 12px;
            border: none;
            outline: none;
        }

        input {
            width: 100%;
            border: none;
            outline: none;
            box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -moz-box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
            -webkit-appearance: none;
            line-height: 44px;
            height: 44px;
            border-radius: 8px;
            background: $color-palette4;
        }

    }

}

.footer-button-form-popup {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 48px;
    text-align: center;

    .button {
        width: 224px;
        height: 46px;
        border: none;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
        border-radius: 30px;
    }

    .button-cancel {
        font-style: normal;
        font-weight: 400;
        font-size: 18px;
        line-height: 26px;
        color: $color-primary1v2;
        border: 1px solid $color-primary1v2;
        background: $color-palette4;
    }

    .button-confirm {
        color: $color-palette4;
        background: $color-brand1;
        border: 1px solid $color-brand1;
    }
}

#dashboard-employer {
    padding: 48px 24px;

    .layout1 {
        margin-bottom: 50px;
    }
}

#modal-crop-ava {
    #upload-demo {
        height: 200px;
        width: 200px;
        margin: 0 auto;
        margin-bottom: 50px;
    }

    .upload-msg {
        text-align: center;
        padding: 50px;
        font-size: 22px;
        color: $color-palette1;
        margin: 0 auto;
        width: 100%;
        border: 1px solid $color-palette3;
    }

    .upload-result {
        width: 224px;
        height: 46px;
        padding: 10px;
        text-align: center;
        box-shadow: 0px 4px 10px rgba(121, 132, 165, 25%);
        border-radius: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        background: $color-brand1;
        color: $color-palette4;
        border: 1px solid $color-brand1;
    }

    .header {
        font-weight: 800;
        font-size: 26px;
        line-height: 38px;
        text-align: center;
        color: $color-palette1;
        margin-bottom: 48px;
    }

    .btn-close {
        position: absolute;
        top: 20px;
        right: 20px;
    }

    .modal-body {
        padding: 48px;
    }
}

#header-about-us {
    background: url("../images/about-us/bg-header.svg") no-repeat;
    background-size: cover;
    padding-top: 77px;
    padding-bottom: 48px;

    .label-header-about-us {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 17px;
    }

    .label-header-about-us-mobile {
        display: none;
    }

    .title-about-us {
        font-style: normal;
        font-weight: 800;
        font-size: 32px;
        line-height: 49px;
        text-align: center;
        margin-bottom: 43px;
        color: $color-palette1;

    }

    .content-about-us {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 32px;
        color: $color-palette1;

        p {
            margin-bottom: 30px;
        }
    }

    .image-header {
        text-align: center;
        position: relative;
        margin: 0 -13.5px;
    }
}

#list-info-about-us {
    padding-top: 26px;
    padding-bottom: 0;
    background: linear-gradient(270.73deg, rgba(32, 143, 161, 0.25) 0.05%, rgba(255, 255, 255, 0.25) 19.31%, rgba(32, 143, 162, 0.25) 100%);

    .col-item-info-about-us {
        margin-bottom: 34px;
    }

    .text-info-about-us {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        display: flex;
    }

    .icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
        background-repeat: no-repeat;
    }

    .icon-1 {
        background-image: url("../images/about-us/icon-1.svg");
    }

    .icon-2 {
        background-image: url("../images/about-us/icon-2.svg");
    }

    .icon-3 {
        background-image: url("../images/about-us/icon-3.svg");
    }

    .icon-4 {
        background-image: url("../images/about-us/icon-4.svg");
    }

    .icon-5 {
        background-image: url("../images/about-us/icon-5.svg");
    }

    .icon-6 {
        background-image: url("../images/about-us/icon-6.svg");
    }

    .list-info-about-us-mobile {
        display: none;
    }
}

#vision-about-us {
    padding-top: 130px;
    padding-bottom: 116px;

    .wapper-image-vision-about-us {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .image-vision-about-us {
        text-align: center;
        position: relative;
        left: 35px;

        img {
            position: relative;
            z-index: 2;
        }
    }

    .image-vision-about-us:before {
        content: '';
        position: absolute;
        width: 216px;
        height: 196px;
        left: -70px;
        top: -50px;
        background: $color-palette9;
        border-radius: 6px;
        z-index: 1;
    }

    .image-vision-about-us:after {
        content: '';
        position: absolute;
        width: 100px;
        height: 98px;
        right: -39px;
        bottom: -40px;
        background: $color-palette9;
        border-radius: 6px;
        z-index: 1;
    }

    .title-vision-about-us {
        font-style: normal;
        font-weight: 800;
        font-size: 32px;
        line-height: 36px;
        color: $color-brand;
        text-align: center;
        margin-bottom: 34px;
    }

    .main-vision-about-us {
        padding: 52px 65px 51px;
        border: 10px solid rgba(0, 131, 165, 0.04);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 15px;
        margin-right: 88px;
    }

    .content-vision-about-us {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 30px;
        color: $color-st24;

        p {
            margin-bottom: 30px;
        }

        p:last-child {
            margin-bottom: 0;
        }
    }

}

#mission-about-us {
    padding: 28px 0 40px;

    .title-mission {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
        position: relative;
        margin-bottom: 15px;
    }

    .title-mission:before {
        content: '';
        width: 200px;
        height: 4px;
        position: absolute;
        top: -24px;
        left: 0;
        background: $color-brand1;
        border-radius: 6px;
    }

    .item-image-mission-about-us {
        margin-bottom: 16px;
    }

    .item-mission-about-us {
        text-align: center;
        padding: 75px 35px 90px;
        border: 10px solid $color-white;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.1);
        position: relative;
        z-index: 999;
        max-width: 459px;
        margin: 0 auto;
    }

    .item-mission-about-us.active {
        padding: 70px 30px 90px;
        box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    }

    .title-item-mission-about-us {
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        text-transform: capitalize;
        color: $color-palette1;
        margin-bottom: 52px;
        position: relative;
    }

    .title-item-mission-about-us:after {
        content: '';
        position: absolute;
        border-radius: 6px;
        width: 46px;
        height: 4px;
        margin: 0 auto;
        left: 0;
        right: 0;
        bottom: -24px;
        background: $color-st6;
    }

    .content-item-mission-about-us {
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 32px;
        text-align: center;
        color: $color-palette1;
        opacity: 0.8;
    }

    .row-mission-about-us {
        padding: 0 188px 10px;
        position: relative;
    }

    .image-mission-about-us {
        position: absolute;
        top: -138px;
        right: 0;
    }

}

#core-value-about-us {
    padding-bottom: 50px;

    .title-core-value {
        font-style: normal;
        font-weight: 800;
        font-size: 32px;
        line-height: 36px;
        margin-bottom: 65px;
        text-align: center;
        color: $color-palette9;
    }

    .core-value-about-us {
        padding: 57px 39px 58px;
        background: linear-gradient(244.12deg, rgba(255, 255, 255, 0.4) -22.22%, rgba(255, 255, 255, 0.4) 8.65%, rgba(200, 228, 230, 0.32) 28.22%, rgba(255, 255, 255, 0.4) 82.73%, rgba(13, 155, 179, 0.4) 125.96%);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.15);
        border-radius: 15px;
    }

    .item-core-value {
        display: flex;
    }

    .title-item-core-value {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        margin-bottom: 24px;
    }

    .title-item-core-value-st1 {
        color: $color-brand;
    }

    .content-item-core-value {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 28px;
        color: $color-secondary1;
    }

    .image-item-core-value {
        min-width: 118px;
        max-width: 118px;

        img {}
    }

    .left-item-core-value {
        .main-item-core-value {
            padding-right: 30px;
        }
    }

    .right-item-core-value {
        .main-item-core-value {
            text-align: right;
            padding-left: 30px;
        }

        flex-direction: row-reverse;
    }

    .row-core-value {
        margin-left: -20px;
        margin-right: -20px;

        .col-item-core-value {
            padding-left: 20px;
            padding-right: 20px;
        }
    }

    .title-item-core-value-st2 {
        color: $color-brand1;
    }

    .row-core-value-2 {
        .left-item-core-value {
            padding-right: 59px;
        }

        .right-item-core-value {
            padding-left: 59px;
        }
    }

    .row-core-value-1 {
        margin-bottom: 12px;
    }

    .center-item-core-value {
        display: block;
        text-align: center;
        max-width: 470px;
        margin: 0 auto;
        position: relative;
        margin-top: -39px;

        .image-item-core-value {
            margin-bottom: 20px;
            margin-left: auto;
            margin-right: auto;
        }
    }

    //.core-value-about-us-desktop{
    //    display: none;
    //}
    .core-value-about-us-mobile {
        display: none;
    }
}

#header-privacy-policy {
    padding-top: 60px;
    padding-bottom: 80px;

    .title-header-privacy-policy {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        position: relative;
        padding-top: 24px;
        margin-bottom: 24px;
    }

    .title-header-privacy-policy:before {
        content: '';
        background: $color-brand1;
        border-radius: 6px;
        width: 200px;
        height: 4px;
        position: absolute;
        top: 0;
    }

    .content-privacy-policy {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        color: $color-palette1;
    }

    .main-header-privacy-policy {
        max-width: 380px;
    }
}

#content-page-privacy-policy {
    padding-bottom: 80px;

    .title-item-page-privacy-policy {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        padding-top: 24px;
        line-height: 36px;
        color: $color-palette1;
        position: relative;
        margin-bottom: 24px;
    }

    .title-item-page-privacy-policy:before {
        content: '';
        position: absolute;
        top: 0;
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 36px;
        background: $color-brand1;
        border-radius: 6px;
        width: 200px;
        height: 4px;
    }

    .edit-item-page-privacy-policy {
        font-style: normal;
        font-size: 16px;
        line-height: 32px;
        color: $color-palette1;
        margin-bottom: 48px;

        b,
        strong {
            font-weight: 700;
        }
    }
}

#map-contact-us {
    position: relative;

    iframe {
        width: 100%;
        height: 600px;
        display: none;
    }

    iframe.active {
        display: block;
    }

    .group-google-map {
        position: absolute;
        padding: 12px 32px;
        background: rgba(52, 52, 52, 0.6);
        border-radius: 4px;
        top: 32px;
        right: 32px;

        ul {
            display: flex;
            gap: 16px;

            li {
                a {
                    padding: 10px 10px;
                    text-align: center;
                    min-width: 109px;
                    display: block;
                    background: $color-palette4;
                    border: 1px solid $color-palette9;
                    border-radius: 4px;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 16px;
                    color: $color-palette9;
                }

                a.active {
                    background: $color-palette9;
                    box-shadow: 0px 2px 15px 6px rgba(224, 255, 245, 0.4);
                    color: $color-white;
                }
            }
        }

    }
}

#content-contact-us {
    padding: 20px 0 60px;
    background: radial-gradient(50% 56.65% at 50% 50%, rgba(226, 239, 241, 0.15) 0%, rgba(160, 215, 224, 0.15) 92.19%);

    .content-contact-us {
        margin: 0 auto 48px;
        max-width: 1200px;
    }

    .main-contact-us {
        padding-top: 60px;
        padding-right: 180px;
    }

    .label-text-contact-us {
        font-style: normal;
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        color: $color-palette1;
        margin-bottom: 19px;
    }

    .title-page-contact-us {
        font-style: normal;
        font-weight: 800;
        font-size: 28px;
        line-height: 48px;
        color: $color-palette1;
        margin-bottom: 19px;
    }

    .list-contact-us {
        li {
            font-style: normal;
            font-size: 18px;
            line-height: 26px;
            color: $color-brand1;
            margin-bottom: 19px;

            b {
                font-weight: 600;
            }
        }

        .icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 7px;
            background-repeat: no-repeat;
            position: relative;
            top: 3px;
        }

        .icon-1 {
            background-image: url("../images/contact-us/icon-1.svg");
        }

        .icon-2 {
            background-image: url("../images/contact-us/icon-2.svg");
        }

        .icon-3 {
            background-image: url("../images/contact-us/icon-3.svg");
        }

        .icon-4 {
            background-image: url("../images/contact-us/icon-4.svg");
        }
    }

    .social-contact-us {
        li {
            display: inline-block;
            margin-right: 27px;

            .icon {
                width: 40px;
                height: 40px;
                display: block;
                background-repeat: no-repeat;
            }

            .icon-social-1 {
                background-image: url("../images/contact-us/icon-social-1.svg");
            }

            .icon-social-2 {
                background-image: url("../images/contact-us/icon-social-2.svg");
            }

            .icon-social-3 {
                background-image: url("../images/contact-us/icon-social-3.svg");
            }

        }
    }

    .list-contact {
        .title-contact {
            font-style: normal;
            font-weight: 800;
            font-size: 28px;
            line-height: 36px;
            text-align: center;
            color: $color-palette4;
            margin-bottom: 18px;
        }

        .list-infor {
            font-style: normal;
            font-weight: 600;
            font-size: 16px;
            line-height: 32px;
            color: $color-palette4;

            li {
                gap: 8px;
                margin-bottom: 18px;
                display: flex;

                .col-icon {
                    width: 20px;
                }

                .icon {
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    background-repeat: no-repeat;
                    position: relative;
                    top: 3px;
                }

                .icon-1 {
                    background-image: url("../images/contact-us/icon-list-contact-1.svg");
                }

                .icon-2 {
                    background-image: url("../images/contact-us/icon-list-contact-2.svg");
                }
            }

            li:last-child {
                margin-bottom: 0;
            }
        }

        .item-contact {
            background: $color-brand1;
            box-shadow: 0px 8px 25px rgba(52, 60, 68, 0.4);
            border-radius: 8px;
            padding: 23px 26px;
            height: 100%;
        }

        .row-contact {
            justify-content: center;
            margin-left: -24px;
            margin-right: -24px;
        }

        .col-contact {
            padding-left: 24px;
            padding-right: 24px;
        }
    }


}

.st-lv {
    display: inline-block;
    padding: 2px 12px;
    gap: 6px;
    border-radius: 12px;
    background: $color-secondary3;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    min-width: 60px;
    color: $color-brand;
    text-align: center;
}

.st-lf-f2 {
    background: $color-secondary4;
    color: $color-primary1;
}

.team-manager-icon-eye {
    width: 24px;
    height: 24px;
    background: url("../images/team-manager/eye.svg") no-repeat;
    display: block;
}

#collaborator-team-manager {
    padding: 48px;

    #form-search {
        width: 100%;
    }

    .right-header {
        display: flex;
        gap: 24px;
    }
}

#modal-item-team-manager {
    .modal-dialog {
        max-width: 900px;
    }

    .name {
        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        color: $color-palette1;
        margin-bottom: 12px;
        align-items: center;

        .data-f {
            margin-left: 10px;
        }
    }

    .icon-popup {
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 6px;
        position: relative;
        top: 3px;
    }

    .icon-phone {
        background: url("../images/team-manager/icon-phone.svg") no-repeat;
    }

    .icon-email {
        background: url("../images/team-manager/icon-email.svg") no-repeat;
    }

    .list-info {
        margin-bottom: 24px;
    }

    .list-info li {
        display: inline-block;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        color: $color-primary1;
        margin-right: 25px;
    }

    .header-tab {
        display: flex;
        border-bottom: 1px solid $color-palette3;
        box-shadow: 0px -2px 6px rgba(121, 132, 165, 0.05);
        border-radius: 8px 8px 0px 0px;

        li {
            a {
                height: 58px;
                width: 140px;
                display: flex;
                justify-content: center;
                align-items: center;
                position: relative;
                font-style: normal;
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
                color: $color-palette1;
                text-align: center;
            }

            a:before {
                content: "";
                width: 100%;
                height: 2px;
                border-radius: 2px;
                background: $color-palette3;
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
            }

            a.active:before {
                background: $color-brand;
            }
        }
    }

    .item-popup {
        padding: 4px 20px;
    }

    .bt-stt {
        width: 68px;
        height: 68px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url("../images/team-manager/bg-number.svg") no-repeat;

        font-style: normal;
        font-weight: 800;
        font-size: 20px;
        line-height: 28px;
        color: $color-brand1;
    }

    .wapper-icon {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .col-item-popup {
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: $color-secondary1;
        display: flex;
        align-items: center;

        .money {
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: $color-palette1;
        }
    }

    .icon-hand {
        width: 16px;
        height: 15px;
        background: url("../images/team-manager/icon-hand.svg") no-repeat;
        margin-right: 5px;
    }

    .icon-hand-2 {
        width: 16px;
        height: 15px;
        background: url("../images/team-manager/icon-hand-2.svg") no-repeat;
        margin-right: 5px;
    }

    .item-popup {
        background: $color-palette4;
        box-shadow: 0px 2px 6px rgba(121, 132, 165, 0.25);
        border-radius: 4px;
        margin-bottom: 20px;
    }

    .item-tab {
        padding-left: 12px;
        padding-right: 36px;
        overflow: scroll;
        height: 480px;

        .item-popup:nth-child(1) .bt-stt {
            background: url("../images/team-manager/bg-number-1.svg") no-repeat;
        }

        .item-popup:nth-child(2) .bt-stt {
            background: url("../images/team-manager/bg-number-2.svg") no-repeat;
        }

        .item-popup:nth-child(3) .bt-stt {
            background: url("../images/team-manager/bg-number-3.svg") no-repeat;
        }
    }

    .item-tab::-webkit-scrollbar {
        width: 4px;
    }

    .tab-content {
        position: relative;
        margin-right: -25px;
    }

    .wapper-tab {
        padding-right: 24px;
    }

    .btn-close {
        position: absolute;
        top: 24px;
        right: 24px;
        width: 44px;
        height: 44px;
        background: url("../images/team-manager/close-popup.svg") no-repeat;
    }
}


@media only screen and (max-width: $desktop-width3) {
    #candidate-introduction-manager {
        .group-item {
            .item {
                .content {
                    .list-info {
                        ul {
                            li {
                                width: 212px;
                            }
                        }
                    }
                }
            }
        }

    }
}

@media only screen and (max-width: $desktop-width1) {
    #banner-ctv {
        .slider-ctv {
            img {
                //height: 568px;
            }
        }
    }

    #candidate-introduction-manager {
        .header {
            .search {
                width: calc(100% - 300px);

                form {
                    width: 100%;
                }

                .input-search {
                    input {
                        width: 100%;
                    }
                }
            }
        }
    }

    #procedure-recland-register-link {
        .icon-procedure:before {
            width: 40%;
            right: -20%;
        }
    }

    #choose-ntd {
        .list-choose {
            left: 0;
        }
    }
}

@media only screen and (max-width: $desktop-width1280) {
    #header-about-us {
        .image-header {
            margin: 90px auto 0;
            max-width: 547px;
        }
    }

    #vision-about-us {
        .main-vision-about-us {
            margin-right: 0;
            margin-left: 25px;
        }
    }

    #mission-about-us {
        .row-mission-about-us {
            padding: 0 100px 10px;
        }
    }

    #procedure-recland-register-link {
        .col-procedure {
            width: 20%;
        }
    }

    #choose-ntd {
        .col-img {
            .main-col-img {
                max-width: 631px;
                margin: 0 auto;
            }
        }

        .list-choose {
            padding-right: 0;
            position: relative;
            margin-left: -23px;
        }
    }
}


@media only screen and (max-width: $desktop-width2) {}

@media only screen and (max-width: $mobile-ipad) {
    #header {
        .right-header {
            .button-login-header {
                padding-right: 10px;
                padding-left: 10px;
            }

            .item-right-header {
                margin-right: 13px;
            }
        }
    }

    #page-login-collaborator {
        .wapper-form-login {
            padding: 96px 30px 24px;
        }
    }

    #procedure-ntd {
        .image-item-procedure-ntd:before {
            display: none;
        }
    }

    #chance-ctv {
        .item-change-ctv {
            .title-item-change-ctv {
                min-height: 72px;
            }
        }

        .col-chance-ctv {
            width: 33.3333333%;
        }

        .owl-theme {
            .owl-nav [class*=owl-]:hover {
                background-image: url(../images/icon-arrow.svg);
            }

            .owl-nav [class*='owl-']:active {
                background-image: url(../images/icon-arrow-hover.svg);
                background-color: initial;
            }
        }
    }

    #banner-ctv {
        .search-box {
            bottom: 30px;
        }

        .owl-dots {
            display: none;
        }
    }

    #mission-about-us {
        .row-mission-about-us {
            padding: 0 0 10px;
        }

        .image-mission-about-us {
            position: absolute;
            top: 10px;
            right: 0;
            width: 480px;
        }
    }
}

@media only screen and (max-width: $mobile-ipad-with2) {
    #header {
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .wapper-logo {
            width: 100%;
        }

        .logo {
            img {
                height: 28px;
            }
        }

        .col-right-header {
            display: none;
        }

        .height-header {
            height: 57px;
        }

        .button-menu-mobile {
            display: block;
        }

        .button-notification-mobile {
            display: block;
        }
    }

    #page-login-collaborator {
        .right-login-collaborator {
            padding-top: 130px;
        }

        .wapper-form-login {
            padding: 96px 126px 24px;
        }

        .row-form-register {
            flex-flow: column-reverse wrap-reverse;
        }
    }

    #copyright {
        font-size: 18px;
        padding: 12px 0;
    }

    #copyright-v2 {
        font-size: 18px;
        padding: 12px 0;
    }

    #banner {
        .main-banner {
            .title-banner {
                font-style: normal;
                font-weight: 800;
                font-size: 28px;
                line-height: 36px;
            }

            .list-icon {
                ul {
                    align-items: flex-start;
                }
            }
        }
    }

    #job {
        background: $color-white;

        #header-job {
            height: 40px;
            background: rgba(0, 0, 0, 0.05);
        }
    }

    .row-main-job-detail {
        flex-direction: column-reverse;
    }

    #main-job-detail {
        margin-bottom: 0;

        .sidebar-job {
            .list-info-job {
                ul {
                    display: flex;
                    flex-wrap: wrap;

                    li {
                        width: 33.33333%;
                        flex: 0 0 auto;
                        padding-right: 30px;
                    }

                    li:nth-child(n+3) {
                        //display: none;
                    }
                }
            }

            .sidebar-skill-desktop {
                display: none;
            }
        }

        .content-job {
            .sidebar-skill-mobi {
                display: block;
            }
        }

    }


    #detail-company {
        .detail-company {
            .main-detail-company {
                .info-company {
                    flex-wrap: wrap;
                }

                .logo-company {
                    width: 100%;
                    text-align: center;
                    max-width: initial;

                    img {
                        max-width: 115px;
                    }
                }

                .main-info-company {
                    width: 100%;
                }

                .col-line-info-company {
                    .line-info-company {
                        margin-bottom: 16px;
                    }
                }

                .col-line-info-company:last-child {
                    .line-info-company {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    #with-recland {
        background-image: none;

        .item-with-recland-slider {
            display: none;
        }

        .item-with-recland-slider:first-child {
            display: block;
        }

        .col-with-recland:nth-child(n+7) {
            display: none;
        }
    }


    #hot-new {
        .col-hot-new-1 {
            margin-right: 0;
        }

        .col-hot-1,
        .col-hot-2,
        .col-hot-3 {
            margin-bottom: 12px;
        }

        .col-hot-new-2 {
            margin-left: 0;
            margin-right: 0;
        }

        .image-main-hot-new-2 {
            img {
                width: 100%;
            }
        }

        .col-hot-new-3 {
            margin-left: 0;
        }

        .item-col-hot-new-3 {
            margin-bottom: 12px;
        }
    }

    #col-detail-job {
        display: none;

        .back-job-mobile {
            display: block;
        }
    }

    #search-job-group,
    #search-job-group-v2 {
        display: none;
    }

    #wapper-serch-job-mobile {
        display: block;
    }

    #col-detail-job.show-render-job {
        display: block;
    }

    #main-col-job {
        transition: 0.4s;
        height: 100%;
        padding-right: 0;

        .list-col-job {
            max-height: inherit;
            padding-right: 4px;
        }

        .list-col-job:hover {
            padding-right: 0;
        }
    }

    #main-col-job.hide-col {
        overflow: hidden;
        height: 0;
    }

    #search-job-group.hide-col {
        display: none;
    }

    #main-search-job {
        padding-top: 12px;

        #header-job {
            .header-job {
                padding-top: 20px;
            }
        }

        .back-mobile {
            display: block;
        }
    }

    #detail-company {
        .detail-company {
            .main-detail-company {
                padding: 0;
            }
        }
    }

    #wapper-page-ctv {
        .sidebar {
            display: none;
        }

        .page-ctv {
            width: 100%;
        }
    }

    #page-user-profile {
        .header-tab {
            li {
                width: 50%;

                a {
                    width: 100%;
                }
            }
        }
    }

    .card-type3 {
        .list-new-cv {
            height: auto;
            overflow-y: inherit;
        }
    }

    #dashboard-collaborator {
        padding: 30px 15px;
    }

    #dashboard-collaborator {
        .layout1 {
            margin-bottom: 0;

        }

        .layout2 {
            margin-bottom: 15px;
        }
    }

    .col-item-count {
        margin-bottom: 15px;
    }

    .card-table {
        .title {
            margin-bottom: 15px;
        }

        .input-table {
            margin-bottom: 15px;
        }

        .button-add-item {
            width: 100%;
            text-align: center;
        }

        .content {
            .table {
                overflow-x: auto;
                position: relative;
            }

            table {
                position: relative;

                th {
                    white-space: nowrap;
                }

                //td:first-child, th:first-child {
                //    position:sticky;
                //    left:0;
                //    z-index:1;
                //    background-color:grey;
                //}
                td.fix-col,
                th.fix-col {
                    position: sticky;
                    right: 0;
                    z-index: 1;
                }

                tr td.fix-col {
                    background: $color-palette4;
                }

                tr:nth-of-type(odd) td.fix-col {
                    background: $color-st12;
                }
            }

            //.fix-col {
            //    position: -webkit-sticky;
            //    position: sticky;
            //    width: 150px;
            //}

        }

        .group-paginate {
            .paginate {
                padding: 0 5px;

                .number {
                    padding: 0 2.5px;
                }
            }

            .goto {
                padding: 0 5px;
            }

        }
    }

    #content-contact-us {
        .col-contact {
            margin-bottom: 30px;
        }

        .item-contact {
            max-width: 414px;
            margin: 0 auto;
        }

        .main-contact-us {
            padding-top: 60px;
            padding-right: 0;
        }
    }

    //about us
    #header-about-us {
        .row-header-about-us {
            flex-flow: column-reverse;
        }

        .label-header-about-us-mobile {
            display: block;
            text-align: left;
        }

        .label-header-about-us-desktop {
            display: none;
        }

        .image-header {
            margin: 0 auto;
            max-width: 100%;
        }
    }

    #list-info-about-us {
        padding-top: 34px;
        padding-bottom: 7px;

        .list-info-about-us-mobile {
            display: flex;
            padding-bottom: 1px;
        }

        .list-info-about-us-desktop {
            display: none;
        }
    }

    #vision-about-us {
        .wapper-image-vision-about-us {
            margin-bottom: 64px;
        }

        .main-vision-about-us {
            max-width: 591px;
            margin: 0 auto;
        }

    }

    #mission-about-us {
        .image-mission-about-us {
            position: initial;
            width: auto;
            text-align: center;
            max-width: 628px;
            margin: 0 auto;
        }

        .row-mission-about-us {
            display: flex;
            flex-flow: column-reverse;
        }
    }

    #core-value-about-us {
        .main-item-core-value {
            width: calc(100% - 130px);
        }

        .core-value-about-us-desktop {
            display: none;
        }

        .core-value-about-us-mobile {
            display: block;
        }

        .main-item-core-value-3 {
            width: 100%;
        }

        .col-item-core-value {
            margin-bottom: 20px;
        }

        .center-item-core-value {
            margin-top: 0;
        }
    }


    #procedure-recland-register-link {
        .icon-procedure:before {
            display: none;
        }

        .col-procedure {
            width: 33%;
            margin-bottom: 20px;
        }
    }

    #choose-ntd {
        .list-choose {
            padding-right: 0;
            margin-left: 0;
        }
    }

}


@media only screen and (max-width: $mobile-width) {}

@media only screen and (max-width: $mobile-width2) {
    #page-login-collaborator {
        .title-form-login-collaborator {
            margin-bottom: 24px;
        }

        .wapper-form-login {
            padding: 96px 15px 24px;
        }

        .wapper-form-register {
            padding: 48px 15px 24px;

            .title-form-register {
                font-size: 20px;
                line-height: 28px;

                span {
                    display: block;
                }
            }
        }

        .title-right-register-collaborator-from-link {
            font-size: 29px;
            line-height: 46px;
        }
    }

    .modal-landing-employee {
        .modal-body {
            padding-left: 12px;
            padding-right: 12px;
            padding-bottom: 24px;
        }

        .button-form-login {
            margin-bottom: 48px;
        }

        .title-modal-login-employee {
            span {
                display: block;
            }
        }
    }

    #ecosystem-ctv {
        padding-top: 0;
    }

    #page-forgot-password-collaborator {
        .main-form-forgot-password {
            padding-left: 12px;
            padding-right: 12px;
        }
    }

    #footer {
        .social-mobile {
            display: block;
        }

        .social-desktop {
            display: none;
        }
    }

    #footer-v2 {
        padding-bottom: 24px;

        .form-footer {
            .group-form {
                margin-bottom: 10px;
            }

            .title {
                font-size: 28px;
            }
        }

        .description {
            display: none;
        }

        .main-footer {
            padding-top: 0;

            .title-footer {
                font-size: 24px;
            }

            .description-footer {
                font-size: 18px;
            }
        }

        .social-mobile {
            display: block;

            img {
                width: 42px;
                height: 48px;
            }
        }

        .social-desktop {
            display: none;
        }
    }

    #banner {
        height: 182px;

        .main-banner {
            padding-top: 18px;
            padding-bottom: 18px;

            .title-banner {
                font-weight: 600;
                font-size: 24px;
                line-height: 32px;
                margin-bottom: 16px;
            }

            .list-icon {
                //display: none;
            }

            .small-title-banner {
                font-weight: 600;
                font-size: 18px;
                line-height: 26px;
            }

            .btn-banner-st1,
            .btn-banner-st2,
            .btn-banner-st3,
            .btn-banner-st4,
            .btn-banner-st5 {
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
            }
        }
    }

    .list-icon-mobile {
        //display: block;
        padding: 20px 0;

        ul {
            justify-content: center;
        }
    }

    .list-icon-mobile-page-list {
        padding-bottom: 0;
    }

    #header-job {
        .header-job {
            padding-top: 24px;
            padding-bottom: 24px;
            gap: 24px;

            .item-header-job {
                padding: 10px 16px;
                width: 275px;
                gap: 4px;
            }

            .icon-item-header-job {
                width: 52px;
                height: 52px;
                flex: 0 0 auto;

                img {
                    width: 14px;
                }
            }

            .content-item-header-job {
                .line1 {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                }

                .line2 {
                    font-weight: 600;
                    font-size: 12px;
                    line-height: 16px;
                }
            }
        }
    }

    #main-job-detail {
        .sidebar-job {
            .list-info-job {
                padding-left: 55px;

                ul {
                    li {
                        //width: 100%;
                    }
                }
            }
        }
    }

    #with-recland {
        .item-with-recland {
            .logo-item-with-recland {
                width: 42px;
            }
        }
    }


    #banner-ctv {
        overflow: auto;

        .slider-ctv {
            img {
                //height: 323px;
            }
        }

        .owl-theme {
            .owl-dots {
                bottom: 8px;

                span {
                    width: 12px !important;
                    height: 12px !important;
                }
            }
        }

        .main-form {
            width: 100%;
            height: 48px;
        }

        .wapper-search-form {
            button {
                font-size: 12px;
                line-height: 22px;
                width: 100px;

                span {
                    display: none;
                }
            }

            input {
                font-size: 14px;
                line-height: 20px;
                width: calc(100% - 100px);
            }

            input::placeholder {
                color: $color-brand1;
            }
        }

        .search-box {
            position: initial;
            bottom: 25px;
            left: 0;
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
            right: 0;
            margin: 30px auto;
        }
    }

    #ecosystem-ctv {
        .description {
            margin-bottom: 48px;
        }
    }

    #chance-ctv {
        .row-chance-ctv {
            margin-left: -10px;
            margin-right: -10px;
        }

        .col-chance-ctv {
            flex: 0 0 auto;
            width: 33.333333333%;
            padding-left: 10px;
            padding-right: 10px;
            margin-bottom: 12px;

            .logo {
                margin-bottom: 16px;
            }
        }

        .item-change-ctv {
            padding: 16px 15px;

            .title-item-change-ctv {
                min-height: 52px;
                margin-bottom: 16px;
            }

            .line-price {
                font-size: 14px;
                min-height: 22px;
                margin-bottom: 6px;
                line-height: 22px;
                padding: 0;
            }

            .footer-item-change-ctv {
                padding-top: 10px;

                .btn {
                    padding: 1px 1px;
                    font-size: 10px;
                }
            }

            .header-item-change-ctv {
                margin-bottom: 18px;
                font-size: 10px;
            }
        }
    }

    #choose-ctv {
        .col-image {
            margin-bottom: 24px;
        }

        .list-choose {
            max-width: 470px;
            margin: 0 auto;
        }
    }

    #procedure-ctv {
        .title {
            font-size: 20px;
        }

        .content-procedure-ctv {
            padding-top: 32px;
        }
    }

    #count-ctv {
        padding-bottom: 24px;

        .text-count {
            margin-bottom: 24px;
        }

        .header-count-ctv {
            max-width: 311px;
            display: block;
            margin: 0 auto 32px;
        }

        .number-count {
            font-weight: 800;
            font-size: 24px;
            line-height: 32px;
            margin-bottom: 12px;
        }

        .text-count {
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
        }
    }

    #banner-ntd {
        overflow: auto;

        .owl-carousel {
            .owl-item {
                img {
                    //height: 316px;
                }
            }
        }

        .owl-theme {
            .owl-dots {
                display: none;
                bottom: 8px;

                span {
                    width: 12px !important;
                    height: 12px !important;
                }
            }
        }

        .content {
            .line-button {
                text-align: center;
            }

            .button-ntd {
                font-size: 14px;
                line-height: 20px;
                min-width: 130px;
                height: 38px;
                padding: 6px;
            }

            .button-ntd-st1 {
                margin-left: 15px;
                margin-right: 15px;
            }

            .button-ntd-st2 {
                margin-left: 15px;
                margin-right: 15px;
            }

            .line-button {
                padding-left: 0;
                position: initial;
            }
        }

        .wapper-main-banner {
            padding-top: 30px;
            padding-bottom: 30px;
            position: initial;
        }

    }

    #choose-ntd {
        padding-top: 0;
    }

    #company-width {
        .title-company-width {
            margin-left: 0;
            text-align: center;
        }

        .title-company-width:before {
            left: 93px;
        }
    }

    .wapper-content-hot-new-2 {
        height: 100%;
    }

    #tags-new {
        .header-tab-new {
            .tabs-title {
                padding-left: 0;

                li {
                    margin-right: 12px;
                }
            }
        }

        .main-tab {
            .row-tab {
                .col-tab {
                    margin-bottom: 12px;
                }
            }
        }
    }

    #col-post-video {
        .video-item {
            .thumb-video-item {
                img {
                    width: 100%;
                }
            }
        }
    }

    #col-trending-video {
        .col-trending-video {
            margin-left: 0;
        }
    }

    #list-recent-new {
        .main-list-recent-new {
            .item-recent-new {
                .thumbnail {
                    img {
                        width: 100%;
                    }
                }
            }

            .content {}
        }

        .bottom-list-new {
            .paginate {
                .number {
                    padding: 0 2px;
                }
            }

            .goto {
                padding: 0 2px;
            }
        }
    }

    #main-col-job {
        .list-col-job {
            .logo {
                max-width: 38px;
            }
        }

        .row-bottom-job {
            //display: block;
        }

        .col-bottom-job {
            text-align: left;
        }

        .bottom-col-job {
            .paginate {
                .number {
                    padding: 0 8px;
                }
            }

            .goto {
                padding: 0 8px;

                input {
                    width: 40px;
                    height: 40px;
                }
            }
        }

        .item-col-job.active {
            box-shadow: none;
            border-right: 2px solid $color-white;
        }

        .item-col-job.active-mobile {
            border-right: 2px solid #61D4EA;
            box-shadow: 0px 4px 10px rgba(121, 132, 165, 25%);
        }
    }

    #list-info-job-search-job {
        margin-bottom: 5px;

        .item-info-job {
            width: 100%;
        }

        .item-info-job:nth-child(n+3) {
            display: none;
        }
    }

    .show-info-job-mobile {
        //display: block;
    }

    #hot-new {
        .col-hot-new-1 {
            .image-col-hot-new-1 {
                height: auto;
            }
        }
    }

    #col-post-video {
        .video-item {
            .thumb-video-item {
                iframe {
                    width: 100%;
                }
            }
        }
    }

    #chance-ctv {
        .item-slider-change-ctv {
            padding: 24px 36px;
        }

        .item-change-ctv {
            padding: 23px 15px;
        }
    }

    #candidate-introduction-manager {
        .header {
            padding: 15px;

            .button-header {
                width: 100%;
            }

            .search {
                width: 100%;
                margin-bottom: 15px;

                .input-search:after {
                    display: none;
                }
            }
        }

        .select-option-group {
            padding: 12px 15px;
        }

        .group-item {
            padding: 0 15px;

            .item {
                padding-top: 40px;

                .ava {
                    margin: 0 auto;
                }

                .content {
                    width: 100%;
                    max-width: 100%;

                    .list-info {
                        ul {
                            li {
                                width: 100%;
                                margin-right: 0;
                                margin-bottom: 10px;
                                display: block;

                                .candidate-currency {
                                    width: auto;
                                    display: unset;
                                }

                                sub {
                                    bottom: 0;
                                }
                            }
                        }
                    }
                }

                .status-action-icon {
                    width: 100%;
                    left: 0;
                    right: 0;
                    top: 15px;
                    padding: 0 15px;
                }

                .status-group {
                    width: 100%;
                    left: 0;
                    right: 0;
                    top: 15px;
                    display: flex;
                    justify-content: space-between;
                    padding: 0 15px;

                    .button {
                        a {
                            font-size: 12px;
                            padding: 3px 14px;
                        }
                    }

                    .status {
                        margin-bottom: 0;
                    }
                }
            }
        }
    }

    .header-layout-edit {
        padding: 15px;

        a {
            font-size: 12px;
        }

        .title-header-layout-edit {
            text-align: right;
        }
    }

    .base-layout-edit {
        .main-form {
            padding: 30px 15px;

            .group-field-repeater {
                margin-right: 0;
            }

            .col-remove-btn {
                height: auto;
                margin-bottom: 15px;
            }
        }
    }

    #modal-confirm {
        .modal-dialog {
            width: auto;
        }
    }

    #modal-candidate-introduction-details {
        .modal-body {
            padding: 15px;
        }

        .header {
            font-size: 20px;
            margin-bottom: 15px;
        }

        .row-form {
            margin-bottom: 0;
        }

        .item-form {
            margin-bottom: 15px;
        }

        .last-form {
            label {
                height: auto;
            }
        }

        .main-form {
            height: calc(100vh - 170px);
            overflow: hidden;
            overflow-y: scroll;
            padding-right: 15px;
            position: relative;
            margin-right: -15px;
        }
    }

    #header-about-us {
        padding-top: 20px;

        .title-about-us {
            font-size: 32px;
            line-height: 49px;
            max-width: 582px;
            margin: 0 auto;
            margin-bottom: 43px;
        }
    }

    #vision-about-us {
        padding-bottom: 64px;
        padding-top: 98px;

        .image-vision-about-us {
            left: 0;
            padding-left: 37px;
            padding-right: 21px;
        }

        .image-vision-about-us:before {
            width: 216px;
            height: 196px;
            left: -30px;
            top: -50px;
        }

        .image-vision-about-us:after {
            width: 100px;
            height: 98px;
            right: -20px;
            bottom: -40px;
        }

        .main-vision-about-us {
            padding: 61px 65px;
        }
    }

    #mission-about-us {
        .row-mission-about-us {
            padding: 0;
        }

        .item-mission-about-us {
            padding: 30px;
            max-width: 462px;
            margin: 0 auto;
        }

        .col-item-mission-about-us {
            margin-bottom: 30px;
        }

        .image-mission-about-us {
            width: auto;
        }
    }

    #core-value-about-us {
        padding-bottom: 0;

        .core-value-about-us {
            padding-left: 56px;
            padding-right: 56px;
            border-radius: 0;
            position: relative;
            margin-left: -12px;
            margin-right: -12px;
        }

        .item-core-value {
            //flex-wrap: wrap-reverse;
        }

        .main-item-core-value {
            width: 100%;
        }

        .left-item-core-value {
            .main-item-core-value {
                padding-right: 0;
            }

            .image-item-core-value {
                text-align: right;
            }
        }

        .image-item-core-value {
            //width: 100%;
        }

        .right-item-core-value {
            .main-item-core-value {
                padding-left: 0;
            }
        }

    }

    #content-contact-us {
        .title-page-contact-us {
            font-size: 18px;
            margin-bottom: 8px;
        }

        .list-contact-us {
            li {
                font-size: 14px;
                top: 4px;
                margin-bottom: 8px;
            }
        }
    }


}

@media only screen and (max-width: $mobile-width628) {
    #banner {
        .list-icon {
            display: none;
        }

        .main-banner {
            padding-top: 8px;
            padding-bottom: 8px;

            .title-banner {
                font-size: 14px;
                line-height: 20px;
                margin-bottom: 8px;
            }

            .small-title-banner {
                font-size: 10px;
                line-height: 12px;
            }

            .btn-banner-st1,
            .btn-banner-st2,
            .btn-banner-st3,
            .btn-banner-st4,
            .btn-banner-st5 {
                font-weight: 400;
                font-size: 10px;
                line-height: 12px;
            }
        }
    }

    .list-icon-mobile {
        display: block;
    }

    #header-job {
        .header-job {
            padding-top: 0;

            .content-item-header-job {
                .line1 {
                    font-size: 10px;
                    line-height: 12px;
                }

                .line2 {
                    font-size: 6px;
                    line-height: 8px;
                }
            }

            .item-header-job {
                padding: 5px 8px;
                width: 190px;
            }

            .icon-item-header-job {
                width: 28px;
                height: 28px;
            }

            .item-header-job-st1 {
                .image-icon {
                    background-size: 48%;
                }
            }

            .item-header-job-st2 {
                .image-icon {
                    background-size: 84%;
                }
            }
        }

    }

    #header-job.header-job-scroll {
        .item-header-job {
            width: 38px;
            right: 15px;
        }

        .item-header-job-st1:hover {
            width: 38px;

            .content-item-header-job {
                display: none;
            }
        }

        .item-header-job-st2:hover {
            width: 38px;

            .content-item-header-job {
                display: none;
            }
        }
    }

    #main-job-detail {
        margin-bottom: 0;

        .sidebar-job {
            .list-info-job {
                ul {
                    li {
                        width: 100%;
                    }

                    li:nth-child(n+3) {
                        display: none;
                    }
                }
            }
        }

    }

    .show-info-job-mobile {
        display: block;
    }
}

@media only screen and (max-width: $mobile-width4) {
    #page-user-profile {
        .main-form {
            .drop-edit-ava {
                transform: translate3d(95px, 31px, 0px) !important;
            }
        }
    }

    #header-about-us {
        .title-about-us {
            font-size: 20px;
            line-height: 28px;
        }
    }

    #vision-about-us {
        .image-vision-about-us:before {
            width: 115px;
            height: 104px;
            left: 0;
            top: -26px;
        }

        .image-vision-about-us:after {
            width: 53px;
            height: 52px;
            right: 0;
            bottom: -21px;
        }

        .title-vision-about-us {
            font-size: 20px;
            line-height: 28px;
        }

        .main-vision-about-us {
            padding: 30px;
        }
    }

    #mission-about-us {
        .item-mission-about-us.active {
            padding: 0 30px 30px;
        }

        .item-mission-about-us {
            padding: 0;
        }
    }

    #core-value-about-us {
        .core-value-about-us {
            padding-left: 20px;
            padding-right: 20px;
        }

        .col-item-core-value {
            margin-bottom: 48px;
        }

        .left-item-core-value {
            flex-direction: column-reverse;
            align-items: flex-end;
        }

        .right-item-core-value {
            flex-direction: column-reverse;
        }

    }

    #choose-ntd {
        .title {
            font-size: 16px;
            line-height: 24px;
        }

        .description {
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 48px !important;
        }

        .col-img {
            .main-col-img {
                margin-right: 0;
            }
        }

        .list-choose {
            .image-item-choose {
                width: 118px;
            }

            .content-item-choose {
                width: calc(100% - 118px);
            }
        }

        .col-img {
            .main-col-img {
                max-width: 329px;
                margin: 0 auto;
            }
        }
    }

    #company-width {
        .title-company-width:before {
            width: 150px;
            height: 212px;
            background-size: 100%;
            left: 132px;
        }

        .title-company-width {
            font-size: 14px;
            line-height: 22px;
        }

        .header-company-width {
            padding-bottom: 72px;
        }
    }

    #procedure-ntd {
        padding-top: 48px;

        .title-procedure-ntd {
            font-size: 15px;
            margin-bottom: 24px;
        }

        .title-item-procedure-ntd {
            font-size: 15px;
            height: 60px;
        }
    }

    #content-contact-us {
        .main-contact-us {
            padding-top: 0;
        }

        .title-page-contact-us {
            font-size: 18px;
            line-height: 28px;
        }

        .list-contact-us {
            li {
                font-size: 14px;
            }

            .icon {
                top: 4px;
            }
        }

        .image-contact-us {
            text-align: right !important;

            img {
                width: 200px;
            }
        }
    }

    #main-col-job {
        .btn-col-bottom-job {
            display: none;
        }

        .btn-col-bottom-job-mobile {
            display: block;
        }

        .row-bottom-job {
            display: block;
        }
    }

    #company-width {
        padding-top: 103px;
        padding-bottom: 50px;

        .owl-nav-custom {
            [class*=owl-] {
                width: 20px;
                height: 20px;
                background-position: center;
                background-size: 100%;
                padding: 0;
            }
        }

        .company-width-slider-1 {
            margin-bottom: 72px;
        }

        .company-width-slider-2 {
            margin-bottom: 48px;
        }
    }

    #with-recland {
        .owl-theme {
            .owl-nav {
                [class*=owl-] {
                    width: 20px;
                    height: 20px;
                    background-size: 100%;
                }
            }
        }
    }


    #main-job-detail {
        .sidebar-job {
            .title-sidebar {
                padding-left: 0;
                padding-right: 0;
            }

            .job-skill {
                padding-left: 0;
            }
        }

        .content-job {
            padding: 0;
            padding-bottom: 24px;

            .edit-content-job {
                font-size: 14px;
            }

            .title-content-job {
                font-size: 16px;
                line-height: 24px;
            }
        }
    }

    #detail-company {
        .show-detail-company {
            padding: 0;
        }

        .detail-company {
            .title-detail-company {
                font-size: 16px;
                line-height: 24px;
            }

            .main-detail-company {
                .title-main-info-company {
                    font-size: 18px;
                    line-height: 26px;
                    text-align: center;
                }

                a {
                    display: block;
                }

                .col-line-info-company {
                    .line-info-company {
                        font-size: 14px;
                        line-height: 20px;

                        .label-list-info-company {
                            display: block;
                        }
                    }
                }

                .line-info-company {
                    font-size: 14px;
                    line-height: 20px;

                    .label-list-info-company {
                        display: block;
                    }
                }
            }

            .content-company {
                font-size: 14px;
                line-height: 26px;
            }
        }
    }

    #banner {
        .main-banner {
            .small-title-banner {
                min-width: 22%;
            }
        }
    }

    #header-job {
        .header-job {
            .item-header-job {
                width: 146px;
            }
        }
    }

    #count-ctv {
        padding-bottom: 0;

        .text-count {
            margin-bottom: 48px;
        }
    }

    #footer-v2 {
        .form-footer {
            .title {
                font-size: 20px;
            }
        }

        .main-footer {
            .title-footer {
                font-size: 20px;
            }

            .menu-footer a {
                font-size: 14px;
            }

            .description-footer {
                font-size: 14px;
            }
        }
    }
}

@media only screen and (max-width: $mobile-width3) {

    #banner-ctv {
        .slider-ctv {
            img {
                //height: 200px;
            }
        }

        .search-box {
            max-width: 350px;
        }

        .main-form {}
    }

    #ecosystem-ctv {
        .title {
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 12px;
        }

        .description {
            font-size: 14px;
            margin-bottom: 12px;
        }
    }

    #chance-ctv {
        .title {
            font-size: 14px;
            margin-bottom: 12px;
        }

        .description {
            font-size: 12px;
            margin-bottom: 12px;
        }

        .col-chance-ctv {
            width: 50%;
        }

        .item-change-ctv {
            padding: 5px;

            .title-item-change-ctv {
                font-weight: 800;
                font-size: 14px;
                line-height: 22px;
                min-height: 41px;
            }

            .line-price {
                font-size: 12px;
                line-height: 16px;
                padding: 0;
                min-height: 16px;

                span {
                    font-size: 12px;
                }
            }
        }

        .owl-theme {
            .owl-nav {
                margin-top: 0;

                [class*=owl-] {
                    width: 20px;
                    height: 20px;
                    background-size: 100%;
                }
            }
        }
    }

    #choose-ctv {
        padding-top: 46px;

        .title {
            font-size: 20px;
        }

        .list-choose {
            .image-item-choose {
                width: 100%;
                text-align: center;
                margin-bottom: 20px;
            }

            .content-item-choose {
                width: 100%;
                padding-left: 0;
                margin-bottom: 24px;
            }

            li:last-child {
                .content-item-choose {
                    margin-bottom: 0;
                }
            }
        }

    }

    #procedure-ctv {
        .title {
            font-size: 14px;
        }

        .content-procedure-ctv {
            padding-top: 2px;
        }
    }

    #customer-ctv {
        .title-customer-ctv {
            font-size: 14px;
            margin-bottom: 20px;
        }

        .slider {
            .item-customer-ctv {
                .description {
                    font-size: 10px;
                    line-height: 12px;
                }

                .name {
                    font-size: 10px;
                    margin-bottom: 0;
                }

                .position {
                    margin-bottom: 0;
                    font-size: 10px;
                }
            }
        }
    }

    #customer-ctv {
        padding-bottom: 0;
    }

    #col-trending-video {
        .col-trending-video {
            .image-big-video {
                iframe {
                    height: 200px;
                }
            }
        }
    }

    #list-recent-new {
        .main-list-recent-new {
            .item-recent-new {
                .thumbnail {
                    width: 100%;
                }
            }

            .content {
                width: 100%;
                padding-left: 0;
            }
        }
    }

    #hot-new {
        .col-hot-new-2 {
            margin-bottom: 12px;

            .image-main-hot-new-2 {
                height: auto;
            }
        }

        .col-hot-new-3 {
            .item-col-hot-new-3 {
                margin-bottom: 12px;

                .image-main-hot-new-2 {
                    height: auto;

                }
            }
        }
    }

    .modal-job {
        .modal-body {
            padding: 25px;
        }

        .bottom-form {
            button {
                font-size: 15px;
            }
        }
    }

    #page-user-profile {
        .header-tab {
            li {
                a {
                    font-size: 15px;
                    padding: 8px 15px;
                }
            }
        }

        .main-form {
            padding: 30px 15px;

            .item-button-form {
                width: 50%;
            }

            .item-button {
                width: 100%;
            }

            .bottom-form {
                justify-content: center;
                align-items: center;
                gap: 12px;
            }
        }

        #content-tab-password {
            .item-button {
                width: 100%;
            }
        }

        #content-tab-user {
            .item-button {
                width: 100%;
            }
        }

    }

    #dashboard-list-recruitment {
        .content-header-recruitment {
            .list {
                ul {
                    li {
                        margin-right: 0;
                        margin-bottom: 15px;
                        display: block;
                    }
                }
            }
        }
    }


    #map-contact-us {
        .group-google-map ul li a {
            font-size: 10px;
            min-width: 70px;
            padding: 6px 10px;
        }

        .group-google-map {
            padding: 6px 12px;
            top: 12px;
            right: 12px;
        }

        .group-google-map ul {
            display: block;
            gap: 12px;

            li {
                margin-bottom: 6px;
            }

            li:last-child {
                margin-bottom: 0;
            }
        }
    }

    #footer-v2 {
        .form-footer {
            .group-form {
                display: block;
                background: rgba(255, 255, 255, 0.9);
                box-shadow: 0px 4px 15px rgba(129, 155, 234, 0.4);
                border-radius: 8px;
                padding: 15px 10px;
                margin-bottom: 10px;

                .input-search {
                    margin-bottom: 16px;
                    height: 40px;
                    box-shadow: 0px 4px 10px rgba(121, 132, 165, 0.25);
                }

                .btn-search {
                    margin: 0 auto;
                    display: block;
                    height: 40px;
                }
            }
        }
    }

    .modal-change-status {
        .close {
            top: 25px;
        }

        .next-step {
            top: 25px;
        }
    }

}

.pending-review {
    color: $color-st6;
}

.accepted {
    color: $color-primary3;
}

.rejected {
    color: $color-secondary1;
}

.pass-interview {
    color: $color-primary1;
}

.fail-interview {
    color: $color-st23;
}

.onboarded {
    color: $color-primary4;
}

.cancel {
    color: $color-primary1v2;
}

.level-button-f1 {
    background: #FFE9D0;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 2px 12px;
    gap: 6px;
    /* position: absolute; */
    height: 24px;
    left: 16px;
    right: 35px;
    top: calc(50% - 24px/2);
}

.text-f1 {
    color: $color-brand;
}

.level-button-f2 {
    background: #CEEEF2;
    border-radius: 12px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 2px 12px;
    gap: 6px;
    /* position: absolute; */
    height: 24px;
    left: 16px;
    right: 35px;
    top: calc(50% - 24px/2);
}

.text-f2 {
    color: #079DB2;
}

.btn-login-to-search {
    background: #17677b;
    border: 1px solid #17677b;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(226, 88, 34, .2);
    color: #fcfcfe;
    font-size: 18px;
    height: 46px;
    width: 173px;
    padding: 8px 20px;
}

.btn-login-to-search:hover {
    color: #fcfcfe;
    box-shadow: 9px 7px 13px 3px rgba(226, 88, 34, .2);
}

.mt-50 {
    margin-top: 50px;
}

.text-gray {
    color: #7984A5;
}

.text-black {
    color: #3c4455;
}

.header-tab {
    li {
        position: relative;

        a {
            display: block;
            width: 250px;
            max-width: 100%;
            padding: 8px 15px;
            font-weight: 600;
            font-size: 18px;
            line-height: 26px;
            color: $color-palette1;
            border-bottom: 2px solid $color-palette3;
            text-align: center;
        }

        .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            background-repeat: no-repeat;
            background-position: center;
            position: relative;
            margin-right: 10px;
            top: 1px;
        }

        .icon-change-password {
            background-image: url("../images/dashboard-ctv/icon-tab-password.svg");
        }

        .icon-company {
            background-image: url("../images/dashboard-ctv/icon-company.svg");
        }

        .icon-change-profile {
            background-image: url("../images/dashboard-ctv/icon-change-profile.svg");
        }

        a.active {
            color: $color-brand1;
            border-bottom: 2px solid $color-brand;

            .icon-change-password {
                background-image: url("../images/dashboard-ctv/icon-tab-password-active.svg");
            }

            .icon-company {
                background-image: url("../images/dashboard-ctv/icon-company-active.svg");
            }

            .icon-change-profile {
                background-image: url("../images/dashboard-ctv/icon-change-profile-active.svg");
            }
        }

    }

    li:after {
        content: '';
        width: 1px;
        height: 32px;
        position: absolute;
        background: $color-palette3;
        right: 0;
        top: 6px;
    }

    li:last-child:after {
        display: none;
    }
}