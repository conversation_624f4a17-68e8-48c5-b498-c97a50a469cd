<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('zalopay_transactions', function (Blueprint $table) {
            $table->string('app_trans_id')->nullable()->default('')->change();
            $table->string('zp_trans_id')->nullable()->default('')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('zalopay_transactions', function (Blueprint $table) {
            $table->integer('app_trans_id')->default(0)->change();
            $table->integer('zp_trans_id')->default(0)->change();
        });
    }
};
