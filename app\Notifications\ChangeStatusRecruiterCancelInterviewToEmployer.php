<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusRecruiterCancelInterviewToEmployer extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }


    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $link = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);
        $linkMarket = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusRecruiterCancelInterviewToEmployer', [
                'employerName' => $employerName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'link' => $link,
                'linkMarket' => $linkMarket,
            ])
            ->subject('[Recland] Thông báo hết hạn đặt lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);

    }


}

