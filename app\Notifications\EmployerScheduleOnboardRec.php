<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerScheduleOnboardRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $rec;
    protected $wareHouseCvSellingBuyOnboard;

    public function __construct($wareHouseCvSellingBuy, $rec, $wareHouseCvSellingBuyOnboard)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->rec = $rec;
        $this->wareHouseCvSellingBuyOnboard = $wareHouseCvSellingBuyOnboard;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName = $this->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv') {
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)) {
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $timeInterview = $this->wareHouseCvSellingBuyOnboard->time_book_format . ' ' . $this->wareHouseCvSellingBuyOnboard->date_book_format;
        $address = $this->wareHouseCvSellingBuyOnboard->address;
        $phone = $this->wareHouseCvSellingBuy->employer->mobile;
        $email = $this->wareHouseCvSellingBuy->employer->email;
        $salary = $this->wareHouseCvSellingBuy->wareHouseCvSelling->candidate_salary_expect;
        $link = route('rec-cv-sold') . '?cv_sold=' . $this->wareHouseCvSellingBuy->id . '&open_comment=1';
        return (new MailMessage)
            ->view('email.employerScheduleOnboardRec', [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'timeInterview' => $timeInterview,
                'address' => $address,
                'link' => $link,
                'phone' => $phone,
                'email' => $email,
                'salary' => $salary,
            ])
            ->subject('[Recland] Lời mời nhận việc Ứng viên ' . $candidateName . ' vị trí  ' . $position . ' từ công ty ' . $companyName);
    }
}
