<?php

namespace App\Services\Admin;

use App\Jobs\UpdateRecuirmentThreeDay;
use App\Notifications\CheckCvExpired;
use App\Notifications\CheckCvPending;
use App\Notifications\ComplaintsAcceptedToEmployerSubmit;
use App\Notifications\ComplaintsAcceptedToRecSubmit;
use App\Notifications\PaymentOpenTurnCv;
use App\Notifications\PaymentOpenTurnCvSubmit;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\SubmitCvBookRepository;
use App\Repositories\SubmitCvDiscussRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvMetaRepository;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\UserRepository;
use App\Services\FileServiceS3;
use Carbon\Carbon;
// use DB;
use Illuminate\Support\Facades\DB;

class SubmitCvService
{
    protected $submitCvRepository;
    protected $userRepository;
    protected $submitCvMetaRepository;
    protected $submitCvDiscussRepository;
    protected $submitCvBookRepository;
    protected $submitCvHistoryStatusRepository;
    protected $submitCvHistoryPaymentRepository;
    protected $payInMonthRepository;
    protected $submitCvOnboardRepository;
    protected $payinMonthRepository;

    public function __construct(
        SubmitCvRepository     $submitCvRepository,
        UserRepository         $userRepository,
        SubmitCvMetaRepository $submitCvMetaRepository,
        SubmitCvDiscussRepository $submitCvDiscussRepository,
        SubmitCvBookRepository $submitCvBookRepository,
        SubmitCvHistoryStatusRepository $submitCvHistoryStatusRepository,
        SubmitCvHistoryPaymentRepository $submitCvHistoryPaymentRepository,
        PayinMonthRepository $payInMonthRepository,
        SubmitCvOnboardRepository $submitCvOnboardRepository,
        PayinMonthRepository $payinMonthRepository,
    ) {
        $this->submitCvRepository = $submitCvRepository;
        $this->userRepository = $userRepository;
        $this->submitCvMetaRepository = $submitCvMetaRepository;
        $this->submitCvDiscussRepository = $submitCvDiscussRepository;
        $this->submitCvBookRepository = $submitCvBookRepository;
        $this->submitCvHistoryStatusRepository = $submitCvHistoryStatusRepository;
        $this->submitCvHistoryPaymentRepository = $submitCvHistoryPaymentRepository;
        $this->payInMonthRepository = $payInMonthRepository;
        $this->submitCvOnboardRepository = $submitCvOnboardRepository;
        $this->payinMonthRepository = $payinMonthRepository;
    }

    public function indexService($params, $order = array(), $paginate = false, $datatable = false)
    {
        if (!$datatable) {
            return $this->submitCvRepository->getListSubmitCv($params, $order, $paginate);
        }
        return $this->submitCvRepository->getQueryListSubmitCv($params, $datatable);
    }

    public function indexPaymentService($params, $order = array(), $paginate = false)
    {
        return $this->submitCvRepository->getListSubmitCvPayment($params, $order, $paginate);
    }

    public function buildDatatabler($userId)
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_name',
                ],
                'value' => 'Tên ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_email',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_candidate_mobile',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_url_cv_public',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'submit_cv_meta_url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'job_name',
                ],
                'value' => 'Tên Job',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'company_name',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'employer_email',
                ],
                'value' => 'Nhà tuyển dụng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_value',
                    'data-fn' => 'renderStatusCv',
                ],
                'value' => 'Trạng thái phê duyệt',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'expected_date_value',
                ],
                'value' => 'Ngày dự kiến vào làm',
            ],
        ];
        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('submit.cv-datatable', ['user_id' => $userId]))
            ->setRenderValue($renderValue);
    }

    public function buildDatatableAuthorize()
    {
        if (request()->get('authorize_status', 0) == 1) {
            $renderValue = [
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                        'data-fn' => 'renderStt',
                    ],
                    'value' => 'No',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                    ],
                    'value' => 'ID',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_name',
                    ],
                    'value' => 'Tên ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_email',
                    ],
                    'value' => 'Email ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_mobile',
                    ],
                    'value' => 'Sđt ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_email',
                    ],
                    'value' => 'Email CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_mobile',
                    ],
                    'value' => 'Sđt CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'created_value',
                    ],
                    'value' => 'Thời gian gửi ủy quyền',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'company_name',
                    ],
                    'value' => 'Tên công ty',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_email',
                    ],
                    'value' => 'Email NTD',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_mobile',
                    ],
                    'value' => 'Sđt NTD',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'job_file_jd',
                        'data-orderable' => 'false',
                        'data-fn' => 'renderJobFileJd',
                    ],
                    'value' => 'JD công việc',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'bonus_type',
                    ],
                    'value' => 'Loại bonus',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'bonus_val',
                    ],
                    'value' => 'Số tiền bonus',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'bonus_authority',
                    ],
                    'value' => 'Bonus cho CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'status_payment',
                        'data-fn' => 'renderStatusPaymentValue',
                    ],
                    'value' => 'Trạng thái thanh toán',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'status_value',
                        'data-fn' => 'renderStatusCv',
                    ],
                    'value' => 'Trạng thái ứng viên',
                ],
            ];
        } elseif (request()->get('authorize_status', 0) == 2) {
            $renderValue = [
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                        'data-fn' => 'renderStt',
                    ],
                    'value' => 'No',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                    ],
                    'value' => 'ID',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_name',
                    ],
                    'value' => 'Tên ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_email',
                    ],
                    'value' => 'Email ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_mobile',
                    ],
                    'value' => 'Sđt ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_email',
                    ],
                    'value' => 'Email CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_mobile',
                    ],
                    'value' => 'Sđt CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'job_file_jd',
                        'data-orderable' => 'false',
                        'data-fn' => 'renderJobFileJd',
                    ],
                    'value' => 'JD công việc',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'created_value',
                    ],
                    'value' => 'Thời gian gửi ủy quyền',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'company_name',
                    ],
                    'value' => 'Tên công ty',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_email',
                    ],
                    'value' => 'Email NTD',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_mobile',
                    ],
                    'value' => 'Sđt NTD',
                ],
            ];
        } else {
            $renderValue = [
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                        'data-fn' => 'renderStt',
                    ],
                    'value' => 'No',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'id',
                    ],
                    'value' => 'ID',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_name',
                    ],
                    'value' => 'Tên ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_email',
                    ],
                    'value' => 'Email ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_candidate_mobile',
                    ],
                    'value' => 'Sđt ứng viên',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_email',
                    ],
                    'value' => 'Email CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'user_mobile',
                    ],
                    'value' => 'Sđt CTV',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_url_cv_public',
                        'data-fn' => 'renderCvPublic',
                    ],
                    'value' => 'CV Public',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'warehouse_cv_url_cv_private',
                        'data-orderable' => 'false',
                        'data-fn' => 'renderCvPrivate',
                    ],
                    'value' => 'CV Private',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'created_value',
                    ],
                    'value' => 'Thời gian gửi ủy quyền',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_email',
                    ],
                    'value' => 'Email NTD',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'employer_mobile',
                    ],
                    'value' => 'Sđt NTD',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'job_file_jd',
                        'data-orderable' => 'false',
                        'data-fn' => 'renderJobFileJd',
                    ],
                    'value' => 'JD công việc',
                ],
                [
                    'attributes' => [
                        'data-mdata' => 'status_value',
                        'data-fn' => 'renderStatusUqCv',
                    ],
                    'value' => 'Trạng thái',
                ]
            ];
        }

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('authorize-datatable', ['authorize_status' => request()->get('authorize_status', 0)]))
            ->setRenderValue($renderValue);
    }

    public function buildDatatablePayment()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'warehouse_cv_candidate_name',
                ],
                'value' => 'Tên ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'warehouse_cv_candidate_email',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'warehouse_cv_candidate_mobile',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'warehouse_cv_url_cv_public',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'warehouse_cv_url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'job_name',
                ],
                'value' => 'Tên Job',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'company_name',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'employer_email',
                ],
                'value' => 'Nhà tuyển dụng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_value',
                    'data-fn' => 'renderStatusCv',
                ],
                'value' => 'Trạng thái ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'expected_date_value',
                ],
                'value' => 'Ngày dự kiến vào làm',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_payment',
                    'data-fn' => 'renderStatusPaymentValue',
                ],
                'value' => 'Trạng thái thanh toán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'payment_fee_format',
                ],
                'value' => 'Số tiền thanh toán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'payment_actual_format',
                ],
                'value' => 'Tổng tiền đã thanh toán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'interest_rate',
                ],
                'value' => 'Lãi xuất',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'interest_money',
                ],
                'value' => 'Tiền lãi',
            ],
        ];
        $renderAction = [
            'actionEditPayment',
        ];
        $datatable = new Datatable();

        return $datatable
            ->setRoute(route('payment-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }

    public function datatablePayment($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexPaymentService($params['request'], $params['orders'], true);

        $total = $data->total();
        $data = $data->load([
            'warehouseCv' => function ($q) {
                $q->select('id', 'candidate_name', 'candidate_email', 'candidate_mobile', 'cv_public', 'cv_private');
            },
            'job' => function ($q) {
                $q->select('id', 'name');
            },
            'company' => function ($q) {
                $q->select('id', 'name');
            },
            'employer' => function ($q) {
                $q->select('users.email');
            },
        ]);
        $data = $data->append([
            'warehouse_cv_candidate_name',
            'warehouse_cv_candidate_email',
            'warehouse_cv_candidate_mobile',
            'warehouse_cv_url_cv_public',
            'warehouse_cv_url_cv_private',
            'job_name',
            'company_name',
            'employer_email',
        ]);
        $response = [
            'data' => $data->toArray(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];
        return $response;
    }

    public function datatable($params = [])
    {
        $userId = $params['user_id'];
        $params = Datatable::convertRequest($params);
        $params['request']['user_id'] = $userId;
        $data = $this->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $data = $data->load([

            'job' => function ($q) {
                $q->select('id', 'name');
            },
            'company' => function ($q) {
                $q->select('id', 'name');
            },
            'employer' => function ($q) {
                $q->select('users.email');
            },
            'submitCvMeta' => function ($q) {
                $q->select('id', 'candidate_name', 'candidate_email', 'candidate_mobile', 'cv_public', 'cv_private', 'submit_cv_id');
            },
        ]);
        $data = $data->append([
            'submit_cv_meta_candidate_name',
            'submit_cv_meta_candidate_email',
            'submit_cv_meta_candidate_email',
            'submit_cv_meta_candidate_mobile',
            'submit_cv_meta_url_cv_public',
            'submit_cv_meta_url_cv_private',
            'job_name',
            'company_name',
            'employer_email',
        ]);
        $response = [
            'data' => $data->toArray(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];
        return $response;
    }

    public function datatableAuthorize($params = [])
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $data = $data->load([
            'warehouseCv' => function ($q) {
                $q->select('id', 'candidate_name', 'candidate_email', 'candidate_mobile', 'cv_public', 'cv_private');
            },
            'job' => function ($q) {
                $q->select('id', 'name', 'file_jd');
            },
            'company' => function ($q) {
                $q->select('id', 'name');
            },
            'employer' => function ($q) {
                $q->select('users.email', 'users.mobile');
            },
            'user' => function ($q) {
                $q->select('id', 'email', 'mobile');
            },
        ]);
        $data = $data->append([
            'warehouse_cv_candidate_name',
            'warehouse_cv_candidate_email',
            'warehouse_cv_candidate_mobile',
            'warehouse_cv_url_cv_public',
            'warehouse_cv_url_cv_private',
            'job_name',
            'job_file_jd',
            'created_value',
            'company_name',
            'employer_email',
            'employer_mobile',
            'user_email',
            'user_mobile',
        ]);

        $response = [
            'data' => $data->toArray(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];
        return $response;
    }

    public function statistical($userId)
    {
        $status = config('constant.submit_cvs_status');
        $statistical = [];
        $now = Carbon::now();
        $end = $now->lastOfMonth()->format('d');
        foreach ($status as $key => $value) {
            if ($key > 5) {
                break;
            }
            $itemStatistical = $this->submitCvRepository->statisticalByStatus($userId, $key);
            $dayValue = [];
            for ($i = 1; $i <= (int)$end; $i++) {
                $item = $itemStatistical->where('day', $i);
                if ($item->count() > 0) {
                    $dayValue[$i] = $item->first()->total;
                } else {
                    $dayValue[$i] = 0;
                }
            }
            $statistical[$value] = $dayValue;
        }
        return $statistical;
    }

    public function getListCvNotif($status)
    {
        if ($status == config('constant.submit_cvs_status_value.pending-review')) {

            $data = $this->submitCvRepository->getAllSubmitCvWithStatus($status);
        } else if ($status == config('constant.submit_cvs_status_value.accepted')) {

            $data = $this->submitCvRepository->getAllSubmitCvWithStatus($status);
        }

        $date = date('w', strtotime(date('Y-m-d')));
        if (count($data)) {
            foreach ($data as $k => $v) {
                $user = $this->userRepository->find($v->job->employer_id);
                if ($user) {
                    //ko gui thong bao t7 cn
                    if ($date != 6 && $date != 0) {
                        //send notif and email
                        $user->notify(new CheckCvPending($v, $user));
                    }
                }
            }
        }

        return $data;
    }

    public function updateStatusCvExpired($status)
    {

        $now = Carbon::now();
        if ($status == config('constant.submit_cvs_status_value.pending-review')) {
            $time = $now->addDays(3)->format('Y-m-d H:i:s');

            //get cv pending-review
            $data = $this->submitCvRepository->updateCvExpired($status);
        } else if ($status == config('constant.submit_cvs_status_value.accepted')) {
            $time = $now->addDays(15)->format('Y-m-d H:i:s');

            //get cv accept
            $data = $this->submitCvRepository->getCvAcceptExpired($status);
        }


        if (count($data)) {
            foreach ($data as $k => $v) {
                $user = $this->userRepository->find($v->job->employer_id);
                if ($user) {
                    //send notif and email
                    $user->notify(new CheckCvExpired($v, $user));
                }
                //update pending-review => reject
                $this->submitCvRepository->update($v->id, [], [
                    'status' => config('constant.submit_cvs_status_value.rejected'),
                    'date_change_status' => Carbon::now()
                ]);
            }
        }

        return true;
    }

    public function updateStatus($id, $statusPayment)
    {
        $data = [
            'status_payment' => $statusPayment,
            'date_change_status_payment' => Carbon::now()
        ];

        return $this->submitCvRepository->update($id, [], $data);
    }

    public function findById($submitCvId)
    {
        return $this->submitCvRepository->find($submitCvId);
    }

    public function checkSubmitCvMeta()
    {
        //get all id submitCv
        $arrSubmitCvID = $this->submitCvMetaRepository->getAll()->pluck('submit_cv_id');

        $dataFail = $this->submitCvRepository->getIdMissing($arrSubmitCvID);

        if (count($dataFail)) {
            //update to submit_cv_meta
            foreach ($dataFail as $item) {
                $paramWareHouseCvs = [
                    'user_id' => $item->id,
                    'candidate_name' => $item->wareHouseCv->candidate_name,
                    'candidate_mobile' => $item->wareHouseCv->candidate_mobile,
                    'candidate_email' => $item->wareHouseCv->candidate_email,
                    'candidate_job_title' => $item->wareHouseCv->candidate_job_title,
                    'candidate_salary_expect' => $item->wareHouseCv->candidate_salary_expect,
                    'candidate_portfolio' => $item->wareHouseCv->candidate_portfolio,
                    'candidate_currency' => $item->wareHouseCv->candidate_currency,
                    'cv_public' => $item->wareHouseCv->cv_public,
                    'cv_private' => $item->wareHouseCv->cv_private,
                    'assessment' => $item->wareHouseCv->assessment,
                    'main_skill' => $item->wareHouseCv->main_skill,
                    'year_experience' => $item->wareHouseCv->year_experience,
                ];

                $paramWareHouseCvs['warehouse_cv_id'] = $item->warehouse_cv_id;
                $paramWareHouseCvs['submit_cv_id'] = $item->id;

                $this->submitCvMetaRepository->create($paramWareHouseCvs);
            }
        }

        return true;
    }

    public function updateCandidateConfirm($candidateId, $jobId): bool
    {
        return $this->submitCvRepository->updateCandidateConfirm($candidateId, $jobId);
    }

    public function updateSeMeta($params, $id)
    {
        $arrSkillDescription = [];
        if (isset($params['skill']) && $params['description']) {
            foreach ($params['skill'] as $k => $v) {
                $arrSkillDescription[$v] = $params['description'][$k];
            }
        }

        $data = [
            'candidate_name' => $params['candidate_name'],
            'candidate_email' => $params['candidate_email'],
            'candidate_mobile' => $params['candidate_mobile'],
            'assessment' => $params['assessment'],
            'candidate_portfolio' => $params['candidate_portfolio'],
            'candidate_job_title' => $params['candidate_job_title'],
            'candidate_currency' => $params['candidate_currency'],
            'candidate_salary_expect' => $params['candidate_salary_expect'],
            'main_skill' => json_encode($arrSkillDescription),
        ];

        if (isset($params['cv_private']) && is_file($params['cv_private'])) {
            $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3($params['cv_private'], config('constant.sub_path_s3.cv'));
        }

        if (isset($params['cv_public']) && is_file($params['cv_public'])) {
            $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3($params['cv_public'], config('constant.sub_path_s3.cv'));
        }

        $submitCv = $this->findById($id);
        if ($submitCv) {

            $submitCv->submitCvMeta()->update($data);
        }
    }

    public function updateService($params, $id)
    {
        return $this->submitCvRepository->update($id, [], $params);
    }

    public function destroy($id)
    {
        return $this->submitCvRepository->delete($id);
    }

    public function getDataDiscuss($params)
    {
        return $this->submitCvDiscussRepository->getBySubmitId($params['id']);
    }

    public function getDataBook($params)
    {
        return  $this->submitCvBookRepository->getBySubmitCvId($params['id']);
    }

    public function getDataHistoryStatus($params)
    {
        return $this->submitCvHistoryStatusRepository->getLogStatusWithSubmitCv($params['id']);
    }

    public function getDataHistoryPaymentStatus($params)
    {
        $data['buy'] = $this->submitCvHistoryPaymentRepository->findAllBySubmit($params['id']);
        $data['month'] = $this->payInMonthRepository->submitCvSellingBuyList($params['id']);
        return $data;
    }

    public function updateComplain($params)
    {
        DB::beginTransaction();
        if ($params['type'] != 4 && $params['type'] != 5) return false;
        //Neu admin xac nhan thi hoan Tien cho NTD
        $statusPayment = '';
        $statusRecruitment = '';
        $submitCv =  $this->submitCvRepository->find($params['id']);

        //dd($submitCv);
        //CTV Tu chối thì admin mới dc duyệt
        if ($submitCv->status_complain != 2) return false;
        //trang thai khieu nai

        $submitCvPaymentSave = [
            'status_complain' => $params['type']
        ];

        if ($params['type'] == 4) {

            //Hoan tien tra NTD
            //CV data
            if ($submitCv->bonus_type == 'cv' || $submitCv->bonus_type == 'interview') {
                //ghi log mua
                $this->submitCvHistoryPaymentRepository->create([
                    'user_id' => $submitCv->user->id,
                    'submit_cv_id' => $submitCv->id,
                    'type' => 1, //0 trừ tiền, 1 hoàn tiền
                    'percent' => 100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'bonus_type' => $submitCv->bonus_type,
                    'amount' => $submitCv->bonus_ntd,
                    'balance' => $submitCv->user->wallet->amount + $submitCv->bonus_ntd,
                    'status' => 0,
                ]);

                if ($submitCv->bonus_type == 'cv') {
                    //interview ko thay đổi trạng thái
                    //Log thay đổi trang thái 
                    $this->submitCvHistoryStatusRepository->create([
                        'user_id'            => $submitCv->employer->id,
                        'submit_cvs_id'      => $submitCv->id,
                        'status_recruitment' => config('constant.status_recruitment_revert.CancelBuyCVdata'),
                        'type'               => 'employer',
                        'authority'          => $submitCv->authorize
                    ]);
                    $statusRecruitment = config('constant.status_recruitment_revert.CancelBuyCVdata');
                    $statusPayment = 3; //Hoan tien
                }

                if ($submitCv->bonus_type == 'interview') {
                    $statusPayment = 3; //Hoan tien
                }
                //Cộng point của NTD
                $point = $submitCv->bonus_ntd;
                // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->bonus_point;
                $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                $submitCv->employer->wallet->addAmount($submitCv->bonus_ntd, $submitCv, 'Hoàn tiền cho ứng tuyển: #' . $submitCv->id, 'complaints_accepted_to_rec_submit');
                // $submitCv->employer->wallet->save();


                $submitCv->employer->notify(new ComplaintsAcceptedToRecSubmit($submitCv));
                $submitCv->user->notify(new ComplaintsAcceptedToEmployerSubmit($submitCv, $point));
            }

            if ($submitCv->bonus_type == 'onboard') {
                //14 => 'Trial work',

                if ($submitCv->status_recruitment_value == 14) {
                    $onboard = $this->submitCvOnboardRepository->getFirstBySubmitCvId($submitCv->id);
                    $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                    $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                    $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                    $dateComplain = Carbon::createFromFormat('Y-m-d H:i:s', $submitCv->date_complain);
                    $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                    $dateComplain =  strtotime($dateComplain);
                    $percent = $point = 0;
                    //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                    if ($createDateTrailWork30 >= $dateComplain) {
                        $percent = 100;
                        $point = $submitCv->point;
                    }
                    //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                    if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                        $percent = 70;
                        $point = (int) round(($submitCv->point * $percent) / 100);
                    }
                    //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                    if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                        $percent = 50;
                        $point = (int) round(($submitCv->point * $percent) / 100);
                    }
                    //ghi log hoan tien
                    $this->submitCvHistoryPaymentRepository->create([
                        'user_id' => $submitCv->user->id,
                        'submit_cv_id' => $submitCv->id,
                        'type' => 1, //0 trừ tiền, 1 hoàn tiền
                        'percent' => $percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'bonus_type' => $submitCv->bonus_type,
                        'amount' => $point,
                        'balance' => $submitCv->user->wallet->amount + $submitCv->bonus_point,
                        'status' => 0,
                    ]);

                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($point, $submitCv, 'Hoàn tiền cho ứng tuyển: #' . $submitCv->id, 'complaints_accepted_to_rec_submit');
                    // $submitCv->employer->wallet->save();


                    $statusPayment = 3; //Hoan tiền
                }
                //Hoàn cả 10% Waiting Interview
                else {
                    //                    dd($submitCv->id);
                    $submitCvHistoryPaymentData = $this->submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($submitCvHistoryPaymentData) {
                        foreach ($submitCvHistoryPaymentData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $this->submitCvHistoryPaymentRepository->create([
                                'user_id'                       =>  $submitCv->user->id,
                                'submit_cv_id'                  => $submitCv->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'bonus_type'                    => $submitCv->bonus_type,
                                'amount'                        =>  $value->bonus_point,
                                'balance'                       =>  $submitCv->user->wallet->amount + $value->bonus_point,
                                'status'                        =>  0
                            ]);
                            $point += $value->point;
                        }
                    }
                    //Cộng point của NTD
                    // $submitCv->user->wallet->amount = $submitCv->user->wallet->amount + $point;
                    $submitCv->user->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->user->wallet->addAmount($point, $submitCv, 'Hoàn tiền cho ứng tuyển: #' . $submitCv->id, 'complaints_accepted_to_rec_submit');
                    // $submitCv->user->wallet->save();
                    $statusPayment = 2; //Hoàn cọc
                }
                $submitCv->employer->notify(new ComplaintsAcceptedToRecSubmit($submitCv));
                $submitCv->user->notify(new ComplaintsAcceptedToEmployerSubmit($submitCv, $point));
            }
        }

        //admin Từ chối khiếu nại
        if ($params['type'] == 5) {
            $submitCv->employer->notify(new ComplaintsAcceptedToRecSubmit($submitCv));
            $submitCv->user->notify(new ComplaintsAcceptedToEmployerSubmit($submitCv));

            if ($submitCv->bonus_type == 'cv') {
                //neu chua co thanh toan tien cho CTV thi thanh toan => chua có record ở table $payinMonthRepository voi warehouse_cv_selling_buy_id = $cvSellingBuy->id
                $now = Carbon::now();

                $payinMonth = $this->payinMonthRepository->findBySubmitCvId($submitCv->id);
                // Kiểm tra quá hạn thanh toán mà vẫn chưa thanh toán do chờ xác nhận khiếu nại thì thanh toán.
                if ($now->timestamp > $submitCv->created_at->addMinutes(7 * 24 * 60)->timestamp && !$payinMonth) {
                    //Cộng tiền trả CTV
                    //1. payins
                    $bonusRepository = resolve(BonusRepository::class);
                    $payinMonthRepository = resolve(PayinMonthRepository::class);
                    $now = \Carbon\Carbon::now();
                    $year = $now->year;
                    $month = $now->month;
                    $bonusCheck = $bonusRepository->getBonusByUser($submitCv->rec->id, $month, $year);
                    if (!$bonusCheck) {
                        //nếu tháng/năm chưa có thì insert mới
                        $bonusRepository->create(
                            [
                                'user_id'   => $submitCv->employer->id,
                                'year'      => $year,
                                'month'     => $month,
                                'price'     => $submitCv->price,
                            ]
                        );
                    } else {
                        //nếu có doanh thu từ trước thì cộng dồn
                        $bonusCheck->price += $submitCv->price;
                        $bonusCheck->save();
                    }
                    //từng giao dịch trong tháng
                    $payinMonthRepository->create(
                        [
                            'user_id'                       => $submitCv->employer->id,
                            'submit_cv_id'                  => $submitCv->id,
                            'year'                          => $year,
                            'month'                         => $month,
                            'price'                         => $submitCv->price,
                            'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                        ]
                    );
                    //2. wallets
                    // $submitCv->rec->wallet->price  = $submitCv->rec->wallet->price + $submitCv->price;
                    $submitCv->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->rec->wallet->addPrice($submitCv->price, $submitCv, 'Thanh toán cho ứng tuyển: #' . $submitCv->id, 'complaints_accepted_to_rec_submit');
                    // $submitCv->rec->wallet->save();

                    $submitCv->rec->notify(new PaymentOpenTurnCvSubmit($submitCv, $submitCv->price));
                }
            }

            if ($submitCv->bonus_type == 'interview') {
                //neu chua co thanh toan tien cho CTV thi thanh toan => chua có record ở table $payinMonthRepository voi warehouse_cv_selling_buy_id = $cvSellingBuy->id
                $now = Carbon::now();
                $payinMonth = $this->payinMonthRepository->findBySubmitCvId($submitCv->id);
                $lastBook = $this->submitCvBookRepository->findByCvSellingBuyId($submitCv->id);
                $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $lastBook->date_book);
                if ($now->timestamp > $carbonDate->addMinutes(7 * 24 * 60)->timestamp && !$payinMonth) {
                    //8 => 'Pass Interview',
                    //sau 3 ngày Nếu ko thay đổi //7 => 'Waiting Interview', thì update //8 => 'Pass Interview',
                    //$wareHouseCvSellingBuySave['status_recruitment'] = 8;
                    UpdateRecuirmentThreeDay::dispatch($submitCv->id, 7)->delay(now()->addMinutes(3 * 24 * 60));
                }
            }
            //14 => 'Trial work',
            if ($submitCv->bonus_type == 'onboard' && $submitCv->status_recruitment_value == 14) {
                //8 => 'Pass Interview',
                //$wareHouseCvSellingBuySave['status_recruitment'] = 8;
                //Onboard ko tự động chuyển, chấp nhận treo
                //16 => 'Success Recruitment',
                UpdateRecuirmentThreeDay::dispatch($submitCv->id, 14)->delay(now()->addMinutes(3 * 24 * 60));
            }
        }

        if ($statusPayment) $submitCvPaymentSave['status_payments'] = $statusPayment;
        if ($statusRecruitment) $submitCvPaymentSave['status_recruitment'] = $statusRecruitment;

        $result = $this->submitCvRepository->update($params['id'], [], $submitCvPaymentSave);
        DB::commit();
        return $result;
    }

    public function totalCv($params)
    {
        return $this->submitCvRepository->total($params);
    }

    public function getDataComplain($params)
    {
        return $this->submitCvHistoryPaymentRepository->getBySubmitId($params['id']);
    }

    public function totalSubmitCv($fromDate = null, $toDate = null)
    {
        return $this->submitCvRepository->numberSubmitCv($fromDate, $toDate);
    }

    public function totalSubmitCvJob($fromDate = null, $toDate = null)
    {
        return $this->submitCvRepository->numberSubmitCvJob($fromDate, $toDate);
    }

    public function searchSubmitCvToJob($key = null)
    {
        return $this->submitCvRepository->searchSubmitCvJob($key);
    }

    public function statisticalByMonth($formDate = null, $toDate = null)
    {
        $data = $this->submitCvRepository->statisticalByMonth($formDate, $toDate);
        $data = $data->keyBy('month')->toArray();

        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }
}
