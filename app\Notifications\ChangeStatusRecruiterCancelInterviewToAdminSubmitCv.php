<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusRecruiterCancelInterviewToAdminSubmitCv extends Mailable
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function content(): Content
    {
        $recName       = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->type_of_sale;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $url           = route('submit-cv.edit',['id' => $this->submitCv->id]);
        return new Content(
            view: 'email.changeStatusRecruiterCancelInterviewToAdmin',
            with: [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'url'           => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $position = $this->submitCv->job->name;
        $message->subject('[Recland] Thông báo đóng trường hợp giới thiệu ứng viên '.$candidateName.' vị trí '.$position. '  của công ty  ' .$companyName);
        return $this;
    }




}
