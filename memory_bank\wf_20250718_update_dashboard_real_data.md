# Workflow: Cậ<PERSON> nhật Dashboard để chỉ lấy dữ liệu thật (is_real = 1)

**<PERSON><PERSON><PERSON> thực hiện:** 18/07/2025  
**<PERSON><PERSON><PERSON> tiêu:** Sửa lại dữ liệu ở dashboard để chỉ lấy các dữ liệu thật (is_real = 1)

## Tổng quan

Dashboard hiện tại đang hiển thị tất cả dữ liệu bao gồm cả dữ liệu fake và thật. Cần cập nhật để chỉ hiển thị dữ liệu thật bằng cách thêm filter `is_real = 1` vào tất cả các query.

## Các bảng có field is_real

- `users` - is_real (1: thật, 0: fake)
- `companies` - is_real (1: thật, 0: fake)  
- `job` - is_real (1: thật, 0: fake)
- `warehouse_cvs` - is_real (1: thật, 0: fake)

## Các file đã đượ<PERSON> cập nhật

### 1. UserRepository.php

**File:** `app/Repositories/UserRepository.php`

**Method được cập nhật:**
- `total()` - Thêm `->where('is_real', 1)`
- `statisticaByMonth()` - Thêm `->where('is_real', 1)`
- `statisticalEmployerByMonth()` - Thêm `->where('is_real', 1)`

**Thay đổi:**
```php
// Thêm vào các method
$query->where('is_real', 1); // Chỉ lấy dữ liệu thật
```

### 2. WareHouseCvRepository.php

**File:** `app/Repositories/WareHouseCvRepository.php`

**Method được cập nhật:**
- `total()` - Thêm `->where('is_real', 1)`
- `statisticalByMonth()` - Thêm `->where('is_real', 1)`

**Thay đổi:**
```php
// Thêm vào các method
$query->where('is_real', 1); // Chỉ lấy dữ liệu thật
```

### 3. JobRepository.php

**File:** `app/Repositories/JobRepository.php`

**Method được cập nhật:**
- `totalActive()` - Thêm `->where('is_real', 1)`
- `jobStatisticalByMonth()` - Thêm `->where('is_real', 1)`

**Thay đổi:**
```php
// Thêm vào các method
$query->where('is_real', 1); // Chỉ lấy dữ liệu thật
```

### 4. CompanyRepository.php

**File:** `app/Repositories/CompanyRepository.php`

**Method được cập nhật:**
- `total()` - Thêm `->where('is_real', 1)`

**Thay đổi:**
```php
// Thêm vào method total()
$query->where('is_real', 1); // Chỉ lấy dữ liệu thật
```

### 5. SubmitCvRepository.php

**File:** `app/Repositories/SubmitCvRepository.php`

**Method được cập nhật:**
- `statisticalByMonth()` - Thêm join với bảng job và filter `job.is_real = 1`

**Thay đổi:**
```php
// Cập nhật method statisticalByMonth()
return $query->where('submit_cvs.is_active', config('constant.active'))
    ->join('job', 'submit_cvs.job_id', '=', 'job.id')
    ->where('job.is_real', 1) // Chỉ lấy submit CV từ job thật
    ->whereBetween('submit_cvs.created_at', [$start, $end])
    ->selectRaw('COUNT(submit_cvs.id) as total, DATE_FORMAT(submit_cvs.created_at, "%m") as month')
    ->groupByRaw('DATE_FORMAT(submit_cvs.created_at, "%m")')
    ->orderByRaw('DATE_FORMAT(submit_cvs.created_at, "%m")')
    ->get();
```

### 6. WareHouseCvSellingRepository.php

**File:** `app/Repositories/WareHouseCvSellingRepository.php`

**Method được cập nhật:**
- `statisticalByMonth()` - Thêm `->where('is_real', 1)`

**Thay đổi:**
```php
// Thêm vào method statisticalByMonth()
$query->where('is_real', 1); // Chỉ lấy dữ liệu thật
```

## Các service được ảnh hưởng

Dashboard sử dụng các service sau, tất cả đều đã được cập nhật thông qua repository:

1. **UserService:**
   - `totalEmployer()` - Đếm tổng số employer thật
   - `totalCollaborator()` - Đếm tổng số collaborator thật
   - `statisticalEmployerByMonth()` - Thống kê employer theo tháng (chỉ thật)
   - `statisticalCollaboratorByMonth()` - Thống kê collaborator theo tháng (chỉ thật)

2. **WareHouseCvService:**
   - `total()` - Đếm tổng số CV thật
   - `statisticalByMonth()` - Thống kê CV theo tháng (chỉ thật)

3. **JobService:**
   - `totalActive()` - Đếm tổng số job active thật
   - `jobStatisticalByMonth()` - Thống kê job theo tháng (chỉ thật)

4. **CompanyService:**
   - `total()` - Đếm tổng số company thật

5. **SubmitCvService:**
   - `statisticalByMonth()` - Thống kê submit CV theo tháng (chỉ từ job thật)

6. **WareHouseCvSellingService:**
   - `statisticalByMonth()` - Thống kê CV selling theo tháng (chỉ thật)

## Kết quả mong đợi

Sau khi cập nhật:
- Dashboard sẽ chỉ hiển thị số liệu từ dữ liệu thật (is_real = 1)
- Các biểu đồ thống kê sẽ chỉ tính toán dựa trên dữ liệu thật
- Dữ liệu fake sẽ bị loại bỏ khỏi tất cả các báo cáo dashboard

## Cách test

1. Truy cập dashboard admin
2. Kiểm tra các số liệu tổng quan
3. Kiểm tra các biểu đồ thống kê theo tháng
4. So sánh với dữ liệu trước khi cập nhật để đảm bảo chỉ hiển thị dữ liệu thật

## Lưu ý

- Tất cả các thay đổi đều backward compatible
- Không ảnh hưởng đến các chức năng khác của hệ thống
- Chỉ ảnh hưởng đến việc hiển thị dữ liệu trên dashboard admin
