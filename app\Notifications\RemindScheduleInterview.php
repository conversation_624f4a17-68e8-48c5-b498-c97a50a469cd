<?php

namespace App\Notifications;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindScheduleInterview extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $user;
    protected $wareHouseCvSellingBuyBook;

    public function __construct($wareHouseCvSellingBuy,$user,$wareHouseCvSellingBuyBook)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->user = $user;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $name = $this->user->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;

        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $timeInterview = $this->wareHouseCvSellingBuyBook->date_time_book_format;
        $address = $this->wareHouseCvSellingBuyBook->address;
        $type_of_sale = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $link = route('employer-cv-bought') . '?update-status=' . $this->wareHouseCvSellingBuy->id;

        return (new MailMessage)
            ->view('email.remindScheduleInterview', [
                'name'  =>  $name,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type_of_sale,
                'time' => $timeInterview,
                'address' => $address,
                'link' => $link,
            ])
            ->subject('[Recland] Cập nhật kết quả phỏng vấn ứng viên '. $candidateName .' vị trí ' . $position);

    }

}
