<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Services\Admin\InformationContactService;
use App\Services\Frontend\PostService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\BannerService;
use App\Services\Frontend\JobService;
use App\Services\Frontend\TestimonialService;
use App\Services\Frontend\TopRecService;

class HomeController extends Controller
{
    protected $bannerService;
    protected $testimonialService;
    protected $settingService;
    protected $jobService;
    protected $topRecService;
    protected $seoService;
    protected $routeName;
    protected $informationContactService;
    protected $postService;

    public function __construct(
        BannerService $bannerService,
        TestimonialService $testimonialService,
        SettingService $settingService,
        TopRecService $topRecService,
        JobService $jobService,
        SeoService $seoService,
        InformationContactService $informationContactService,
        PostService $postService
    )
    {
        $this->bannerService = $bannerService;
        $this->testimonialService = $testimonialService;
        $this->settingService = $settingService;
        $this->jobService = $jobService;
        $this->seoService = $seoService;
        $this->topRecService = $topRecService;
        $this->informationContactService = $informationContactService;
        $this->postService = $postService;
        $this->routeName = \Route::currentRouteName();
    }

    public function index()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        //get testimonations CTV
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ctv'));

        // get top rec list
        $topRec = $this->topRecService->getTopRecList();

        $blogNew = $this->postService->getPostNewFirst();
        //key value theo ngon ngu
//        $this->generateParams();


        return view('frontend.pages.home.index', compact('topRec','blogNew', 'listTestimonations'));
    }

    private function generateParams()
    {
        $cities = Common::getCities();
        $arrSettingGlobal = $this->settingService->getListSettingGlobal();
        view()->share([
            'cities' => $cities,
            'arrLangGlobal' => $arrSettingGlobal
        ]);
    }


}
