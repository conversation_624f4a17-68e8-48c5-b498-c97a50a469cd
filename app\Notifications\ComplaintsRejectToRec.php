<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintsRejectToRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $content = $this->wareHouseCvSellingBuy->txt_complain;
        $image = $this->wareHouseCvSellingBuy->img_complain;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $link = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id,'open_complain' => 1]);
        $marketCv = route('market-cv');
        return (new MailMessage)
            ->view('email.complaintsRejectToRec', [
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'type' => $type,
                'content' => $content,
                'image' => gen_url_file_s3($image),
                'recName' => $recName,
                'link' => $link,
                'marketCv' => $marketCv,
                'wareHouseCvSellingBuyId' => $this->wareHouseCvSellingBuy->id
            ])
            ->subject('[Recland][Case #'.$this->wareHouseCvSellingBuy->id.'] Kết quả khiếu nại');
    }

}
