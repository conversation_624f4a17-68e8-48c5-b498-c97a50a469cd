<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class PayoutLog extends BaseModel
{
    use HasFactory, Notifiable;
    protected $appends = ['created_at_value'];

    protected $table = 'payout_logs';
    public $timestamps = true;
    protected $guarded = ['id'];

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }

    public function payoutLogHistory()
    {
        return $this->hasMany(PayoutLogHistory::class, 'payoutlog_id', 'id');
    }
}
