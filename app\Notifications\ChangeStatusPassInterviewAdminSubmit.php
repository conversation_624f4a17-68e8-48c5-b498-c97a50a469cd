<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusPassInterviewAdminSubmit extends Mailable
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function content(): Content
    {
        $recName = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->employer->name;
        $url           = route('submit-cv.edit', ['id' => $this->submitCv->id]);
        return new Content(
            view: 'email.changeStatusPassInterviewAdminSubmit',
            with: [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'url'           => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $message->subject('[Recland] Thông báo Ứng viên '.$candidateName.' đã Pass phỏng vấn của công ty '.$companyName);
        return $this;
    }




}
