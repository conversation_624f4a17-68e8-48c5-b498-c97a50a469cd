<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerSendDiscusToRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $warehouseCvSellingBuyDiscuss;

    public function __construct($wareHouseCvSellingBuy,$warehouseCvSellingBuyDiscuss)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->warehouseCvSellingBuyDiscuss = $warehouseCvSellingBuyDiscuss;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $link = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id,'open_comment' => 1]);
        return (new MailMessage)
            ->view('email.employerSendDiscusToRec', [
                'link' => $link,
                'recName' => $recName,
                'position' => $position,
                'candidateName' => $candidateName,
                'comment' => $this->warehouseCvSellingBuyDiscuss->comment,
            ])
            ->subject('[Recland] Bạn nhận được 1 thảo luận mới ứng viên '.$candidateName);

    }


}
