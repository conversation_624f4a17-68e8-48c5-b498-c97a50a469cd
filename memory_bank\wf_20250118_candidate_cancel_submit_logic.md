# Workflow: C<PERSON><PERSON> nh<PERSON>t Logic Hủy Ứng tuyển CTV - 18/01/2025

## Tổng quan

Cập nhật logic hủy ứng tuyển của Cộng tác viên (CTV) vào tài liệu Software Requirements Specification (SRS) trong file `document/recruitment_flow_detail.mmd`.

## Mục tiêu

-   Tài liệu hóa chức năng hủy ứng tuyển mới của CTV
-   B<PERSON> sung vào quy trình tuyển dụng hiện tại
-   <PERSON><PERSON> tả rõ điều kiện, quy trình và kết quả

## Công việc đã thực hiện

### 1. Cập nhật Giao diện (UI/UX)

**File: `resources/views/frontend/pages/collaborator/submitcv.blade.php`**

#### 1.1 Loại bỏ nút hủy cũ ở góc

-   X<PERSON>a nút "×" màu đỏ hình tròn ở góc trên phải
-   Style cũ: `position: absolute; top: 10px; right: 10px; z-index: 10;`

#### 1.2 Thêm nút hủy mới vào status-group

-   Đặt bên dưới nút "Chi tiết"
-   Style mới: border đỏ, hover effect, transition mượt
-   Điều kiện hiển thị: `(!$item->status_payment_ctv || $item->status_payment_ctv == 0) && $item->can_cancel_apply`

#### 1.3 Thêm text "Đã hủy" cho items đã hủy

-   Điều kiện: `$item->status == config('constant.status_recruitment_revert.CandidateCancelApply')`
-   Style: màu đỏ, font-weight bold, nổi bật

### 2. Cập nhật Backend Logic

**File: `app/Http/Controllers/Frontend/RecController.php`**

#### 2.1 Method candidateCancelSubmitCv

```php
public function candidateCancelSubmitCv($id)
{
    try {
        $client = auth('client')->user();
        $result = $this->submitCvService->candidateCancelSubmitWithRefund($id, $client->id);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Đã hủy ứng tuyển thành công. Hệ thống sẽ hoàn tiền cho nhà tuyển dụng trong vòng 24 giờ.'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Không thể hủy ứng tuyển này.'
            ]);
        }
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Có lỗi xảy ra, vui lòng thử lại sau.'
        ], 500);
    }
}
```

### 3. Cập nhật SRS (Software Requirements Specification)

**File: `document/recruitment_flow_detail.mmd`**

#### 3.1 Giai đoạn 2: NTD Thanh toán & Review

Thêm nhánh hủy ứng tuyển từ trạng thái "Chờ Xếp lịch Phỏng vấn":

```
J -- "CTV hủy ứng tuyển" --> J1["CTV Hủy Ứng tuyển"]
```

**Chi tiết node J1:**

-   Controller: `RecController@candidateCancelSubmitCv`
-   Service: `SubmitCvService@candidateCancelSubmitWithRefund`
-   DB: Cập nhật `SubmitCv.status` = 2 (CandidateCancelApply)
-   Action: Hoàn tiền cho NTD trong vòng 24 giờ
-   Notify: Gửi email thông báo cho NTD về việc hủy ứng tuyển
-   **Điều kiện:** Chỉ cho phép khi chưa thanh toán CTV (`status_payment_ctv` = 0)

#### 3.2 Giai đoạn 3: Phỏng vấn

Thêm nhánh hủy ứng tuyển từ trạng thái "Đã xếp lịch":

```
M -- "CTV hủy ứng tuyển" --> M1["CTV Hủy Ứng tuyển"]
```

**Chi tiết node M1:** Tương tự node J1

#### 3.3 Kết thúc Luồng

Thêm các node kết thúc:

-   J1 --> End
-   M1 --> End

## Quy tắc Kinh doanh (Business Rules)

### 1. Điều kiện hủy ứng tuyển

-   CTV chỉ được hủy khi chưa thanh toán (`status_payment_ctv` = 0)
-   Trường `can_cancel_apply` phải = true
-   Không áp dụng cho các trạng thái đã hoàn thành thanh toán

### 2. Quy trình hoàn tiền

-   Hệ thống tự động hoàn tiền cho NTD trong vòng 24 giờ
-   Không tính phí xử lý hoàn tiền
-   Gửi email thông báo cho NTD về việc hủy và hoàn tiền

### 3. Trạng thái sau khi hủy

-   `SubmitCv.status` = 2 (CandidateCancelApply)
-   Hiển thị text "Đã hủy" thay vì nút hủy
-   Không thể thực hiện các thao tác khác trên record này

## Tác động Hệ thống

### 1. Database

-   Cập nhật trạng thái SubmitCv
-   Ghi log transaction hoàn tiền
-   Lưu lịch sử thay đổi

### 2. Email/Notification

-   Thông báo cho NTD về việc hủy ứng tuyển
-   Thông báo về hoàn tiền
-   Cập nhật CTV về việc hủy thành công

### 3. Báo cáo

-   Thống kê tỷ lệ hủy ứng tuyển
-   Theo dõi số tiền hoàn trả
-   Phân tích lý do hủy (nếu có)

## Kiểm thử

### 1. Test Cases

-   [ ] Hủy ứng tuyển thành công khi đủ điều kiện
-   [ ] Từ chối hủy khi đã thanh toán CTV
-   [ ] Từ chối hủy khi `can_cancel_apply` = false
-   [ ] Hiển thị đúng UI theo trạng thái
-   [ ] Gửi email thông báo chính xác

### 2. Edge Cases

-   [ ] Xử lý lỗi khi service hoàn tiền thất bại
-   [ ] Xử lý concurrent request
-   [ ] Validate quyền truy cập của CTV

## Tài liệu Liên quan

-   `document/recruitment_flow_detail.mmd` - SRS chính
-   `document/recruitment_flow.mmd` - Flow tổng quan
-   Route definition trong `routes/web.php`
-   Controller `app/Http/Controllers/Frontend/RecController.php`
-   Service `app/Services/Frontend/SubmitCvService.php`

### 4. Cập nhật SRS riêng lẻ theo từng giai đoạn

#### 4.1 `document/SRS_F02.1_Job_Submit_CV_Data.md`

**Giai đoạn**: CV Data (chỉ mua thông tin CV)

**Thêm section**: "5.6. CTV hủy ứng tuyển"

**Điều kiện**:

-   `status` = 21 (WaitingPayment)
-   `status_payment_ctv` = 0 (chưa thanh toán CTV)
-   `can_cancel_apply` = true

**Xử lý**: Hoàn 100% tiền cho NTD, cập nhật status = 2

#### 4.2 `document/SRS_F02.2_Job_Submit_CV_Interview.md`

**Giai đoạn**: Interview (đã trả phí phỏng vấn)

**Thêm section**: "5.6. CTV hủy ứng tuyển"

**Điều kiện**:

-   `status` ∈ [3, 7, 21] (Waiting setup interview, Waiting Interview, WaitingPayment)
-   `status_payment_ctv` = 0 (chưa thanh toán CTV)
-   `can_cancel_apply` = true

**Xử lý**: Hoàn 100% phí Interview, hủy Job liên quan (`PayInterviewSubmit`, `PassInterviewSubmit`)

#### 4.3 `document/SRS_F02.3_Job_Submit_CV_Onboard.md`

**Giai đoạn**: Onboard (giai đoạn onboard và thử việc)

**Thêm section**: "5.5. CTV hủy ứng tuyển"

**Điều kiện**:

-   `status` ∈ [11, 13, 14] (Offering, Waitingonboard, Trialwork)
-   `status_payment_ctv` = 0 (chưa thanh toán CTV)
-   `can_cancel_apply` = true

**Xử lý**:

-   Nếu status = 11 hoặc 13: Hoàn 100% phí Onboard đã trả
-   Nếu status = 14: Hoàn tiền theo thời gian thử việc (100%/70%/50% tùy thời điểm)
-   Hủy tất cả Job `PayOnboardSubmit` đã dispatch

## Ghi chú

-   Logic này chỉ áp dụng cho CTV, không phải cho candidate tự ứng tuyển
-   Cần đảm bảo tính nhất quán với quy trình hoàn tiền hiện tại
-   Theo dõi impact đến business metrics sau khi deploy
-   Đã cập nhật đầy đủ vào cả SRS tổng quan và các SRS chi tiết theo từng giai đoạn

---

**Người thực hiện:** AI Assistant  
**Ngày hoàn thành:** 18/01/2025  
**Status:** Completed ✅
