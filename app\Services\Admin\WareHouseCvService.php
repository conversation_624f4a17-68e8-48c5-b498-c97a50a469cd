<?php

namespace App\Services\Admin;

use App\Repositories\WareHouseCvRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;

class WareHouseCvService
{
    protected $wareHouseCvRepository;

    public function __construct(WareHouseCvRepository $wareHouseCvRepository)
    {
        $this->wareHouseCvRepository = $wareHouseCvRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        try {
            $data = $this->wareHouseCvRepository->getListCv($params, $order, $paginate);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_name',
                    'data-orderable' => 'false',
                ],
                'value' => 'Họ tên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_mobile',
                    'data-orderable' => 'false',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_job_title',
                    'data-orderable' => 'false',
                ],
                'value' => 'Vị trí công việc',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'url_cv_public',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_portfolio',
                    'data-orderable' => 'false',
                ],
                'value' => 'Portfolio',
            ],
        ];

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('cv-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function total()
    {
        return $this->wareHouseCvRepository->total();
    }

    public function totalCurrentMonth()
    {
        $start = Carbon::now()->startOfMonth();
        return $this->wareHouseCvRepository->total(['after_date' => $start]);
    }

    public function detailService($id)
    {
        return $this->wareHouseCvRepository->find($id);
    }

    public function updateService($params, $id)
    {
        $arrSkillDescription = [];
        if (isset($params['skill']) && $params['description']) {
            foreach ($params['skill'] as $k => $v) {
                $arrSkillDescription[$v] = $params['description'][$k];
            }
        }

        $data = [
            'candidate_name' => $params['candidate_name'],
            'candidate_email' => $params['candidate_email'],
            'candidate_mobile' => $params['candidate_mobile'],
            'assessment' => $params['assessment'],
            'year_experience' => $params['year_experience'],
            'candidate_portfolio' => $params['candidate_portfolio'],
            'candidate_job_title' => $params['candidate_job_title'],
            'candidate_currency' => $params['candidate_currency'],
            'candidate_salary_expect' => $params['candidate_salary_expect'],
            'main_skill' => json_encode($arrSkillDescription),
            'career' => $params['career'],
            'candidate_est_timetowork' => $params['candidate_est_timetowork'],
        ];

        if (isset($params['cv_private']) && is_file($params['cv_private'])) {
            $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3($params['cv_private'], config('constant.sub_path_s3.cv'));
        }

        if (isset($params['cv_public']) && is_file($params['cv_public'])) {
            $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3($params['cv_public'], config('constant.sub_path_s3.cv'));
        }

        return $this->wareHouseCvRepository->update($id, [], $data);
    }

    public function statisticalByMonth($fromDate = null, $toDate = null)
    {
        $data = $this->wareHouseCvRepository->statisticalByMonth($fromDate, $toDate);
        $data = $data->keyBy('month')->toArray();
        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }

}
