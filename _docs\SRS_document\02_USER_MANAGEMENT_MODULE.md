# Module <PERSON>uản lý <PERSON> (User Management Module)

## 1. Tổng quan

Module Quản lý Người dùng là module cốt lõi của hệ thống RecLand, quản lý toàn bộ thông tin và hoạt động của các loại người dùng trong hệ thống.

## 2. <PERSON><PERSON><PERSON>ờ<PERSON> dùng

### 2.1. <PERSON><PERSON><PERSON> tác viên (Collaborator/Rec)
- **Vai trò**: Người giới thiệu ứng viên cho các vị trí tuyển dụng
- **Quyền hạn**:
  - Xem danh sách việc làm
  - Giới thiệu ứng viên
  - Quản lý kho CV cá nhân
  - Bán CV trong kho
  - Theo dõi trạng thái ứng tuyển
  - Quản lý ví và rút tiền hoa hồng

### 2.2. <PERSON><PERSON><PERSON> tuyể<PERSON> (Employer)
- **<PERSON><PERSON> trò**: <PERSON><PERSON><PERSON> nghiệp có nhu cầu tuyển dụng
- **<PERSON>uyền hạn**:
  - Đăng tin tuyển dụng
  - Xem và đánh giá CV ứng viên
  - Mua CV từ kho
  - Quản lý quy trình tuyển dụng
  - Thanh toán phí dịch vụ

### 2.3. Quản trị viên (Admin)
- **Vai trò**: Quản lý toàn bộ hệ thống
- **Quyền hạn**:
  - Quản lý tất cả người dùng
  - Duyệt tin tuyển dụng
  - Xử lý tranh chấp
  - Quản lý thanh toán
  - Xem báo cáo hệ thống

### 2.4. Ứng viên (Candidate) 
- **Vai trò**: Người được giới thiệu cho vị trí tuyển dụng
- **Quyền hạn**: Hạn chế, chủ yếu được quản lý bởi cộng tác viên

## 3. Chức năng Chi tiết

### 3.1. Đăng ký Tài khoản

#### 3.1.1. Đăng ký Cộng tác viên
- **Input**:
  - Họ tên (*)
  - Email (*) - unique
  - Số điện thoại (*) - unique
  - Mật khẩu (*)
  - Xác nhận mật khẩu (*)
  - Mã giới thiệu (nếu có)
  - Đồng ý điều khoản (*)

- **Process**:
  1. Validate thông tin đầu vào
  2. Kiểm tra email/phone đã tồn tại
  3. Mã hóa mật khẩu
  4. Tạo mã xác thực email
  5. Gửi email xác thực
  6. Tạo ví điện tử mặc định
  7. Gán người giới thiệu (nếu có mã)

- **Output**:
  - Tài khoản được tạo (trạng thái chưa xác thực)
  - Email xác thực được gửi

#### 3.1.2. Đăng ký Nhà tuyển dụng
- **Input**:
  - Thông tin công ty:
    - Tên công ty (*)
    - Mã số thuế
    - Địa chỉ (*)
    - Website
    - Quy mô
    - Lĩnh vực hoạt động (*)
  - Thông tin người đại diện:
    - Họ tên (*)
    - Chức vụ (*)
    - Email (*) - unique
    - Số điện thoại (*) - unique
    - Mật khẩu (*)

- **Process**:
  1. Validate thông tin
  2. Kiểm tra công ty đã tồn tại
  3. Tạo record công ty
  4. Tạo tài khoản employer
  5. Gửi email xác thực
  6. Tạo ví điện tử công ty

- **Output**:
  - Công ty và tài khoản được tạo
  - Email thông báo cho admin duyệt

### 3.2. Đăng nhập

#### 3.2.1. Đăng nhập thông thường
- **Input**:
  - Email/Số điện thoại
  - Mật khẩu
  - Remember me (optional)

- **Process**:
  1. Validate credentials
  2. Kiểm tra tài khoản active
  3. Tạo session/token
  4. Log hoạt động đăng nhập
  5. Cập nhật last_login_at

- **Output**:
  - Redirect đến dashboard tương ứng
  - Session/Token được tạo

#### 3.2.2. Đăng nhập Social (Google/Facebook)
- **Process**:
  1. Redirect đến OAuth provider
  2. Callback với user info
  3. Kiểm tra/tạo tài khoản
  4. Tạo session

### 3.3. Quản lý Hồ sơ

#### 3.3.1. Hồ sơ Cộng tác viên
- **Thông tin cá nhân**:
  - Họ tên
  - Ngày sinh
  - Giới tính
  - Địa chỉ
  - CMND/CCCD
  - Avatar

- **Thông tin nghề nghiệp**:
  - Lĩnh vực chuyên môn
  - Kinh nghiệm
  - Mạng lưới quan hệ

- **Thông tin ngân hàng** (cho rút tiền):
  - Tên ngân hàng
  - Số tài khoản
  - Tên chủ tài khoản
  - Chi nhánh

#### 3.3.2. Hồ sơ Nhà tuyển dụng
- **Thông tin công ty**:
  - Logo
  - Mô tả công ty
  - Văn hóa công ty
  - Hình ảnh/Video giới thiệu
  - Chính sách phúc lợi

- **Thông tin liên hệ**:
  - Địa chỉ các chi nhánh
  - Người liên hệ
  - Hotline

### 3.4. Quản lý Mật khẩu

#### 3.4.1. Đổi mật khẩu
- **Input**:
  - Mật khẩu cũ
  - Mật khẩu mới
  - Xác nhận mật khẩu mới

- **Validation**:
  - Mật khẩu cũ đúng
  - Mật khẩu mới >= 8 ký tự
  - Có chữ hoa, chữ thường, số

#### 3.4.2. Quên mật khẩu
- **Process**:
  1. Nhập email/phone
  2. Gửi link reset password
  3. Verify token
  4. Cho phép đặt mật khẩu mới

### 3.5. Phân quyền và Vai trò

#### 3.5.1. Hệ thống Role
- **Super Admin**: Toàn quyền hệ thống
- **Admin**: Quản lý operations
- **Employer Admin**: Quản lý công ty
- **Employer User**: Nhân viên tuyển dụng
- **Collaborator**: Cộng tác viên

#### 3.5.2. Permission Matrix
```
| Chức năng | Super Admin | Admin | Employer Admin | Employer User | Collaborator |
|-----------|-------------|-------|----------------|---------------|--------------|
| Quản lý user | ✓ | ✓ | - | - | - |
| Đăng job | - | - | ✓ | ✓ | - |
| Giới thiệu CV | - | - | - | - | ✓ |
| Mua CV | - | - | ✓ | ✓ | - |
| Rút tiền | - | - | ✓ | - | ✓ |
```

### 3.6. Xác thực và Bảo mật

#### 3.6.1. Email Verification
- Gửi email với token xác thực
- Token hết hạn sau 24h
- Cho phép gửi lại email

#### 3.6.2. Two-Factor Authentication (2FA)
- Optional cho employer admin
- Sử dụng Google Authenticator
- Backup codes

#### 3.6.3. Session Management
- Session timeout: 2 giờ
- Remember me: 30 ngày
- Single device login option

### 3.7. Quản lý Team (Cho Employer)

#### 3.7.1. Thêm nhân viên
- **Input**:
  - Email nhân viên
  - Vai trò
  - Phòng ban

- **Process**:
  1. Gửi email mời
  2. Nhân viên xác nhận
  3. Tạo tài khoản
  4. Gán quyền

#### 3.7.2. Phân quyền nhân viên
- Xem job của phòng ban
- Đăng job (cần duyệt)
- Mua CV (có limit)

### 3.8. Mạng lưới Cộng tác viên

#### 3.8.1. Affiliate System
- Mỗi CTV có mã giới thiệu unique
- Track người đăng ký qua mã
- Hoa hồng từ downline (2 cấp)

#### 3.8.2. Team Management
- Xem danh sách downline
- Thống kê doanh thu team
- Training và support

## 4. Database Schema

### 4.1. Bảng users
```sql
- id (primary key)
- name
- email (unique)
- phone (unique)
- password
- user_type (collaborator/employer/admin)
- email_verified_at
- phone_verified_at
- status (active/inactive/banned)
- referral_code (unique)
- parent_id (referrer)
- avatar
- last_login_at
- remember_token
- created_at
- updated_at
```

### 4.2. Bảng user_infos
```sql
- id
- user_id (foreign key)
- birthday
- gender
- address
- id_card
- bank_name
- bank_account
- bank_branch
- work_position
- experience
- specialization
- created_at
- updated_at
```

### 4.3. Bảng companies
```sql
- id
- name
- tax_code
- address
- website
- size
- industry
- logo
- description
- culture
- benefits
- admin_id (employer admin)
- status
- created_at
- updated_at
```

### 4.4. Bảng roles
```sql
- id
- name
- slug
- permissions (json)
- created_at
- updated_at
```

### 4.5. Bảng user_roles
```sql
- user_id
- role_id
- assigned_by
- created_at
```

## 5. API Endpoints

### 5.1. Authentication
- `POST /api/register` - Đăng ký
- `POST /api/login` - Đăng nhập
- `POST /api/logout` - Đăng xuất
- `POST /api/refresh-token` - Làm mới token
- `POST /api/forgot-password` - Quên mật khẩu
- `POST /api/reset-password` - Reset mật khẩu
- `GET /api/verify-email/{token}` - Xác thực email

### 5.2. User Management
- `GET /api/profile` - Lấy thông tin profile
- `PUT /api/profile` - Cập nhật profile
- `POST /api/change-password` - Đổi mật khẩu
- `POST /api/upload-avatar` - Upload avatar
- `PUT /api/bank-info` - Cập nhật thông tin ngân hàng

### 5.3. Team Management
- `GET /api/team/members` - Danh sách thành viên
- `POST /api/team/invite` - Mời thành viên
- `PUT /api/team/member/{id}/role` - Cập nhật role
- `DELETE /api/team/member/{id}` - Xóa thành viên

## 6. Các Tình huống Sử dụng (Use Cases)

### 6.1. UC01: Đăng ký Cộng tác viên
1. User truy cập trang đăng ký
2. Chọn loại tài khoản "Cộng tác viên"
3. Điền form thông tin
4. Submit form
5. Hệ thống validate và tạo tài khoản
6. Gửi email xác thực
7. User xác thực email
8. Tài khoản được kích hoạt

### 6.2. UC02: Employer mời nhân viên
1. Employer admin đăng nhập
2. Vào trang quản lý team
3. Click "Mời nhân viên"
4. Nhập email và chọn role
5. Hệ thống gửi email mời
6. Nhân viên click link trong email
7. Điền thông tin và tạo mật khẩu
8. Tài khoản được tạo và link với công ty

## 7. Validation Rules

### 7.1. Email
- Format: valid email
- Unique trong hệ thống
- Không chứa ký tự đặc biệt

### 7.2. Password
- Độ dài: 8-32 ký tự
- Phải có: chữ hoa, chữ thường, số
- Không được trùng với email

### 7.3. Phone
- Format: 10-11 số
- Bắt đầu bằng: 0
- Unique trong hệ thống

## 8. Security Considerations

### 8.1. Password Security
- Bcrypt hashing với cost factor 10
- Password history (không dùng lại 5 password gần nhất)
- Force change password sau 90 ngày

### 8.2. Session Security
- HTTP Only cookies
- Secure flag cho HTTPS
- CSRF token cho forms
- Rate limiting cho login attempts

### 8.3. Data Privacy
- Mã hóa thông tin nhạy cảm
- Audit log cho các thao tác quan trọng
- GDPR compliance cho EU users
