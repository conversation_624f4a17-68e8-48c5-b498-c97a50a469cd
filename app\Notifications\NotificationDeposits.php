<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class NotificationDeposits extends Mailable
{
    use Queueable;

    protected $denominations;
    protected $user;

    public function __construct($user, $denominations)
    {
        $this->user = $user;
        $this->denominations = $denominations;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        return new Content(
            view: 'email.notificationDeposits',
            with: [
                'name' => $this->user->name,
                'employerId' => $this->user->id,
                'denominations' => $this->denominations['price'],
            ],
        );
    }
    protected function buildSubject($message)
    {
        $message->subject('[Thanh toán] Thông báo nhận thanh toán đơn hàng mới');
        return $this;
    }

}
