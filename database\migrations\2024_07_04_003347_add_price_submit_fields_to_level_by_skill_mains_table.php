<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('level_by_skill_mains', function (Blueprint $table) {
            $table->integer('price_cv_min_submit')->after('price_cv_max')->default(0);
            $table->integer('price_interview_min_submit')->after('price_interview_max')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('level_by_skill_mains', function (Blueprint $table) {
            $table->dropColumn(['price_cv_min_submit', 'price_interview_min_submit']);
        });
    }
};
