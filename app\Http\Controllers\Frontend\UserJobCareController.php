<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Services\Frontend\UserJobCareService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserJobCareController extends Controller
{

    protected $userJobCareService;

    public function __construct(UserJobCareService $userJobCareService)
    {
        $this->userJobCareService = $userJobCareService;
    }

    public function change(Request $request)
    {
        try {
            $response = $this->userJobCareService->changeStatus($request->job_id, $request->type);
            return response()->json(['code' => 'success', 'message' => $response]);
        } catch (\Exception $exception) {
            return response()->json(['code' => $exception->getCode(), 'message' => $exception->getMessage()]);
        }
    }
}
