<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\BannerRequest;
use App\Services\Admin\BannerService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BannerController extends Controller
{

    protected $bannerService;

    public function __construct(BannerService $bannerService)
    {
        $this->bannerService = $bannerService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->bannerService->buildDatatable();
        return view('admin.pages.banner.index',
            compact('datatable'));
    }

    /**
     * @param BannerRequest $request
     * @return mixed
     */
    public function store(BannerRequest $request)
    {
        return $this->bannerService->createService($request->all());
    }

    public function show($id)
    {
        return $this->bannerService->detailService($id);
    }

    public function update(BannerRequest $request, $id)
    {
        return $this->bannerService->updateService($request->all(), $id);
    }

    /**
     * @param Request $request
     * @return Application|ResponseFactory|Response
     */
    public function datatable(Request $request)
    {
        $data = $this->bannerService->datatable($request->all());
        return response($data);
    }
}
