<?php

namespace App\Services\Frontend;

use App\Repositories\PayoutLogHistoryRepository;
use App\Repositories\PayoutLogRepository;
use App\Repositories\WalletRepository;

class PayoutLogService
{

    protected $payoutLogRepository;
    protected $payoutLogHistoryRepository;
    protected $walletRepository;

    public function __construct(
        PayoutLogRepository $payoutLogRepository,
        PayoutLogHistoryRepository $payoutLogHistoryRepository,
        WalletRepository $walletRepository,
    ) {
        $this->payoutLogRepository = $payoutLogRepository;
        $this->payoutLogHistoryRepository = $payoutLogHistoryRepository;
        $this->walletRepository = $walletRepository;
    }

    public function requestWithdraw($params)
    {
        $user = auth('client')->user();
        $userInfo = $user->userInfo;
        $userWallet = $user->wallet;
        if (!$userInfo || !$userWallet) {
            return false;
        }
        $balance = $userWallet->price - $params['price_withdraw'];

        $data = [
            'user_id'       => $user->id,
            'amount'        => $params['price_withdraw'],
            'balance'       => $balance,
            'name'          =>  $userInfo->bank_account,
            'bank_name'     =>  $userInfo->bank_name,
            'bank_number'   =>  $userInfo->bank_account_number,
            'bank_province' =>  $userInfo->bank_branch,
        ];

        $payout = $this->payoutLogRepository->create($data);

        $this->payoutLogHistoryRepository->create([
            'payoutlog_id'  => $payout->id,
        ]);

        $this->walletRepository->update($userWallet->id, [], [
            'price' => $balance,
        ]);

        return $payout;
    }

    public function getHistoryWithdraw($params)
    {
        $user = auth('client')->user();
        return $this->payoutLogRepository->getHistoryWithdraw($params, $user->id);
    }

    public function statistical()
    {
        $user = auth('client')->user();
        $statistical = [];
        $statistical['statusPending'] = $this->payoutLogRepository->countStatusPending($user->id);
        $statistical['totalPending'] = $this->payoutLogRepository->totalWithdraw($user->id, 0);
        $statistical['totalWithdraw'] = $this->payoutLogRepository->totalWithdraw($user->id, 1);
        $remaining = $this->payoutLogRepository->totalRemaining($user->id);
        $statistical['totalRemaining'] = $remaining ? $remaining->balance : 0;
        return $statistical;
    }

    public function getDetail($id)
    {
        $user = auth('client')->user();
        return $this->payoutLogRepository->getDetail($id, $user->id);
    }

    public function sumAmountPending(){
        $user = auth('client')->user();
        return $this->payoutLogRepository->sumAmountPending($user->id);
    }

}
