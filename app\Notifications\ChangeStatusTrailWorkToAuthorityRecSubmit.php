<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusTrailWorkToAuthorityRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $recName       = $this->submitCv->rec->name;
        $position      = $this->submitCv->job->name;
        return (new MailMessage)
            ->view('email.changeStatusTrailWorkToAuthorityRecSubmit', [
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'recName'       => $recName,
            ])
            ->subject('[Recland] Ứng viên '.$candidateName.' bắt đầu ngày làm việc đầu tiên tại công ty '.$companyName);
    }

}
