<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $recName       = $this->submitCv->rec->name;
        $position      = $this->submitCv->job->name;
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id]);
        return (new MailMessage)
            ->view('email.changeStatusCancelOnboardToRecSubmit', [
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'recName'       => $recName,
                'url'           => $url,
            ])
            ->subject('[Recland] Thông báo ứng viên '.$candidateName.' đã “Cancel Onboard” vị trí '.$position);
    }

}
