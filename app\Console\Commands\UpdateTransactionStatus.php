<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class UpdateTransactionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transactions:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update the status of transactions that are older than 12 hours';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $transactions = DB::table('zalopay_transactions')
            ->where('status', 0)
            ->where('created_at', '<=', now()->subHours(12))
            ->get();

        $config = [
            "appid"    => config('payment.zalo_appid'),
            "key1"     => config('payment.zalo_key1'),
            "key2"     => config('payment.zalo_key2'),
            "endpoint" => config('payment.zalo_query'),
        ];

//        dd($transactions);
        foreach ($transactions as $tran) {
            $app_trans_id = $tran->app_trans_id;
            $data = $config["appid"] . "|" . $app_trans_id . "|" . $config["key1"];
            $params = [
                "app_id" => $config["appid"],
                "app_trans_id" => $app_trans_id,
                "mac" => hash_hmac("sha256", $data, $config["key1"])
            ];

            $response = Http::asForm()->post($config["endpoint"], $params);

            if ($response->successful()) {
                $result = $response->json();
                if ($result['return_code'] == 1) {
                    // Update transaction status to successful
                    DB::table('zalopay_transactions')
                        ->where('app_trans_id', $app_trans_id)
                        ->update(['status' => 1]);
                    $updatedTransactions[] = $app_trans_id;
                } else {
                    // Log or handle failed updates
                    $this->error("Failed to update transaction $app_trans_id: " . $result['return_message']);
                }
            } else {
                // Log or handle failed API call
                $this->error("API call failed for transaction $app_trans_id: " . $response->body());
            }
        }

        if (!empty($updatedTransactions)) {
            $this->info('Transaction statuses updated successfully for the following transactions:');
            foreach ($updatedTransactions as $transaction) {
                $this->info(" - $transaction");
            }
        } else {
            $this->info('No transactions were updated.');
        }

        return 0;
    }
}
