<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeRoleUser extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $roleName;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $roleName)
    {
        $this->user = $user;
        $this->roleName = $roleName;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ntd_change_role'], ['company' => $this->user->company->name, 'roleName' => $this->roleName]);
        $contentEn = Common::transLang($arrLangEn['ntd_change_role'], ['company' => $this->user->company->name, 'roleName' => $this->roleName]);

        return [
            'content_vi' => $contentVi,
            'content_en' => $contentEn,
        ];
    }
}
