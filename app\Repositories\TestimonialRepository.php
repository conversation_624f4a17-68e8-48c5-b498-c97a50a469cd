<?php

namespace App\Repositories;

use App\Models\Testimonial;

class TestimonialRepository extends BaseRepository
{

    const MODEL = Testimonial::class;

    public function getListTestimonial($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

        $query->orderBy($order_by, $sort);

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('name', 'like', '%' . $params['search'] . '%');
        }

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    function getListByType($type){

        return $this->query()
            ->where('type', $type)
            ->where('is_active', 1)
            ->get();
    }
}
