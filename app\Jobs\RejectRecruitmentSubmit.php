<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuyBook;
use App\Models\WareHouseCvSellingBuyHistoryStatus;
use App\Notifications\CandidateRejectRecruitmentAdmin;
use App\Notifications\CandidateRejectRecruitmentAdminSubmit;
use App\Notifications\CandidateRejectRecruitmentEmployer;
use App\Notifications\CandidateRejectRecruitmentEmployerSubmit;
use App\Notifications\CandidateRejectRecruitmentRec;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class RejectRecruitmentSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    /**
     * @return void
     * Sau 48h mà Ứng viên ko xác nhận, thì Hoàn tiền trả NTD
     * HT NTD
     */
    public function handle()
    {
        return false; // Tạm thời bỏ
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
        $submitCvHistoryPaymentRepository = resolve(SubmitCvHistoryPaymentRepository::class);
        $submitCvHistoryStatusRepository = resolve(SubmitCvHistoryStatusRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        //sau 48h mà ứng viên ko xac nhan thi chuyển trạng thái từ chối
        //1 => 'Waiting candidate confirm',
        if (!empty($submitCv)) {
            if ($submitCv->status == config('constant.status_recruitment_revert.Waitingcandidateconfirm')) {
                //interview
                if($submitCv->job->bonus_type == 'interview'){
                    Log::info('interview $submitCv 222222: ', [
                        'xxxx: ' => $submitCv
                    ]);
                    //ghi log hoan tien
                    // $submitCvHistoryPaymentRepository->create([
                    //     'user_id'      => $submitCv->employer->id,
                    //     'submit_cv_id' => $submitCv->id,
                    //     'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                    //     'percent'      => 100,                                                           //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    //     'type_of_sale' => $submitCv->job->bonus_type,
                    //     'amount'       => $submitCv->bonus_point,
                    //     'balance'      => $submitCv->employer->wallet->amount + $submitCv->bonus_point,
                    //     'status'       => 0
                    // ]);
                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $submitCv->status = config('constant.status_recruitment_revert.CandidateCancelApply');
                    // $submitCv->status_payment = 3; //3 => 'Hoàn tiền'
                    $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                    $submitCv->save();
                    //Log thay đổi trang thái
                    $historyStatus = $submitCvHistoryStatusRepository->create([
                        'user_id'            => $submitCv->employer->id,
                        'submit_cvs_id'      => $submitCv->id,
                        'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name' => $submitCv->warehouseCv->candidate_name,
                        'type'               => 'rec',
                        'authority'          => $submitCv->authorize,
                    ]);
                    // Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->bonus_point;
                    // $submitCv->employer->wallet->save();
                    Log::info('interview $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $submitCv
                    ]);
                }
                //onboard
                if($submitCv->job->bonus_type == 'onboard'){
                    //2 => 'Candidate Cancel Apply',
                    # dang sai cho nay
                    $submitCvHistoryStatusData = $submitCvHistoryStatusRepository->finByTypeStatus($submitCv->id, 0, 0);//type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    Log::info('onboard $wareHouseCvSellingBuy 222222: ', [
                        'xxxx: ' => $submitCvHistoryStatusData
                    ]);
                    //hoàn bao nhiêu point
                    // $point = 0;
                    // if($submitCvHistoryStatusData){
                    //     foreach ($submitCvHistoryStatusData as $key => $value){
                    //         //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                    //         $value->status = 1;
                    //         $value->save();
                    //         //ghi log hoan tien
                    //         $submitCvHistoryPaymentRepository->create([
                    //             'user_id'      => $submitCv->employer->id,
                    //             'submit_cv_id' => $submitCv->id,
                    //             'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                    //             'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    //             'type_of_sale' => $submitCv->job->bonus_type,
                    //             'amount'       => $value->amount,
                    //             'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                    //             'status'       => 0
                    //         ]);
                    //         $point += $value->amount;
                    //     }
                    // }

                    //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                    $submitCv->status = config('constant.status_recruitment_revert.CandidateCancelApply');
                    // $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                    $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                    $submitCv->save();
                    //Log thay đổi trang thái
                    $historyStatus = $submitCvHistoryStatusRepository->create([
                        'user_id'            => $submitCv->employer->id,
                        'submit_cvs_id'      => $submitCv->id,
                        'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'), //2 => 'Candidate Cancel Apply',
                        'candidate_name' => $submitCv->warehouseCv->candidate_name,
                        'type'               => 'rec',
                        'authority'          => $submitCv->authorize,
                    ]);
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
                    // $submitCv->employer->wallet->save();
                    Log::info('onboard $wareHouseCvSellingBuy 3333333355555: ', [
                        'xxxx: ' => $submitCv
                    ]);
                }

                //gui mail cho ctv
                $submitCv->rec->notify(new CandidateRejectRecruitmentRec($submitCv)); // tam comment de lam chuc nang

                //gui mail cho ntd
                $submitCv->employer->notify(new CandidateRejectRecruitmentEmployerSubmit($submitCv)); // tam comment de lam chuc nang

                //gui mail cho admin
                Mail::to(config('settings.global.email_admin'))->send(new CandidateRejectRecruitmentAdminSubmit($submitCv)); // tam comment de lam chuc nang
            }
        }
    }


}