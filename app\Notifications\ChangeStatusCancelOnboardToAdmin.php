<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToAdmin extends Mailable
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function content(): Content
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $link = route('luot-ban.show',['luot_ban' => $this->wareHouseCvSellingBuy->id]);
        return new Content(
            view: 'email.changeStatusCancelOnboardToAdmin',
            with: [
                'candidateName' => $candidateName,
                'position' => $position,
                'companyName' => $companyName,
                'link' => $link,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = '';
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $message->subject('[Ủy quyền] Thông báo ứng viên '.$candidateName.' đã “Cancel Onboard” vị trí '.$position);
        return $this;
    }


}

