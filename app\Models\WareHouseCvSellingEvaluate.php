<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class WareHouseCvSellingEvaluate extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'warehouse_cv_selling_evaluates';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = ['created_at_value'];

    public function wareHouseCvSelling()
    {
        return $this->belongsTo(WareHouseCvSelling::class, 'warehouse_cv_selling_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }
}
