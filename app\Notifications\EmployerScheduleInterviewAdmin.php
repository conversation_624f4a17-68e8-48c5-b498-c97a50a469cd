<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerScheduleInterviewAdmin extends Mailable
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $rec;
    protected $wareHouseCvSellingBuyBook;

    public function __construct($wareHouseCvSellingBuy,$rec,$wareHouseCvSellingBuyBook)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->rec = $rec;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $recName = $this->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $timeInterview = $this->wareHouseCvSellingBuyBook->date_time_book_format;
        $address = $this->wareHouseCvSellingBuyBook->address;
        $type_of_sale = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $linkAdminDiscuss = route('luot-ban.show', $this->wareHouseCvSellingBuy->id) . '?tab=thaoluan';
        $linkAdminPreview = route('luot-ban.show', $this->wareHouseCvSellingBuy->id);

        return new Content(
            view: 'email.employerScheduleInterviewAdmin',
            with: [
                'recName'          => $recName,
                'candidateName'    => $candidateName,
                'companyName'      => $companyName,
                'position'         => $position,
                'type'             => $type,
                'timeInterview'    => $timeInterview,
                'address'          => $address,
                'linkAdminDiscuss' => $linkAdminDiscuss,
                'linkAdminPreview' => $linkAdminPreview,
                'type_of_sale'     => $type_of_sale,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $message->subject('[Ủy Quyền] Thông báo lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);
        return $this;
    }

}
