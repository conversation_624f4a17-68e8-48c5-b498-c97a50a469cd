<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CandidateRejectRecruitmentEmployerSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->submitCv->employer->name;  //name ntd
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->wareHouseCvSelling->type_of_sale;
        $candidateJobTitle = '';
        if ($type == 'cv'){
            $candidateJobTitle = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $candidateJobTitle = $this->submitCv->job->name;
        }
        $money = $this->submitCv->bonus;
        if ($type == 'onboard') {
            $money = 0.1 * $this->submitCv->submitCv->bonus;
        }
        $linkWallet = route('employer-wallet');
        // $linkMarket = route('market-cv');

       return (new MailMessage)
            ->view('email.ungvien_tuchoi_ungtuyen_ntd_submit', [
                'name'              => $name,
                'candidateName'     => $candidateName,
                'candidateJobTitle' => $candidateJobTitle,
                'money'             => Common::formatNumber($money),
                'linkWallet'        => $linkWallet,
                // 'linkMarket'        => $linkMarket,
            ])
            ->subject('[Recland] Ứng viên ' . $candidateName . ' đã từ chối lời mời ứng tuyển vị trí ' . $candidateJobTitle);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [

        ];
    }
}
