<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ReportGovRequest;
use App\Services\Admin\JobService;
use App\Services\Admin\PermissionService;
use App\Services\Admin\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Shetabit\Visitor\Models\Visit;

class ReportGovController extends Controller
{
    protected $userService;
    protected $jobService;

    public function __construct(UserService $userService, JobService $jobService)
    {
        $this->userService = $userService;
        $this->jobService = $jobService;
    }

    public function report()
    {
        // $validation = $this->validateData();
        // if ($validation) {
        //     return response()->json($validation, 422);
        // }
        // if ($this->attemptLogin()) {
        //     if (!$this->checkPermission()) {
        //         return response()->json(['message' => 'Không có quyền được truy cập'], 401);
        //     }
        $data = [
            'soLuongTruyCap' => Visit::when(\request('star_date'), function ($q) {
                $q->where('created_at', '>=', Carbon::parse(\request('star_date'))->format('Y-m-d'));
            })->when(\request('end_date'), function ($q) {
                $q->where('created_at', '<', Carbon::parse(\request('end_date'))->addDay()->format('Y-m-d'));
            })->distinct('ip')->count('ip'),
            'soNguoiBan' => $this->userService->indexService(array_merge(['type' => config('constant.role.rec'), 'is_active' => ACTIVE], $this->formatDate()))->count(),
            'soNguoiBanMoi' => $this->userService->indexService(['type' => config('constant.role.rec'), 'cvt_new' => Carbon::now()->format('Y-m-d')])->count(),
            'tongSoSanPham' => $this->jobService->indexService(array_merge(['is_active' => ACTIVE], $this->formatDate()))->count(),
            'soSanPhamMoi' => $this->jobService->indexService(['job_new' => Carbon::now()->format('Y-m-d')])->count(),
            'soLuongGiaoDich' => 0,
            'tongSoDonHangThanhCong' => 0,
            'tongSoDonHangKhongThanhCong' => 0,
            'tongGiaTriGiaoDich' => 0,
        ];
        return response()->json($data, 200);
        // }
        // return response()->json(['message' => 'Tài khoản mật khẩu không chính xác'], 401);

    }

    function validateData()
    {
        $response = null;
        $data = [
            'username' => 'required',
            'password' => 'required',
            'star_date' => ['nullable', 'date', 'date_format:Y-m-d'],
            'end_date' => ['nullable', 'date', 'date_format:Y-m-d'],
        ];

        $customMessages = [
            'required' => 'Trường :attribute là bắt buộc.',
            'date' => 'The :attribute chưa đúng định dạng.',
            'date_format' => 'Trường :attribute phải là định dạng :format.'
        ];
        $validation = \Validator::make(\request()->all(), $data, $customMessages);

        if ($validation->fails()) {
            return $validation->messages();
        }
        return $response;
    }

    public function attemptLogin()
    {
        if (Auth::guard('admin')->attempt(['name' => \request('username'), 'password' => \request('password')])) {
            return true;
        } else {
            return false;
        }
    }

    public function formatDate()
    {
        $arr = [];
        if (\request('start_date')) {
            $start = Carbon::parse(\request('start_date'))->format('Y-m-d');
            $arr['start_date'] = $start;
        }

        if (\request('end_date')) {
            $end = Carbon::parse(\request('end_date'))->format('Y-m-d');
            $arr['end_date'] = $end;
        }

        return $arr;
    }

    public function checkPermission()
    {
        return PermissionService::checkPermission(\request()->route()->getName());
    }
}
