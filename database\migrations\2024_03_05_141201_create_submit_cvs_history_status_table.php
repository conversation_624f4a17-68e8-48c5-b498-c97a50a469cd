<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_history_status', function (Blueprint $table) {
            $table->id();
            $table->integer('submit_cvs_id');
            // $table->string('candidate_name')->nullable()->comment('Ứng viên đồng ý hoặc từ chối');
            $table->integer('user_id')->nullable()->comment('NTD/CTV/ADMIN');
            $table->enum('type', ['admin', 'rec', 'employer'])->nullable()
                ->comment('admin, rec đại diện cho CTV, employer cho nhà tuyển dụng');
            $table->text('comment')->nullable();
            $table->integer('status_recruitment');
            $table->integer('authority')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_history_status');
    }
};
