# Tài liệu Tổng quan Dự án RecLand

## 1. G<PERSON><PERSON>i thiệu

RecLand là một nền tảng tuyển dụng trực tuyến sử dụng mô hình mạng lưới cộng tác viên (Collaborator Network). Hệ thống kết nối nhà tuyển dụng với ứng viên thông qua các cộng tác viên - những người có khả năng giới thiệu ứng viên phù hợp cho các vị trí tuyển dụng.

## 2. <PERSON><PERSON><PERSON> tiêu của Dự án

- **Tối ưu hóa quá trình tuyển dụng**: Gi<PERSON>m thời gian và chi phí tuyển dụng cho các doanh nghiệp
- **Tận dụng mạng lưới quan hệ**: <PERSON><PERSON> thá<PERSON> hiệu quả mạng lưới quan hệ của các cộng tác viên
- **<PERSON><PERSON><PERSON> cơ hội thu nhập**: <PERSON><PERSON> lại thu nhập cho cộng tác viên khi giới thiệu ứng viên thành công
- **Nâng cao chất lượng tuyển dụng**: Ứng viên được giới thiệu thường có độ tin cậy cao hơn

## 3. Các Bên Liên quan (Stakeholders)

### 3.1. Nhà tuyển dụng (Employer)
- Các công ty, doanh nghiệp có nhu cầu tuyển dụng nhân sự
- Đăng tin tuyển dụng và quản lý quy trình tuyển dụng
- Mua CV từ kho CV hoặc nhận CV từ cộng tác viên giới thiệu

### 3.2. Cộng tác viên (Collaborator/Rec)
- Cá nhân có mạng lưới quan hệ và khả năng giới thiệu ứng viên
- Giới thiệu ứng viên cho các vị trí tuyển dụng
- Nhận hoa hồng khi ứng viên được tuyển dụng thành công

### 3.3. Ứng viên (Candidate)
- Người tìm kiếm việc làm
- Được cộng tác viên giới thiệu cho các vị trí phù hợp
- CV có thể được lưu trong kho CV để bán cho nhà tuyển dụng

### 3.4. Quản trị viên (Admin)
- Quản lý toàn bộ hệ thống
- Giám sát hoạt động và xử lý các vấn đề phát sinh
- Quản lý thanh toán và hoa hồng

## 4. Các Module Chính

### 4.1. Module Quản lý Người dùng
- Đăng ký/Đăng nhập cho các loại người dùng
- Quản lý hồ sơ và thông tin cá nhân
- Phân quyền và xác thực

### 4.2. Module Tuyển dụng
- Đăng tin tuyển dụng
- Tìm kiếm và lọc việc làm
- Quản lý quy trình tuyển dụng

### 4.3. Module Giới thiệu Ứng viên
- Cộng tác viên giới thiệu ứng viên
- Theo dõi trạng thái ứng tuyển
- Quản lý hoa hồng

### 4.4. Module Kho CV
- Lưu trữ và quản lý CV
- Mua bán CV giữa cộng tác viên và nhà tuyển dụng
- Đánh giá chất lượng CV

### 4.5. Module Thanh toán
- Ví điện tử cho người dùng
- Nạp tiền và rút tiền
- Quản lý giao dịch và hoa hồng

### 4.6. Module Báo cáo & Thống kê
- Báo cáo hoạt động tuyển dụng
- Thống kê doanh thu
- Phân tích hiệu quả

## 5. Công nghệ Sử dụng

### 5.1. Backend
- **Framework**: Laravel (PHP)
- **Database**: MySQL
- **Cache**: Redis
- **Queue**: Laravel Queue

### 5.2. Frontend
- **Template Engine**: Blade (Laravel)
- **JavaScript**: jQuery, Alpine.js
- **CSS Framework**: Bootstrap, Tailwind CSS

### 5.3. Tích hợp
- **Payment Gateway**: ZaloPay
- **Email Service**: SMTP
- **SMS Service**: Đang cập nhật
- **Storage**: Local/Cloud Storage

## 6. Quy trình Nghiệp vụ Chính

### 6.1. Quy trình Đăng tin Tuyển dụng
1. Nhà tuyển dụng đăng nhập hệ thống
2. Tạo tin tuyển dụng mới
3. Cài đặt phí hoa hồng cho cộng tác viên
4. Duyệt và đăng tin

### 6.2. Quy trình Giới thiệu Ứng viên
1. Cộng tác viên tìm kiếm việc làm phù hợp
2. Giới thiệu ứng viên (upload CV hoặc chọn từ kho)
3. Nhà tuyển dụng xem xét hồ sơ
4. Cập nhật trạng thái tuyển dụng
5. Thanh toán hoa hồng khi tuyển dụng thành công

### 6.3. Quy trình Mua bán CV
1. Cộng tác viên đăng CV lên kho
2. Đặt giá bán cho CV
3. Nhà tuyển dụng tìm kiếm và mua CV
4. Thanh toán và chuyển thông tin CV

## 7. Yêu cầu Phi chức năng

### 7.1. Hiệu năng
- Hỗ trợ đồng thời 1000+ người dùng
- Thời gian phản hồi < 2 giây cho các thao tác thông thường
- Khả năng mở rộng theo chiều ngang

### 7.2. Bảo mật
- Mã hóa thông tin nhạy cảm
- Xác thực 2 lớp cho tài khoản quan trọng
- Phân quyền chi tiết theo vai trò

### 7.3. Khả dụng
- Uptime >= 99.5%
- Backup dữ liệu hàng ngày
- Khôi phục dữ liệu trong vòng 4 giờ

### 7.4. Khả năng Sử dụng
- Giao diện thân thiện, dễ sử dụng
- Hỗ trợ đa ngôn ngữ (Tiếng Việt, Tiếng Anh)
- Responsive trên các thiết bị di động

## 8. Rủi ro và Thách thức

### 8.1. Rủi ro Kỹ thuật
- Tấn công bảo mật và rò rỉ dữ liệu
- Sự cố hệ thống và mất dữ liệu
- Khả năng mở rộng khi người dùng tăng cao

### 8.2. Rủi ro Nghiệp vụ
- Gian lận trong giới thiệu ứng viên
- Tranh chấp về hoa hồng
- Chất lượng ứng viên không đảm bảo

### 8.3. Biện pháp Giảm thiểu
- Xây dựng hệ thống xác thực và kiểm tra chặt chẽ
- Quy trình giải quyết tranh chấp rõ ràng
- Hệ thống đánh giá và phản hồi từ các bên

## 9. Kế hoạch Phát triển

### Phase 1: MVP (Đã hoàn thành)
- Chức năng cơ bản cho 3 loại người dùng
- Quy trình giới thiệu ứng viên
- Thanh toán cơ bản

### Phase 2: Mở rộng (Hiện tại)
- Kho CV và mua bán CV
- Ví điện tử và thanh toán nâng cao
- Báo cáo và thống kê chi tiết

### Phase 3: Tối ưu (Tương lai)
- AI matching ứng viên - việc làm
- Mobile app
- Tích hợp với các nền tảng khác
