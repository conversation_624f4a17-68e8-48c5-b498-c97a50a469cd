<?php

namespace App\Services\Frontend;

use App\Jobs\OutOfDateBookInterviewSubmitCv;
use App\Jobs\PassInterviewSubmit;
use App\Jobs\RejectBookExpire;
use App\Jobs\RejectBookExpireSubmit;
use App\Jobs\SendemailBook;
use App\Jobs\SendemailBookSubmit;
use App\Models\SubmitCvHistoryStatus;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ChangeStatusCancelInterviewToAdminSubmit;
use App\Notifications\ChangeStatusCancelInterviewToEmployerSubmit;
use App\Notifications\ChangeStatusCancelInterviewToRecSubmit;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmailConfirmInterViewSubmit;
use App\Notifications\EmailRejectInterViewSubmit;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewAdminSubmit;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\EmployerScheduleInterviewRecSubmit;
use App\Notifications\RecRefuseComplain;
use App\Repositories\SubmitCvBookRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class SubmitCvBookService
{

    protected $submitCvBookRepository;
    protected $submitCvRepository;
    protected $submitCvHistoryStatusRepository;

    public function __construct(
        SubmitCvBookRepository $submitCvBookRepository,
        SubmitCvRepository $submitCvRepository,
        SubmitCvHistoryStatusRepository $submitCvHistoryStatusRepository
    ) {
        $this->submitCvBookRepository = $submitCvBookRepository;
        $this->submitCvRepository = $submitCvRepository;
        $this->submitCvHistoryStatusRepository = $submitCvHistoryStatusRepository;
    }

    /**
     * @param $data
     * @return mixed
     * @throws \Exception
     * NTD đặt lich phỏng vấn
     * HT NTD
     */
    public function scheduleInterview($data)
    {
        $submitCv = $this->submitCvRepository->find($data['submit_id']);

        if ($submitCv->bonus_type == 'interview' || $submitCv->bonus_type == 'onboard') {
            if (!in_array($submitCv->status, [3, 5])) {
                //3 => 'Waiting setup interview',
                //5 => 'Reject Interview schedule',
                throw new \Exception('Không đủ điều kiện đặt lịch phỏng vấn');
            }
            $user = auth('client')->user();
            $data = [
                'ntd_id'        => $user->id,
                'address'       => $data['address'],
                'phone'         => $data['phone'],
                'name'          => $data['name'],
                'submit_cvs_id' => $data['submit_id'],
                'date_book'     => Carbon::createFromFormat('d/m/Y', $data['date']),
                'time_book'     => $data['hour'] . ':' . $data['minute'],
            ];
            $book = $this->submitCvBookRepository->create($data);
            $book->load('rec');
            $submitCv->update([
                'status' => config('constant.status_recruitment_revert.Waitingconfirmcalendar') //4 => 'Waiting confirm calendar',
            ]);

            if ($submitCv->authorize) {
                Mail::to(config('settings.global.email_admin'))->send(new EmployerScheduleInterviewAdminSubmit($submitCv, $submitCv->rec, $book));
            } else {
                $submitCv->rec->notify(new EmployerScheduleInterviewRecSubmit($submitCv, $submitCv->rec, $book));
            }
            // - sau 48h mà CTV ko xác nhận hay Hủy thì cũng update về  status_recruitment = 5 => 'Reject Interview schedule', và warehouse_cv_selling_buy_books.status = 2
            RejectBookExpireSubmit::dispatch($book, $user)->delay(now()->addMinutes(48 * 60));
            //sau 1 ngay Lich phong vấn thi gui email cho NTD
            $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book)
                ->startOfDay()
                ->addMinutes($book->book_time_minute + 24 * 60);
            SendemailBookSubmit::dispatch($submitCv, $user, $book)->delay($timeInterval);
        }

        //log
        $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
        // dd($book);
        return $book;
    }
    public function getBySubmitCvId($submit_id)
    {
        return $this->submitCvBookRepository->getBySubmitCvId($submit_id);
    }


    public function updateScheduleInterview($id, $statusRecruitment, $user = null)
    {
        $submitCvHistoryPaymentRepository = app(SubmitCvHistoryPaymentRepository::class);
        $SubmitCvBook = $this->submitCvBookRepository->find($id);
        $submitCv = $SubmitCvBook->submitCv;

        if ($submitCv->bonus_type == 'interview' || $submitCv->bonus_type == 'onboard') {
            if ($SubmitCvBook->status > 0) {
                throw new \Exception('Lịch phỏng vấn đã được xử lý');
            }
            if ($user == null) {
                $user = auth('client')->user();
            }
            //Tu choi phong van 5 => 'Reject Interview schedule',
            if ($statusRecruitment == config('constant.status_recruitment_revert.RejectInterviewschedule')) {
                //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
                $SubmitCvBook->update([
                    'status' => 2
                ]);
                $employer = $SubmitCvBook->employer;
                $employer->notify(new EmailRejectInterViewSubmit($employer, $SubmitCvBook, $submitCv));

                $countBookReject = $this->submitCvBookRepository->countBookReject($SubmitCvBook->ntd_id, $submitCv->id);
                if ($countBookReject >= 3) {
                    $statusRecruitment = config('constant.status_recruitment_revert.CandidateCancelApply'); //9 => 'Candidate Cancel Interview',
                    // Từ chối quá 3 lần
                    // Trả tiền Cọc
                    $bonus = $submitCv->hoanTienChoNtd('Từ chối quá 3 lần lịch phỏng vấn', 'update_status_recruitment');
                    $submitCv->employer->notify(new ChangeStatusCancelInterviewToEmployerSubmit($submitCv, $bonus));
                    if ($submitCv->authorize == 1) {
                        Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdminSubmit($submitCv, $bonus));
                    } else {
                        $submitCv->rec->notify(new ChangeStatusCancelInterviewToRecSubmit($submitCv, $bonus));
                    }
                } else {
                    OutOfDateBookInterviewSubmitCv::dispatch($submitCv, auth()->user())->delay(now()->addMinutes(7 * 24 * 60));
                }
                $submitCv->update([
                    'status' => $statusRecruitment,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
            }
            //dong y phong van 7 => 'Waiting Interview',
            if ($statusRecruitment == config('constant.status_recruitment_revert.WaitingInterview')) {
                //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
                $SubmitCvBook->update([
                    'status' => 1
                ]);
                $employer = $SubmitCvBook->employer;
                $employer->notify(new EmailConfirmInterViewSubmit($employer, $SubmitCvBook, $submitCv));
                $submitCv->update([
                    'status' => $statusRecruitment,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
                //Sau 120h sau khi đổi sang status "Waiting Interview" NTD k cập nhật status sẽ tự động chuyển sang status Pass Interview
                //sửa logic thành:
                //Sau 7 ngày sau khi đổi sang status “Waiting interview” , NTD ko cập nhật trạng thái sẽ tự động chuyển về  “Pass Interview”
                //thời gian 7 ngày bắt đầu tính từ thời điểm phỏng vấn"
                //                PassInterview::dispatch($wareHouseCvSellingBuy->id,$user)->delay(now()->addMinutes(7 * 24 * 60));
                //DungDQ sua tiep vao day ,tinh lai time
                $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $SubmitCvBook->date_book)
                    ->startOfDay()
                    ->addMinutes($SubmitCvBook->book_time_minute + 7 * 24 * 60);
                if ($submitCv->bonus_type == 'interview') PassInterviewSubmit::dispatch($submitCv->id, $user)->delay($timeInterval);
            }
            //dong y phong van 7 => 'Waiting Interview',
            if ($statusRecruitment == config('constant.status_recruitment_revert.CandidateCancelApply')) {
                // Từ chối ứng tuyển
                $SubmitCvBook->update([
                    'status' => 1
                ]);
                // $employer = $SubmitCvBook->employer;
                // $employer->notify(new EmailConfirmInterViewSubmit($employer, $SubmitCvBook, $submitCv));
                $submitCv->update([
                    'status' => $statusRecruitment,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
            }
            $submitCvHistoryStatusRepository = resolve(SubmitCvHistoryStatusRepository::class);
            $historyStatus = $submitCvHistoryStatusRepository->create([
                'user_id'            => $submitCv->employer->id,
                'submit_cvs_id'      => $submitCv->id,
                'status_recruitment' => $statusRecruitment,
                'candidate_name'     => $submitCv->warehouseCv->candidate_name,
                'type'               => 'rec',
                'authority'          => $submitCv->authorize,
            ]);
        }

        return $SubmitCvBook;
    }
}
