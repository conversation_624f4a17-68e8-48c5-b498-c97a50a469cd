<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerIntroduceCandidate extends Notification implements ShouldQueue
{
    use Queueable;

    protected $job;
    protected $warehouseCv;
    protected $user;
    protected $employer;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($job, $warehouseCv, $user, $employer)
    {
        $this->job = $job;
        $this->warehouseCv = $warehouseCv;
        $this->user = $user;
        $this->employer = $employer;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $name = $this->employer->name;
        $candidate = $this->warehouseCv->candidate_name;
        $jobName = $this->job->name;
        $rec = $this->user->name;

        return (new MailMessage)
            ->view('email.employerIntroduceCandidate', [
                'name'      => $name,
                'candidate' => $candidate,// . $this->warehouseCv->candidate_mobile . $this->warehouseCv->candidate_email,
                'jobName'   => $jobName,
                'rec'       => $rec,
//                    'content' => $content
            ])
            ->subject('[HRI RECLAND] [THÔNG BÁO ỨNG VIÊN ĐÃ APPLY VÀO JOB]');

    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $candidate = $this->warehouseCv->candidate_name;
        $job = $this->job->name;
        $thumbnail = @$this->user->company->path_logo;

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ntd_dacoungvienduocgioithieu'], ['candidate' => $candidate, 'job' => $job]);
        $contentEn = Common::transLang($arrLangEn['ntd_dacoungvienduocgioithieu'], ['candidate' => $candidate, 'job' => $job]);

        return [
            'job_id'          => $this->job->id,
            'warehouse_cv_id' => $this->warehouseCv->id,
            'user_id'         => $this->user->id,
            'content_vi'      => $contentVi,
            'content_en'      => $contentEn,
            'thumbnail'       => $thumbnail,
        ];
    }
}
