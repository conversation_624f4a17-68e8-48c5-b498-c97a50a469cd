<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class BannerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
            {
                return [
                    'position' => 'required',
                    'type' => 'required',
                    'img_vn' => 'required|mimes:jpeg,jpg,png|max:5120',
                    'img_en' => 'required|mimes:jpeg,jpg,png|max:5120',
                ];
            }
            case 'PUT':
            {
                return [
                    'position' => 'required',
                    'type' => 'required',
                    'img_vn' => 'mimes:jpeg,jpg,png|max:5120',
                    'img_en' => 'mimes:jpeg,jpg,png|max:5120',
                ];
            }
            default:
                break;
        }
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'mimes' => __('message.mimes'),
            'max' => __('message.file_max'),
        ];
    }
}
