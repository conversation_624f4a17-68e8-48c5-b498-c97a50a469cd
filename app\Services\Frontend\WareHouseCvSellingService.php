<?php

namespace App\Services\Frontend;

use App\Models\JobTop;
use App\Models\SkillMain;
use App\Notifications\RecSellingCvAuthorityAdmin;
use App\Repositories\CompanyRepository;
use App\Repositories\JobRepository;
use App\Repositories\JobTopRepository;
use App\Repositories\LevelByJobTopRepository;
use App\Repositories\LevelBySkillMainRepository;
use App\Repositories\SkillMainRepository;
use App\Repositories\WareHouseCvRepository;
use App\Repositories\WareHouseCvSellingRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingService
{
    protected $wareHouseCvSellingRepository;
    protected $skillMainRepository;
    protected $levelBySkillMainRepository;
    protected $jobTopRepository;
    protected $levelByJobTopRepository;
    protected $wareHouseCvRepository;
    protected $companyRepository;
    protected $jobRepository;

    public function __construct(
        WareHouseCvSellingRepository $wareHouseCvSellingRepository,
        SkillMainRepository $skillMainRepository,
        LevelBySkillMainRepository $levelBySkillMainRepository,
        JobTopRepository $jobTopRepository,
        LevelByJobTopRepository $levelByJobTopRepository,
        WareHouseCvRepository  $wareHouseCvRepository,
        CompanyRepository  $companyRepository,
        JobRepository  $jobRepository,
    ) {
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
        $this->skillMainRepository = $skillMainRepository;
        $this->levelBySkillMainRepository = $levelBySkillMainRepository;
        $this->jobTopRepository = $jobTopRepository;
        $this->levelByJobTopRepository = $levelByJobTopRepository;
        $this->wareHouseCvRepository = $wareHouseCvRepository;
        $this->companyRepository = $companyRepository;
        $this->jobRepository = $jobRepository;
    }

    public function getCvSelling($params)
    {
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingRepository->getCvSellingByUser($userId, $params, true);
    }

    public function createCvSelling($params)
    {
        $params['type'] = $params['type_of_sale'];
        $check = $this->wareHouseCvSellingRepository->findCvWithType($params);
        if ($check) {
            throw new \Exception('cv selling exits');
        }
        $wareHouseCv = $this->wareHouseCvRepository->find($params['warehouse_cv_id']);
        $data = [
            'warehouse_cv_id'           =>  $params['warehouse_cv_id'],
            'level'                     =>  $params['level'],
            'type_of_sale'              =>  $params['type_of_sale'],
            'candidate_salary_expect'   =>  $params['candidate_salary_expect'],
            'price'                     =>  $params['price'],
            'exclude_company'           =>  isset($params['company_id']) ? implode(",", $params['company_id']) : null,
            'authority'                 =>  !empty($params['is_authority']) ? $params['is_authority'] : 0,
            'skill'                     =>  $params['skill'],
            'candidate_mobile'          =>  $wareHouseCv['candidate_mobile'],
            'candidate_email'           =>  $wareHouseCv['candidate_email'],
            'user_id'                   =>  data_get($params, 'user_id', auth('client')->user()->id),
        ];
        //uy quyen thi status = 1
        if ($data['authority'] == 1) {
            //đợi admin xác nhận ủy quyền thì mới cho lên chợ cv
            $data['status'] = 2;
        }
        //Tinh
        $point = 0;
        if ($params['type_of_sale'] == 'cv') {
            $point = (int) round($params['price'] * 2 / 1000);
        } elseif ($params['type_of_sale'] == 'interview') {
            $point = (int) round($params['price'] * 3 / 1000);
        } elseif ($params['type_of_sale'] == 'onboard') {
            if ($params['candidate_salary_expect'] < 15000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.2 / 1000);
            }

            if ($params['candidate_salary_expect'] >= 15000000 && $params['candidate_salary_expect'] < 35000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.4 / 1000);
            }

            if ($params['candidate_salary_expect'] >= 35000000 && $params['candidate_salary_expect'] < 45000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.6 / 1000);
            }

            if ($params['candidate_salary_expect'] >= 45000000) {
                $point = (int) round($params['candidate_salary_expect'] * 2 / 1000);
            }
        }

        // $data['point'] = $point;
        $data['point'] = $point * 1000;
        $cvSelling = $this->wareHouseCvSellingRepository->create($data);

        if ($cvSelling->authority == 1) {
            //send mail admin
            Mail::to(config('settings.global.email_admin'))->send(new RecSellingCvAuthorityAdmin($cvSelling));
        }

        return $cvSelling;
    }

    public function cancelCvSelling($id, $userId)
    {
        try {
            $cvExist = $this->wareHouseCvSellingRepository->findCvSellingByUser($id, $userId);
            if ($cvExist) {
                //soft delete
                //$isSoftDelete = $this->wareHouseCvSellingRepository->delete($id);
                //0 dang ban, 1 huy dang ban
                $cvExist->status = 1;
                return $cvExist->save();
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getSkillMainIT($params)
    {
        $career = isset($params['career']) ? (string) $params['career'] : '';
        $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
        if ($isIT) {
            return $this->skillMainRepository->getSkill($params);
        } else {
            return $this->jobTopRepository->getSkill($params);
        }
    }

    public function getLevel($params)
    {
        $isIT = isset($params['is_it']) && $params['is_it'] == 'true' ? true : false;
        if ($isIT) {
            return $this->levelBySkillMainRepository->getByGroup($params);
        } else {
            return $this->levelByJobTopRepository->getByGroup($params);
        }
    }
    public function getLevelByCareer($params)
    {
        $career_id = isset($params['career_id']) ? $params['career_id'] : '';
        $skill_id = isset($params['skill_id']) ? $params['skill_id'] : '';
        $isIT = (in_array($career_id, config('job.it_career'))) ? true : false;
        if ($isIT) {
            $skill = SkillMain::where('id', $skill_id)->first();
            if (!$skill) {
                return [];
            }
            $params = ['group' => $skill->group];
            return $this->levelBySkillMainRepository->getByGroup($params);
        } else {
            $skill = JobTop::where('id', $skill_id)->first();
            $params = ['group' => $skill['group'] ?? ''];
            return $this->levelByJobTopRepository->getByGroup($params);
        }
    }

    public function getPrice($params)
    {
        $isIT = isset($params['is_it']) && $params['is_it'] == 'true' ? true : false;
        if ($isIT) {
            return $this->levelBySkillMainRepository->find($params['level']);
        } else {
            return $this->levelByJobTopRepository->find($params['level']);
        }
    }

    public function checkCvWithType($params)
    {
        if (!isset($params['warehouse_cv_id'])) {
            return false;
        }

        if (!isset($params['type'])) {
            return false;
        }
        return $this->wareHouseCvSellingRepository->findCvWithType($params);
    }

    public function getDetailCvSelling($id)
    {
        return $this->wareHouseCvSellingRepository->find($id);
    }

    public function getCvSeeMore($email, $mobile, $id)
    {
        return $this->wareHouseCvSellingRepository->getCvSeeMore($email, $mobile, $id);
    }

    public function getCvAnother($skill, $type, $id)
    {
        return $this->wareHouseCvSellingRepository->getCvAnother($skill, $type, $id);
    }

    public function findDetailCvSelling($id)
    {
        $data = $this->wareHouseCvSellingRepository->findWithId($id);

        $user = auth('client')->user();
        if ($data['user_id'] != $user->id) {
            throw new \Exception('Bạn không có quyền xem CV này');
        }
        $data['company'] = [];
        if ($data['exclude_company']) {
            foreach (explode(',', $data['exclude_company']) as $companyId) {
                $company = $this->companyRepository->find($companyId);
                array_push($data['company'], $company->name);
            }
        }

        $data['level_name'] = '';
        if (isset($data['ware_house_cv']['career'])) {
            $career = isset($data['ware_house_cv']['career']) ? (string) $data['ware_house_cv']['career'] : '';
            $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
            if ($isIT) {
                $level = $this->levelBySkillMainRepository->find($data['level']);
            } else {
                $level =  $this->levelByJobTopRepository->find($data['level']);
            }

            $data['level_name'] = $level->name_en;
        }

        $data['skill_name'] = '';
        if ($data['skill']) {
            $career = isset($data['ware_house_cv']['career']) ? (string) $data['ware_house_cv']['career'] : '';
            $isIT = (str_contains($career, '30') || str_contains($career, '31')) ? true : false;
            if ($isIT) {
                $skill = $this->skillMainRepository->find($data['skill']);
            } else {
                $skill =  $this->jobTopRepository->find($data['skill']);
            }

            $data['skill_name'] = $skill->name_en;
        }

        return $data;
    }

    public function getDataBeforeBy($id)
    {
        $data = $this->wareHouseCvSellingRepository->find($id);
        return $data;
    }

    public function getJobDataBeforeBy()
    {
        $user = auth('client')->user();
        $companyId = $user->company_id;
        $data = $this->jobRepository->getListJobEmployer($companyId);
        return $data;
    }

    public function statisticalByMonth($fromDate = null, $toDate = null)
    {
        $data = $this->wareHouseCvSellingRepository->statisticalByMonth($fromDate, $toDate);
        $data = $data->keyBy('month')->toArray();
        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }

    public function checkView($view_id, $view_token)
    {
        $view = $this->wareHouseCvSellingRepository->find($view_id);
        if ($view->view_token == $view_token) {
            return $view;
        }
        return false;
    }
}
