<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Notifications\AdminChangeAuthorize;
use App\Notifications\ConfirmSupportJob;
use App\Services\Admin\BonusService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AuthorizeController extends Controller
{

    protected $userService;
    protected $submitCvService;
    protected $bonusService;

    public function __construct(UserService $userService, SubmitCvService $submitCvService, BonusService $bonusService)
    {
        $this->userService = $userService;
        $this->submitCvService = $submitCvService;
        $this->bonusService = $bonusService;
    }

    public function datatableAuthorize(Request $request)
    {
        $params = $request->all();
        $params['authorize'] = 1;
        //Danh sách chờ ủy quyền
//        $params['authorize_status'] = 0;
//        //Danh sách xác nhận ủy quyền
//        $params['authorize_status'] = 1;
//        //Danh sách từ chối ủy quyền
//        $params['authorize_status'] = 2;
        $data = $this->submitCvService->datatableAuthorize($params);
        return response($data);

    }

    public function index()
    {
        $datatable = $this->submitCvService->buildDatatableAuthorize();
        return view('admin.pages.submitcv.index', compact('datatable'));
    }

    public function changeAuthorize($id, $authorize)
    {
        try {
            DB::beginTransaction();
            $submitCv = $this->submitCvService->findById($id);
            if ($submitCv) {
                if (!empty($submitCv->authorize) || $submitCv->authorize_status > 0) {
                    $submitCv->authorize_status = $authorize;

                    //đồng ý
                    if ($authorize == 1) {
                        $user = $submitCv->rec;
                        $wareHouseCvs = $submitCv->warehouseCv;
                        $jobDetail = $submitCv->job;
                        // $submitCv->status = config('constant.status_recruitment_revert.WaitingPayment');
                        $submitCv->confirm_candidate = Carbon::now()->addDay(2);
                        $wareHouseCvs->notify(new ConfirmSupportJob($jobDetail, $wareHouseCvs, $user));
                    }
                    //từ chối
                    if ($authorize == 2) $submitCv->status = config('constant.status_recruitment_revert.RecruiterRejectCV');
                    $submitCv->save();

                    //send mail
                    $user = $this->userService->findById($submitCv->user_id);

                    $user->notify(new AdminChangeAuthorize($user, $authorize, $submitCv));
                } else {
                    Toast::warning(__('message.update_fail'));
                }
            }
            DB::commit();
            Toast::success(__('message.edit_success'));
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log update change status: ', [
                'content: ' => $e->getMessage()
            ]);

            return false;
        }
    }
}
