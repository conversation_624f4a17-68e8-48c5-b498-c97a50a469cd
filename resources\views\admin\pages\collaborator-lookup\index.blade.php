@extends('admin.layouts.app')

@section('content')
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Tra Cứu Thông Tin Cộng Tác Viên</h4>
    </div>
</div>
<!--End Page header-->

<!-- Search Form -->
<div class="row">
    <div class="col-xl-12 col-md-12 col-lg-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Tìm kiếm cộng tác viên</h3>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="form-group">
                        <label for="collaborator_email">Email cộng tác viên hoặc mã cộng tác viên:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="collaborator_email" name="email"
                                placeholder="Nhập email cộng tác viên hoặc mã cộng tác viên..." required>
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fa fa-search"></i> Tìm kiếm
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading indicator -->
<div id="loading" class="text-center" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="sr-only">Đang tải...</span>
    </div>
</div>

<!-- Results container -->
<div id="results" style="display: none;">
    <!-- Collaborator Info Box -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thông tin cộng tác viên</h3>
                </div>
                <div class="card-body">
                    <div id="collaboratorInfo"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Referral Info Box -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thông tin tài khoản giới thiệu</h3>
                </div>
                <div class="card-body">
                    <div id="referralInfo"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submits Table -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Danh sách ứng tuyển</h3>
                    <button class="btn btn-success" id="viewAllSubmits" style="display: none;">
                        <i class="fa fa-list"></i> Xem tất cả với phân trang
                    </button>
                </div>
                <div class="card-body">
                    <!-- Search form for submits (initially hidden) -->
                    <div id="submitsSearchForm" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="submitsSearch"
                                        placeholder="Tìm kiếm theo tên hoặc email ứng viên...">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" id="searchSubmitsBtn">
                                            <i class="fa fa-search"></i> Tìm kiếm
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <select class="form-control" id="submitsPerPage">
                                    <option value="10">10 / trang</option>
                                    <option value="20" selected>20 / trang</option>
                                    <option value="50">50 / trang</option>
                                    <option value="100">100 / trang</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="submitsTable">
                            <thead>
                                <tr>
                                    <th>Submit ID</th>
                                    <th>Tên Job</th>
                                    <th>Tên ứng viên</th>
                                    <th>Email ứng viên</th>
                                    <th>Loại ứng tuyển</th>
                                    <th>Trạng thái</th>
                                    <th>Bonus</th>
                                    <th>Ngày ứng tuyển</th>
                                </tr>
                            </thead>
                            <tbody id="submitsTableBody">
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="submitsPagination" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="dataTables_info" id="submitsInfo">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="dataTables_paginate paging_simple_numbers" id="submitsPaginateContainer">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wallet Transactions Table -->
    <div class="row">
        <div class="col-xl-12 col-md-12 col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Lịch sử giao dịch ví</h3>
                </div>
                <div class="card-body">
                    <div id="walletInfo" class="mb-3"></div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="transactionsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Loại</th>
                                    <th>Số tiền</th>
                                    <th>Số dư sau GD</th>
                                    <th>Mô tả</th>
                                    <th>Tên ứng viên</th>
                                    <th>Email ứng viên</th>
                                    <th>Tên Job</th>
                                    <th>Thời gian</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    // Global variables
    let currentCollaboratorId = null;
    let currentPage = 1;
    let currentSearch = '';
    let currentPerPage = 20;
    let paginationMode = false;

    $(document).ready(function() {
    // Form submit handler
    $('#searchForm').on('submit', function(e) {
        e.preventDefault();
        
        const email = $('#collaborator_email').val();
        if (!email) {
            alert('Vui lòng nhập email cộng tác viên');
            return;
        }

        searchCollaborator(email);
    });

    // View all submits button handler
    $('#viewAllSubmits').on('click', function() {
        if (currentCollaboratorId) {
            paginationMode = true;
            $('#submitsSearchForm').show();
            $('#viewAllSubmits').hide();
            loadSubmitsPaginated(currentCollaboratorId, 1, '', currentPerPage);
        }
    });

    // Search submits button handler
    $('#searchSubmitsBtn').on('click', function() {
        if (currentCollaboratorId) {
            currentSearch = $('#submitsSearch').val();
            currentPage = 1;
            loadSubmitsPaginated(currentCollaboratorId, currentPage, currentSearch, currentPerPage);
        }
    });

    // Per page change handler
    $('#submitsPerPage').on('change', function() {
        if (currentCollaboratorId) {
            currentPerPage = parseInt($(this).val());
            currentPage = 1;
            loadSubmitsPaginated(currentCollaboratorId, currentPage, currentSearch, currentPerPage);
        }
    });

    // Search on Enter key
    $('#submitsSearch').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchSubmitsBtn').click();
        }
    });

    // Search collaborator function
    function searchCollaborator(email) {
        $('#loading').show();
        $('#results').hide();

        $.ajax({
            url: '{{ route("admin.collaborator-lookup.search") }}',
            type: 'POST',
            data: {
                email: email,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                $('#loading').hide();
                
                if (response.success) {
                    displayResults(response.data);
                } else {
                    alert(response.message || 'Có lỗi xảy ra');
                }
            },
            error: function(xhr) {
                $('#loading').hide();
                
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMsg = 'Dữ liệu không hợp lệ:\n';
                    for (let field in errors) {
                        errorMsg += '- ' + errors[field].join('\n- ') + '\n';
                    }
                    alert(errorMsg);
                } else {
                    alert('Có lỗi xảy ra khi tìm kiếm');
                }
            }
        });
    }

    // Display results function
    function displayResults(data) {
        // Store current collaborator ID
        currentCollaboratorId = data.collaborator.id;
        
        // Reset pagination mode
        paginationMode = false;
        $('#submitsSearchForm').hide();
        $('#viewAllSubmits').show();
        $('#submitsPagination').hide();
        
        // Display collaborator info
        displayCollaboratorInfo(data.collaborator);
        
        // Display referral info
        displayReferralInfo(data.referral_info);
        
        // Display submits table
        displaySubmitsTable(data.submits);
        
        // Display wallet transactions
        displayWalletTransactions(data.wallet_transactions);
        
        $('#results').show();
    }

    // Display collaborator information
    function displayCollaboratorInfo(collaborator) {
        const html = `
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td>${collaborator.id}</td>
                        </tr>
                        <tr>
                            <td><strong>Email:</strong></td>
                            <td>${collaborator.email}</td>
                        </tr>
                        <tr>
                            <td><strong>Tên CTV:</strong></td>
                            <td>${collaborator.name}</td>
                        </tr>
                        <tr>
                            <td><strong>Mã CTV:</strong></td>
                            <td><strong class="text-primary">${collaborator.referral_define || 'N/A'}</strong></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Ngày đăng ký:</strong></td>
                            <td>${collaborator.created_at}</td>
                        </tr>
                        <tr>
                            <td><strong>Đăng nhập gần nhất:</strong></td>
                            <td>${collaborator.last_login_at}</td>
                        </tr>
                        <tr>
                            <td><strong>Tổng số ứng tuyển:</strong></td>
                            <td><strong class="text-info">${collaborator.total_submits}</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Số dư ví:</strong></td>
                            <td><strong class="text-success">${collaborator.wallet_amount} VND</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
        $('#collaboratorInfo').html(html);
    }

    // Display referral information
    function displayReferralInfo(referralInfo) {
        let html = '';
        if (referralInfo.has_referral) {
            html = `
                <div class="alert alert-success">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Mã referral:</strong> ${referralInfo.referral_code}
                        </div>
                        <div class="col-md-6">
                            <strong>Người giới thiệu:</strong> ${referralInfo.referrer_name} (${referralInfo.referrer_email})
                        </div>
                    </div>
                </div>
            `;
        } else {
            if (referralInfo.referral_code) {
                html = `
                    <div class="alert alert-warning">
                        <strong>Mã referral:</strong> ${referralInfo.referral_code} - <em>Không tìm thấy thông tin người giới thiệu</em>
                    </div>
                `;
            } else {
                html = `
                    <div class="alert alert-info">
                        <em>Không có thông tin tài khoản giới thiệu</em>
                    </div>
                `;
            }
        }
        $('#referralInfo').html(html);
    }

    // Display submits table
    function displaySubmitsTable(submits) {
        let html = '';
        if (submits && submits.length > 0) {
            submits.forEach(function(submit) {
                html += `
                    <tr>
                        <td>
                            <span class="copy-id" data-id="${submit.id}" style="cursor: pointer; color: #007bff;">
                                ${submit.id} <i class="fa fa-copy"></i>
                            </span>
                            <br>
                            <a href="/admin/submit-cv/${submit.id}/edit" target="_blank" class="btn btn-primary btn-sm">Edit</a>
                        </td>
                        <td>${submit.job_name}</td>
                        <td>${submit.candidate_name || 'N/A'}</td>
                        <td>${submit.candidate_email || 'N/A'}</td>
                        <td><span class="badge badge-${submit.bonus_type != 'cv' ? 'primary' : 'success'}">${submit.bonus_type}</span></td>
                        <td>${submit.status}</td>
                        <td class="text-success"><strong>${submit.bonus} VND</strong></td>
                        <td>${submit.created_at}</td>
                    </tr>
                `;
            });
            
            // Add note if showing limited results
            if (submits.length === 10 && !paginationMode) {
                html += `
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            <em>Hiển thị 10 ứng tuyển gần nhất. Nhấn "Xem tất cả với phân trang" để xem toàn bộ.</em>
                        </td>
                    </tr>
                `;
            }
        } else {
            html = '<tr><td colspan="8" class="text-center">Không có ứng tuyển nào</td></tr>';
        }
        $('#submitsTableBody').html(html);
    }

    // Display wallet transactions
    function displayWalletTransactions(walletData) {
        // Display wallet ID info
        if (walletData.wallet_id) {
            const walletInfoHtml = `
                <div class="alert alert-info">
                    <strong>ID Ví:</strong> ${walletData.wallet_id}
                </div>
            `;
            $('#walletInfo').html(walletInfoHtml);
        } else {
            $('#walletInfo').html('<div class="alert alert-warning">Không tìm thấy thông tin ví</div>');
        }

        // Display transactions
        let html = '';
        if (walletData.transactions && walletData.transactions.length > 0) {
            walletData.transactions.forEach(function(transaction) {
                html += `
                    <tr>
                        <td>${transaction.id}</td>
                        <td><span class="badge badge-${transaction.type === 'Nhận tiền' ? 'success' : 'danger'}">${transaction.type}</span></td>
                        <td class="${transaction.amount.includes('-') ? 'text-danger' : 'text-success'}">${transaction.amount}</td>
                        <td>${transaction.balance_after}</td>
                        <td>${transaction.description}</td>
                        <td>${transaction.candidate_name}</td>
                        <td>${transaction.candidate_email}</td>
                        <td>${transaction.job_title}</td>
                        <td>${transaction.created_at}</td>
                    </tr>
                `;
            });
        } else {
            html = '<tr><td colspan="9" class="text-center">Không có giao dịch nào</td></tr>';
        }
        $('#transactionsTableBody').html(html);
    }

    // Copy ID to clipboard
    $(document).on('click', '.copy-id', function() {
        const id = $(this).data('id');
        copyToClipboard(id);
        
        // Show feedback
        const originalHtml = $(this).html();
        $(this).html(id + ' <i class="fa fa-check text-success"></i>');
        setTimeout(() => {
            $(this).html(originalHtml);
        }, 1500);
    });

    // Load submits with pagination
    function loadSubmitsPaginated(userId, page = 1, search = '', perPage = 20) {
        $.ajax({
            url: '{{ route("admin.collaborator-lookup.submits-paginated") }}',
            type: 'GET',
            data: {
                user_id: userId,
                page: page,
                search: search,
                per_page: perPage
            },
            success: function(response) {
                if (response.success) {
                    displaySubmitsTablePaginated(response.data);
                    displayPagination(response.pagination);
                } else {
                    alert('Không thể tải danh sách ứng tuyển');
                }
            },
            error: function() {
                alert('Có lỗi xảy ra khi tải danh sách ứng tuyển');
            }
        });
    }

    // Display submits table with pagination
    function displaySubmitsTablePaginated(submits) {
        let html = '';
        if (submits && submits.length > 0) {
            submits.forEach(function(submit) {
                html += `
                    <tr>
                        <td>
                            <span class="copy-id" data-id="${submit.id}" style="cursor: pointer; color: #007bff;">
                                ${submit.id} <i class="fa fa-copy"></i>
                            </span>
                            <br>
                            <a href="/admin/submit-cv/${submit.id}/edit" target="_blank" class="btn btn-primary btn-sm">Edit</a>
                        </td>
                        <td>${submit.job_name}</td>
                        <td>${submit.candidate_name || 'N/A'}</td>
                        <td>${submit.candidate_email || 'N/A'}</td>
<td><span class="badge badge-${submit.bonus_type != 'cv' ? 'primary' : 'success'}">${submit.bonus_type}</span></td>                        <td>${submit.status}</td>
                        <td class="text-success"><strong>${submit.bonus} VND</strong></td>
                        <td>${submit.created_at}</td>
                    </tr>
                `;
            });
        } else {
            html = '<tr><td colspan="8" class="text-center">Không có ứng tuyển nào</td></tr>';
        }
        $('#submitsTableBody').html(html);
    }

    // Display pagination
    function displayPagination(pagination) {
        // Show pagination info
        const infoText = `Hiển thị ${pagination.from || 0} đến ${pagination.to || 0} của ${pagination.total} kết quả`;
        $('#submitsInfo').html(infoText);

        // Show pagination buttons
        let paginationHtml = '';
        if (pagination.total > pagination.per_page) {
            paginationHtml = '<ul class="pagination pagination-sm">';
            
            // Previous button
            if (pagination.current_page > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.current_page - 1}">Trước</a></li>`;
            }
            
            // Page numbers
            const startPage = Math.max(1, pagination.current_page - 2);
            const endPage = Math.min(pagination.last_page, pagination.current_page + 2);
            
            if (startPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
                if (startPage > 2) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
            }
            
            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.current_page) {
                    paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                } else {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
                }
            }
            
            if (endPage < pagination.last_page) {
                if (endPage < pagination.last_page - 1) {
                    paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
                }
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.last_page}">${pagination.last_page}</a></li>`;
            }
            
            // Next button
            if (pagination.current_page < pagination.last_page) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pagination.current_page + 1}">Tiếp</a></li>`;
            }
            
            paginationHtml += '</ul>';
        }
        
        $('#submitsPaginateContainer').html(paginationHtml);
        $('#submitsPagination').show();
    }

    // Handle pagination click
    $(document).on('click', '.page-link', function(e) {
        e.preventDefault();
        const page = $(this).data('page');
        if (page && currentCollaboratorId) {
            currentPage = page;
            loadSubmitsPaginated(currentCollaboratorId, currentPage, currentSearch, currentPerPage);
        }
    });

    // Copy to clipboard function
    function copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
            } catch (err) {
                console.error('Fallback: Unable to copy', err);
            }
            document.body.removeChild(textArea);
        }
    }
});
</script>
@endsection