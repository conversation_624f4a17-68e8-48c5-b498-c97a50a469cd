# Module Kho CV (Warehouse CV Module)

## 1. Tổng quan

Module Kho CV cho phép cộng tác viên lưu trữ, qu<PERSON>n lý và bán CV của ứng viên cho nhà tuyển dụng. <PERSON><PERSON><PERSON> là một marketplace nội bộ giúp tối ưu hóa việc sử dụng lại CV và tạo thêm nguồn thu cho cộng tác viên.

## 2. <PERSON><PERSON><PERSON>ức năng Chính

### 2.1. <PERSON><PERSON><PERSON><PERSON> lý Kho CV (Cho CTV)

#### 2.1.1. Thêm CV vào Kho
- **<PERSON><PERSON><PERSON> thức thêm**:
  - Upload file CV (PDF, DOC, DOCX)
  - Nhập thông tin thủ công qua form
  - Import từ LinkedIn (future)
  - Sync từ Google Drive (future)

- **Thông tin CV cần nhập**:
  - **Thông tin cá nhân**:
    - <PERSON><PERSON> tên (*)
    - Email (*)
    - <PERSON><PERSON> điện thoại (*)
    - <PERSON><PERSON><PERSON> sinh
    - Giới tính
    - Địa chỉ
    - Ảnh đại diện
  
  - **Thông tin nghề nghiệp**:
    - Vị trí hiện tại
    - Công ty hiện tại
    - Tổng năm kinh nghiệm
    - Ngành nghề/Lĩnh vực
    - Kỹ năng chính (tags)
    - Trình độ học vấn
    - Chứng chỉ
  
  - **Thông tin lương**:
    - Mức lương hiện tại
    - Mức lương mong muốn
    - Sẵn sàng làm việc

- **Validation**:
  - Check duplicate (email + phone)
  - Verify email/phone (optional)
  - CV quality score

#### 2.1.2. Danh sách CV trong Kho
- **Filter & Search**:
  - Theo tên, email, phone
  - Theo kỹ năng
  - Theo kinh nghiệm
  - Theo mức lương
  - Theo trạng thái (Active/Inactive/Sold)

- **Actions**:
  - Xem chi tiết
  - Chỉnh sửa thông tin
  - Active/Deactive
  - Xóa (soft delete)
  - Đưa lên bán

#### 2.1.3. Chỉnh sửa CV
- Update thông tin mới
- Upload CV mới nhất
- Thêm/xóa kỹ năng
- Update trạng thái làm việc
- Ghi chú nội bộ

### 2.2. Marketplace CV

#### 2.2.1. Đăng bán CV
- **Quy trình**:
  1. Chọn CV từ kho
  2. Set giá bán
  3. Chọn thời hạn bán (30/60/90 ngày)
  4. Viết mô tả bán hàng
  5. Ẩn thông tin nhạy cảm
  6. Submit để duyệt

- **Thông tin hiển thị khi bán**:
  - Tiêu đề (vị trí + kinh nghiệm)
  - Ngành nghề
  - Kỹ năng chính
  - Năm kinh nghiệm
  - Trình độ học vấn
  - Mức lương mong muốn
  - Preview CV (blurred)

- **Định giá CV**:
  - Gợi ý giá dựa trên:
    - Level (Junior/Middle/Senior)
    - Ngành nghề (IT cao hơn)
    - Độ hiếm kỹ năng
    - Chất lượng CV
  - Min price: 200,000 VNĐ
  - Max price: 5,000,000 VNĐ

#### 2.2.2. Tìm kiếm và Mua CV (Cho NTD)
- **Search & Filter**:
  - Từ khóa (skill, position)
  - Ngành nghề
  - Kinh nghiệm
  - Mức lương
  - Địa điểm
  - Sẵn sàng làm việc

- **Xem trước CV**:
  - Thông tin cơ bản
  - Kỹ năng và kinh nghiệm
  - CV preview (watermark)
  - Đánh giá từ NTD khác

- **Quy trình mua**:
  1. Tìm CV phù hợp
  2. Xem preview
  3. Add to cart
  4. Checkout
  5. Thanh toán từ ví
  6. Download full CV

#### 2.2.3. Quản lý CV đã mua (NTD)
- **Danh sách CV đã mua**:
  - Thông tin đầy đủ ứng viên
  - Ngày mua
  - Giá mua
  - CTV bán
  - Download lại CV

- **Actions**:
  - Đánh giá CV
  - Báo cáo CV không đúng
  - Yêu cầu hoàn tiền
  - Liên hệ ứng viên

### 2.3. Hệ thống Đánh giá và Phản hồi

#### 2.3.1. Đánh giá CV
- **Rating**: 1-5 sao
- **Tiêu chí đánh giá**:
  - Độ chính xác thông tin
  - Chất lượng ứng viên
  - Phản hồi của ứng viên
  - Kết quả tuyển dụng

- **Comment**: Text feedback

#### 2.3.2. Q&A System
- NTD có thể hỏi về CV trước khi mua
- CTV trả lời trong 24h
- Public Q&A cho CV

#### 2.3.3. Xử lý Khiếu nại
- **Lý do khiếu nại**:
  - Thông tin không chính xác
  - Ứng viên không phản hồi
  - CV fake/duplicate
  - Đã được tuyển nơi khác

- **Quy trình**:
  1. NTD submit khiếu nại
  2. CTV phản hồi trong 48h
  3. Admin xem xét
  4. Quyết định hoàn tiền

### 2.4. Bảo vệ Thông tin

#### 2.4.1. Ẩn Thông tin Nhạy cảm
- Auto blur: Email, Phone, Address
- Watermark trên CV preview
- Chỉ hiện full info sau khi mua

#### 2.4.2. Ngăn chặn Lạm dụng
- Limit download: 5 lần/CV
- Track IP download
- Block screenshot (JavaScript)
- Time-based access token

### 2.5. Thống kê và Báo cáo

#### 2.5.1. Cho CTV
- **Dashboard metrics**:
  - Tổng CV trong kho
  - CV đang bán
  - CV đã bán
  - Doanh thu từ bán CV
  - Top CV bán chạy

- **Analytics**:
  - View/Buy conversion
  - Giá bán trung bình
  - Thời gian bán
  - Rating trung bình

#### 2.5.2. Cho Admin
- Tổng CV trong hệ thống
- Giao dịch mua bán
- Top sellers
- CV quality metrics

## 3. Business Rules

### 3.1. CV Ownership
- CTV sở hữu CV đã upload
- Không được bán CV của CTV khác
- CV public sau 6 tháng inactive

### 3.2. Pricing Rules
- **Commission platform**: 20% giá bán
- **Giá bán tối thiểu**: 200,000 VNĐ
- **Bulk discount**: 
  - Mua 5 CV: Giảm 10%
  - Mua 10 CV: Giảm 15%
  - Mua 20+ CV: Giảm 20%

### 3.3. Quality Control
- **CV bị reject nếu**:
  - Thông tin không đầy đủ
  - CV cũ (> 1 năm)
  - Duplicate trong system
  - Spam/Fake content

- **Auto deactive nếu**:
  - Rating < 2 sao
  - Nhiều report
  - Không update > 6 tháng

### 3.4. Refund Policy
- **Hoàn 100% nếu**:
  - Thông tin sai sự thật
  - Ứng viên không tồn tại
  - CV duplicate (đã mua)

- **Hoàn 50% nếu**:
  - Ứng viên không phản hồi trong 7 ngày
  - Đã nhận việc nơi khác

- **Không hoàn nếu**:
  - Đã download CV
  - Đã liên hệ ứng viên
  - Quá 30 ngày từ ngày mua

## 4. Database Schema

### 4.1. Bảng warehouse_cvs
```sql
- id
- user_id (CTV owner)
- candidate_name
- candidate_email
- candidate_phone
- candidate_avatar
- date_of_birth
- gender
- address
- current_position
- current_company
- years_experience
- education_level
- certificates (json)
- skills (json)
- current_salary
- expected_salary
- cv_file_path
- cv_quality_score
- is_verified
- status (active/inactive/sold)
- source (upload/linkedin/manual)
- created_at
- updated_at
```

### 4.2. Bảng warehouse_cv_sellings
```sql
- id
- warehouse_cv_id
- seller_id (CTV)
- title
- description
- price
- discount_percentage
- view_count
- buy_count
- average_rating
- status (pending/active/sold/expired)
- expired_at
- created_at
- updated_at
```

### 4.3. Bảng warehouse_cv_selling_buys
```sql
- id
- warehouse_cv_selling_id
- buyer_id (Employer)
- company_id
- price_paid
- discount_applied
- payment_method
- transaction_id
- download_count
- last_download_at
- rating
- review
- is_refunded
- refund_reason
- refund_amount
- created_at
- updated_at
```

### 4.4. Bảng warehouse_cv_selling_evaluates
```sql
- id
- warehouse_cv_selling_buy_id
- rating (1-5)
- accuracy_score
- quality_score
- response_score
- comment
- is_hired
- created_at
- updated_at
```

### 4.5. Bảng warehouse_cv_selling_qas
```sql
- id
- warehouse_cv_selling_id
- asker_id
- question
- answer
- answered_by
- answered_at
- is_public
- created_at
- updated_at
```

## 5. API Endpoints

### 5.1. CTV APIs
- `GET /api/warehouse/cvs` - Danh sách CV trong kho
- `POST /api/warehouse/cvs` - Thêm CV mới
- `PUT /api/warehouse/cvs/{id}` - Update CV
- `DELETE /api/warehouse/cvs/{id}` - Xóa CV
- `POST /api/warehouse/cvs/{id}/sell` - Đăng bán CV
- `GET /api/warehouse/sales` - DS CV đang bán
- `GET /api/warehouse/sold` - DS CV đã bán

### 5.2. Employer APIs  
- `GET /api/marketplace/cvs` - Tìm CV để mua
- `GET /api/marketplace/cvs/{id}` - Chi tiết CV
- `POST /api/marketplace/cvs/{id}/buy` - Mua CV
- `GET /api/marketplace/purchased` - CV đã mua
- `POST /api/marketplace/cvs/{id}/rate` - Đánh giá CV
- `POST /api/marketplace/cvs/{id}/report` - Báo cáo CV

### 5.3. Q&A APIs
- `GET /api/marketplace/cvs/{id}/questions` - DS câu hỏi
- `POST /api/marketplace/cvs/{id}/questions` - Đặt câu hỏi
- `POST /api/marketplace/questions/{id}/answer` - Trả lời

## 6. Security & Privacy

### 6.1. Data Protection
- Encrypt sensitive data
- Access control by ownership
- Audit trail for all actions
- GDPR compliance

### 6.2. Anti-Fraud Measures
- Duplicate detection algorithm
- Phone/Email verification
- AI-based fake CV detection
- Manual review for high-value CVs

### 6.3. Watermarking
- Dynamic watermark with buyer info
- Invisible tracking pixels
- PDF encryption
- Time-limited access URLs

## 7. Integration

### 7.1. Payment Integration
- Deduct from employer wallet
- Credit to CTV wallet
- Commission calculation
- Auto refund process

### 7.2. Notification Integration
- New CV matching criteria
- CV sold notification
- Price drop alerts
- Q&A notifications

### 7.3. Analytics Integration
- Track user behavior
- Conversion funnel
- Popular search terms
- Pricing optimization

## 8. Mobile Features

### 8.1. Mobile Upload
- Camera capture for CV
- OCR for data extraction
- Quick CV creation
- Batch upload

### 8.2. Mobile Marketplace
- Swipe to browse CVs
- Quick filters
- In-app CV viewer
- One-tap purchase

## 9. Future Enhancements

### 9.1. AI Features
- Smart pricing suggestion
- CV quality scoring
- Auto-matching with jobs
- Duplicate detection
- Skill extraction from CV

### 9.2. Advanced Features
- CV subscription plans
- Bulk CV packages
- API for partners
- White-label marketplace
- Blockchain verification
