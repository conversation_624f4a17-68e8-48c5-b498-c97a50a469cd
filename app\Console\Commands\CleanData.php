<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\EmailLog;

class CleanData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:clean-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dọn dẹp dữ liệu rác ít sử dụng';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info($this->description);

        $time_to_clean = date('Y-m-d H:i:s', strtotime('-180 day'));
        EmailLog::where('created_at', '<=', $time_to_clean)->delete();
        return Command::SUCCESS;
    }
}