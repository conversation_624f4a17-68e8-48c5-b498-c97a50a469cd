<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Job;
use App\Models\SubmitCv;
use App\Models\WalletTransaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmployerLookupController extends Controller
{
    /**
     * Hiển thị trang tra cứu thông tin nhà tuyển dụng
     */
    public function index(Request $request)
    {
        // Kiểm tra nếu có email trong request thì tự động tra cứu
        $autoSearchEmail = $request->get('email');
        

        return view('admin.pages.employer-lookup.index', compact('autoSearchEmail'));
    }

    /**
     * Tìm kiếm thông tin nhà tuyển dụng theo email
     */
    public function search(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $email = $request->input('email');

        // Tìm nhà tuyển dụng theo email
        $employer = User::where('email', $email)
            ->where('type', 'employer')
            ->with(['company', 'wallet'])
            ->first();

        if (!$employer) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy nhà tuyển dụng với email này.'
            ]);
        }

        // Lấy thông tin cơ bản của employer
        $employerInfo = $this->getEmployerInfo($employer);

        // Lấy danh sách jobs
        $jobs = $this->getEmployerJobs($employer->id);

        // Lấy lịch sử giao dịch ví
        $walletTransactions = $this->getWalletTransactions($employer->id);

        return response()->json([
            'success' => true,
            'data' => [
                'employer' => $employerInfo,
                'jobs' => $jobs,
                'wallet_transactions' => $walletTransactions
            ]
        ]);
    }

    /**
     * Lấy thông tin cơ bản của nhà tuyển dụng
     */
    private function getEmployerInfo($employer)
    {
        $totalJobs = Job::where('employer_id', $employer->id)->count();

        // Kiểm tra tình trạng thật/ảo (dựa vào company_id)
        $isReal = $employer->company_id ? 'Thật' : 'Ảo';

        return [
            'id' => $employer->id,
            'email' => $employer->email,
            'name' => $employer->name,
            'company_name' => $employer->company ? $employer->company->name : $employer->company_name,
            'total_jobs' => $totalJobs,
            'created_at' => $employer->created_at ? $employer->created_at->format('d/m/Y H:i') : '',
            'last_login_at' => $employer->last_login_at ? $employer->last_login_at->format('d/m/Y H:i') : 'Chưa đăng nhập',
            'status' => $isReal,
            'wallet_amount' => $employer->wallet ? number_format($employer->wallet->amount, 0, ',', '.') : '0'
        ];
    }

    /**
     * Lấy danh sách jobs của nhà tuyển dụng
     */
    private function getEmployerJobs($employerId)
    {
        return Job::where('employer_id', $employerId)
            ->with(['submitCv'])
            ->select([
                'id',
                'name',
                'slug',
                'bonus',
                'bonus_type',
                'manual_bonus_for_ctv',
                'created_at',
                'expire_at',
                'is_active'
            ])
            ->get()
            ->map(function ($job) {
                $submitCount = $job->submitCv->count();

                return [
                    'id' => $job->id,
                    'name' => $job->name,
                    'slug' => $job->slug,
                    'bonus' => number_format($job->bonus, 0, ',', '.'),
                    'bonus_for_ctv' => number_format($job->bonus_for_ctv, 0, ',', '.'),
                    'bonus_type' => $job->bonus_type,
                    'manual_bonus_for_ctv' => $job->manual_bonus_for_ctv ? number_format($job->manual_bonus_for_ctv, 0, ',', '.') : '',
                    'submit_count' => $submitCount,
                    'created_at' => $job->created_at ? $job->created_at->format('d/m/Y') : '',
                    'expire_at' => $job->expire_at,
                    'is_active' => $job->is_active,
                    'edit_url' => route('job.edit', $job->id)
                ];
            });
    }

    /**
     * Lấy danh sách ứng tuyển của một job
     */
    public function getJobSubmits(Request $request, $jobId)
    {
        $job = Job::findOrFail($jobId);

        $submits = SubmitCv::where('job_id', $jobId)
            ->with(['submitCvMeta', 'user'])
            ->select([
                'id',
                'job_id',
                'user_id',
                'warehouse_cv_id',
                'status',
                'bonus',
                'bonus_self_apply',
                'is_self_apply',
                'authorize',
                'created_at'
            ])
            ->get()
            ->map(function ($submit) use ($job) {
                $candidateName = '';
                $candidateEmail = '';

                // Lấy thông tin ứng viên từ submitCvMeta hoặc user
                if ($submit->submitCvMeta) {
                    $candidateName = $submit->submitCvMeta->candidate_name;
                    $candidateEmail = $submit->submitCvMeta->candidate_email;
                } elseif ($submit->user) {
                    $candidateName = $submit->user->name;
                    $candidateEmail = $submit->user->email;
                }

                return [
                    'id' => $submit->id,
                    'candidate_name' => $candidateName,
                    'candidate_email' => $candidateEmail,
                    'job_name' => $job->name,
                    'status' => $submit->status_value ?? '',
                    'bonus_for_ctv' => number_format($submit->getSubmitBonusForCtv(), 0, ',', '.'),
                    'created_at' => $submit->created_at ? $submit->created_at->format('d/m/Y H:i') : '',
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $submits
        ]);
    }

    /**
     * Lấy lịch sử giao dịch ví
     */
    private function getWalletTransactions($userId)
    {
        $user = User::find($userId);
        if (!$user || !$user->wallet) {
            return [
                'wallet_id' => null,
                'transactions' => []
            ];
        }

        $transactions = WalletTransaction::where('wallet_id', $user->wallet->id)
            // ->with([
            //     'object.job',
            //     'object.submitCvMeta',
            //     'object.warehouseCv',
            //     'object.wareHouseCvSelling.wareHouseCv',
            //     'object.user:id,name,referral_define,type' // Thêm thông tin cộng tác viên
            // ])
            ->orderBy('created_at', 'desc')
            ->limit(50) // Giới hạn 50 giao dịch gần nhất
            ->get()
            ->map(function ($transaction) {
                $jobTitle = '';
                $candidateName = '';
                $candidateEmail = '';
                $ctvName = '';
                $ctvCode = '';

                // Xử lý thông tin dựa trên loại object
                if ($transaction->object) {
                    if ($transaction->object instanceof SubmitCv) {
                        // Lấy thông tin từ SubmitCv
                        $jobTitle = $transaction->object->job ? $transaction->object->job->name : '';

                        // Lấy thông tin ứng viên từ submitCvMeta hoặc warehouseCv
                        if ($transaction->object->submitCvMeta) {
                            $candidateName = $transaction->object->submitCvMeta->candidate_name;
                            $candidateEmail = $transaction->object->submitCvMeta->candidate_email;
                        } elseif ($transaction->object->warehouseCv) {
                            $candidateName = $transaction->object->warehouseCv->candidate_name;
                            $candidateEmail = $transaction->object->warehouseCv->candidate_email;
                        }

                        // Lấy thông tin cộng tác viên
                        if ($transaction->object->user && $transaction->object->user->type == 'rec') {
                            $ctvName = $transaction->object->user->name;
                            $ctvCode = $transaction->object->user->referral_define;
                        }
                    } elseif ($transaction->object instanceof \App\Models\WareHouseCvSellingBuy) {
                        // Lấy thông tin từ WareHouseCvSellingBuy
                        if ($transaction->object->wareHouseCvSelling && $transaction->object->wareHouseCvSelling->wareHouseCv) {
                            $candidateName = $transaction->object->wareHouseCvSelling->wareHouseCv->candidate_name;
                            $candidateEmail = $transaction->object->wareHouseCvSelling->wareHouseCv->candidate_email;
                        }
                        $jobTitle = 'Mua CV từ kho'; // Hoặc có thể lấy từ nguồn khác nếu có
                    }
                }

                return [
                    'id' => $transaction->id,
                    'amount' => number_format($transaction->amount, 0, ',', '.'),
                    'balance_after' => number_format($transaction->balance_after, 0, ',', '.'),
                    'description' => $transaction->note ?? '',
                    'job_title' => $jobTitle,
                    'candidate_name' => $candidateName,
                    'candidate_email' => $candidateEmail,
                    'ctv_name' => $ctvName,
                    'ctv_code' => $ctvCode,
                    'created_at' => $transaction->created_at ? $transaction->created_at->format('d/m/Y H:i') : '',
                    'type' => $transaction->amount >= 0 ? 'Nạp tiền' : 'Trừ tiền'
                ];
            });

        return [
            'wallet_id' => $user->wallet->id,
            'transactions' => $transactions
        ];
    }
}
