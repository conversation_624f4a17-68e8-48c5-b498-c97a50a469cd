<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Services\Frontend\JobService;
use Illuminate\Http\Request;

class CommonController extends Controller
{
    public function __construct(
        JobService $jobService,
    )
    {
        $this->jobService = $jobService;
    }

    public function getCommonJobList()
    {

        $lang = \request()->lang ?? 'vi';
        $career = config('job.career.' . $lang);
        $rank = Common::convertArray(config('job.rank.' . $lang));
        $bonusType = config('job.bonus_type.' . $lang);
        $salary = Common::convertArray(config('job.salary'));
        $locations = Common::getCities();
        $bonuses = Common::convertArray(config('job.bonus'));

        $top_skill_data = $this->jobService->getTopSkill();

        $top_skill = [];
        if (count($top_skill_data) > 0) {
            foreach ($top_skill_data as $item) {
                $item = explode(',', $item);
                foreach ($item as $value) {
                    $top_skill[] = ucfirst($value);
                }
            }
        }
        return response()->json([
            'career' => $career,
            'rank' => $rank,
            'bonusType' => $bonusType,
            'salary' => $salary,
            'locations' => $locations,
            'top_skill' => $top_skill,
            'bonuses' => $bonuses
        ]);
    }

}
