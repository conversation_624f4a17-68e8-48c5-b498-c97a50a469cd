<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\CheckEmailEmployer;
use App\Http\Requests\Frontend\CheckEmailRequest;
use App\Http\Requests\Frontend\CheckEmployerEmailRequest;
use App\Http\Requests\Frontend\EmployerRegisterRequest;
use App\Http\Requests\Frontend\LoginRequest;
use App\Http\Requests\Frontend\ProfileRequest;
use App\Http\Requests\Frontend\RecChangePasswordRequest;
use App\Http\Requests\Frontend\RecRegisterRequest;
use App\Http\Requests\Frontend\ResetPasswordRequest;
use App\Http\Requests\Frontend\UpdateBankInfo;
use App\Mail\SendEmailEmployerRegistration;
use App\Notifications\ActivateRecAccount;
use App\Repositories\PasswordResetRepository;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\UserService;
use App\Services\Frontend\BannerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Session;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Services\Frontend\CompanyService;
use App\Services\Frontend\TestimonialService;
use Illuminate\Support\Facades\View;
use App\Services\Frontend\WareHouseCvSellingService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use App\Services\Frontend\WalletService;

class UserController extends Controller
{
    protected $userService;
    protected $seoService;
    protected $settingService;
    protected $bannerService;
    protected $companyService;
    protected $testimonialService;
    protected $passwordResetRepository;
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingBuyService;
    protected $routeName;
    protected $walletService;

    public function __construct(
        UserService        $userService,
        SeoService         $seoService,
        SettingService     $settingService,
        BannerService      $bannerService,
        CompanyService     $companyService,
        TestimonialService $testimonialService,
        PasswordResetRepository $passwordResetRepository,
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvSellingBuyService $wareHouseCvSellingBuyService,
        WalletService $walletService

    ) {

        $this->userService = $userService;
        $this->seoService = $seoService;
        $this->settingService = $settingService;
        $this->bannerService = $bannerService;
        $this->companyService = $companyService;
        $this->testimonialService = $testimonialService;
        $this->passwordResetRepository = $passwordResetRepository;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingBuyService = $wareHouseCvSellingBuyService;
        $this->routeName = \Route::currentRouteName();
        $this->walletService = $walletService;
    }

    public function recLogin()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        return view('frontend.pages.login.rec.login');
    }

    public function recRegister()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        View::share('is_employer', true);
        return view('frontend.pages.login.rec.register');
    }

    public function recPostLogin(LoginRequest $request)
    {
        $type = config('constant.role.rec');
        $status = __('message.login_fail');
        try {
            $data = $this->userService->login($request->all(), $type);
            if ($data === true) {
                $user = auth('client')->user();
                $wallet = $user->wallet;
                if (!$wallet) {
                    $this->userService->createWallet($user);
                }
                if (!empty($request->redirect) && $request->redirect != '') {
                    return redirect(base64_decode($request->redirect));
                }
                return redirect()->route('rec-dashboard', ['show_intro' => 'market']);
            }
        } catch (\Exception $exception) {
            $status = $exception->getMessage();
        }

        return redirect()->route('rec-login')
            ->withInput($request->input())
            ->with(['status' => $status]);
    }

    public function recPostRegister(RecRegisterRequest $request)
    {
        $request->validate([
            // ... các validation rules khác
            'g-recaptcha-response' => 'required|captcha'
        ]);
        $type = config('constant.role.rec');
        $data = $this->userService->register($request->all(), $type);
        // Session::flash('message', __('frontend/login/message.please_check_email'));
        return redirect()->route('rec-login', ['msg' => 'register_success']);
    }

    public function verifyEmail(Request $request)
    {
        try {
            $result = $this->userService->verifyEmail($request->all());

            return redirect('rec');
        } catch (\Exception $e) {
            return redirect(route('rec-login'));
        }
    }

    public function verifyEmailEmployer(Request $request)
    {
        $view_id = $request->get('view_id');
        $view_token = $request->get('view_token');
        try {
            $result = $this->userService->verifyEmail($request->all());
            $user = auth('client')->user();
            if ($user && $result) {
                $result = $this->wareHouseCvSellingBuyService->addBonusEmployerRefer($user->email);
            }
            if ($user && $view_id && $view_token) {
                // $result = $this->wareHouseCvSellingBuyService->buyCVByToken($user->email, $view_id, $view_token);
                // add bonus employer refer (200.000 VNĐ)
                // if ($result) {
                // $amount = $user->wallet->amount;
                return redirect()->route('market-cv', ['view_id' => $view_id, 'view_token' => $view_token, 'from' => 'verify-email'])->with('success', 'Kích hoạt tài khoản thành công');
                // }
            } elseif ($user) {
                return redirect()->route('market-cv', ['from' => 'verify-email'])->with('success', 'Kích hoạt tài khoản thành công');
            }
            return redirect()->route('home')->with('error', 'Kích hoạt tài khoản không thành công');
        } catch (\Exception $e) {
            return redirect()->route('home')->with('error', 'Kích hoạt tài khoản không thành công');
        }
    }

    public function employerLogin(Request $request)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        if (\Auth::guard('client')->user() && \Auth::guard('client')->user()->type == config('constant.role.employer')) {
            return redirect()->route('employer-dashboard');
        }
        //banner NTD
        $listBanners = $this->bannerService->getListByType(config('constant.role_frontend_revest.ntd'), strtolower(config('constant.position_banner.home-ntd-banner')));
        //logo công ty

        $arrBanner = [];
        foreach ($listBanners as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBanner, $item->image_url_vn);
            } else {
                array_push($arrBanner, $item->image_url_en);
            }
        }

        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));

        return view('frontend.pages.login.employer.login', compact(
            'listTestimonations',
        ));
    }


    public function employerPostLogin(LoginRequest $request)
    {
        $type = config('constant.role.employer');
        $data = $this->userService->login($request->all(), $type);
        if ($data === true) {
            if (!empty($request->redirect) && $request->redirect != '') {
                return redirect(base64_decode($request->redirect));
            }
            return redirect()->route('employer-manager-dashboard');
        }
        $status = __('message.login_fail');

        return back()->withInput($request->input())->with(compact('status'));
    }

    public function employerPostLoginAjax(LoginRequest $request)
    {
        $type = config('constant.role.employer');
        $data = $this->userService->login($request->all(), $type);
        if ($data === true) {
            $view_id = $request->get('view_id');
            $view_token = $request->get('view_token');
            if ($view_id && $view_token) {
                // $result = $this->wareHouseCvSellingBuyService->buyCVByToken($request->email, $view_id, $view_token);
                $result = $this->wareHouseCvSellingBuyService->addBonusEmployerRefer($request->email);
                // if ($result) {
                $user = auth('client')->user();
                $amount = $user->wallet->amount;
                return response()->json(['success' => true, 'add_bonus' => $result ? config('constant.bonus_employer_amount_refer') : false, 'message' => 'Đăng nhập thành công', 'amount' => number_format($amount, 0, ',', '.')]);
                // }
            }
            return response()->json(['success' => false, 'message' => __('frontend/login/message.register_error')]);
        }
        $status = __('message.login_fail');

        return response()->json(['success' => false, 'message' => $status]);
    }

    public function employerRegister(Request $request)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        //banner NTD
        $listBanners = $this->bannerService->getListByType(config('constant.role_frontend_revest.ntd'), strtolower(config('constant.position_banner.home-ntd-banner')));
        //logo công ty

        $arrBanner = [];
        foreach ($listBanners as $item) {
            if ($lang == config('constant.language.vi')) {
                array_push($arrBanner, $item->image_url_vn);
            } else {
                array_push($arrBanner, $item->image_url_en);
            }
        }
        $listCompanies = $this->companyService->getListByHome();
        //testimonations NTD
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));
        //key value theo ngon ngu
        $token = $request->token;


        //        dd($listTestimonations);
        return view('frontend.pages.login.employer.register', compact(
            'listTestimonations',
            'listCompanies',
            'arrBanner',
            'token'
        ));
    }

    public function employerPostRegister(EmployerRegisterRequest $request)
    {
        $type = config('constant.role.employer');
        $result = $this->userService->register($request->all(), $type);

        $email_lead = explode(',', config('settings.global.email_lead'));

        if (is_array($email_lead)) {
            $email_count = count($email_lead);
            if ($email_count == 1) {
                $mail = Mail::to($email_lead[0]);
            } elseif ($email_count == 2) {
                $mail = Mail::to($email_lead[0])->cc($email_lead[1]);
            } elseif ($email_count >= 3) {
                $mail = Mail::to($email_lead[0])->cc($email_lead[1]);
                for ($i = 2; $i < $email_count; $i++) {
                    $mail->addCc($email_lead[$i]);
                }
            }
        }
        $mail->send(new SendEmailEmployerRegistration($result));

        $emailEmployer = $result->email;
        Notification::route('mail', $emailEmployer)->notify(new ActivateRecAccount($result));

        if ($result) {
            // Session::flash('message', __('frontend/login/message.please_check_email'));
        }
        return redirect()->route('employer-dashboard', ['from' => 'registed-success']);
    }

    public function employerPostRegisterAjax(EmployerRegisterRequest $request)
    {
        $type = config('constant.role.employer');
        $result = $this->userService->register($request->all(), $type, false);

        $email_lead = explode(',', config('settings.global.email_lead'));

        if (is_array($email_lead)) {
            $email_count = count($email_lead);
            if ($email_count == 1) {
                $mail = Mail::to($email_lead[0]);
            } elseif ($email_count == 2) {
                $mail = Mail::to($email_lead[0])->cc($email_lead[1]);
            } elseif ($email_count >= 3) {
                $mail = Mail::to($email_lead[0])->cc($email_lead[1]);
                for ($i = 2; $i < $email_count; $i++) {
                    $mail->addCc($email_lead[$i]);
                }
            }
        }
        $mail->send(new SendEmailEmployerRegistration($result));
        $view_id = $request->get('view_id');
        $view_token = $request->get('view_token');

        $emailEmployer = $result->email;
        Notification::route('mail', $emailEmployer)->notify(new ActivateRecAccount($result, $view_id, $view_token));

        if ($result) {
            return response()->json(['success' => true, 'message' => __('frontend/login/message.please_check_email')]);
        }
        return response()->json(['success' => false, 'message' => __('frontend/login/message.register_error')]);
    }

    public function employerRegisterSuccess()
    {

        return view('frontend.pages.login.employer.register_success');
    }



    public function redirect($social)
    {
        try {
            return Socialite::driver($social)->redirect();
        } catch (\Exception $exception) {
            return back();
        }
    }

    public function recForgotPassword()
    {

        return view('frontend.pages.login.rec.forgot-password');
    }


    public function callback($social)
    {
        try {
            $this->userService->loginSocial(Socialite::driver($social)->user(), $social);
            return redirect()->route('home', ['show_intro' => 'market']);
        } catch (\Exception $exception) {
            return back();
        }
    }

    public function employerSendMailPassword(CheckEmployerEmailRequest $request)
    {
        $type = config('constant.role.employer');
        $send = $this->userService->sendEmailResetPassword($request->email, $type, route('employer-dashboard'));
        if ($send) {
            return true;
        } else {
            return false;
        }
    }

    public function employerResetPassword(ResetPasswordRequest $request)
    {
        try {
            $result = false;
            DB::beginTransaction();
            $result = $this->userService->resetPassword($request->all());
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log user reset pass: ', [
                'content: ' => $e->getMessage(),
            ]);
        }
        if ($result === true) {
            Toast::success(__('frontend/login/message.update_password_success'));
            return redirect()->to($request->current_url);
        }

        return back()->with(['result' => $result]);
    }

    public function landingPageDemo(Request $request)
    {

        return view('frontend.pages.login.employer.landing-demo', $request->all());
    }

    public function recLogout()
    {
        Auth::guard('client')->logout();
        Cache::forget('accept_token_api_' . auth('client')->id());
        return redirect()->route('rec-login');
    }

    public function employerLogout()
    {
        Auth::guard('client')->logout();
        return redirect()->route('employer-dashboard');
    }

    public function ajaxUpdateNoti(Request $request)
    {
        $user = auth('client')->user();
        $user->notifications()->where('id', $request->id)->update([
            'read_at' => now(),
        ]);

        $countNoti = count($user->unreadNotifications);

        return $countNoti;
    }

    public function recRegisterFromAffi($referralCode)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        $userRef = $this->userService->checkReferralCode($referralCode);
        if (!$userRef) {
            return abort(404);
        }

        return view('frontend.pages.login.rec.register_from_affi', compact('referralCode'));
    }


    public function recPostLoginAffi(RecRegisterRequest $request)
    {
        $type = config('constant.role.rec');
        $this->userService->register($request->all(), $type);
        Toast::success(__('frontend/login/message.please_check_email'));

        return redirect()->route('home');
    }


    // forgot password employer
    public function employerShowSendEmailForgotPassword()
    {
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));

        return \view('frontend.pages.employer.forgot-password', compact('listTestimonations'));
    }

    public function employerFormResetPassword(Request $request)
    {
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));
        $token = $request->query('token');
        $tokenExists = $this->passwordResetRepository->findByToken($token);

        if ($tokenExists) {
            return view('frontend.pages.employer.form-reset-password', compact('token', 'listTestimonations'));
        }
        Session::flash('message', __('frontend/login/message.token_expired'));
        return redirect()->route('employer-login');
    }

    public function employerSubmitSendEmailForgotPassword(CheckEmailEmployer $request)
    {
        $type = config('constant.role.employer');
        $send = $this->userService->sendEmailResetPassword($request->email, $type, route('employer-form-reset-password'));
        if ($send) {
            Session::flash('message', __('frontend/login/message.please_check_email'));
            return redirect()->route('employer-dashboard')->with('success', 'Email reset password has been sent.');
        } else {
            return redirect()->back()->with('error', 'Failed to send reset password email.');
        }
    }

    public function employerSubmitResetPassword(ResetPasswordRequest $request)
    {
        try {
            $result = false;
            DB::beginTransaction();
            $result = $this->userService->resetPassword($request->all());
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log user reset pass: ', [
                'content: ' => $e->getMessage(),
            ]);
        }
        if ($result === true) {
            Session::flash('message', __('frontend/login/message.update_password_success'));
            return redirect(route('employer-dashboard'));
        }

        return back()->with(['result' => $result]);
    }


    // forgot password rec
    public function recShowSendEmailForgotPassword()
    {
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));

        return \view('frontend.pages.rec.forgot-password', compact('listTestimonations'));
    }

    public function recSendMailPassword(CheckEmailRequest $request)
    {
        $type = config('constant.role.rec');
        $send = $this->userService->sendEmailResetPassword($request->email, $type, route('rec-form-reset-password'));
        if ($send) {
            Session::flash('message', __('frontend/login/message.please_check_email'));
            return redirect()->route('rec-login')->with('success', 'Email reset password has been sent.');
        } else {
            return redirect()->back()->with('error', 'Failed to send reset password email.');
        }
    }
    public function recFormResetPassword(Request $request)
    {
        $listTestimonations = $this->testimonialService->getListByType(config('constant.role_frontend_revest.ntd'));
        $token = $request->query('token');
        $tokenExists = $this->passwordResetRepository->findByToken($token);

        if ($tokenExists) {
            return view('frontend.pages.rec.form-reset-password', compact('token', 'listTestimonations'));
        }
        Session::flash('message', __('frontend/login/message.token_expired'));
        return redirect()->route('rec-login');
    }

    public function recResetPassword(ResetPasswordRequest $request)
    {
        try {
            $result = false;
            DB::beginTransaction();
            $result = $this->userService->resetPassword($request->all());
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log user reset pass: ', [
                'content: ' => $e->getMessage(),
            ]);
        }
        if ($result === true) {
            Session::flash('message', __('frontend/login/message.update_password_success'));
            return redirect(route('rec-login'));
        }

        return back()->with(['result' => $result]);
    }
}
