<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Job;
use App\Models\SubmitCv;
use App\Models\WalletTransaction;
use App\Repositories\UserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CollaboratorLookupController extends Controller
{
    protected $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Hiển thị trang tra cứu thông tin cộng tác viên
     */
    public function index()
    {
        return view('admin.pages.collaborator-lookup.index');
    }

    /**
     * Tìm kiếm thông tin cộng tác viên theo email
     */
    public function search(Request $request)
    {
        $request->validate([
            'email' => 'required'
        ]);

        $email = $request->input('email');

        // Tìm cộng tác viên theo email
        $collaborator = User::where(function ($query) use ($email) {
            $query->where('email', $email)
                ->orWhere('referral_define', $email);
        })
            ->where('type', 'rec')
            ->with(['wallet'])
            ->first();

        if (!$collaborator) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy cộng tác viên với email này.'
            ]);
        }

        // Lấy thông tin cơ bản của collaborator
        $collaboratorInfo = $this->getCollaboratorInfo($collaborator);

        // Lấy thông tin tài khoản giới thiệu
        $referralInfo = $this->getReferralInfo($collaborator);

        // Lấy danh sách ứng tuyển
        $submits = $this->getCollaboratorSubmits($collaborator->id);

        // Lấy lịch sử giao dịch ví
        $walletTransactions = $this->getWalletTransactions($collaborator->id);

        return response()->json([
            'success' => true,
            'data' => [
                'collaborator' => $collaboratorInfo,
                'referral_info' => $referralInfo,
                'submits' => $submits,
                'wallet_transactions' => $walletTransactions
            ]
        ]);
    }

    /**
     * Lấy thông tin cơ bản của cộng tác viên
     */
    private function getCollaboratorInfo($collaborator)
    {
        $totalSubmits = SubmitCv::where('user_id', $collaborator->id)->count();

        return [
            'id' => $collaborator->id,
            'email' => $collaborator->email,
            'name' => $collaborator->name,
            'referral_define' => $collaborator->referral_define,
            'total_submits' => $totalSubmits,
            'created_at' => $collaborator->created_at ? $collaborator->created_at->format('d/m/Y H:i') : '',
            'last_login_at' => $collaborator->last_login_at ? $collaborator->last_login_at->format('d/m/Y H:i') : 'Chưa đăng nhập',
            'wallet_amount' => $collaborator->wallet ? number_format($collaborator->wallet->price, 0, ',', '.') : '0'
        ];
    }

    /**
     * Lấy thông tin tài khoản giới thiệu
     */
    private function getReferralInfo($collaborator)
    {
        if (empty($collaborator->referral_code)) {
            return [
                'has_referral' => false,
                'referrer_name' => '',
                'referrer_email' => '',
                'referral_code' => ''
            ];
        }

        // Tìm người giới thiệu dựa vào referral_code
        $referrer = $this->userRepository->findByReferralDefine($collaborator->referral_code);

        if (!$referrer) {
            return [
                'has_referral' => false,
                'referrer_name' => '',
                'referrer_email' => '',
                'referral_code' => $collaborator->referral_code
            ];
        }

        return [
            'has_referral' => true,
            'referrer_name' => $referrer->name,
            'referrer_email' => $referrer->email,
            'referral_code' => $collaborator->referral_code
        ];
    }

    /**
     * Lấy danh sách ứng tuyển của cộng tác viên (cho hiển thị tổng quan)
     */
    private function getCollaboratorSubmits($userId)
    {
        return SubmitCv::where('user_id', $userId)
            ->with(['job', 'submitCvMeta', 'warehouseCv'])
            ->select([
                'id',
                'job_id',
                'user_id',
                'warehouse_cv_id',
                'status',
                'bonus',
                'bonus_self_apply',
                'bonus_type',
                'is_self_apply',
                'authorize',
                'created_at'
            ])
            ->orderBy('created_at', 'desc')
            ->limit(10) // Chỉ lấy 10 record đầu tiên cho hiển thị tổng quan
            ->get()
            ->map(function ($submit) {
                $candidateName = '';
                $candidateEmail = '';
                $jobName = '';

                // Lấy thông tin job
                if ($submit->job) {
                    $jobName = $submit->job->name;
                }

                // Lấy thông tin ứng viên từ submitCvMeta hoặc warehouseCv
                if ($submit->submitCvMeta) {
                    $candidateName = $submit->submitCvMeta->candidate_name;
                    $candidateEmail = $submit->submitCvMeta->candidate_email;
                } elseif ($submit->warehouseCv) {
                    $candidateName = $submit->warehouseCv->candidate_name;
                    $candidateEmail = $submit->warehouseCv->candidate_email;
                }

                return [
                    'id' => $submit->id,
                    'job_name' => $jobName,
                    'candidate_name' => $candidateName,
                    'candidate_email' => $candidateEmail,
                    'status' => $submit->status_value ?? '',
                    'bonus_type' => $submit->bonus_type,
                    'bonus' => number_format($submit->getSubmitBonusForCtv(), 0, ',', '.'),
                    'is_self_apply' => $submit->is_self_apply ? 'Tự ứng tuyển' : 'Giới thiệu',
                    'created_at' => $submit->created_at ? $submit->created_at->format('d/m/Y H:i') : '',
                ];
            });
    }

    /**
     * Lấy danh sách ứng tuyển với phân trang và tìm kiếm
     */
    public function getSubmitsPaginated(Request $request)
    {
        $userId = $request->input('user_id');
        $search = $request->input('search', '');
        $perPage = $request->input('per_page', 20);

        $query = SubmitCv::where('user_id', $userId)
            ->with(['job', 'submitCvMeta', 'warehouseCv'])
            ->select([
                'id',
                'job_id',
                'user_id',
                'warehouse_cv_id',
                'status',
                'bonus',
                'bonus_self_apply',
                'bonus_type',
                'is_self_apply',
                'authorize',
                'created_at'
            ]);

        // Tìm kiếm theo tên/email ứng viên
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('submitCvMeta', function ($subQuery) use ($search) {
                    $subQuery->where('candidate_name', 'like', '%' . $search . '%')
                        ->orWhere('candidate_email', 'like', '%' . $search . '%');
                })
                    ->orWhereHas('warehouseCv', function ($subQuery) use ($search) {
                        $subQuery->where('candidate_name', 'like', '%' . $search . '%')
                            ->orWhere('candidate_email', 'like', '%' . $search . '%');
                    });
            });
        }

        $submits = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        $data = $submits->getCollection()->map(function ($submit) {
            $candidateName = '';
            $candidateEmail = '';
            $jobName = '';

            // Lấy thông tin job
            if ($submit->job) {
                $jobName = $submit->job->name;
            }

            // Lấy thông tin ứng viên từ submitCvMeta hoặc warehouseCv
            if ($submit->submitCvMeta) {
                $candidateName = $submit->submitCvMeta->candidate_name;
                $candidateEmail = $submit->submitCvMeta->candidate_email;
            } elseif ($submit->warehouseCv) {
                $candidateName = $submit->warehouseCv->candidate_name;
                $candidateEmail = $submit->warehouseCv->candidate_email;
            }

            return [
                'id' => $submit->id,
                'job_name' => $jobName,
                'candidate_name' => $candidateName,
                'candidate_email' => $candidateEmail,
                'status' => $submit->status_value ?? '',
                'bonus_type' => $submit->bonus_type,
                'bonus' => number_format($submit->getSubmitBonusForCtv(), 0, ',', '.'),
                'is_self_apply' => $submit->is_self_apply ? 'Tự ứng tuyển' : 'Giới thiệu',
                'created_at' => $submit->created_at ? $submit->created_at->format('d/m/Y H:i') : '',
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $submits->currentPage(),
                'per_page' => $submits->perPage(),
                'total' => $submits->total(),
                'last_page' => $submits->lastPage(),
                'from' => $submits->firstItem(),
                'to' => $submits->lastItem(),
                'has_more_pages' => $submits->hasMorePages()
            ]
        ]);
    }

    /**
     * Lấy lịch sử giao dịch ví
     */
    private function getWalletTransactions($userId)
    {
        $user = User::find($userId);
        if (!$user || !$user->wallet) {
            return [
                'wallet_id' => null,
                'transactions' => []
            ];
        }

        $transactions = WalletTransaction::where('wallet_id', $user->wallet->id)
            ->orderBy('created_at', 'desc')
            ->limit(50) // Giới hạn 50 giao dịch gần nhất
            ->get()
            ->map(function ($transaction) {
                $jobTitle = '';
                $candidateName = '';
                $candidateEmail = '';

                // Xử lý thông tin dựa trên loại object
                if ($transaction->object) {
                    if ($transaction->object instanceof SubmitCv) {
                        // Lấy thông tin từ SubmitCv
                        $jobTitle = $transaction->object->job ? $transaction->object->job->name : '';

                        // Lấy thông tin ứng viên từ submitCvMeta hoặc warehouseCv
                        if ($transaction->object->submitCvMeta) {
                            $candidateName = $transaction->object->submitCvMeta->candidate_name;
                            $candidateEmail = $transaction->object->submitCvMeta->candidate_email;
                        } elseif ($transaction->object->warehouseCv) {
                            $candidateName = $transaction->object->warehouseCv->candidate_name;
                            $candidateEmail = $transaction->object->warehouseCv->candidate_email;
                        }
                    } elseif ($transaction->object instanceof \App\Models\WareHouseCvSellingBuy) {
                        // Lấy thông tin từ WareHouseCvSellingBuy
                        if ($transaction->object->wareHouseCvSelling && $transaction->object->wareHouseCvSelling->wareHouseCv) {
                            $candidateName = $transaction->object->wareHouseCvSelling->wareHouseCv->candidate_name;
                            $candidateEmail = $transaction->object->wareHouseCvSelling->wareHouseCv->candidate_email;
                        }
                        $jobTitle = 'Mua CV từ kho';
                    }
                }

                return [
                    'id' => $transaction->id,
                    'amount' => number_format($transaction->amount, 0, ',', '.'),
                    'balance_after' => number_format($transaction->balance_after, 0, ',', '.'),
                    'description' => $transaction->note ?? '',
                    'job_title' => $jobTitle,
                    'candidate_name' => $candidateName,
                    'candidate_email' => $candidateEmail,
                    'created_at' => $transaction->created_at ? $transaction->created_at->format('d/m/Y H:i') : '',
                    'type' => $transaction->amount >= 0 ? 'Nhận tiền' : 'Trừ tiền'
                ];
            });

        return [
            'wallet_id' => $user->wallet->id,
            'transactions' => $transactions
        ];
    }
}
