# SRS - F01.3: Mua CV Onboard từ Marketplace

**<PERSON><PERSON><PERSON> bản:** 6.0
**Ngày:** 2025-07-08
**T<PERSON><PERSON> gi<PERSON>:** Gemini

---

### 1. <PERSON><PERSON> tả

Đây là giai đoạn cuối của luồng Marketplace, xử lý các bước sau phỏng vấn. NTD cập nhật trạng thái của ứng viên, và hệ thống sẽ tự động xử lý các giao dịch thanh toán phí Onboard (bằng **Point**) và trả hoa hồng (bằng **Price**) cho CTV theo nhiều giai đoạn. Logic được điều phối bởi `WareHouseCvSellingBuyService` và Job `PayOnboard`.

### 2. Đ<PERSON>i tượng tham gia

*   **Nhà tuyển dụng (NTD):** Ngư<PERSON><PERSON> cập nhật trạng thái, tr<PERSON> phí.
*   **<PERSON><PERSON><PERSON> tá<PERSON> viên (CTV):** <PERSON><PERSON><PERSON><PERSON> nhận hoa hồng.
*   **Hệ thống:** Tự động hóa thanh toán.

### 3. Điều kiện tiên quyết

*   Quy trình đang ở trạng thái **7 (Chờ phỏng vấn)** hoặc **8 (Đồng ý phỏng vấn)**.
*   Ví của NTD có đủ số dư Point cho các khoản phí Onboard.

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (Pass phỏng vấn):** NTD cập nhật trạng thái ứng viên thành **8 (Pass phỏng vấn)** hoặc **10 (Fail phỏng vấn)**. Service chỉ cập nhật trạng thái và ghi log lịch sử.

2.  **Bước 2 (Bắt đầu thử việc - `Trial work`):** NTD cập nhật trạng thái thành **14 (Trial work - Thử việc)**. Hành động này kích hoạt một chuỗi xử lý trong `WareHouseCvSellingBuyService`:
    *   **a. Kiểm tra số dư ví NTD:** Hệ thống sẽ kiểm tra số dư ví của NTD để đảm bảo đủ tiền thanh toán phần còn lại của phí "Onboard" (90% giá trị, vì 10% có thể đã được đặt cọc trước đó).
    *   **b. Xử lý ghi nợ:** Nếu số dư không đủ, hệ thống sẽ ghi nhận khoản nợ vào bảng `PaymentDebit` cho NTD. NTD sẽ nhận được chuỗi thông báo nhắc nhở thanh toán nợ (email nhắc nhở sau 24 giờ, sau đó lặp lại từ ngày thứ 10 đến ngày thứ 15).
    *   **c. Trừ tiền từ ví NTD (nếu đủ):** Nếu số dư đủ, hệ thống sẽ trừ 90% giá trị "Onboard" từ ví của NTD. Giao dịch trừ tiền sẽ được ghi lại chi tiết.
    *   **d. Cập nhật trạng thái và lịch sử:** Trạng thái của `WareHouseCvSellingBuy` sẽ được cập nhật thành `14` ("Thử việc"). Lịch sử thay đổi trạng thái sẽ được ghi lại.
    *   **e. Thông báo:** Admin sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc (nếu CV thuộc "authority"). CTV sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc.
    *   **f. Nhắc nhở hết hạn thử việc:** Hệ thống sẽ gửi email nhắc nhở NTD về việc sắp hết hạn thử việc của ứng viên, bắt đầu từ ngày thứ 55 đến ngày thứ 59 của giai đoạn thử việc.
    *   **g. Tự động chuyển trạng thái "Tuyển dụng thành công":** Nếu NTD không thay đổi trạng thái của ứng viên sau 67 ngày kể từ ngày bắt đầu thử việc, hệ thống sẽ tự động cập nhật trạng thái thành `16` ("Tuyển dụng thành công"). Job `SuccessRecuitment` sẽ được kích hoạt sau 67 ngày.
    *   **h. Lên lịch thanh toán hoa hồng cho CTV theo giai đoạn (Job `PayOnboard`):**
        *   **Lần 1 (15%):** Job `PayOnboard` sẽ được kích hoạt sau 30 ngày kể từ ngày bắt đầu thử việc để thanh toán 15% hoa hồng cho CTV.
        *   **Lần 2 (10%):** Job `PayOnboard` sẽ được kích hoạt sau 45 ngày kể từ ngày bắt đầu thử việc để thanh toán 10% hoa hồng cho CTV.
        *   **Lần 3 (75%):** Job `PayOnboard` sẽ được kích hoạt sau 67 ngày kể từ ngày bắt đầu thử việc để thanh toán 75% hoa hồng còn lại cho CTV (khi ứng viên đã ký hợp đồng chính thức).

3.  **Bước 3 (Job `PayOnboard` thực thi):**
    *   **a. Kiểm tra điều kiện:** Job `PayOnboard` được worker thực thi. Nó kiểm tra lại `status_recruitment` phải là 14 hoặc 16 và `status_complain` phải hợp lệ (không có khiếu nại).
    *   **b. Xử lý thanh toán:** Nếu hợp lệ, job gọi phương thức `payOnboardCtv` trong `WareHouseCvSellingBuyService`. Service này sẽ:
        *   Cộng phần trăm hoa hồng tương ứng vào ví **Price** của CTV (tính trên tổng giá trị hoa hồng). *Lưu ý: Nếu `wareHouseCvSelling->authority == 2`, tổng hoa hồng CTV nhận được sẽ là 20% của `wareHouseCvSellingBuy->price`, và các phần trăm trên sẽ được tính trên 20% đó.*
        *   Ghi bản ghi tương ứng vào `payin_months` và `wallet_transactions`.

4.  **Bước 4 (Kết thúc):** Quy trình được xem là hoàn tất. `status_payment` trong `warehouse_cv_selling_buys` được cập nhật thành **4 (NTD Đã thanh toán)** khi NTD thanh toán đủ hoặc **3 (Hoàn tiền)** nếu có hoàn tiền.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Ứng viên Fail phỏng vấn (Status 10) hoặc Từ chối Offer (Status 12):**
    *   **Nguyên nhân:** NTD cập nhật trạng thái thành 10 hoặc 12.
    *   **Xử lý:** Service cập nhật trạng thái và dispatch job **`DepositRefundRejectOffer`** để **hoàn lại phí Interview** đã trừ trước đó cho NTD. Quy trình kết thúc.

*   **5.2. Ứng viên Thất bại thử việc (Status 17):**
    *   **Nguyên nhân:** NTD cập nhật trạng thái thành 17 sau khi đã ở trạng thái 14 (Trial Work).
    *   **Xử lý:** Service cập nhật trạng thái và ghi log. Hệ thống sẽ hoàn lại một phần phí Onboard cho NTD dựa trên thời gian thử việc (100% cho 0-30 ngày, 70% cho 31-60 ngày, 50% cho 61-67 ngày). Khoản phí 10% đã trả ở Bước 3 sẽ **không** được hoàn lại. Không có khoản phí nào khác bị trừ.

*   **5.3. Lỗi không đủ Point:**
    *   **Nguyên nhân:** Tại thời điểm job `PayOnboard` thực thi (Bước 3 hoặc 5), ví của NTD không đủ Point.
    *   **Xử lý:** Phương thức trong `WalletService` sẽ ném ra Exception. Job `PayOnboard` sẽ thất bại. Hệ thống cần có cơ chế theo dõi (failed jobs) để Admin có thể thấy lỗi, thông báo cho NTD nạp tiền và thực hiện chạy lại job theo cách thủ công.

*   **5.4. NTD khiếu nại trước khi Job thực thi:**
    *   **Nguyên nhân:** NTD cập nhật trạng thái thành công (ví dụ: 14), job `PayOnboard` được dispatch. Nhưng trước khi worker thực thi job, NTD vào hệ thống và tạo khiếu nại (`status_complain` = 1).
    *   **Xử lý:** Tại **Bước 3a** hoặc **5a**, job `PayOnboard` sẽ kiểm tra `status_complain` và thấy có khiếu nại. Job sẽ kết thúc ngay lập tức mà không xử lý thanh toán, ngăn việc trả tiền cho CTV khi đang có tranh chấp.

### 6. Yêu cầu phi chức năng

*   **Độ tin cậy của Job:** Cần có hệ thống theo dõi (monitoring) các job bị lỗi (failed jobs), đặc biệt là `PayOnboard`, để Admin có thể can thiệp và chạy lại khi cần thiết.
*   **Linh hoạt:** Hệ thống nên cho phép Admin cấu hình các mốc phần trăm thanh toán (15/10/75) trong file config.

### 7. Mô hình dữ liệu liên quan

*   **Models:** `WarehouseCvSellingBuy`, `WarehouseCvSellingBuyHistoryStatus`, `Wallet`, `WalletTransaction`, `WareHouseCvSellingHistoryBuy`, `WarehouseCvSellingBuyOnboard`.
*   **Repositories:** `WareHouseCvSellingBuyRepository`, `WalletRepository`, `WareHouseCvSellingBuyHistoryStatusRepository`, `WareHouseCvSellingHistoryBuyRepository`, `WareHouseCvSellingBuyOnboardRepository`, `PaymentDebitRepository`.
*   **Services:** `WareHouseCvSellingBuyService`, `WalletService`.
*   **Controller:** `WareHouseCvSellingBuyController` (Frontend).

### 8. Các Job liên quan (Background Processes)

*   **`ChangeToRejectOffer`**
    *   **Mô tả:** Xử lý khi ứng viên từ chối lời mời làm việc (Reject Offer) trong luồng Marketplace. Job này cập nhật trạng thái của `warehouse_cv_selling_buys` và dispatch job hoàn tiền.
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành **12 (Reject Offer)**.
    *   **Happy Path:** Nếu `warehouse_cv_selling_buy->status_recruitment` là 11 (Offering), job sẽ:
        *   Cập nhật trạng thái của `onboard` (liên quan đến `wareHouseCvSellingBuyOnboardRepository`) thành 2.
        *   Cập nhật `status_recruitment` của `warehouse_cv_selling_buys` thành **12 (Reject Offer)**.
        *   Ghi log lịch sử trạng thái vào `warehouse_cv_selling_buy_history_status`.
        *   Gửi thông báo cho NTD (`EmailRejectOnboard`).
        *   Nếu CV có `authority > 0`, gửi thông báo cho Admin (`ChangeStatusCancelOnboardToAdmin`).
        *   Nếu CV không có `authority > 0`, gửi thông báo cho CTV (`ChangeStatusCancelOnboardToRec`).
        *   Dispatch job **`DepositRefundRejectOffer`** với độ trễ (48 giờ) để hoàn tiền cho NTD.
    *   **Sad Path / Xử lý lỗi:** Nếu `warehouse_cv_selling_buy->status_recruitment` không phải là 11, job sẽ không thực hiện hành động nào.

*   **`PayOnboard`**
    *   **Mô tả:** Xử lý việc thanh toán hoa hồng Onboard cho CTV, theo các phần trăm được chỉ định (15%, 10%, 75%).
    *   **Khi nào được dispatch:** Khi `warehouse_cv_selling_buys` chuyển trạng thái sang 14 (Trial work) hoặc 16 (Success Recruitment).
    *   **Happy Path:** Nếu `warehouse_cv_selling_buy->status_recruitment` là 14 hoặc 16 VÀ `status_complain` là 0 (Không khiếu nại) hoặc 5 (Admin từ chối khiếu nại), job sẽ gọi `WareHouseCvSellingBuyService->payOnboardCtv()` để cộng tiền vào ví CTV và ghi log giao dịch cho phần trăm được chỉ định.
        *   **Mức thanh toán:**
            *   **Đợt 1 (15%):** Khi `status_recruitment` = 14 (Trial work).
            *   **Đợt 2 (10%):** Khi `status_recruitment` = 16 (Success Recruitment), sau 45 ngày thử việc.
            *   **Đợt 3 (75%):** Khi `status_recruitment` = 16 (Success Recruitment), sau 67 ngày thử việc.
        *   **Lưu ý:** Nếu `wareHouseCvSelling->authority == 2`, tổng hoa hồng CTV nhận được sẽ là 20% của `wareHouseCvSellingBuy->price`, và các phần trăm trên sẽ được tính trên 20% đó.
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không hợp lệ hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thanh toán. Lỗi trong quá trình thanh toán sẽ khiến job thất bại và có thể được thử lại.

*   **`DepositRefundRejectOffer`**
    *   **Mô tả:** Hoàn lại Point cho NTD khi một giao dịch bị hủy hoặc từ chối (ví dụ: ứng viên Fail phỏng vấn, từ chối Offer).
    *   **Khi nào được dispatch:** Khi một sự kiện hủy/từ chối xảy ra sau khi NTD đã trả phí (ví dụ: NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành 10, 12, 15, hoặc 17).
    *   **Happy Path:** Job tìm các bản ghi thanh toán trước đó (`WareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus`), đánh dấu chúng là đã hoàn tiền, tạo các bản ghi log hoàn tiền mới, cập nhật `status_payment` của `warehouse_cv_selling_buys` thành 2 (Hoàn cọc) hoặc 3 (Hoàn tiền), và cộng Point vào ví của NTD thông qua `WalletService`.
    *   **Sad Path / Xử lý lỗi:** Nếu không tìm thấy bản ghi thanh toán nào để hoàn tiền hoặc nếu đã được hoàn tiền trước đó, job sẽ không thực hiện hành động hoàn tiền. Lỗi trong quá trình hoàn tiền sẽ khiến job thất bại và có thể được thử lại.

*   **`SuccessRecuitment`**
    *   **Mô tả:** Tự động cập nhật trạng thái ứng viên thành "Tuyển dụng thành công" nếu NTD không cập nhật sau một khoảng thời gian nhất định.
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành **14 (Trial work)**.
    *   **Happy Path:** Nếu `warehouse_cv_selling_buy->status_recruitment` là 14 ("Trial work") và đã quá 67 ngày kể từ ngày bắt đầu thử việc, job sẽ cập nhật `status_recruitment` thành 16 ("Success Recruitment"), ghi log lịch sử trạng thái, và gửi thông báo cho Admin (nếu ủy quyền) hoặc CTV.
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không phải là 14 hoặc chưa đủ thời gian, job sẽ không thực hiện thay đổi trạng thái.

### 9. Các thông báo liên quan

*   **`EmailConfirmOnboard` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên đã đồng ý lời mời làm việc (onboard).
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, thời gian phỏng vấn (từ `wareHouseCvSellingBuyBook`), link đến trang quản lý CV đã mua.
    *   **Kênh:** Mail.
*   **`EmailRejectOnboard` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên đã từ chối lời mời làm việc (onboard).
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, thời gian phỏng vấn, link đến trang quản lý CV đã mua và marketplace.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailIInterview` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên Fail phỏng vấn.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailIInterviewToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên Fail phỏng vấn (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusCancelOnboardToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên hủy onboard (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên ứng viên, công ty, vị trí, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusCancelOnboardToEmployer` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên hủy onboard.
    *   **Nội dung chính:** Tên ứng viên, tên NTD, vị trí, link đến trang CV đã mua, ví, marketplace, và số Point được hoàn lại.
    *   **Kênh:** Mail.
*   **`ChangeStatusCancelOnboardToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên hủy onboard.
    *   **Nội dung chính:** Tên ứng viên, công ty, vị trí, tên CTV, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailTrialWorkToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên Fail thử việc (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, vị trí, công ty, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailTrialWorkToEmployer` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên Fail thử việc.
    *   **Nội dung chính:** Tên NTD, tên ứng viên, công ty, vị trí, link đến trang CV đã mua, ví, marketplace, và số Point được hoàn lại.
    *   **Kênh:** Mail.
*   **`ChangeStatusFailTrialWorkToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên Fail thử việc.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, vị trí, công ty, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`ChangeStatusPassInterview` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên Pass phỏng vấn.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`ChangeStatusPassInterviewAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên Pass phỏng vấn (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty, vị trí, loại hình, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusSuccessRecruitmentToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên thử việc thành công (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, vị trí, công ty, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusSuccessRecruitmentToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên thử việc thành công.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, vị trí, công ty, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`ChangeStatusTrailWorkToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên bắt đầu thử việc (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên ứng viên, công ty, vị trí, loại hình, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusTrailWorkToAuthorityRec` (gửi cho Admin/Authority Rec):**
    *   **Mô tả:** Thông báo ứng viên bắt đầu thử việc.
    *   **Nội dung chính:** Tên ứng viên, công ty, tên CTV, vị trí.
    *   **Kênh:** Mail.
*   **`ChangeStatusTrailWorkToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên bắt đầu thử việc.
    *   **Nội dung chính:** Tên ứng viên, công ty, tên CTV, vị trí, link chi tiết.
    *   **Kênh:** Mail.
*   **`RemindExpireTrailWork` (gửi cho NTD):**
    *   **Mô tả:** Nhắc nhở NTD cập nhật kết quả thử việc.
    *   **Nội dung chính:** Tên ứng viên, tên NTD, vị trí, số ngày còn lại của thử việc, link cập nhật trạng thái.
    *   **Kênh:** Mail.
*   **`RemindPaymentDebit` (gửi cho NTD):**
    *   **Mô tả:** Nhắc nhở NTD thanh toán các khoản nợ (khi không đủ Point).
    *   **Nội dung chính:** Tên ứng viên, vị trí, tên NTD, số Point còn nợ, link đến ví NTD.
    *   **Kênh:** Mail.
*   **`PaymentTrialWork` (gửi cho CTV):**
    *   **Mô tả:** Thông báo thanh toán hoa hồng thử việc theo đợt.
    *   **Nội dung chính:** Tên ứng viên, loại hình, tên CTV, vị trí, công ty, link đến trang CV đã bán, doanh thu, số đợt, số tiền, phần trăm, tổng giá trị.
    *   **Kênh:** Mail.