<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;

class RecRefuseComplainSubmit extends Mailable
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$submitCv)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $candidateName = '';
        $position      = '';
        $type          = '';
        $content       = $this->submitCv->txt_complain;
        $image         = $this->submitCv->img_complain;
        $companyName   = $this->employer->name;

        $type = $this->submitCv->bonus_type;
        if (!empty($this->submitCv->warehouseCv)){
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->submitCv->warehouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
                $position = $this->submitCv->job->name;
            }
        }
        $recName = $this->submitCv->rec->name;
        // $url = route('luot-ban.show',['luot_ban' => $this->submitCv->id,'tab' => 'khieunai']);
        $url = route('submit-cv.edit', ['id' => $this->submitCv->id]) . '?tab=khieunai';

        return new Content(
            view: 'email.ctv_tuchoi',
            with: [
                'name'          => $this->rec->name,
                'recName'       => $recName,
                'companyName'   => $companyName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'type'          => $type,
                'content'       => $content,
                'recId'         => $this->rec->id,
                'image'         => gen_url_file_s3($image),
                'url'           => $url,
                'buyId'         => $this->submitCv->id,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $message->subject('[Recland][Case #'.$this->submitCv->id.'] Thông báo khiếu nại cần xử lý');
        return $this;
    }

}
