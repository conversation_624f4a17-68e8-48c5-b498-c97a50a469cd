<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerSendSubmitDiscusToRec extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $submitDiscuss;

    public function __construct($submitCv, $submitDiscuss)
    {
        $this->submitCv = $submitCv;
        $this->submitDiscuss = $submitDiscuss;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $recName       = $this->submitCv->rec->name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $link           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id, 'open_comment' => 1]);

        return (new MailMessage)
            ->view('email.employerSendDiscusToRecSubmit', [
                'link'          => $link,
                'recName'       => $recName,
                'position'      => $position,
                'candidateName' => $candidateName,
                'comment'       => $this->submitDiscuss->comment,
            ])
            ->subject('[Recland] Bạn nhận được 1 thảo luận mới ứng viên ' . $candidateName);
    }
}
