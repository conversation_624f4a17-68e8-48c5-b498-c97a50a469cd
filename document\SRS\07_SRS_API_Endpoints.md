# Tài liệu Đặc tả API Endpoints - RecLand

**<PERSON><PERSON><PERSON> bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> gi<PERSON>:** AI Assistant

---

## 1. Tổng quan API System

### 1.1. API Architecture

RecLand cung cấp RESTful API với các đặc điểm:

- **Base URL:** `https://domain.com/api/`
- **Authentication:** Laravel Sanctum + API Key
- **Response Format:** JSON
- **HTTP Methods:** GET, POST, PUT, DELETE
- **Rate Limiting:** 60 requests/minute (configurable)
- **Versioning:** Implicit versioning (future enhancement)

### 1.2. API Categories

| Category | Endpoints | Authentication | Description |
|----------|-----------|----------------|-------------|
| **Public APIs** | 8 endpoints | None/API Key | Job listing, company search |
| **Authenticated APIs** | 12 endpoints | Sanctum Token | User-specific operations |
| **External Integration** | 6 endpoints | API Key | Third-party integrations |
| **Internal APIs** | 15 endpoints | Session Auth | AJAX calls from frontend |

---

## 2. Authentication & Authorization

### 2.1. Authentication Methods

#### 2.1.1. Laravel Sanctum (User Authentication)

```http
Authorization: Bearer {token}
```

**Usage:** User-specific operations, protected resources

#### 2.1.2. API Key Authentication

```http
X-API-Key: {api_key}
```

**Usage:** External integrations, third-party access

#### 2.1.3. Session Authentication

**Usage:** Internal AJAX calls from authenticated web sessions

### 2.2. Rate Limiting

```php
// Default rate limiting
'throttle:api' => 60 requests per minute per IP

// Custom rate limiting for specific endpoints
'throttle:api-heavy' => 10 requests per minute per IP
'throttle:api-light' => 120 requests per minute per IP
```

---

## 3. Public APIs (No Authentication Required)

### 3.1. Job Listing APIs

#### 3.1.1. GET /api/job/common

**Description:** Lấy danh sách công việc phổ biến

**Request:**
```http
GET /api/job/common
Content-Type: application/json
Accept-Language: vi|en
```

**Query Parameters:**
```json
{
  "limit": 20,
  "skill": "php,javascript",
  "location": "ho-chi-minh",
  "salary_min": 1000,
  "salary_max": 3000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "id": 123,
        "name": "Senior PHP Developer",
        "slug": "senior-php-developer-123",
        "company": {
          "id": 45,
          "name": "ABC Company",
          "logo": "https://s3.amazonaws.com/logos/abc.png"
        },
        "salary_min": 1500,
        "salary_max": 2500,
        "salary_currency": "USD",
        "location": "Ho Chi Minh City",
        "skills": ["PHP", "Laravel", "MySQL"],
        "created_at": "2025-01-09T10:00:00Z",
        "expire_at": "2025-02-09"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 100,
      "per_page": 20
    }
  }
}
```

#### 3.1.2. GET /api/job/detail/{slug}

**Description:** Lấy chi tiết công việc

**Request:**
```http
GET /api/job/detail/senior-php-developer-123
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "Senior PHP Developer",
    "slug": "senior-php-developer-123",
    "jd": "Job description content...",
    "company": {
      "id": 45,
      "name": "ABC Company",
      "logo": "https://s3.amazonaws.com/logos/abc.png",
      "address": ["123 Main St, District 1, HCMC"],
      "about": "Company description..."
    },
    "requirements": {
      "skills": ["PHP", "Laravel", "MySQL"],
      "experience": "3+ years",
      "rank": "Senior"
    },
    "benefits": {
      "salary_min": 1500,
      "salary_max": 2500,
      "salary_currency": "USD",
      "bonus_type": "onboard",
      "bonus": 1000,
      "remote": true
    },
    "meta": {
      "views": 1250,
      "applications": 45,
      "created_at": "2025-01-09T10:00:00Z",
      "expire_at": "2025-02-09"
    }
  }
}
```

### 3.2. Company APIs

#### 3.2.1. POST /api/company/search

**Description:** Tìm kiếm công ty

**Request:**
```http
POST /api/company/search
Content-Type: application/json
```

**Body:**
```json
{
  "keyword": "technology",
  "location": "ho-chi-minh",
  "scale": "100-500",
  "industry": "software",
  "page": 1,
  "limit": 20
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "companies": [
      {
        "id": 45,
        "name": "ABC Technology",
        "slug": "abc-technology",
        "logo": "https://s3.amazonaws.com/logos/abc.png",
        "scale": "100-500 employees",
        "address": ["123 Main St, District 1, HCMC"],
        "website": "https://abc.com",
        "job_count": 15,
        "priority_career": "Software Development"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_items": 60,
      "per_page": 20
    }
  }
}
```

#### 3.2.2. POST /api/company/detail/{slug}

**Description:** Chi tiết công ty

**Request:**
```http
POST /api/company/detail/abc-technology
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 45,
    "name": "ABC Technology",
    "slug": "abc-technology",
    "logo": "https://s3.amazonaws.com/logos/abc.png",
    "banner": "https://s3.amazonaws.com/banners/abc.png",
    "about": "Company description...",
    "scale": "100-500 employees",
    "website": "https://abc.com",
    "address": ["123 Main St, District 1, HCMC"],
    "images": [
      "https://s3.amazonaws.com/images/office1.jpg"
    ],
    "videos": [
      "https://youtube.com/watch?v=abc123"
    ],
    "active_jobs": [
      {
        "id": 123,
        "name": "Senior PHP Developer",
        "salary_min": 1500,
        "salary_max": 2500,
        "created_at": "2025-01-09T10:00:00Z"
      }
    ],
    "stats": {
      "total_jobs": 25,
      "active_jobs": 15,
      "total_applications": 450
    }
  }
}
```

---

## 4. External Integration APIs (API Key Required)

### 4.1. Search APIs

#### 4.1.1. GET /api/company/search

**Description:** Tìm kiếm công ty cho hệ thống bên ngoài

**Headers:**
```http
X-API-Key: your-api-key-here
Content-Type: application/json
```

**Query Parameters:**
```json
{
  "q": "technology company",
  "limit": 50,
  "fields": "id,name,address,scale"
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 45,
      "name": "ABC Technology",
      "address": ["123 Main St, District 1, HCMC"],
      "scale": "100-500 employees"
    }
  ],
  "meta": {
    "total": 150,
    "limit": 50,
    "api_version": "2.0"
  }
}
```

#### 4.1.2. GET /api/employer/search

**Description:** Tìm kiếm nhà tuyển dụng

**Headers:**
```http
X-API-Key: your-api-key-here
```

**Query Parameters:**
```json
{
  "company_id": 45,
  "active_only": true,
  "with_jobs": true
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>",
      "company": {
        "id": 45,
        "name": "ABC Technology"
      },
      "active_jobs_count": 5,
      "last_login": "2025-01-09T08:30:00Z"
    }
  ]
}
```

### 4.2. Job Management APIs

#### 4.2.1. POST /api/job/create

**Description:** Tạo tin tuyển dụng từ hệ thống bên ngoài

**Headers:**
```http
X-API-Key: your-api-key-here
Content-Type: application/json
```

**Body:**
```json
{
  "employer_id": 123,
  "company_id": 45,
  "name": "Senior PHP Developer",
  "jd": "Job description content...",
  "address": [
    {
      "city": "Ho Chi Minh City",
      "district": "District 1",
      "address": "123 Main Street"
    }
  ],
  "salary_min": 1500,
  "salary_max": 2500,
  "salary_currency": "USD",
  "skills": ["PHP", "Laravel", "MySQL"],
  "rank": "Senior",
  "bonus_type": "onboard",
  "bonus": 1000,
  "expire_at": "2025-02-09",
  "remote": true,
  "urgent": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 456,
    "slug": "senior-php-developer-456",
    "status": "active",
    "created_at": "2025-01-09T10:00:00Z",
    "expire_at": "2025-02-09T23:59:59Z"
  },
  "message": "Job created successfully"
}
```

### 4.3. Reference Data APIs

#### 4.3.1. GET /api/career

**Description:** Lấy danh sách ngành nghề

**Headers:**
```http
X-API-Key: your-api-key-here
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Information Technology",
      "slug": "information-technology",
      "job_count": 1250
    },
    {
      "id": 2,
      "name": "Marketing",
      "slug": "marketing", 
      "job_count": 450
    }
  ]
}
```

#### 4.3.2. GET /api/job-type

**Description:** Lấy danh sách loại công việc

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "value": "full-time",
      "label": "Full-time"
    },
    {
      "value": "part-time", 
      "label": "Part-time"
    },
    {
      "value": "contract",
      "label": "Contract"
    },
    {
      "value": "internship",
      "label": "Internship"
    }
  ]
}
```

---

## 5. Authenticated APIs (Sanctum Token Required)

### 5.1. User Management APIs

#### 5.1.1. GET /api/user

**Description:** Lấy thông tin user hiện tại

**Headers:**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>",
    "type": "employer",
    "avatar": "https://s3.amazonaws.com/avatars/john.jpg",
    "company": {
      "id": 45,
      "name": "ABC Technology"
    },
    "wallet": {
      "point": 1500,
      "amount": 2500.50
    },
    "last_login_at": "2025-01-09T08:30:00Z",
    "email_verified_at": "2025-01-01T10:00:00Z"
  }
}
```

#### 5.1.2. POST /api/user-job-care/change

**Description:** Thay đổi trạng thái quan tâm công việc

**Headers:**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**Body:**
```json
{
  "job_id": 123,
  "action": "add" // or "remove"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "is_caring": true,
    "total_cares": 45
  },
  "message": "Job care status updated successfully"
}
```

---

## 6. CV Management APIs

### 6.1. CV Selling APIs

#### 6.1.1. POST /api/selling-cv/create

**Description:** Tạo CV để bán trên marketplace

**Headers:**
```http
Content-Type: application/json
```

**Body:**
```json
{
  "warehouse_cv_id": 456,
  "type_of_sale": "onboard",
  "skill": "PHP,Laravel,MySQL",
  "candidate_salary_expect": 2000,
  "price": 1000,
  "point": 1000,
  "exclude_company": "45,67,89",
  "authority": 0,
  "level": 2
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 789,
    "warehouse_cv_id": 456,
    "type_of_sale": "onboard",
    "price": 1000,
    "point": 1000,
    "status": 0,
    "created_at": "2025-01-09T10:00:00Z"
  },
  "message": "CV selling created successfully"
}
```

### 6.2. Rec Team APIs

#### 6.2.1. GET /api/rec/apply-list-in-team

**Description:** Lấy danh sách ứng tuyển trong team (cho CTV)

**Headers:**
```http
Authorization: Bearer {token}
```

**Query Parameters:**
```json
{
  "team_id": 123,
  "status": "pending",
  "page": 1,
  "limit": 20
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "submit_cvs": [
      {
        "id": 789,
        "code": "SUB-2025-001",
        "candidate_name": "Jane Smith",
        "job": {
          "id": 123,
          "name": "Senior PHP Developer",
          "company": "ABC Technology"
        },
        "status": 0,
        "status_text": "Pending Review",
        "bonus": 500,
        "created_at": "2025-01-09T10:00:00Z"
      }
    ],
    "jobs": [
      {
        "id": 123,
        "name": "Senior PHP Developer",
        "applications_count": 15
      }
    ],
    "team_members": [
      {
        "id": 456,
        "name": "Team Member 1",
        "applications_count": 5
      }
    ]
  }
}
```

---

## 7. Internal AJAX APIs

### 7.1. Job Management AJAX

#### 7.1.1. GET /ajax/list-job

**Description:** Lấy danh sách job cho dropdown/autocomplete

**Authentication:** Session-based

**Query Parameters:**
```json
{
  "q": "php developer",
  "company_id": 45,
  "active_only": true,
  "limit": 10
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 123,
      "text": "Senior PHP Developer - ABC Technology",
      "company": "ABC Technology",
      "salary": "$1500-$2500",
      "status": "active"
    }
  ]
}
```

#### 7.1.2. GET /ajax/ajax-get-job

**Description:** Lấy thông tin job cụ thể

**Query Parameters:**
```json
{
  "job_id": 123
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "Senior PHP Developer",
    "company_id": 45,
    "company_name": "ABC Technology",
    "bonus_type": "onboard",
    "bonus": 1000,
    "salary_min": 1500,
    "salary_max": 2500,
    "skills": ["PHP", "Laravel", "MySQL"],
    "expire_at": "2025-02-09"
  }
}
```

### 7.2. Company & Skill AJAX

#### 7.2.1. GET /ajax/list-company

**Description:** Lấy danh sách công ty cho autocomplete

**Query Parameters:**
```json
{
  "q": "technology",
  "limit": 10
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 45,
      "text": "ABC Technology",
      "logo": "https://s3.amazonaws.com/logos/abc.png",
      "scale": "100-500 employees"
    }
  ]
}
```

#### 7.2.2. GET /ajax/list-skill

**Description:** Lấy danh sách kỹ năng

**Query Parameters:**
```json
{
  "q": "php",
  "limit": 10
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "text": "PHP",
      "slug": "php"
    },
    {
      "id": 2,
      "text": "Laravel",
      "slug": "laravel"
    }
  ]
}
```

### 7.3. Status Update AJAX

#### 7.3.1. GET /ajax/update-status-submitcv/{id}

**Description:** Cập nhật trạng thái submit CV

**Authentication:** Session-based (Employer)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 789,
    "status": 1,
    "status_text": "Accepted",
    "updated_at": "2025-01-09T10:00:00Z"
  },
  "message": "Status updated successfully"
}
```

---

## 8. Error Handling

### 8.1. Standard Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid.",
    "details": {
      "email": ["The email field is required."],
      "password": ["The password must be at least 8 characters."]
    }
  },
  "meta": {
    "timestamp": "2025-01-09T10:00:00Z",
    "request_id": "req_123456789"
  }
}
```

### 8.2. HTTP Status Codes

| Status Code | Description | Usage |
|-------------|-------------|-------|
| **200** | OK | Successful GET, PUT requests |
| **201** | Created | Successful POST requests |
| **204** | No Content | Successful DELETE requests |
| **400** | Bad Request | Invalid request format |
| **401** | Unauthorized | Authentication required |
| **403** | Forbidden | Access denied |
| **404** | Not Found | Resource not found |
| **422** | Unprocessable Entity | Validation errors |
| **429** | Too Many Requests | Rate limit exceeded |
| **500** | Internal Server Error | Server errors |

### 8.3. Error Codes

```php
// Authentication Errors
'AUTH_TOKEN_MISSING' => 'Authentication token is required',
'AUTH_TOKEN_INVALID' => 'Invalid authentication token',
'AUTH_TOKEN_EXPIRED' => 'Authentication token has expired',
'API_KEY_MISSING' => 'API key is required',
'API_KEY_INVALID' => 'Invalid API key',

// Validation Errors
'VALIDATION_ERROR' => 'The given data was invalid',
'REQUIRED_FIELD_MISSING' => 'Required field is missing',
'INVALID_FORMAT' => 'Invalid data format',

// Business Logic Errors
'INSUFFICIENT_BALANCE' => 'Insufficient wallet balance',
'CV_ALREADY_PURCHASED' => 'CV has already been purchased',
'JOB_EXPIRED' => 'Job posting has expired',
'COMPANY_NOT_ACTIVE' => 'Company is not active',

// Rate Limiting
'RATE_LIMIT_EXCEEDED' => 'Too many requests, please try again later',

// Server Errors
'INTERNAL_ERROR' => 'An internal server error occurred',
'SERVICE_UNAVAILABLE' => 'Service temporarily unavailable'
```

---

## 9. API Versioning (Future Enhancement)

### 9.1. URL Versioning

```http
GET /api/v1/jobs
GET /api/v2/jobs
```

### 9.2. Header Versioning

```http
Accept: application/vnd.recland.v1+json
Accept: application/vnd.recland.v2+json
```

### 9.3. Version Compatibility

```php
class ApiVersionMiddleware {
    public function handle($request, Closure $next) {
        $version = $request->header('API-Version', '1.0');

        // Set version context
        app()->instance('api.version', $version);

        // Version-specific logic
        if (version_compare($version, '2.0', '<')) {
            // Legacy format
            $request->attributes->set('legacy_format', true);
        }

        return $next($request);
    }
}
```

---

## 10. API Testing

### 10.1. Postman Collection

```json
{
  "info": {
    "name": "RecLand API",
    "description": "Complete API collection for RecLand platform",
    "version": "2.0"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{access_token}}",
        "type": "string"
      }
    ]
  },
  "variable": [
    {
      "key": "base_url",
      "value": "https://api.recland.com"
    },
    {
      "key": "api_key",
      "value": "your-api-key"
    }
  ]
}
```

### 10.2. API Testing Examples

```bash
# Test authentication
curl -X GET "https://api.recland.com/api/user" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json"

# Test job listing
curl -X GET "https://api.recland.com/api/job/common?limit=5" \
  -H "Accept-Language: vi" \
  -H "Content-Type: application/json"

# Test API key endpoint
curl -X GET "https://api.recland.com/api/company/search?q=technology" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json"

# Test job creation
curl -X POST "https://api.recland.com/api/job/create" \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "employer_id": 123,
    "company_id": 45,
    "name": "Senior Developer",
    "salary_min": 1500,
    "salary_max": 2500
  }'
```

---

## 11. API Documentation

### 11.1. OpenAPI Specification

```yaml
openapi: 3.0.0
info:
  title: RecLand API
  description: Recruitment platform API
  version: 2.0.0
  contact:
    name: RecLand Support
    email: <EMAIL>

servers:
  - url: https://api.recland.com
    description: Production server
  - url: https://staging-api.recland.com
    description: Staging server

security:
  - BearerAuth: []
  - ApiKeyAuth: []

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    Job:
      type: object
      properties:
        id:
          type: integer
          example: 123
        name:
          type: string
          example: "Senior PHP Developer"
        slug:
          type: string
          example: "senior-php-developer-123"
        salary_min:
          type: integer
          example: 1500
        salary_max:
          type: integer
          example: 2500
```

### 11.2. Interactive Documentation

```php
// Generate API documentation
class ApiDocumentationController {
    public function index() {
        $routes = collect(Route::getRoutes())
            ->filter(function($route) {
                return str_starts_with($route->uri(), 'api/');
            })
            ->map(function($route) {
                return [
                    'method' => implode('|', $route->methods()),
                    'uri' => $route->uri(),
                    'name' => $route->getName(),
                    'action' => $route->getActionName(),
                    'middleware' => $route->middleware()
                ];
            });

        return view('api.documentation', compact('routes'));
    }
}
```

---

## 12. Performance & Monitoring

### 12.1. API Performance Metrics

```php
class ApiMetricsMiddleware {
    public function handle($request, Closure $next) {
        $startTime = microtime(true);

        $response = $next($request);

        $duration = microtime(true) - $startTime;

        // Log API metrics
        ApiMetric::create([
            'endpoint' => $request->path(),
            'method' => $request->method(),
            'status_code' => $response->getStatusCode(),
            'duration' => $duration,
            'memory_usage' => memory_get_peak_usage(true),
            'user_id' => auth()->id(),
            'ip_address' => $request->ip(),
            'created_at' => now()
        ]);

        return $response;
    }
}
```

### 12.2. API Health Check

```php
Route::get('/api/health', function() {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version'),
        'services' => [
            'database' => DB::connection()->getPdo() ? 'up' : 'down',
            'redis' => Cache::store('redis')->get('health_check') !== null ? 'up' : 'down',
            'storage' => Storage::disk('s3')->exists('health_check.txt') ? 'up' : 'down'
        ]
    ]);
});
```

---

*Tài liệu API Endpoints hoàn tất với đầy đủ chi tiết về tất cả endpoints, authentication, error handling, testing và monitoring.*
