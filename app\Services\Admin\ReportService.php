<?php

namespace App\Services\Admin;

use App\Notifications\RecEmployerUpdateStatusReportSystem;
use App\Repositories\ReportRepository;
use App\Services\FileServiceS3;

class ReportService
{

    protected $reportRepository;

    public function __construct(ReportRepository $reportRepository)
    {
        $this->reportRepository = $reportRepository;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'user.name',
                ),
                'value' => 'Người báo cáo'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'user.email',
                ),
                'value' => 'Email người báo cáo'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'created_at_value',
                ),
                'value' => 'Thời gian báo cáo'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'type_issue_value',
                ),
                'value' => 'Chủ đề báo cáo'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'description',
                ),
                'value' => 'Mô tả'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'description_admin',
                ),
                'value' => 'Ghi chú Admin'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderFileReport',
                    'data-mdata' => 'file_url',
                ),
                'value' => 'File Report'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderStatusReport',
                    'data-mdata' => 'status',
                ),
                'value' => 'Trạng thái'
            ),
        );

        $renderAction = [
            'actionEditReportAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('report-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->reportRepository->getListReport($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()
        ];
        return $response;
    }

    public function changeStatus($params)
    {
        if ($params['status'] == 0) {
            return false;
        }

        $report = $this->reportRepository->find($params['id']);
        if ($report) {
            $report->status = $params['status'];
            $report->description_admin = isset($params['description_admin']) ? $params['description_admin'] : null;
            $report->save();

            if ($params['status'] == 1) {
                $report->user->notify(new RecEmployerUpdateStatusReportSystem($report));
            }

            return true;
        }

        return false;
    }

}
