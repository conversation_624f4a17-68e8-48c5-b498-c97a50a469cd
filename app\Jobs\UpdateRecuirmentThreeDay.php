<?php

namespace App\Jobs;

use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\ChangeStatusPassInterviewAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdmin;
use App\Notifications\ChangeStatusSuccessRecruitmentToRec;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class UpdateRecuirmentThreeDay implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;
    public $statusRecruitment;

    public function __construct($wareHouseCvSellingBuyId, $statusRecruitment)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->statusRecruitment = $statusRecruitment;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = app(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        //interview
        if ($wareHouseCvSellingBuy->status_recruitment == 7 && $this->statusRecruitment == 7 &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
            //8 => 'Pass Interview',
            $wareHouseCvSellingBuy->update(['status_recruitment' => 8]);
            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy);
            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdmin($wareHouseCvSellingBuy));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new ChangeStatusPassInterview($wareHouseCvSellingBuy));
            }
            //sau 24h thi Thanh toan tien tra CTV -> interview
            PayInterview::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(24 * 60));
            /*//Cộng tiền trả CTV
            //1. payins
            $bonusRepository = resolve(BonusRepository::class);
            $payinMonthRepository = resolve(PayinMonthRepository::class);
            $now = \Carbon\Carbon::now();
            $year = $now->year;
            $month = $now->month;
            $payinMonth = $payinMonthRepository->findByWarehouseCvSellingBuyId($wareHouseCvSellingBuy->id);
            if(!$payinMonth){
                $bonusCheck = $bonusRepository->getBonusByUser($wareHouseCvSellingBuy->rec->id, $month, $year);
                if(!$bonusCheck){
                    //nếu tháng/năm chưa có thì insert mới
                    $bonusRepository->create(
                        [
                            'user_id'   => $wareHouseCvSellingBuy->rec->id,
                            'year'      => $year,
                            'month'     => $month,
                            'price'     => $wareHouseCvSellingBuy->price,
                        ]
                    );
                }else{
                    //nếu có doanh thu từ trước thì cộng dồn
                    $bonusCheck->price += $wareHouseCvSellingBuy->price;
                    $bonusCheck->save();
                }
                //từng giao dịch trong tháng
                $payinMonthRepository->create(
                    [
                        'user_id'                       => $wareHouseCvSellingBuy->rec->id,
                        'warehouse_cv_selling_buy_id'   => $wareHouseCvSellingBuy->id,
                        'year'                          => $year,
                        'month'                         => $month,
                        'price'                         => $wareHouseCvSellingBuy->price,
                        'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                    ]
                );
                //2. wallets
                $wareHouseCvSellingBuy->rec->wallet->price  = $wareHouseCvSellingBuy->rec->wallet->price + $wareHouseCvSellingBuy->price;
                $wareHouseCvSellingBuy->rec->wallet->save();
            }*/
        }
        //onboard
        if ($wareHouseCvSellingBuy->status_recruitment == 14 && $this->statusRecruitment == 14 &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)) {
            //16 => 'Success Recruitment',
            $wareHouseCvSellingBuy->update(['status_recruitment' => 16, 'status_payment' => 4]);
            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy);

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusSuccessRecruitmentToAdmin($wareHouseCvSellingBuy));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new ChangeStatusSuccessRecruitmentToRec($wareHouseCvSellingBuy));
            }
            //sau 5phut thì tra tien 75% CTV
            PayOnboard::dispatch($wareHouseCvSellingBuy->id, 75)->delay(now()->addMinutes(5));
        }

    }




}
