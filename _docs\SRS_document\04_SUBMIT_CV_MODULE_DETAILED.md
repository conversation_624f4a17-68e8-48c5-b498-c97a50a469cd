# Module G<PERSON>ới thiệu Ứng viên - Logic Chi tiết & Jobs Tự động

## 1. Tổng quan Quy trình

Quy trình giới thiệu ứng viên trong RecLand được chia thành 4 giai đoạn chính:

1. **CV Data**: Submit CV và xác nhận từ ứng viên
2. **Interview**: Phỏng vấn và đánh giá
3. **Onboard**: <PERSON>hậ<PERSON> việc và thử việc  
4. **Success**: Hoàn thành tuyển dụng

Mỗi giai đoạn có các job tự động chạy ngầm để đảm bảo quy trình diễn ra suôn sẻ và xử lý các trường hợp timeout.

## 2. Giai đoạn 1: CV Data

### 2.1. Luồng chính

#### Bước 1: CTV Submit CV
- **Controller**: `WareHouseSubmitCvController@candidateIntroduction`
- **Service**: `WareHouseSubmitCvService@candidateIntroduction`
- **Database Transaction**:
  - Tạo record `warehouse_cvs` (nếu CV mới)
  - Tạo record `submit_cvs` với:
    - `status = 1` (Waitingcandidateconfirm)
    - `confirm_token` = random 16 chars
    - `confirm_candidate` = now() + 2 days (nếu không ủy quyền)
- **Notifications**:
  - `EmployerIntroduceCandidate` → NTD
  - `RecIntroduceCandidate` → CTV
  - `RecNewCv` → Admin

#### Bước 2: Ứng viên xác nhận qua email
- **Timeout**: 48 giờ
- **Đồng ý**: 
  - `status = 3` (Waiting Payment)
  - Clear `confirm_token`
- **Từ chối**:
  - `status = 2` (Candidate Cancel Apply)
  - Notifications cho CTV & Admin

#### Bước 3: NTD thanh toán để xem CV
- **Service**: `SubmitCvService@submitCvChangeStatus`
- **Database Transaction**:
  - Trừ Point từ ví NTD
  - Cập nhật `status = 21` (WaitingPayment)
  - Nếu `bonus_type = 'cv'` và không ủy quyền: Trả hoa hồng CTV ngay lập tức
  - Ghi log `wallet_transactions`

### 2.2. Jobs tự động

#### Job: RecSumPointSubmit
- **Mục đích**: Tự động trả hoa hồng cho CTV sau khi submit thành công (cho trường hợp ủy quyền)
- **Dispatch**: Ngay sau khi submit CV
- **Delay**: Không có (chạy ngay)
- **Logic**:
  ```php
  if ($submitCv->authorize == 1 && $submitCv->bonus_type == 'cv') {
      // Trả hoa hồng cho CTV
      $bonus = $submitCv->getSubmitBonusForCtv();
      $ctv->wallet->addAmount($bonus);
      // Ghi log transaction
  }
  ```

#### Job: RecSumExpiredPointSubmit
- **Mục đích**: Xử lý khiếu nại tự động khi hết thời gian phản hồi
- **Dispatch**: Khi NTD gửi khiếu nại
- **Delay**: 7 ngày
- **Logic chi tiết**:
  ```php
  // Chỉ chạy nếu khiếu nại chưa được xử lý
  if ($submitCv->status_complain == $statusComplain) {
      // Cập nhật trạng thái khiếu nại
      if ($status_complain == 1) { // NTD khiếu nại
          $submitCv->status_complain = 3; // CTV xác nhận
      } elseif ($status_complain == 2) { // CTV từ chối
          $submitCv->status_complain = 4; // Admin xác nhận
      }
      
      // Hoàn tiền theo loại
      if ($submitCv->bonus_type == 'cv' || $submitCv->bonus_type == 'interview') {
          // Hoàn 100% cho CV Data và Interview
          $refundAmount = $submitCv->payment_fee;
          $submitCv->status_payment = 3; // Đã hoàn tiền
      } elseif ($submitCv->status == 14) { // Thử việc
          // Hoàn tiền theo thời gian thử việc
          $daysTrial = Carbon::parse($submitCv->date_complain)
                              ->diffInDays($submitCv->date_book);
          
          if ($daysTrial <= 30) {
              $refundPercent = 100;
          } elseif ($daysTrial <= 60) {
              $refundPercent = 70;
          } else {
              $refundPercent = 50;
          }
          
          $refundAmount = $submitCv->payment_fee * $refundPercent / 100;
      }
      
      // Thực hiện hoàn tiền
      $employer->wallet->addAmount($refundAmount);
      
      // Ghi log và gửi notifications
  }
  ```

### 2.3. Xử lý ngoại lệ

#### CTV hủy ứng tuyển
- **Điều kiện**: 
  - `status = 21` (WaitingPayment)
  - `status_payment_ctv = 0` (chưa thanh toán CTV)
  - `can_cancel_apply = true`
- **Controller**: `RecController@candidateCancelSubmitCv`
- **Service**: `SubmitCvService@candidateCancelSubmitWithRefund`
- **Xử lý**:
  - Cập nhật `status = 2` (CandidateCancelApply)
  - Hoàn tiền 100% cho NTD trong 24h
  - Gửi notification cho NTD

## 3. Giai đoạn 2: Interview

### 3.1. Luồng chính

#### Bước 1: NTD mời phỏng vấn
- **Controller**: `EmployerController@scheduleInterviewSubmit`
- **Service**: `SubmitCvService@scheduleInterview`
- **Database**:
  - Cập nhật `status = 4` (Waiting confirm calendar)
  - Tạo record `submit_cv_books` với thông tin lịch PV
- **Dispatch Job**: `RejectBookExpireSubmit` (delay 48h)

#### Bước 2: CTV xác nhận lịch
- **Xác nhận**: `status = 7` (Waiting Interview)
- **Từ chối 3 lần**: 
  - `status = 9` (Reject Interview)
  - Hoàn tiền cho NTD

#### Bước 3: Sau phỏng vấn
- **Pass**: `status = 8` (Pass Interview)
- **Fail**: `status = 10` (Fail Interview)

### 3.2. Jobs tự động

#### Job: RejectBookExpireSubmit
- **Mục đích**: Tự động từ chối nếu CTV không xác nhận lịch PV
- **Dispatch**: Khi NTD đặt lịch phỏng vấn
- **Delay**: 48 giờ
- **Logic**:
  ```php
  // Kiểm tra xem CTV đã xác nhận chưa
  if ($submitCv->status == 4) { // Vẫn đang chờ xác nhận
      $rejectCount = $submitCv->books()
                              ->where('status', 'rejected')
                              ->count();
      
      if ($rejectCount >= 2) { // Đã từ chối 2 lần trước đó
          // Từ chối hoàn toàn
          $submitCv->status = 9; // Reject Interview
          
          // Hoàn tiền cho NTD
          $this->refundToEmployer($submitCv);
      } else {
          // Tăng số lần từ chối
          $currentBook->status = 'rejected';
          
          // Gửi notification nhắc nhở
      }
  }
  ```

#### Job: SendemailBookSubmit
- **Mục đích**: Gửi email nhắc lịch phỏng vấn
- **Dispatch**: Khi lịch PV được xác nhận
- **Delay**: 1 ngày sau lịch PV
- **Logic**:
  ```php
  // Gửi email nhắc nhở cho:
  // - NTD: Nhắc chuẩn bị phỏng vấn
  // - CTV: Nhắc ứng viên chuẩn bị
  // - Ứng viên: Thông tin chi tiết về buổi PV
  ```

#### Job: PassInterviewSubmit
- **Mục đích**: Tự động chuyển sang Pass Interview nếu NTD không cập nhật
- **Dispatch**: Khi xác nhận lịch PV
- **Delay**: 7 ngày sau lịch PV
- **Logic**:
  ```php
  // Chỉ chạy nếu NTD chưa cập nhật kết quả
  if ($submitCv->status == 7) { // Vẫn đang Waiting Interview
      // Tự động Pass
      $submitCv->status = 8; // Pass Interview
      
      // Gửi notification
      // Dispatch job trả hoa hồng nếu cần
  }
  ```

#### Job: PayInterviewSubmit
- **Mục đích**: Trả hoa hồng cho CTV khi hoàn thành phỏng vấn
- **Dispatch**: Khi NTD cập nhật kết quả PV (Pass hoặc Fail)
- **Delay**: 24 giờ
- **Logic**:
  ```php
  // Chỉ áp dụng cho bonus_type = 'interview'
  if ($submitCv->bonus_type == 'interview' && 
      in_array($submitCv->status, [8, 10])) { // Pass hoặc Fail
      
      // Kiểm tra khiếu nại
      if ($submitCv->status_complain == 0) {
          // Trả hoa hồng cho CTV
          $bonus = $submitCv->getSubmitBonusForCtv();
          $ctv->wallet->addAmount($bonus);
          
          // Cập nhật status_payment
          $submitCv->status_payment = 1; // Đã thanh toán
      }
  }
  ```

### 3.3. Xử lý ngoại lệ

#### Job: OutOfDateBookInterviewSubmitCv
- **Mục đích**: Xử lý khi NTD không đặt lại lịch sau khi bị hủy
- **Delay**: 7 ngày
- **Logic**: Hủy hoàn toàn và hoàn tiền

## 4. Giai đoạn 3: Onboard

### 4.1. Luồng chính

#### Bước 1: NTD gửi Offer
- **Status progression**:
  - `8` (Pass Interview) → `11` (Offering)
  - `11` → `13` (Waiting Onboard) khi đặt lịch

#### Bước 2: Ứng viên xác nhận Onboard
- **Đồng ý**: Trigger job `ChangeToTrailWorkSubmit`
- **Từ chối**: `status = 12` (Reject Offer)

#### Bước 3: Thử việc
- **Bắt đầu**: `status = 14` (Trial work)
- **Thời gian**: 67 ngày

### 4.2. Jobs tự động

#### Job: ChangeToRejectOfferSubmit
- **Mục đích**: Tự động từ chối offer nếu ứng viên không phản hồi
- **Dispatch**: Khi NTD đặt lịch onboard
- **Delay**: 2 ngày
- **Logic**:
  ```php
  if ($submitCv->status == 13) { // Vẫn Waiting Onboard
      // Chuyển sang Reject Offer
      $submitCv->status = 12;
      
      // Dispatch job hoàn cọc
      DepositRefundRejectOfferSubmit::dispatch($submitCv->id)
                                    ->delay(now()->addHours(48));
  }
  ```

#### Job: ChangeToTrailWorkSubmit
- **Mục đích**: Chuyển sang thử việc và xử lý thanh toán
- **Dispatch**: Khi ứng viên xác nhận onboard
- **Delay**: 7 ngày sau ngày onboard
- **Logic chi tiết**:
  ```php
  // Kiểm tra số dư ví NTD
  $requiredAmount = $submitCv->payment_fee * 0.9; // 90% giá trị
  
  if ($employer->wallet->balance >= $requiredAmount) {
      // Trừ tiền từ ví
      $employer->wallet->deduct($requiredAmount);
      
      // Cập nhật trạng thái
      $submitCv->status = 14; // Trial work
      $submitCv->status_payment = 1; // Đã thanh toán
  } else {
      // Ghi nợ
      PaymentDebit::create([
          'employer_id' => $employer->id,
          'submit_cv_id' => $submitCv->id,
          'amount' => $requiredAmount,
          'type' => 'onboard_fee'
      ]);
      
      // Gửi email nhắc nợ từ ngày 1-15
      for ($day = 1; $day <= 15; $day++) {
          SendDebtReminderEmail::dispatch($employer, $submitCv)
                               ->delay(now()->addDays($day));
      }
  }
  
  // Dispatch jobs thanh toán hoa hồng
  $onboardDate = $submitCv->onboard->date_book;
  
  // 15% sau 30 ngày
  PayOnboardSubmit::dispatch($submitCv->id, 15, 30)
                  ->delay($onboardDate->addDays(30));
  
  // 10% sau 45 ngày
  PayOnboardSubmit::dispatch($submitCv->id, 10, 45)
                  ->delay($onboardDate->addDays(45));
  
  // 75% sau 67 ngày
  PayOnboardSubmit::dispatch($submitCv->id, 75, 67)
                  ->delay($onboardDate->addDays(67));
  
  // Tự động success sau 67 ngày
  SuccessRecuitmentSubmit::dispatch($submitCv->id)
                         ->delay($onboardDate->addDays(67));
  
  // Email nhắc hết hạn thử việc (ngày 55-60)
  for ($day = 55; $day <= 60; $day++) {
      SendTrialEndReminder::dispatch($submitCv)
                          ->delay($onboardDate->addDays($day));
  }
  ```

#### Job: PayOnboardSubmit
- **Mục đích**: Trả hoa hồng theo từng mốc thời gian
- **Dispatch**: 3 lần với các mốc 30/45/67 ngày
- **Parameters**: 
  - `submitCvId`: ID của submit CV
  - `percent`: Phần trăm hoa hồng (15/10/75)
  - `days`: Số ngày từ onboard (30/45/67)
- **Logic**:
  ```php
  public function handle($submitCvId, $percent, $days)
  {
      $submitCv = SubmitCv::find($submitCvId);
      
      // Kiểm tra điều kiện
      if (!$this->canPayCommission($submitCv, $days)) {
          return;
      }
      
      // Tính toán hoa hồng
      $totalBonus = $submitCv->getSubmitBonusForCtv();
      $amount = $totalBonus * $percent / 100;
      
      // Kiểm tra khiếu nại
      if ($submitCv->status_complain == 0) {
          // Trả hoa hồng
          $ctv->wallet->addAmount($amount);
          
          // Ghi log
          SubmitCvHistoryPayment::create([
              'submit_cv_id' => $submitCvId,
              'type' => 0, // Commission
              'percent' => $percent,
              'amount' => $amount,
              'note' => "Hoa hồng onboard {$percent}% sau {$days} ngày"
          ]);
          
          // Cập nhật status_payment khi trả đủ 100%
          if ($days == 67) {
              $submitCv->status_payment = 1; // Hoàn tất
          }
      }
  }
  
  private function canPayCommission($submitCv, $days)
  {
      // Kiểm tra trạng thái
      if (!in_array($submitCv->status, [14, 16])) { // Trial work hoặc Success
          return false;
      }
      
      // Kiểm tra đã qua đủ ngày chưa
      $daysPassed = Carbon::parse($submitCv->onboard->date_book)
                          ->diffInDays(now());
      
      return $daysPassed >= $days;
  }
  ```

#### Job: SuccessRecuitmentSubmit
- **Mục đích**: Tự động chuyển thành công nếu NTD không cập nhật
- **Dispatch**: Khi bắt đầu thử việc
- **Delay**: 67 ngày từ ngày onboard
- **Logic**:
  ```php
  if ($submitCv->status == 14) { // Vẫn đang Trial work
      // Chuyển thành Success
      $submitCv->status = 16; // Success Recruitment
      
      // Gửi notifications
      // Báo cáo thành công cho admin
  }
  ```

#### Job: DepositRefundRejectOfferSubmit
- **Mục đích**: Hoàn cọc khi ứng viên từ chối offer
- **Dispatch**: Khi status = Reject Offer hoặc Fail Interview
- **Delay**: 48 giờ
- **Logic**:
  ```php
  // Chỉ hoàn cọc cho bonus_type = 'onboard'
  if ($submitCv->bonus_type == 'onboard' && 
      in_array($submitCv->status, [10, 12])) {
      
      // Tính toán số tiền hoàn
      $refundAmount = $submitCv->payment_fee * 0.1; // 10% cọc
      
      // Hoàn tiền
      $employer->wallet->addAmount($refundAmount);
      
      // Ghi log
      SubmitCvHistoryPayment::create([
          'submit_cv_id' => $submitCv->id,
          'type' => 1, // Refund
          'percent' => 10,
          'amount' => $refundAmount,
          'note' => 'Hoàn cọc do từ chối offer'
      ]);
  }
  ```

### 4.3. Xử lý thất bại thử việc

Khi NTD cập nhật `status = 17` (Fail trial work):
- Hủy các job `PayOnboardSubmit` chưa chạy
- Hoàn tiền theo mốc:
  - 0-30 ngày: Hoàn 100%
  - 31-60 ngày: Hoàn 70%
  - 61-67 ngày: Hoàn 50%

## 5. Tổng kết Jobs và Timing

### 5.1. Jobs theo giai đoạn

| Giai đoạn | Job | Delay | Mục đích |
|-----------|-----|-------|----------|
| **CV Data** | RecSumPointSubmit | Ngay lập tức | Trả hoa hồng ủy quyền |
| | RecSumExpiredPointSubmit | 7 ngày | Xử lý khiếu nại timeout |
| **Interview** | RejectBookExpireSubmit | 48 giờ | Timeout xác nhận lịch |
| | SendemailBookSubmit | 1 ngày sau PV | Nhắc lịch phỏng vấn |
| | PassInterviewSubmit | 7 ngày sau PV | Auto pass nếu không cập nhật |
| | PayInterviewSubmit | 24 giờ | Trả hoa hồng interview |
| | OutOfDateBookInterviewSubmitCv | 7 ngày | Timeout đặt lại lịch |
| **Onboard** | ChangeToRejectOfferSubmit | 2 ngày | Timeout xác nhận offer |
| | ChangeToTrailWorkSubmit | 7 ngày sau onboard | Chuyển sang thử việc |
| | PayOnboardSubmit (15%) | 30 ngày sau onboard | Trả hoa hồng đợt 1 |
| | PayOnboardSubmit (10%) | 45 ngày sau onboard | Trả hoa hồng đợt 2 |
| | PayOnboardSubmit (75%) | 67 ngày sau onboard | Trả hoa hồng đợt 3 |
| | SuccessRecuitmentSubmit | 67 ngày sau onboard | Auto success |
| | DepositRefundRejectOfferSubmit | 48 giờ | Hoàn cọc |

### 5.2. Email Reminders

| Email | Timing | Đối tượng |
|-------|--------|-----------|
| Xác nhận ứng tuyển | Ngay lập tức | Ứng viên |
| Nhắc lịch phỏng vấn | 1 ngày trước | NTD, CTV, UV |
| Nhắc nợ | Ngày 1-15 | NTD (nếu không đủ tiền) |
| Nhắc hết hạn thử việc | Ngày 55-60 | NTD, CTV |

## 6. Best Practices

### 6.1. Transaction Safety
- Tất cả operations liên quan đến tiền đều trong DB Transaction
- Rollback khi có lỗi
- Double-entry bookkeeping cho wallet

### 6.2. Job Reliability
- Sử dụng database queue driver
- Set timeout phù hợp cho mỗi job
- Retry failed jobs với backoff
- Monitor job failures

### 6.3. Notification Strategy
- Gửi qua nhiều kênh (Email, SMS, In-app)
- Template đa ngôn ngữ
- Track open/click rates

### 6.4. Performance
- Index các trường query thường xuyên
- Cache các query phức tạp
- Batch processing cho bulk operations
