
# SRS - F03: <PERSON><PERSON><PERSON><PERSON> lý Ví điện tử (Wallet)

**Phiên bản:** 6.0
**Ngày:** 2025-07-08
**<PERSON><PERSON><PERSON> gi<PERSON>:** Gemini

---

### 1. <PERSON><PERSON> tả

Tính năng này là trung tâm cho mọi giao dịch tài chính. <PERSON><PERSON> thống quản lý hai loại số dư riêng biệt cho hai vai trò chính: **Point** cho Nhà tuyển dụng (NTD) và **Price (VNĐ)** cho Cộng tác viên (CTV). Logic chính được đóng gói trong `WalletService` và `Wallet` Model, được gọi bởi các service nghiệp vụ khác.

### 2. Đ<PERSON><PERSON> tượng tham gia

*   **Nhà tuyển dụng (NTD):** Nạp tiền để có <PERSON>, sử dụng Point để mua dịch vụ.
*   **<PERSON><PERSON>ng tác viên (CTV):** <PERSON><PERSON><PERSON><PERSON> hoa hồ<PERSON> (Price), y<PERSON><PERSON> cầu rút tiền.
*   **Quản trị viên (Admin):** Quản lý giao dịch, cấu hình và điều chỉnh số dư.

### 3. Mô hình dữ liệu cốt lõi

*   **`wallets`**: Một bảng duy nhất lưu ví cho tất cả người dùng.
    *   `user_id`, `type` (enum: 'employer', 'rec'), `point` (decimal), `price` (decimal).
*   **`wallet_transactions`**: Sổ cái ghi lại MỌI thay đổi số dư.
    *   `wallet_id`, `amount`, `balance_after`, `object_id`, `object_type`, `type`, `note`.
*   **`deposits`**: Bảng ghi lại các yêu cầu nạp tiền của NTD.
*   **`payout_logs`**: Bảng ghi lại các yêu cầu rút tiền của CTV.
*   **`zalopay_transactions`**: Bảng ghi lại log giao dịch từ cổng thanh toán ZaloPay.

### 4. Yêu cầu chức năng

#### 4.1. Luồng của Nhà tuyển dụng (Employer)

*   **4.1.1. Nạp Point:**
    1.  **Controller & Service:** `ZalopayTransactionController` nhận yêu cầu nạp tiền và gọi `ZalopayTransactionService`.
    2.  **Tạo yêu cầu:** Service tạo một bản ghi `deposits` (trạng thái "chờ xử lý") và giao tiếp với API của ZaloPay để lấy link thanh toán.
    3.  **Thanh toán:** NTD được chuyển hướng đến ZaloPay. Sau khi thanh toán, ZaloPay gọi lại một webhook của hệ thống (ví dụ: `/api/zalopay-ipn`).
    4.  **Xử lý Webhook:** Phương thức `ipn` trong `ZalopayTransactionController` tiếp nhận, xác thực chữ ký và dữ liệu từ ZaloPay. Nếu hợp lệ, nó gọi `ZalopayTransactionService` để xử lý.
    5.  **Cộng Point:** Service cập nhật trạng thái `deposits` thành "thành công", sau đó gọi `WalletService->addAmountTransaction()` (hoặc `Wallet->addAmount()`) để cộng `point` vào ví NTD và ghi log vào `wallet_transactions` (`type`='deposit').
    6.  **Xử lý nợ tự động:** Nếu có các khoản nợ chưa thanh toán (`PaymentDebit`), hệ thống sẽ tự động thanh toán các khoản nợ này nếu số dư ví đủ sau khi nạp tiền. Trạng thái của khoản nợ và `WareHouseCvSellingBuy` liên quan sẽ được cập nhật.
*   **4.1.2. Sử dụng & Hoàn Point:**
    *   Khi các service nghiệp vụ (`WareHouseCvSellingBuyService`, `SubmitCvService`) cần trừ hoặc hoàn Point, chúng sẽ gọi các phương thức tương ứng trong `WalletService` (`subtractAmountTransaction` hoặc `addAmountTransaction`).
    *   Các phương thức này trong `WalletService` sẽ gọi đến `subtractAmount()` hoặc `addAmount()` của `Wallet` Model.

#### 4.2. Luồng của Cộng tác viên (CTV)

*   **4.2.1. Nhận hoa hồng:**
    *   Các Job (`PayOnboard`, `PayInterview`...) gọi các service nghiệp vụ. Các service này lại gọi `WalletService->addPrice()` (hoặc `Wallet->addPrice()`) để cộng `price` (VNĐ) vào ví của CTV và ghi log.
*   **4.2.2. Rút tiền:**
    1.  **Controller & Service:** `UserController` (hoặc tương tự) nhận yêu cầu rút tiền và gọi `PayoutLogService`.
    2.  **Tạo yêu cầu:** Service kiểm tra số dư `price` trong ví CTV. Nếu hợp lệ, nó tạo một bản ghi `payout_logs` (trạng thái "chờ xử lý") và gọi `WalletService->subtractPrice()` (hoặc `Wallet->subtractPrice()`) để **trừ ngay lập tức** số tiền yêu cầu rút khỏi ví của CTV để tránh rút tiền hai lần. Giao dịch trừ tiền này cũng được ghi vào `wallet_transactions` (`type`='withdraw_request').
    3.  **Admin xử lý:** Admin xem danh sách `payout_logs`, thực hiện chuyển khoản thủ công bên ngoài.
    4.  **Admin xác nhận:** Admin cập nhật trạng thái trong `payout_logs` thành "đã xử lý". Hành động này sẽ không trừ tiền nữa (vì đã trừ ở bước 2), nhưng có thể ghi thêm log xác nhận vào `wallet_transactions`.

#### 4.3. Chức năng của Admin

*   **4.3.1. Điều chỉnh số dư:** Admin có giao diện để gọi một phương thức trong `WalletService`, cho phép cộng/trừ `point` hoặc `price` thủ công. Service sẽ gọi `Wallet->addAmount()`/`subtractAmount()` hoặc `Wallet->addPrice()`/`subtractPrice()` và ghi log với `type`='admin_adjustment'.
*   **4.3.2. Xác nhận nạp tiền (Chuyển khoản):** Admin có giao diện quản lý `deposits`. Khi xác nhận một giao dịch chuyển khoản, hệ thống sẽ gọi `WalletService->addAmount()` để cộng Point cho NTD.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi ZaloPay Webhook không hợp lệ:**
    *   **Nguyên nhân:** Chữ ký (mac) từ ZaloPay không khớp khi xác thực trong `ZalopayTransactionController`.
    *   **Xử lý:** Controller bỏ qua yêu cầu, ghi log lỗi và không thực hiện cộng tiền, tránh các cuộc tấn công giả mạo.
*   **5.2. Lỗi CTV yêu cầu rút tiền không đủ số dư:**
    *   **Nguyên nhân:** Tại **Bước 4.2.2**, `PayoutLogService` kiểm tra thấy số tiền yêu cầu rút > `wallet->price`.
    *   **Xử lý:** Service trả về lỗi, Controller hiển thị thông báo "Số dư không đủ để thực hiện yêu cầu rút tiền."
*   **5.3. Admin từ chối yêu cầu rút tiền:**
    *   **Nguyên nhân:** Admin phát hiện gian lận hoặc sai thông tin và từ chối yêu cầu trong `payout_logs`.
    *   **Xử lý:** Hệ thống cần có một hành động "từ chối". Hành động này sẽ kích hoạt một luồng **hoàn lại tiền** vào ví CTV (cộng lại số tiền đã trừ ở Bước 4.2.2) và ghi log vào `wallet_transactions` (`type`='withdraw_rejected').
*   **5.4. Lỗi số tiền không hợp lệ (âm hoặc 0):**
    *   **Nguyên nhân:** Bất kỳ phương thức `addAmount`, `subtractAmount`, `addPrice`, `subtractPrice` nào trong `Wallet` Model nhận vào số tiền <= 0.
    *   **Xử lý:** Model sẽ ném ra `Exception('Số tiền không hợp lệ')`. Transaction sẽ được rollback (nếu có) và lỗi sẽ được ghi log.
*   **5.5. Lỗi số dư không đủ khi trừ tiền:**
    *   **Nguyên nhân:** Phương thức `subtractAmount` hoặc `subtractPrice` trong `Wallet` Model được gọi khi số dư hiện tại không đủ.
    *   **Xử lý:** Model sẽ ném ra `Exception('Số dư không đủ')`. Transaction sẽ được rollback (nếu có) và lỗi sẽ được ghi log.

### 6. Yêu cầu phi chức năng

*   **Bảo mật:** Logic xác thực webhook của cổng thanh toán là tối quan trọng. Các hành động của Admin phải được ghi vào bảng `audits`.
*   **Khả năng truy vết:** `wallet_transactions` là sổ cái không thể thay đổi, ghi lại mọi giao dịch và liên kết đến đối tượng gốc.
*   **Chính xác & Toàn vẹn:** Mọi phép toán số dư phải sử dụng kiểu dữ liệu `decimal` và được thực hiện trong DB transaction. `balance_after` trong `wallet_transactions` đóng vai trò là một chốt kiểm tra quan trọng.

### 7. Các thông báo liên quan

*   **`DepositToEmployer` (gửi cho NTD):**
    *   **Mô tả:** Thông báo xác nhận nạp tiền thành công.
    *   **Nội dung chính:** ID đơn hàng, tên NTD, link đến ví NTD.
    *   **Kênh:** Mail.
*   **`NotificationDeposits` (gửi cho Admin):**
    *   **Mô tả:** Thông báo có yêu cầu nạp tiền mới (thường là chuyển khoản ngân hàng).
    *   **Nội dung chính:** Tên NTD, ID NTD, số tiền nạp.
    *   **Kênh:** Mail.

