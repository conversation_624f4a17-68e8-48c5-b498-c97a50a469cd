<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_books', function (Blueprint $table) {
            $table->id()->comment('Chỉ có NTD đã mua thì mới dc đặt lịch phỏng vấn với CTV đã tạo CV');
            $table->integer('ntd_id')->comment('NTD')->nullable();
            $table->integer('ctv_id')->comment('CTV')->nullable();
            $table->integer('submit_cvs_id')->comment('submit_cvs_id');
            $table->dateTime('date_book')->comment('ngày phỏng vấn')->nullable();
            $table->time('time_book')->comment('Giờ phút phỏng vấn')->nullable();
            $table->integer('status')->comment('<PERSON>rang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối')->default(0);
            $table->string('address')->comment('địa chỉ phỏng vấn');
            $table->string('name')->comment('Tên ứng viên')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_books');
    }
};
