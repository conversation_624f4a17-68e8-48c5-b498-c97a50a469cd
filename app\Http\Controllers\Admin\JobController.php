<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\JobRequest;
use App\Models\JobTop;
use App\Models\SkillMain;
use App\Services\Admin\CompanyService;
use App\Services\Admin\JobService;
use App\Services\Admin\SettingService;
use App\Services\Admin\SkillService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use App\Services\Frontend\WareHouseCvSellingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JobController extends Controller
{
    protected $jobService;
    protected $companyService;
    protected $userService;
    protected $skillService;
    protected $settingService;


    public function __construct(
        JobService     $jobService,
        CompanyService $companyService,
        UserService    $userService,
        SkillService   $skillService,
        SettingService $settingService
    ) {
        $this->jobService = $jobService;
        $this->companyService = $companyService;
        $this->userService = $userService;
        $this->skillService = $skillService;
        $this->settingService = $settingService;
    }

    public function index()
    {
        $datatable = $this->jobService->buildDatatable();
        return view('admin.pages.job.index', compact('datatable'));
    }

    public function create(Request $request)
    {
        $this->generateParams();
        $company_id = $request->old('company_id') !== null ? $request->old('company_id') : 0;
        $companies = [];
        if ($company_id) {
            $dataTemp = $this->companyService->detail($company_id);
            $companies[] = $dataTemp[0];
        }

        $employer_id = $request->old('employer_id') !== null ? $request->old('employer_id') : 0;
        $employer = [];
        if ($employer_id) {
            $employer[] = $this->userService->detailService($employer_id);
        }
        return view('admin.pages.job.create', compact('companies', 'employer'));
    }

    public function store(JobRequest $request)
    {
        try {
            DB::beginTransaction();
            $data = $this->jobService->createService($request->all());
            DB::commit();
            Toast::success(__('message.edit_success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log store job: ', [
                'content: ' => $e->getMessage()
            ]);
        }
        return redirect()->route('job.edit', ['job' => $data->id]);
    }

    public function edit($id, Request $request)
    {
        $lang = app()->getLocale();
        $job = $this->jobService->detailService($id, null);
        $isIt = in_array($job->career, config('job.it_career'));
        if ($isIt) {
            $skills = SkillMain::all()->pluck('name_' . $lang, 'id');
            $skill = SkillMain::where('id', $job->skill_id)->first();
        } else {
            $skills = JobTop::all()->pluck('name_' . $lang, 'id');
            $skill = JobTop::where('id', $job->skill_id)->first();
        }
        $wareHouseCvSellingService = resolve(WareHouseCvSellingService::class);

        $levels = $wareHouseCvSellingService->getLevelByCareer(['career_id' => $job->career, 'skill_id' => $job->skill_id]);

        $data = $this->jobService->detailService($id);
        $dataMeta = $this->jobService->detailPostMeta($id);
        $dataSeo = $this->jobService->detailPostSeo($id);
        $priority = config('job.priority');
        $datatable = $this->jobService->buildRefDatatable($id);
        $this->generateParams();

        $company_id = $request->old('company_id') !== null ? $request->old('company_id') : $data->company_id;
        $companies = [];
        if ($company_id) {
            $dataTemp = $this->companyService->detail($company_id);
            $companies[] = $dataTemp[0];
        }

        $employer_id = $request->old('employer_id') !== null ? $request->old('employer_id') : $data->employer_id;
        $employer = [];
        if ($employer_id) {
            $employer[] = $this->userService->detailService($employer_id);
        }
        return view('admin.pages.job.edit', compact(
            'data',
            'dataMeta',
            'dataSeo',
            'priority',
            'datatable',
            'companies',
            'employer',
            'job',
            'skills',
            'skill',
            'isIt',
            'levels'
        ));
    }

    public function update(JobRequest $request, $id)
    {
        $this->jobService->updateService($request->all(), $id);
        Toast::success(__('message.edit_success'));
        return back();
    }

    public function datatable(Request $request)
    {
        $data = $this->jobService->datatable($request->all(), true);
        return response($data);
    }

    public function refDatatable(Request $request, $jobId)
    {
        $params = $request->all();
        $params['job_id'] = $jobId;
        $params['authorized'] = true;
        $data = $this->jobService->refDatatable($params);
        return response($data);
    }

    private function generateParams()
    {
        $lang = app()->getLocale();
        $str = 'employer_job';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $cities = Common::getCities();
        $career = config('job.career.' . $lang);

        $rank             = config('job.rank.' . $lang);
        $type             = config('job.type.' . $lang);
        $bonusType        = config('job.bonus_type.' . $lang);
        $currency         = config('constant.currency');
        $isActive         = config('job.is_active.' . $lang);
        $status           = config('job.status.' . $lang);
        $skills           = $this->jobService->getSkill();
        $yearOfExperience = config('constant.sonamkinhnghiem');
        $candidateStatus  = config('constant.thoigiandilamdukien');
        $jobType          = config('job.job_type.' . $lang);
        $level            = config('job.level.' . $lang);

        view()->share([
            'cities'           => $cities,
            'career'           => $career,
            'rank'             => $rank,
            'type'             => $type,
            'bonusType'        => $bonusType,
            'currency'         => $currency,
            'arrLang'          => $arrLang,
            'skills'           => $skills,
            'isActive'         => $isActive,
            'status'           => $status,
            'yearOfExperience' => $yearOfExperience,
            'candidateStatus'  => $candidateStatus,
            'jobType'          => $jobType,
            'level'            => $level,
        ]);
    }

    //get skill
    public function listSkill(Request $request)
    {
        $request = $request->all();
        $search = [
            'size' => config('constant.limit_search_ajax_selectbox'),
        ];
        if (isset($request['searchTerm']) && $request['searchTerm'] != '') $search['search'] = $request['searchTerm'];
        $companies = $this->skillService->indexService($search, [], true);
        $response = [];
        if ($companies) {
            foreach ($companies as $key => $value) {
                $response[$key]['id'] = $value->name;
                $response[$key]['text'] = $value->name;
            }
        }

        return json_encode($response);
    }


    public function calculateBonusCtv(Request $request)
    {
        try {
            // Logic tính toán bonus_for_ctv dựa trên các tham số
            $bonus = $request->bonus;
            $bonus_type = $request->bonus_type;

            $jobService = resolve(\App\Services\Frontend\JobService::class);

            // Thực hiện tính toán bonus_for_ctv theo logic nghiệp vụ
            $bonus_for_ctv = $jobService->calculateBonusForCtv($bonus, $bonus_type);

            return response()->json([
                'success' => true,
                'bonus_for_ctv' => $bonus_for_ctv
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function duplicate($id)
    {
        try {
            DB::beginTransaction();
            $duplicatedJob = $this->jobService->duplicateService($id);
            DB::commit();
            Toast::success('Nhân bản tin tuyển dụng thành công!');
            return redirect()->route('job.edit', ['job' => $duplicatedJob->id]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log duplicate job: ', [
                'content: ' => $e->getMessage()
            ]);
            Toast::error('Có lỗi xảy ra khi nhân bản tin tuyển dụng!');
            return back();
        }
    }
}
