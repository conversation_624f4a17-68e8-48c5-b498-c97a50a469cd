<?php

namespace App\Repositories;

use App\Models\Deposit;
use Illuminate\Support\Carbon;

class DepositRepository extends BaseRepository
{
    const MODEL = Deposit::class;

    public function getListDepositByUser($params, $userId)
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_10');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        $query->where('user_id', $userId);

        if (isset($params['search'])) {
            $time = '01/' . $params['search'];
            $parseTime = Carbon::createFromFormat('d/m/Y', $time);
            $start = $parseTime->startOfMonth()->format('Y-m-d 00:00:00');
            $end = $parseTime->lastOfMonth()->format('Y-m-d 23:59:59');
            $query = $query->whereBetween('created_at', [$start, $end]);
        }

        return $query->orderBy('id', 'desc')->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
    }

    public function statisticalDeposit($userId, $start, $end)
    {
        return $this->query()->select('amount')->where('user_id', $userId)->whereBetween('created_at', [$start, $end])->sum('amount');
    }
}
