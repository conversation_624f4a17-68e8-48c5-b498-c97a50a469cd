<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('about_us_album_photos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('album_id')->comment('id album');
            $table->string('photo_url')->nullable()->comment('duong dan anh');
            $table->tinyInteger('type')->nullable()->comment('loai anh');
            // $table->foreign('album_id')->references('id')->on('about_us_album')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('about_us_album_photos');
    }
};
