<?php

namespace App\Rules\Frontend;

use Hash;
use Illuminate\Contracts\Validation\Rule;

class CheckCurrentPasswordRule implements Rule
{

    public function __construct()
    {

    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $user = auth('client')->user();
        return Hash::check($value, $user->password);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('frontend/collaborator/message.current_password_error');
    }
}
