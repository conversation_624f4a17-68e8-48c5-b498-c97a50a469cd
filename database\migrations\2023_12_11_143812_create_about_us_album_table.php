<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('about_us_albums', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('ten album');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void\
     *
     */
    public function down()
    {
        Schema::dropIfExists('about_us_albums');
    }
};
