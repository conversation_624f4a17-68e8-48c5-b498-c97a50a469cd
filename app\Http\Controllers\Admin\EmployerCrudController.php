<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\EmployerRequest;
use App\Models\User;
use App\Models\Company;
use App\Models\EmployerType;
use App\Models\EmployeeRole;
use App\Models\Role;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Prologue\Alerts\Facades\Alert;
use Carbon\Carbon;

/**
 * Class EmployerCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class EmployerCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(User::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/employers');
        CRUD::setEntityNameStrings('nhà tuyển dụng', 'nhà tuyển dụng');
        // CRUD::setListView('admin.pages.employer.crud_list');
        // Chỉ hiển thị các user có role employer
        // $this->crud->addClause('whereHas', 'roles', function ($query) {
        //     $query->where('roles.name', 'employer');
        // });
        $this->crud->addClause('where', 'type', 'employer');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Giới hạn số bản ghi mỗi trang
        $this->crud->setDefaultPageLength(25);
        $this->crud->setPageLengthMenu([10, 25, 50, 100]);
        // Tắt các operations không cần thiết
        $this->crud->denyAccess(['create', 'delete']);

        // Tắt tất cả các nút mặc định
        $this->crud->removeAllButtons();

        // Thêm nút "tạo nhà tuyển dụng"
        $this->crud->addButtonFromView('top', 'employer.create_employer', 'admin.buttons.create_employer', 'beginning');
        // Thêm nút edit tùy chỉnh
        $this->crud->addButtonFromView('line', 'employer.custom_edit', 'admin.pages.employer.list_button', 'beginning');
        // Thêm widgets thống kê
        // $this->crud->enableDetailsRow();

        // Widget thống kê tổng quan
        Widget::add([
            'type'         => 'div',
            'class'        => 'row',
            'content'      => [
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-primary col-md-12',
                    'value'        => $this->getTotalEmployers(),
                    'description'  => 'Tổng số nhà tuyển dụng',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Tổng cộng tất cả nhà tuyển dụng trong hệ thống',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-success col-md-12',
                    'value'        => $this->getTotalEmployersCurrentMonth(),
                    'description'  => 'Nhà tuyển dụng tháng này',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số nhà tuyển dụng đăng ký trong tháng này',
                ],
                // [
                //     'type'         => 'progress',
                //     'class'        => 'card text-white bg-info col-md-12',
                //     'value'        => $this->getTotalVerifiedEmployers(),
                //     'description'  => 'Đã xác thực',
                //     'progress'     => 100,
                //     'progressClass' => 'progress-bar bg-light',
                //     'hint'         => 'Số nhà tuyển dụng đã xác thực công ty',
                // ]
            ]
        ])->to('before_content');

        // Cột ID
        CRUD::column('id')
            ->label('ID')
            ->type('number');

        // Cột tên
        CRUD::column('name')
            ->label('Tên nhà tuyển dụng')
            ->type('custom_html')
            ->value(function ($entry) {
                $html = '<strong>' . e($entry->name) . '</strong>';
                if ($entry->is_real) {
                    // $html .= '<br><small class="badge badge-success mt-2">Nhà tuyển dụng thật</small>';
                } else {
                    $html .= '<br><small class="badge badge-danger mt-2">Tài khoản ảo</small>';
                }
                return $html;
            });

        // Cột email
        CRUD::column('email')
            ->label('Email')
            ->type('email');

        // // Cột số điện thoại
        // CRUD::column('mobile')
        //     ->label('Số điện thoại')
        //     ->type('text');

        // Cột công ty
        CRUD::column('company_value')
            ->label('Công ty')
            ->type('text')
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhereHas('company', function ($q) use ($searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            });


        // Cột trạng thái xác thực email
        CRUD::column('email_verified_at')
            ->label('Email đã xác thực')
            ->type('boolean')
            ->options([
                1 => 'Đã xác thực',
                0 => 'Chưa xác thực'
            ])
            ->wrapper([
                'element' => 'span',
                'class' => function ($crud, $column, $entry, $related_key) {
                    return $entry->email_verified_at ? 'badge badge-success' : 'badge badge-warning';
                }
            ]);
        // Cột trạng thái xác thực email
        CRUD::column('is_active')
            ->label('Kích hoạt')
            ->type('boolean')
            ->options([
                1 => 'Đã kích hoạt',
                0 => 'Chưa kích hoạt'
            ])
            ->wrapper([
                'element' => 'span',
                'class' => function ($crud, $column, $entry, $related_key) {
                    return $entry->is_active ? 'badge badge-success' : 'badge badge-warning';
                }
            ]);


        // Cột số dư ví
        CRUD::column('wallet_balance')
            ->label('Số dư')
            ->type('custom_html')
            ->value(function ($entry) {
                $balance = $entry->wallet ? number_format($entry->wallet->amount, 0, ',', '.') . ' VNĐ' : '0 VNĐ';
                $lookupUrl = route('admin.employer-lookup.index', ['email' => $entry->email]);

                return '<a href="' . $lookupUrl . '" target="_blank" class="btn btn-sm btn-outline-primary" title="Tra cứu thông tin chi tiết của ' . e($entry->name) . '">'
                    . $balance . ' <i class="fa fa-external-link-alt"></i></a>';
            })
            ->orderable(false)
            ->searchLogic(false);

        // Cột tổng số job
        CRUD::column('total_jobs')
            ->label('Số job')
            ->type('custom_html')
            ->value(function ($entry) {
                $totalJobs = $entry->jobs()->count();
                if ($totalJobs > 0) {
                    $url = '/' . config('backpack.base.route_prefix') . '/jobs-management?employer_id=' . $entry->id;
                    return '<a target="_blank" href="' . $url . '" class="btn btn-sm btn-info" title="Xem danh sách job của ' . e($entry->name) . '">' . $totalJobs . '</a>';
                }
                return '<span class="text-muted">0</span>';
            })
            ->orderable(false)
            ->searchLogic(false);

        // Cột ngày tạo
        CRUD::column('created_at')
            ->label('Ngày tạo');
        // ->type('datetime')
        // ->format('d/m/Y H:i');

        // Tùy chỉnh ordering
        $this->crud->orderBy('id', 'desc');

        // Thêm custom button
        // $this->crud->addButtonFromView('line', 'deposit', 'deposit', 'end');
        // $this->crud->addButtonFromView('line', 'wallet_history', 'wallet_history', 'end');

        // Thêm filters
        // $this->crud->addFilter(
        //     [
        //         'type'  => 'dropdown',
        //         'name'  => 'company_id',
        //         'label' => 'Công ty'
        //     ],
        //     function () {
        //         return Company::orderBy('name')->pluck('name', 'id')->toArray();
        //     },
        //     function ($value) {
        //         $this->crud->addClause('where', 'company_id', $value);
        //     }
        // );

        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'verified',
                'label' => 'Trạng thái xác thực'
            ],
            [
                '1' => 'Đã xác thực',
                '0' => 'Chưa xác thực'
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_active', $value);
                // if ($value == '1') {
                //     $this->crud->addClause('where', 'company_id', '>', 0);
                // } else {
                //     $this->crud->addClause('where', function ($query) {
                //         $query->where('company_id', 0)->orWhereNull('company_id');
                //     });
                // }
            }
        );

        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'email_verified',
                'label' => 'Trạng thái xác thực email'
            ],
            [
                '1' => 'Email đã xác thực',
                '0' => 'Email chưa xác thực'
            ],
            function ($value) {
                if ($value == '1') {
                    $this->crud->addClause('whereNotNull', 'email_verified_at');
                } else {
                    $this->crud->addClause('whereNull', 'email_verified_at');
                }
            }
        );
        // Filter tin thật/ảo
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_real',
                'label' => 'Tin thật/ảo'
            ],
            [
                '1' => 'Tài khoản thật',
                '0' => 'Tài khoản ảo',
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_real', $value);
            }
        );

        $this->crud->addFilter(
            [
                'type'  => 'date_range',
                'name'  => 'created_at',
                'label' => 'Khoảng thời gian tạo'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from && $dates->to) {
                    $this->crud->addClause('whereBetween', 'created_at', [
                        $dates->from . ' 00:00:00',
                        $dates->to . ' 23:59:59'
                    ]);
                }
            }
        );
        // $this->crud->addFilter(
        //     [
        //         'type' => 'date',
        //         'name' => 'created_at_2',
        //         'label' => 'Ngày tạo'
        //     },
        //     false,
        //     function ($value) {
        //         $this->crud->addClause('whereBetween', 'created_at', [
        //             $value . ' 00:00:00',
        //             $value . ' 23:59:59'
        //         ]);
        //     }
        // );
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(EmployerRequest::class);

        // Tab thông tin cơ bản
        CRUD::field('name')
            ->label('Tên nhà tuyển dụng')
            ->type('text')
            ->tab('Thông tin cơ bản');

        CRUD::field('email')
            ->label('Email')
            ->type('email')
            ->tab('Thông tin cơ bản');

        CRUD::field('mobile')
            ->label('Số điện thoại')
            ->type('text')
            ->tab('Thông tin cơ bản');

        CRUD::field('password')
            ->label('Mật khẩu')
            ->type('password')
            ->tab('Thông tin cơ bản');

        CRUD::field('birthday')
            ->label('Ngày sinh')
            ->type('date')
            ->tab('Thông tin cơ bản');

        CRUD::field('gender')
            ->label('Giới tính')
            ->type('select_from_array')
            ->options([
                'male' => 'Nam',
                'female' => 'Nữ',
                'other' => 'Khác'
            ])
            ->tab('Thông tin cơ bản');

        // Tab thông tin công ty
        CRUD::field('company_id')
            ->label('Công ty')
            ->type('select2_from_ajax')
            ->entity('company')
            ->attribute('name')
            ->data_source('/admin/ajax/companies')
            ->placeholder('Chọn công ty')
            ->minimum_input_length(0)
            ->tab('Thông tin công ty');

        CRUD::field('company_name')
            ->label('Tên công ty (tạm thời)')
            ->type('text')
            ->hint('Sử dụng khi chưa có công ty trong hệ thống')
            ->tab('Thông tin công ty');

        CRUD::field('mst')
            ->label('Mã số thuế')
            ->type('text')
            ->tab('Thông tin công ty');

        // Tab thông tin employer type
        // CRUD::field('employer_type.employee_role_id')
        //     ->label('Vai trò')
        //     ->type('select2')
        //     ->entity('employeeRole')
        //     ->attribute('name')
        //     ->model(EmployeeRole::class)
        //     ->tab('Thông tin vai trò');

        // CRUD::field('employer_type.company_size')
        //     ->label('Quy mô công ty')
        //     ->type('select_from_array')
        //     ->options([
        //         '1-10' => '1-10 nhân viên',
        //         '11-50' => '11-50 nhân viên',
        //         '51-200' => '51-200 nhân viên',
        //         '201-500' => '201-500 nhân viên',
        //         '500+' => 'Trên 500 nhân viên'
        //     ])
        //     ->tab('Thông tin vai trò');
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();

        // Loại bỏ field password trong update
        CRUD::removeField('password');

        // Thêm field đổi mật khẩu riêng
        CRUD::field('new_password')
            ->label('Mật khẩu mới (để trống nếu không đổi)')
            ->type('password')
            ->tab('Thông tin cơ bản');
    }

    /**
     * Define what happens when the Show operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-show
     * @return void
     */
    protected function setupShowOperation()
    {
        $this->setupListOperation();

        // Thêm thông tin chi tiết
        CRUD::column('birthday_value')
            ->label('Ngày sinh')
            ->type('text');

        CRUD::column('gender')
            ->label('Giới tính')
            ->type('text');

        CRUD::column('email_verified_at')
            ->label('Email đã xác thực')
            ->type('datetime')
            ->format('d/m/Y H:i');

        CRUD::column('company.name')
            ->label('Tên công ty')
            ->type('text');

        CRUD::column('company.mst')
            ->label('Mã số thuế công ty')
            ->type('text');

        CRUD::column('userEmployerType.employeeRole.name')
            ->label('Vai trò')
            ->type('text');

        CRUD::column('userEmployerType.company_size')
            ->label('Quy mô công ty')
            ->type('text');
    }

    /**
     * Store operation
     */
    public function store()
    {
        $this->crud->hasAccessOrFail('create');

        $request = $this->crud->validateRequest();

        // Hash password
        if (isset($request['password'])) {
            $request['password'] = Hash::make($request['password']);
        }

        // Tạo user
        $user = User::create($request);

        // Tạo employer type nếu có dữ liệu
        if (isset($request['employer_type'])) {
            $employerTypeData = $request['employer_type'];
            $employerTypeData['user_id'] = $user->id;
            EmployerType::create($employerTypeData);
        }

        // Assign role employer
        $employerRole = Role::where('name', 'employer')->first();
        if ($employerRole) {
            $user->roles()->attach($employerRole->id);
        }

        Alert::success(trans('backpack::crud.insert_success'))->flash();

        return $this->crud->performSaveAction($user->getKey());
    }

    /**
     * Update operation
     */
    public function update()
    {
        $this->crud->hasAccessOrFail('update');

        $request = $this->crud->validateRequest();
        $id = $this->crud->getCurrentEntryId();

        // Hash password mới nếu có
        if (isset($request['new_password']) && !empty($request['new_password'])) {
            $request['password'] = Hash::make($request['new_password']);
        }
        unset($request['new_password']);

        // Update user
        $user = User::findOrFail($id);
        $user->update($request);

        // Update employer type
        if (isset($request['employer_type'])) {
            $employerType = $user->userEmployerType;
            if ($employerType) {
                $employerType->update($request['employer_type']);
            } else {
                $employerTypeData = $request['employer_type'];
                $employerTypeData['user_id'] = $user->id;
                EmployerType::create($employerTypeData);
            }
        }

        Alert::success(trans('backpack::crud.update_success'))->flash();

        return $this->crud->performSaveAction($user->getKey());
    }

    /**
     * Method nạp tiền cho nhà tuyển dụng
     */
    public function deposit(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'amount' => 'required|numeric|min:1000',
            'note' => 'nullable|string|max:255'
        ]);

        try {
            // Tạo hoặc lấy ví của user
            $wallet = $user->wallet;
            if (!$wallet) {
                $wallet = $user->wallet()->create(['balance' => 0]);
            }

            // Cập nhật số dư
            $wallet->increment('balance', $request->amount);

            // Tạo transaction record (nếu có model WalletTransaction)
            if (class_exists('\App\Models\WalletTransaction')) {
                \App\Models\WalletTransaction::create([
                    'user_id' => $user->id,
                    'type' => 'deposit',
                    'amount' => $request->amount,
                    'balance_after' => $wallet->balance,
                    'description' => $request->note ?: 'Nạp tiền từ admin',
                    'admin_id' => auth()->id()
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Nạp tiền thành công',
                'new_balance' => number_format($wallet->balance, 0, ',', '.') . ' VNĐ'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Xem lịch sử ví của nhà tuyển dụng
     */
    public function walletHistory($id)
    {
        $user = User::with(['wallet.transactions' => function ($query) {
            $query->orderBy('created_at', 'desc');
        }])->findOrFail($id);

        return view('admin.pages.employer.wallet_history', compact('user'));
    }

    /**
     * Helper methods cho thống kê
     */
    private function getTotalEmployers()
    {
        return User::where('type', 'employer')->count();
    }

    private function getTotalEmployersCurrentMonth()
    {
        return User::where('type', 'employer')->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }

    private function getTotalVerifiedEmployers()
    {
        return User::where('type', 'employer')->where('company_id', '>', 0)->count();
    }
}
