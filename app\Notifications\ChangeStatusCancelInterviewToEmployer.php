<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelInterviewToEmployer extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $point;

    public function __construct($wareHouseCvSellingBuy,$point)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->point = $point;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $point = $this->point;
        $link = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);
        return (new MailMessage)
            ->view('email.changeStatusCancelInterviewToEmployer', [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'point' => $point,
                'urlWallet' => route('employer-wallet'),
                'urlMarket' => route('market-cv'),
                'link' => $link,
            ])
            ->subject('[Recland] Thông báo hủy phỏng vấn ứng viên  '.$candidateName.' vị trí  '.$position);

    }




}
