<?php

namespace App\Notifications;

use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecAgreeComplainEmployerSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $submitCv;
    protected $point;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$submitCv,$point)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->submitCv = $submitCv;
        $this->point = $point;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $companyName = $this->employer->name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        return (new MailMessage)
            ->view('email.ctv_dongy_ntd_submit', [
                'name'        => $this->employer->name,
                'companyName' => $companyName,
                'position'    => $position,
                'urlWallet'   => route('employer-wallet'),
                'urlMarket'   => route('market-cv'),
                'point'       => $this->point,
                'type'        => $type,
            ])
            ->subject('[Recland][Case #'.$this->submitCv->id.'] Kết quả khiếu nại');
    }

}
