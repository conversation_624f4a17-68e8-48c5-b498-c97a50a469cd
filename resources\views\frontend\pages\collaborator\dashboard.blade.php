@extends('frontend.layouts.collaborator.app')
@section('content-collaborator')
    <div id="dashboard-collaborator">
        <div class="row">

            <div class="col-md-12 col-lg-9">
                <div class="layout1">
                    <div class="row">
                        <div class="col-md-3 col-item-count">
                            <div class="card">
                                <div class="row">
                                    <div class="col-4 icon1">
                                        <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-number1.svg')}}">
                                    </div>
                                    <div class="col-8">
                                        <div class="text-right">
                                            <div class="title-number">{{$arrLang['cvhienco']}}</div>
                                            <div class="count-number">{{$totalWareHouseCv}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-item-count">
                            <div class="card">
                                <div class="row">
                                    <div class="col-4 icon1">
                                        <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-number2.svg')}}">
                                    </div>
                                    <div class="col-8">
                                        <div class="text-right">
                                            <div class="title-number">{{$arrLang['cvdagioithieu']}}</div>
                                            <div class="count-number">{{$totalSubmitCvService}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-item-count">
                            <div class="card">
                                <div class="row">
                                    <div class="col-4 icon1">
                                        <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-number3.svg')}}">
                                    </div>
                                    <div class="col-8">
                                        <div class="text-right">
                                            <div class="title-number">{{$arrLang['cvdaonboard']}}</div>
                                            <div class="count-number">{{$countJobOnboard}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-item-count">
                            <div class="card">
                                <div class="row">
                                    <div class="col-4 icon1">
                                        <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-number4.svg')}}">
                                    </div>
                                    <div class="col-8">
                                        <div class="text-right">
                                            <div class="title-number">{{$arrLang['tiennhanduoc']}}</div>
                                            <div
                                                class="count-number">{{number_format(auth('client')->user()->bonus_value)}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="layout2">
                    <div class="card card-type2">
                        <div class="header">
                            {{$arrLang['bieudotangtruongtheothang']}} {{$now->format('m/Y')}}
                        </div>
                        <div id="charts"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-3">
                <div class="card card-type3">
                    <div class="header">
                        Truy cập nhanh
                    </div>
                    <div class="quick-access-menu">
                        <div class="quick-access-item">
                            <a href="{{ route('rec-warehousecv') }}?open_popup=1" class="quick-access-link" onclick="openAddCvModal()">
                                <div class="quick-access-icon">
                                    <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-add-cv.svg')}}" alt="Đăng CV mới">
                                </div>
                                <div class="quick-access-content">
                                    <div class="quick-access-title">Đăng CV mới</div>
                                    <div class="quick-access-desc">Thêm CV ứng viên vào kho</div>
                                </div>
                            </a>
                        </div>

                        <div class="quick-access-item">
                            <a target="_blank" href={{ config('settings.global.group_zalo_link') }}" class="quick-access-link">
                                <div class="quick-access-icon">
                                    <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-community.svg')}}" alt="Gia nhập cộng đồng">
                                </div>
                                <div class="quick-access-content">
                                    <div class="quick-access-title">Gia nhập cộng đồng</div>
                                    <div class="quick-access-desc">Kết nối với cộng tác viên</div>
                                </div>
                            </a>
                        </div>

                        {{-- <div class="quick-access-item">
                            <a href="{{ route('guideDeleteData') }}" class="quick-access-link" target="_blank">
                                <div class="quick-access-icon">
                                    <img src="{{asset2('frontend/asset/images/dashboard-ctv/icon-guide.svg')}}" alt="Xem file hướng dẫn">
                                </div>
                                <div class="quick-access-content">
                                    <div class="quick-access-title">File hướng dẫn</div>
                                    <div class="quick-access-desc">Tài liệu hướng dẫn sử dụng</div>
                                </div>
                            </a>
                        </div> --}}
                    </div>
                </div>
                
                <div class="card card-type3 mt-3">
                    <div class="header">
                        Việc làm mới nhất
                    </div>
                    <div class="list-new-cv">
                        @if($latestJobs && $latestJobs->count() > 0)
                            @foreach($latestJobs as $job)
                                <div class="item-new-cv">
                                    <div class="date-time">
                                        <span class="date">{{$job->created_at->format('d/m')}}</span>
                                        {{$job->created_at->diffForHumans()}}
                                    </div>
                                    <div class="job-name">
                                        <a href="{{ route('job-detail', ['slug' => $job->slug]) }}"
                                           class="text-decoration-none text-dark" target="_blank">
                                            {{$job->name}}
                                        </a>
                                    </div>
                                    <div class="name">
                                        @if($job->company)
                                            {{$job->company->name}}
                                        @endif
                                        @if($job->group_address)
                                            @php
                                                $firstAddress = array_values($job->group_address)[0] ?? null;
                                            @endphp
                                            @if($firstAddress)
                                                - {{$firstAddress['city']}}
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="item-new-cv">
                                <div class="name text-muted">
                                    Chưa có việc làm mới nào
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

        </div>

    </div>
@endsection
@section('scripts')
    <script>
        var colors = Highcharts.getOptions().colors;

        Highcharts.chart('charts', {
            chart: {
                type: 'spline'
            },

            legend: {
                symbolWidth: 40
            },

            title: {
                text: ''
            },


            yAxis: {
                title: {
                    text: 'Percentage usage'
                },
                accessibility: {
                    description: 'Percentage usage'
                },
                allowDecimals: false,
            },

            xAxis: {
                title: {
                    text: 'Time'
                },
                categories: [{{implode(',',$daysOfMonth)}}]
            },

            plotOptions: {
                series: {
                    point: {
                        events: {}
                    },
                    cursor: 'pointer'
                }
            },

            series: [
                    @if($statistical)
                    @php $count = 0 @endphp
                    @foreach($statistical as $key => $item)
                    @php $count++ @endphp
                {
                    name: '{{$key}}',
                    data: [{{implode(',',$item)}}],
                    color: colors[{{$count}}]
                },
                @endforeach
                @endif
            ],

            // responsive: {
            //     rules: [{
            //         condition: {
            //             maxWidth: 550
            //         },
            //         chartOptions: {
            //             chart: {
            //                 spacingLeft: 3,
            //                 spacingRight: 3
            //             },
            //             legend: {
            //                 itemWidth: 150
            //             },
            //             xAxis: {
            //                 categories: ['Dec. 2010', 'May 2012', 'Jan. 2014', 'July 2015', 'Oct. 2017', 'Sep. 2019'],
            //                 title: ''
            //             },
            //             yAxis: {
            //                 visible: false
            //             }
            //         }
            //     }]
            // }
        });

        // Function để mở popup đăng CV mới
        function openAddCvModal() {
            // Redirect đến trang quản lý CV và trigger mở popup
            window.location.href = "{{ route('rec-warehousecv') }}?action=add_cv";
        }
    </script>
@endsection

