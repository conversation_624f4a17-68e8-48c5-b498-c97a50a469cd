
# SRS - F02.1: Submit CV - Giai đoạn CV Data

**Phiên bản:** 6.0
**Ngày:** 2025-07-08
**T<PERSON>c giả:** Gemini

---

### 1. <PERSON><PERSON> tả

Quy trình này mô tả việc một ứng viên (UV) tự ứng tuyển hoặc một Cộng tác viên (CTV) giới thiệu ứng viên vào một tin tuyển dụng (Job). Nhà tuyển dụng (NTD) sẽ trả một khoản phí ban đầu (bằng **Point**) để xem thông tin chi tiết của hồ sơ này. Logic được điều phối bởi `WareHouseSubmitCvController` và `SubmitCvService`.

### 2. Đối tượng tham gia

*   **Ứng viên (UV):** <PERSON><PERSON><PERSON><PERSON> tự ứng tuyển.
*   **Cộng tác viên (CTV):** Người giới thiệu ứng viên.
*   **<PERSON><PERSON><PERSON> tuyển dụng (NTD):** Người nhận và trả phí xem CV.

### 3. Điều kiện tiên quyết

*   Một tin tuyển dụng (`jobs` table) đang ở trạng thái hoạt động.
*   UV/CTV đã đăng nhập.
*   Ví của NTD có đủ số dư Point.

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (HTTP Request - Submit CV):** UV/CTV submit form ứng tuyển/giới thiệu. Request được gửi đến phương thức `saveData` hoặc `candidateIntroduction` trong `WareHouseSubmitCvService`.

2.  **Bước 2 (Service xử lý logic - Submit CV):** Toàn bộ logic được bao bọc trong một **Database Transaction**.
    *   **a. Kiểm tra điều kiện:** Service kiểm tra các điều kiện nghiệp vụ (ví dụ: ứng viên này đã được nộp vào job này chưa).
    *   **b. Tạo bản ghi ứng tuyển:** Service sử dụng `SubmitCvRepository` để tạo một bản ghi mới trong bảng `submit_cvs` với `status` = `Waitingcandidateconfirm` (1) và `is_self_apply` tương ứng.
    *   **c. Ghi lịch sử:** Service sử dụng `SubmitCvHistoryStatusRepository` để ghi lại hành động "Nộp hồ sơ".

3.  **Bước 3 (Thông báo):** Service dispatch các notification sau:
    *   **`EmployerIntroduceCandidate` (gửi cho NTD):** Thông báo có ứng viên mới cho vị trí.
    *   **`RecIntroduceCandidate` (gửi cho CTV):** Thông báo giới thiệu ứng viên thành công.
    *   **`RecNewCv` (gửi cho Admin):** Thông báo có CV mới từ CTV.
    *   **`RecApplyJobNotiSendAdmin` (gửi cho Admin):** Nếu là tự ứng tuyển, thông báo ứng viên đã tự ứng tuyển.

4.  **Bước 4 (HTTP Request - Mở khóa CV):** NTD xem danh sách hồ sơ và nhấn "Chấp nhận" / "Mở khóa CV". Request được gửi đến phương thức `submitCvChangeStatus` trong `SubmitCvService` với `status_recruitment` là `WaitingPayment` (21).

5.  **Bước 5 (Service xử lý logic - Mở khóa CV):** Toàn bộ logic được bao bọc trong **DB Transaction**.
    *   **a. Kiểm tra:** Service kiểm tra lại trạng thái của `submit_cvs` phải là `Waitingcandidateconfirm` (1).
    *   **b. Trừ Point NTD:** Service gọi `WalletService` để trừ phí xem CV từ ví Point của NTD và ghi log vào `wallet_transactions` (liên kết đa hình với `SubmitCv`).
    *   **c. Trả hoa hồng cho CTV (Synchronous):** Nếu `submitCv->authorize` là `0` (không ủy quyền) và `submitCv->bonus_type` là 'cv', Service sẽ tính toán và cộng hoa hồng (`submitCv->getSubmitBonusForCtv()`) vào ví của CTV ngay lập tức, đồng thời ghi log giao dịch cho ví của CTV.
    *   **d. Cập nhật trạng thái:** Service cập nhật `status` trong `submit_cvs` thành `WaitingPayment` (21).
    *   **e. Ghi lịch sử:** Ghi lại sự kiện thay đổi trạng thái vào `submit_cvs_history_status`.

6.  **Bước 6 (Commit & Phản hồi):** DB Transaction được commit. Controller trả về HTTP `200 OK`. Frontend hiển thị thông tin chi tiết của ứng viên.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi ứng viên đã tồn tại:**
    *   **Nguyên nhân:** Tại **Bước 2a**, `SubmitCvService` kiểm tra và phát hiện `warehouse_cv_id` này đã có bản ghi `submit_cvs` cho `job_id` này.
    *   **Xử lý:** Service ném ra `Exception`. Controller bắt và trả về HTTP `409 Conflict` với thông báo "Ứng viên này đã được nộp vào tin tuyển dụng này trước đó."

*   **5.2. Lỗi không đủ Point:**
    *   **Nguyên nhân:** Tại **Bước 5b**, ví NTD không đủ Point.
    *   **Xử lý:** `WalletService` ném Exception. Transaction được rollback. Controller trả về HTTP `422` với thông báo lỗi.

*   **5.3. Lỗi trạng thái không hợp lệ khi chấp nhận:**
    *   **Nguyên nhân:** Tại **Bước 5a**, NTD cố gắng chấp nhận một CV không ở trạng thái `Waitingcandidateconfirm` (1).
    *   **Xử lý:** Service ném ra `Exception`. Controller trả về HTTP `409 Conflict` với thông báo "Hành động không thể thực hiện do hồ sơ đã được xử lý trước đó."

*   **5.4. NTD từ chối hồ sơ:**
    *   **Nguyên nhân:** NTD nhấn nút "Từ chối" (`submitCvChangeStatus` với `status_recruitment` là `CandidateCancelApply` - 2).
    *   **Xử lý:** Controller gọi `submitCvChangeStatus` trong `SubmitCvService`. Service cập nhật `status` trong `submit_cvs` thành `CandidateCancelApply` (2) và ghi log lịch sử. Không có phí nào bị trừ. Controller trả về HTTP `200 OK`.

*   **5.5. Ứng viên từ chối lời mời qua email (`verifyEmailCandidate`):**
    *   **Mô tả:** Ứng viên từ chối lời mời tuyển dụng thông qua liên kết trong email.
    *   **Khi nào xảy ra:** Khi ứng viên click vào link từ chối trong email.
    *   **Xử lý:**
        *   `submitCv->status` được cập nhật thành `2` (Candidate Cancel Apply).
        *   `submitCv->confirm_token` được đặt thành `null`.
        *   Thông báo được gửi đến CTV (`CandidateRejectRecruitmentRecSubmit`), Admin (`CandidateRejectRecruitmentAdminSubmit`).
        *   Lịch sử trạng thái được ghi lại.

### 6. Yêu cầu phi chức năng

*   **Kiểm toán:** Mọi thay đổi trạng thái của một đơn ứng tuyển phải được ghi lại trong `submit_cvs_history_status`, bao gồm cả người thực hiện và thời gian thực hiện.
*   **Toàn vẹn dữ liệu:** Việc thanh toán và cập nhật trạng thái ở Bước 6 phải được thực hiện trong một DB Transaction.

### 7. Mô hình dữ liệu liên quan

*   **Models:** `SubmitCv`, `SubmitCvHistoryStatus`, `Job`, `Wallet`, `WalletTransaction`, `SubmitCvHistoryPayment`.
*   **Repositories:** `SubmitCvRepository`, `SubmitCvHistoryStatusRepository`, `SubmitCvHistoryPaymentRepository`.
*   **Services:** `SubmitCvService`, `WalletService`.
*   **Controller:** `RecController` (Frontend).

### 8. Các Job liên quan (Background Processes)

*   **`RecSumExpiredPointSubmit`**
    *   **Mô tả:** Job này xử lý các khiếu nại đã hết hạn mà không có phản hồi từ CTV hoặc Admin. Nó tự động chấp nhận khiếu nại và hoàn tiền cho NTD.
    *   **Khi nào được dispatch:** Khi NTD gửi khiếu nại (`SubmitCvService->complain()`), job này được dispatch với một độ trễ (ví dụ: 7 ngày).
    *   **Happy Path:** Nếu `submit_cv->status_complain` đang ở trạng thái chờ phản hồi (1 hoặc 2) và `statusComplain` trong job khớp với trạng thái đó, job sẽ:
        *   Cập nhật `submit_cv->status_complain` thành 3 (CTV chấp nhận) hoặc 4 (Admin chấp nhận).
        *   Hoàn lại Point cho NTD dựa trên `bonus_type` của job và thời gian khiếu nại (đối với onboard).
        *   Ghi log hoàn tiền vào `submitCvHistoryPaymentRepository` và `wallet_transactions`.
        *   Gửi thông báo cho CTV và NTD về việc khiếu nại đã được chấp nhận và tiền đã được hoàn.
    *   **Sad Path / Xử lý lỗi:**
        *   Nếu `submit_cv` không tồn tại hoặc `status_complain` không ở trạng thái chờ phản hồi, job sẽ không thực hiện hành động nào.
        *   Lỗi trong quá trình hoàn tiền sẽ khiến job thất bại và có thể được thử lại.

### 9. Các thông báo liên quan

*   **`EmployerIntroduceCandidate` (gửi cho NTD):**
    *   **Mô tả:** Thông báo có ứng viên mới được giới thiệu vào vị trí.
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, link đến trang quản lý ứng viên của job.
    *   **Kênh:** Mail, Database.
*   **`RecIntroduceCandidate` (gửi cho CTV):**
    *   **Mô tả:** Thông báo CTV đã giới thiệu ứng viên thành công.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, vị trí, link đến trang quản lý CV đã nộp của CTV.
    *   **Kênh:** Mail, Database.
*   **`RecNewCv` (gửi cho Admin):**
    *   **Mô tả:** Thông báo có CV mới được thêm vào hệ thống bởi CTV.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, link đến trang quản lý CV của Admin.
    *   **Kênh:** Mail, Database.
*   **`RecApplyJobNotiSendAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên tự ứng tuyển vào vị trí.
    *   **Nội dung chính:** Tên ứng viên, vị trí, link đến trang quản lý ứng viên của Admin.
    *   **Kênh:** Mail.

