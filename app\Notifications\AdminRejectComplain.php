<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminRejectComplain extends Notification implements ShouldQueue
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $cvSellingBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$cvSellingBuy)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->cvSellingBuy = $cvSellingBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $candidateName = '';
        $position = '';
        $type = '';
        $content = $this->cvSellingBuy->txt_complain;
        $image = $this->cvSellingBuy->img_complain;
        $companyName = $this->employer->name;

        if (!empty($this->cvSellingBuy->wareHouseCvSelling)){
            $type = $this->cvSellingBuy->wareHouseCvSelling->type_of_sale;
        }

        if (!empty($this->cvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->cvSellingBuy->job)){
                $position = $this->cvSellingBuy->job->name;
            }
        }

        return (new MailMessage)
            ->view('email.ctv_dongy', [
                'name' => $this->rec->name,
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type,
                'content' => $content,
                'image' => gen_url_file_s3($image),

            ])
            ->subject('[Recland] Kết quả khiếu nại');
    }

}
