<?php

namespace App\Services\Admin;

use App\Jobs\DepositRefundRejectOffer;
use App\Jobs\OutOfDateBookInterview;
use App\Jobs\PassInterview;
use App\Jobs\UpdateRecuirmentThreeDay;
use App\Notifications\ChangeStatusCancelInterviewToAdmin;
use App\Notifications\ChangeStatusCancelInterviewToEmployer;
use App\Notifications\ChangeStatusCancelInterviewToRec;
use App\Notifications\ChangeStatusCancelOnboardToAdmin;
use App\Notifications\ChangeStatusCancelOnboardToRec;
use App\Notifications\ComplaintsAcceptedToEmployer;
use App\Notifications\ComplaintsAcceptedToRec;
use App\Notifications\ComplaintsRejectToEmployer;
use App\Notifications\ComplaintsRejectToRec;
use App\Notifications\EmailConfirmInterView;
use App\Notifications\EmailConfirmOnboard;
use App\Notifications\EmailRejectInterView;
use App\Notifications\EmailRejectOnboard;
use App\Notifications\PaymentInterviewToRec;
use App\Notifications\PaymentOpenTurnCv;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyDiscussRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingEvaluateRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use App\Repositories\WareHouseCvSellingRepository;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingBuyService
{
    protected $wareHouseCvSellingRepository;
    protected $wareHouseCvSellingBuyRepository;
    protected $wareHouseCvSellingEvaluateRepository;
    protected $wareHouseCvSellingBuyDiscussRepository;
    protected $wareHouseCvSellingBuyBookRepository;
    protected $wareHouseCvSellingBuyHistoryStatusRepository;
    protected $wareHouseCvSellingHistoryBuyRepository;
    protected $wareHouseCvSellingBuyOnboardRepository;
    protected $payinMonthRepository;

    public function __construct(
        WareHouseCvSellingRepository $wareHouseCvSellingRepository,
        WareHouseCvSellingBuyRepository $wareHouseCvSellingBuyRepository,
        WareHouseCvSellingEvaluateRepository $wareHouseCvSellingEvaluateRepository,
        WareHouseCvSellingBuyDiscussRepository $wareHouseCvSellingBuyDiscussRepository,
        WareHouseCvSellingBuyBookRepository $wareHouseCvSellingBuyBookRepository,
        WareHouseCvSellingBuyHistoryStatusRepository $wareHouseCvSellingBuyHistoryStatusRepository,
        WareHouseCvSellingHistoryBuyRepository $wareHouseCvSellingHistoryBuyRepository,
        WareHouseCvSellingBuyOnboardRepository $wareHouseCvSellingBuyOnboardRepository,
        PayinMonthRepository $payinMonthRepository
    ) {
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
        $this->wareHouseCvSellingBuyRepository = $wareHouseCvSellingBuyRepository;
        $this->wareHouseCvSellingEvaluateRepository = $wareHouseCvSellingEvaluateRepository;
        $this->wareHouseCvSellingBuyDiscussRepository = $wareHouseCvSellingBuyDiscussRepository;
        $this->wareHouseCvSellingBuyBookRepository = $wareHouseCvSellingBuyBookRepository;
        $this->wareHouseCvSellingBuyHistoryStatusRepository = $wareHouseCvSellingBuyHistoryStatusRepository;
        $this->wareHouseCvSellingHistoryBuyRepository = $wareHouseCvSellingHistoryBuyRepository;
        $this->wareHouseCvSellingBuyOnboardRepository = $wareHouseCvSellingBuyOnboardRepository;
        $this->payinMonthRepository = $payinMonthRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        try {
            $data = $this->wareHouseCvSellingBuyRepository->getListCvSellingBuyAll($params, $order, $paginate);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.candidate_name',
                    'data-orderable' => 'false',
                ],
                'value' => 'Tên ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.candidate_email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.candidate_mobile',
                    'data-orderable' => 'false',
                ],
                'value' => 'SĐT',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'created_at_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Thời gian mua',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.user_id',
                    'data-orderable' => 'false',
                ],
                'value' => 'Mã CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_recruitment_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái tuyển dụng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_payment_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái thanh toán NTD',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_payment_ctv',
                    'data-fn' => 'renderStatusPaymentCtv',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái thanh toán CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.authority_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái ủy quyền',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_complain_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái khiếu nại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.url_cv_public',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv_selling.ware_house_cv.url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'employer.company.name',
                    'data-fn'   => 'renderCompanyName',
                    'data-orderable' => 'false',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'employer.name',
                    'data-fn'   => 'renderEmployer',
                    'data-orderable' => 'false',
                ],
                'value' => 'HR',
            ]
        ];

        $renderAction = [
            'actionDetail',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('luot-ban-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function detail($id)
    {
        return $this->wareHouseCvSellingBuyRepository->findCvId($id);
    }

    public function getDataPreview($params)
    {
        $data = [];
        if ($params['tab'] == 'thaoluan') {
        }

        if ($params['tab'] == 'khieunai') {
        }

        if ($params['tab'] == 'danhgia') {
            //            $data = $this->wareHouseCvSellingEvaluateRepository->getListCvSellingEvaluate($params);
        }

        if ($params['tab'] == 'trangthai') {
        }

        if ($params['tab'] == 'thanhtoan') {
        }

        return $data;
    }

    public function getDataComplain($params)
    {
        //        dd($this->wareHouseCvSellingBuyRepository->find($params['id']));
        return $this->wareHouseCvSellingBuyRepository->find($params['id']);
    }

    /**
     * @param $params
     * @return false
     * Admin phê duyệt khiếu nại
     * HT NTD
     */
    public function updateComplain($params)
    {
        if ($params['type'] != 4 && $params['type'] != 5) return false;
        //Neu admin xac nhan thi hoan Tien cho NTD
        $statusPayment = '';
        $statusRecruitment = '';
        $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($params['id']);
        //CTV Tu chối thì admin mới dc duyệt
        if ($cvSellingBuy->status_complain != 2) return false;
        //trang thai khieu nai
        $wareHouseCvSellingBuySave = [
            'status_complain' => $params['type']
        ];

        if ($params['type'] == 4) {
            //Hoan tien tra NTD
            //CV data
            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' || $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                //ghi log mua
                $this->wareHouseCvSellingHistoryBuyRepository->create([
                    'user_id'                     => $cvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id' => $cvSellingBuy->id,
                    'type'                        => 1,                                                                //0 trừ tiền, 1 hoàn tiền
                    'percent'                     => 100,                                                              //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale'                => $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                    'point'                       => $cvSellingBuy->point,
                    'balance'                     => $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point,
                    'status'                      => 0,
                ]);

                if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv') {
                    //interview ko thay đổi trạng thái
                    //Log thay đổi trang thái
                    /*$this->wareHouseCvSellingBuyHistoryStatusRepository->create([
                        'user_id' => $cvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id' => $cvSellingBuy->id,
                        'status_recruitment' => config('constant.status_recruitment_revert.CancelBuyCVdata'),
                        'type' => 'employer',
                        'authority' => $cvSellingBuy->wareHouseCvSelling->authority
                    ]);*/
                    $statusRecruitment = config('constant.status_recruitment_revert.CancelBuyCVdata');
                    $statusPayment = 3; //Hoan tien
                }

                if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                    $statusPayment = 3; //Hoan tien
                }
                //Cộng point của NTD
                $point = $cvSellingBuy->point;
                // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point;
                $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                $cvSellingBuy->employer->wallet->addAmount($cvSellingBuy->point, $cvSellingBuy, 'Cộng tiền vào ví', 'complaints_accepted_to_rec');
                // $cvSellingBuy->employer->wallet->save();


                $cvSellingBuy->rec->notify(new ComplaintsAcceptedToRec($cvSellingBuy));
                $cvSellingBuy->employer->notify(new ComplaintsAcceptedToEmployer($cvSellingBuy, $point));
            }

            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                //14 => 'Trial work',
                if ($cvSellingBuy->status_recruitment == 14) {
                    $onboard = $this->wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($cvSellingBuy->id);
                    $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                    $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                    $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                    $dateComplain = Carbon::createFromFormat('Y-m-d H:i:s', $cvSellingBuy->date_complain);
                    $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                    $dateComplain =  strtotime($dateComplain);
                    $percent = $point = 0;
                    //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                    if ($createDateTrailWork30 >= $dateComplain) {
                        $percent = 100;
                        $point = $cvSellingBuy->point;
                    }
                    //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                    if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                        $percent = 70;
                        $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                    }
                    //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                    if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                        $percent = 50;
                        $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                    }
                    //ghi log hoan tien
                    $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $cvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                        'percent'                       =>  $percent,
                        'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $point,
                        'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $point,
                        'status'                        =>  0
                    ]);

                    //Cộng point của NTD
                    // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                    $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                    $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Cộng tiền vào ví', 'complaints_accepted_to_rec');
                    // $cvSellingBuy->employer->wallet->save();

                    $statusPayment = 3; //Hoan tiền
                }
                //Hoàn cả 10% Waiting Interview
                else {
                    $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($cvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($wareHouseCvSellingHistoryBuyData) {
                        foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $this->wareHouseCvSellingHistoryBuyRepository->create([
                                'user_id'                       =>  $cvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                                'point'                         =>  $value->point,
                                'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $value->point,
                                'status'                        =>  0
                            ]);
                            $point += $value->point;
                        }
                    }
                    //Cộng point của NTD
                    // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                    $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                    $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Cộng tiền vào ví', 'complaints_accepted_to_rec');
                    // $cvSellingBuy->employer->wallet->save();
                    $statusPayment = 2; //Hoàn cọc
                }

                $cvSellingBuy->rec->notify(new ComplaintsAcceptedToRec($cvSellingBuy));
                $cvSellingBuy->employer->notify(new ComplaintsAcceptedToEmployer($cvSellingBuy, $point));
            }
        }

        //admin Từ chối khiếu nại
        if ($params['type'] == 5) {
            $cvSellingBuy->rec->notify(new ComplaintsRejectToRec($cvSellingBuy));
            $cvSellingBuy->employer->notify(new ComplaintsRejectToEmployer($cvSellingBuy));

            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv') {
                //neu chua co thanh toan tien cho CTV thi thanh toan => chua có record ở table $payinMonthRepository voi warehouse_cv_selling_buy_id = $cvSellingBuy->id
                $now = Carbon::now();

                $payinMonth = $this->payinMonthRepository->findByWarehouseCvSellingBuyId($cvSellingBuy->id);
                if ($now->timestamp > $cvSellingBuy->created_at->addMinutes(7 * 24 * 60)->timestamp && !$payinMonth) {
                    //Cộng tiền trả CTV
                    //1. payins
                    $bonusRepository = resolve(BonusRepository::class);
                    $payinMonthRepository = resolve(PayinMonthRepository::class);
                    $now = \Carbon\Carbon::now();
                    $year = $now->year;
                    $month = $now->month;
                    $bonusCheck = $bonusRepository->getBonusByUser($cvSellingBuy->rec->id, $month, $year);
                    if (!$bonusCheck) {
                        //nếu tháng/năm chưa có thì insert mới
                        $bonusRepository->create(
                            [
                                'user_id'   => $cvSellingBuy->rec->id,
                                'year'      => $year,
                                'month'     => $month,
                                'price'     => $cvSellingBuy->price,
                            ]
                        );
                    } else {
                        //nếu có doanh thu từ trước thì cộng dồn
                        $bonusCheck->price += $cvSellingBuy->price;
                        $bonusCheck->save();
                    }
                    //từng giao dịch trong tháng
                    $payinMonthRepository->create(
                        [
                            'user_id'                       => $cvSellingBuy->rec->id,
                            'warehouse_cv_selling_buy_id'   => $cvSellingBuy->id,
                            'year'                          => $year,
                            'month'                         => $month,
                            'price'                         => $cvSellingBuy->price,
                            'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                        ]
                    );
                    //2. wallets
                    // $cvSellingBuy->rec->wallet->price  = $cvSellingBuy->rec->wallet->price + $cvSellingBuy->price;
                    $cvSellingBuy->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                    $cvSellingBuy->rec->wallet->addPrice($cvSellingBuy->price, $cvSellingBuy, 'Cộng tiền vào ví', 'complaints_accepted_to_rec');
                    // $cvSellingBuy->rec->wallet->save();

                    $cvSellingBuy->rec->notify(new PaymentOpenTurnCv($cvSellingBuy, $cvSellingBuy->price));
                }
            }

            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                //neu chua co thanh toan tien cho CTV thi thanh toan => chua có record ở table $payinMonthRepository voi warehouse_cv_selling_buy_id = $cvSellingBuy->id
                $now = Carbon::now();
                $payinMonth = $this->payinMonthRepository->findByWarehouseCvSellingBuyId($cvSellingBuy->id);
                $lastBook = $this->wareHouseCvSellingBuyBookRepository->findByCvSellingBuyId($cvSellingBuy->id);
                $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $lastBook->date_book);
                if ($now->timestamp > $carbonDate->addMinutes(7 * 24 * 60)->timestamp && !$payinMonth) {
                    //8 => 'Pass Interview',
                    //sau 3 ngày Nếu ko thay đổi //7 => 'Waiting Interview', thì update //8 => 'Pass Interview',
                    //$wareHouseCvSellingBuySave['status_recruitment'] = 8;
                    UpdateRecuirmentThreeDay::dispatch($cvSellingBuy->id, 7)->delay(now()->addMinutes(3 * 24 * 60));
                }
            }
            //14 => 'Trial work',
            if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard' && $cvSellingBuy->status_recruitment == 14) {
                //8 => 'Pass Interview',
                //$wareHouseCvSellingBuySave['status_recruitment'] = 8;
                //Onboard ko tự động chuyển, chấp nhận treo
                //16 => 'Success Recruitment',
                UpdateRecuirmentThreeDay::dispatch($cvSellingBuy->id, 14)->delay(now()->addMinutes(3 * 24 * 60));
            }
        }

        if ($statusPayment) $wareHouseCvSellingBuySave['status_payment'] = $statusPayment;
        if ($statusRecruitment) $wareHouseCvSellingBuySave['status_recruitment'] = $statusRecruitment;

        return $this->wareHouseCvSellingBuyRepository->update($params['id'], [], $wareHouseCvSellingBuySave);
    }

    public function getDataDiscuss($params)
    {
        return $this->wareHouseCvSellingBuyDiscussRepository->getByWarehouseCvBuyId($params['id']);
    }

    public function getDataBuyBook($params)
    {
        return $this->wareHouseCvSellingBuyBookRepository->getByWarehouseCvBuyId($params['id']);
    }

    public function getDataEvaluate($params)
    {
        $wareHouseCvSelling = $this->wareHouseCvSellingBuyRepository->find($params['id']);
        return $this->wareHouseCvSellingEvaluateRepository->getWithCvSelling($wareHouseCvSelling->warehouse_cv_selling_id);
    }

    public function deleteEvaluate($id)
    {
        return $this->wareHouseCvSellingEvaluateRepository->delete($id);
    }

    public function getDataHistoryStatus($params)
    {
        return $this->wareHouseCvSellingBuyHistoryStatusRepository->getLogStatusWithCvBuy($params['id']);
    }

    public function getDataHistoryPaymentStatus($params)
    {
        $data['buy'] = $this->wareHouseCvSellingHistoryBuyRepository->findAllBySelling($params['id']);
        $data['month'] = $this->payinMonthRepository->warehouseCvSellingBuyList($params['id']);

        return $data;
    }

    public function getTotalComplain()
    {
        return $this->wareHouseCvSellingBuyRepository->getTotalComplain();
    }

    public function getTotalInterview()
    {
        return $this->wareHouseCvSellingBuyRepository->getTotalInterview();
    }

    public function getTotalOnboard()
    {
        return $this->wareHouseCvSellingBuyRepository->getTotalOnboard();
    }

    /**
     * @param $wareHouseCvSellingBuyId
     * @param $status
     * @return mixed
     * admin đồng ý, từ chối phỏng vấn
     */
    public function updateStatusRecruitmentById($wareHouseCvSellingBuyId, $status)
    {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($wareHouseCvSellingBuyId);

        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview' || $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
            //            5 => 'Reject Interview schedule',
            if ($status == 5) {
                $employer = $wareHouseCvSellingBuy->employer;
                $lastBook = $this->wareHouseCvSellingBuyBookRepository->getLastBookBySellingBuyId($wareHouseCvSellingBuyId);
                $lastBook->update(['status' => 2]);
                $employer->notify(new EmailRejectInterView($employer, $lastBook, $wareHouseCvSellingBuy));
                $countBookReject = $this->wareHouseCvSellingBuyBookRepository->countBookReject($lastBook->ntd_id, $wareHouseCvSellingBuy->id);
                $point = 0;
                if ($countBookReject >= 3) {
                    //                    9 => 'Candidate Cancel Interview',
                    $status = 9;
                    // Từ chối quá 3 lần
                    // Trả tiền Cọc
                    //CV interview
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                        //ghi log hoan tien
                        $this->wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $wareHouseCvSellingBuy->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                            'status'                        =>  0,
                        ]);
                        $wareHouseCvSellingBuy->status_payment = 3; //Hoan tien
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        $point = $wareHouseCvSellingBuy->point;
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'update_status_recruitment');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }
                    //cv onboard
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                        $wareHouseCvSellingHistoryBuyData = $this->wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                        //hoàn bao nhiêu point
                        $point = 0;
                        if ($wareHouseCvSellingHistoryBuyData) {
                            foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                $value->status = 1;
                                $value->save();
                                //ghi log hoan tien
                                $this->wareHouseCvSellingHistoryBuyRepository->create([
                                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                    'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                                    'point'                         =>  $value->point,
                                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                                    'status'                        =>  0
                                ]);
                                $point += $value->point;
                            }
                        }
                        $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'update_status_recruitment');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }
                    $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
                        Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
                    } else {
                        $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
                    }
                } else {
                    OutOfDateBookInterview::dispatch($wareHouseCvSellingBuy, auth()->user())->delay(now()->addMinutes(7 * 24 * 60));
                }
                $wareHouseCvSellingBuy->update(['status_recruitment' => $status]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth()->user());
            }
            //7 => 'Waiting Interview', châp nhận phỏng vấn
            if ($status == 7) {
                $lastBook = $this->wareHouseCvSellingBuyBookRepository->getLastBookBySellingBuyId($wareHouseCvSellingBuyId);
                $employer = $wareHouseCvSellingBuy->employer;
                $lastBook->update(['status' => 1]);
                $wareHouseCvSellingBuy->update(['status_recruitment' => $status]);
                $employer->notify(new EmailConfirmInterView($employer, $lastBook, $wareHouseCvSellingBuy));
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $lastBook->date_book)
                        ->startOfDay()
                        ->addMinutes($lastBook->book_time_minute + 7 * 24 * 60);
                    PassInterview::dispatch($wareHouseCvSellingBuy->id, auth()->user())->delay($timeInterval);
                }
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth()->user());
            }
        }
        return $wareHouseCvSellingBuy;
    }

    public function updateStatusOnboardById($wareHouseCvSellingBuyId, $status)
    {
        $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($wareHouseCvSellingBuyId);
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
            $onboard = $this->wareHouseCvSellingBuyOnboardRepository->getCurrentByWarehouseCvBuyId($wareHouseCvSellingBuyId);
            //13 => 'Waiting onboard'
            if ($status == 13) {
                if ($onboard) {
                    $onboard->update([
                        'status' => 1
                    ]);
                }
                $employer = $wareHouseCvSellingBuy->employer;
                $employer->notify(new EmailConfirmOnboard($employer, $onboard, $wareHouseCvSellingBuy));
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $status,
                ]);
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth()->user());
            }
            //12 => 'Reject Offer',
            if ($status == 12) {
                $onboard->update([
                    'status' => 2
                ]);
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => $status,
                ]);
                $employer = $wareHouseCvSellingBuy->employer;
                $employer->notify(new EmailRejectOnboard($employer, $onboard, $wareHouseCvSellingBuy));
                $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, auth()->user());
                if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdmin($wareHouseCvSellingBuy));
                } else {
                    $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelOnboardToRec($wareHouseCvSellingBuy));
                }
                //todo hoàn cọc
                DepositRefundRejectOffer::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(48 * 60));;
            }
        }

        return $wareHouseCvSellingBuy;
    }

    public function getMessageRejectInterview($cvSellingBuyId, $bock)
    {
        $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($cvSellingBuyId);
        if ($cvSellingBuy->status_recruitment != 5) {
            return null;
        }
        return $bock->last();
    }
}
