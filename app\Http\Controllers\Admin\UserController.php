<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AdminChangePasswordRequest;
use App\Http\Requests\Admin\UserRequest;
use App\Services\Admin\RoleService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    protected $userService;
    protected $roleService;

    public function __construct(UserService $userService, RoleService $roleService)
    {
        $this->userService = $userService;
        $this->roleService = $roleService;
    }

    public function index()
    {
        $datatable = $this->userService->buildDatatable();
        $role = $this->roleService->getRole();
        return view('admin.pages.user.index', compact('datatable', 'role'));
    }

    public function store(UserRequest $request)
    {
        return $this->userService->createService($request->all());
    }

    public function show($id)
    {
        return $this->userService->detailService($id);
    }

    public function update(UserRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $response = $this->userService->updateService($request->all(), $id);
            DB::commit();
            return $response;
        }catch (\Exception $e){
            DB::rollBack();
            Log::info('error update user: ', [
                'content: ' => $e->getMessage()
            ]);
            return $e;
        }
    }

    public function datatable(Request $request)
    {
        $data = $this->userService->datatable($request->all(), true);
        return response($data);
    }

    public function changePassword()
    {
        return view('admin.pages.user.change-password');
    }

    public function postChangePassword(AdminChangePasswordRequest $request)
    {
        $user = Auth::guard('admin')->user();
        $this->userService->changePasswordById($user->id, $request->password);
        Toast::success(__('message.edit_success'));
        return back();
    }

    public function teamByUser($id, Request $request)
    {
        $data = $this->userService->teams($id, $request->all());

        $countData = $this->userService->countTeams($id, $request->all());

        $currentPage = isset($request->page) ? $request->page : 1;
        $lastPage = (int)ceil($countData / config('constant.limitPaginate'));
        $paginate = [
            'currentPage' => $currentPage,
            'lastPage'    => $lastPage,
        ];

        return json_encode([
            'success' => count($data) ? true : false,
            'data' => $data,
            'paginate' => $paginate
        ]);
    }

    public function detailTeamByUser($id)
    {
        $result = $this->userService->detailUserTeam($id);

        return json_encode([
            'success' => count($result) ? true : false,
            'data' => $result,
        ]);
    }
}
