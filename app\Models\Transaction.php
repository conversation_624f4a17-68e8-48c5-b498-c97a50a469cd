<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends BaseModel
{
    use HasFactory;

    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['file_full_url', 'payment_method_value', 'created_at_format','payment_date_format'];

    public function getFileFullUrlAttribute()
    {
        $arrNewFilePath = [];
        if ($this->file) {
            $arrFile = json_decode($this->file);
            foreach ($arrFile as $k => $item) {
                array_push($arrNewFilePath, gen_url_file_s3($item));
            }
        }
        return $arrNewFilePath;
    }

    public function getPaymentMethodValueAttribute()
    {
        $status = config('constant.payment_method.' . $this->payment_method);
        return $status;
    }

    public function getCreatedAtFormatAttribute()
    {
        $date = Carbon::parse($this->created_at)->format('Y-m-d H:i:s');
        return $date;
    }

    public function getPaymentDateFormatAttribute()
    {
        $date = '';
        if (isset($this->payment_date)){
            $date = Carbon::parse($this->payment_date)->format('Y-m-d');
        }
        return $date;
    }

}
