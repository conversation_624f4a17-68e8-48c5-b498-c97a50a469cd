<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PostRequest;
use App\Services\Admin\CategoryService;
use App\Services\Admin\PostService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\FileServiceS3;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PostController extends Controller
{
    protected $postService;
    protected $categoryService;

    public function __construct(PostService $postService, CategoryService $categoryService)
    {
        $this->postService = $postService;
        $this->categoryService = $categoryService;
    }

    public function index()
    {
        $datatable = $this->postService->buildDatatable();
        return view('admin.pages.post.index', compact('datatable'));
    }

    public function create()
    {
        $category = $this->categoryService->indexService([]);
        $status = array_flip(config('constant.status_post'));
        return view('admin.pages.post.create', compact('category', 'status'));
    }

    public function store(PostRequest $request)
    {
        try {
            DB::beginTransaction();
            $data = $this->postService->createService($request->all());
            DB::commit();
            Toast::success(__('message.edit_success'));
        }catch (\Exception $e){
            DB::rollBack();
            Log::info('error post store job: ', [
                'content: ' => $e->getMessage()
            ]);
        }
        return redirect()->route('post.edit', ['post' => $data->id]);
    }
    public function uploadImage()
    {
        if (\request()->hasFile('upload')) {
            $url = FileServiceS3::getInstance()->uploadToS3(\request()->upload, config('constant.sub_path_s3.post'));
            $msg = 'Image uploaded successfully';
            $CKEditorFuncNum = request()->input('CKEditorFuncNum');
            $url = gen_url_file_s3($url);
            $response = "<script>window.parent.CKEDITOR.tools.callFunction($CKEditorFuncNum, '$url', '$msg')</script>";
            return response($response)->header('Content-Type', 'text/html')->header('charset', 'utf-8');

        }

    }

    public function edit($id)
    {
        $data = $this->postService->detailService($id);
        $category = $this->categoryService->indexService([]);
        $status = array_flip(config('constant.status_post'));
        $position = config('constant.position_meta');
        $dataMeta = $this->postService->detailPostMeta($id);
        $dataSeo = $this->postService->detailPostSeo($id);
        return view('admin.pages.post.edit',
            compact('data', 'category', 'status', 'position', 'dataMeta', 'dataSeo'));
    }

    public function update(PostRequest $request, $id)
    {
        $this->postService->updateService($request->all(), $id);
        Toast::success(__('message.edit_success'));
        return back();
    }

    public function datatable(Request $request)
    {
        $data = $this->postService->datatable($request->all(), true);
        return response($data);
    }
}
