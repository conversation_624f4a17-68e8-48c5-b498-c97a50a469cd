<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Discuss extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'discuss';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['date_hour'];
    protected static function booted()
    {
        static::created(function ($model) {
            if ($model->object_type === SubmitCv::class) {
                $model->object->update([
                    'last_discuss_time' => $model->created_at
                ]);
                // $submitCv = SubmitCv::find($model->object_id);
                $receiver = $model->receiver;
                $model->object->increaseMeta($receiver->type . '_unread_discuss');
            }
        });
    }

    public function object(): MorphTo
    {
        return $this->morphTo();
    }

    public function sub_object(): MorphTo
    {
        return $this->morphTo();
    }

    public function sender()
    {
        return $this->hasOne(User::class, 'id', 'sender_id');
    }

    public function receiver()
    {
        return $this->hasOne(User::class, 'id', 'receiver_id');
    }

    public function getDateHourAttribute()
    {
        if (!empty($this->created_at)) {
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function getFullMessageAttribute()
    {
        $message = $this->message;
        $sub_object = $this->sub_object;
        if ($sub_object) {
            if ($sub_object instanceof SubmitCvBook) {
                $message .= "<br />SĐT: <strong>" . $sub_object->phone . "</strong>";
                $status = $sub_object->status;
                if ($status == 0) {
                    $message .= "<br />Trạng thái: <span class='font-weight-bold' style='color: #df651c;'>Chờ xác nhận</span>";
                } else if ($status == 1) {
                    $message .= "<br />Trạng thái: <span class='text-success font-weight-bold'>Đã xác nhận</span>";
                } else if ($status == 2) {
                    $message .= "<br />Trạng thái: <span class='text-danger font-weight-bold'>Đã từ chối</span>";
                }
            }
        }
        return $message;
    }
}
