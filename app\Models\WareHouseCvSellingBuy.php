<?php

namespace App\Models;

use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class WareHouseCvSellingBuy extends BaseModel implements Auditable
{
    use HasFactory, Notifiable, SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $table = 'warehouse_cv_selling_buys';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $audit_tags = [];

    protected $appends = [
        'date_hour',
        'status_payment_value',
        'status_complain_value',
        'status_recruitment_value',
        'created_at_value',
    ];
    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }

    public function wareHouseCvSelling()
    {
        return $this->belongsTo(WareHouseCvSelling::class, 'warehouse_cv_selling_id', 'id');
    }

    public function employer()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function rec()
    {
        return $this->hasOne(User::class, 'id', 'ctv_id');
    }

    public function job()
    {
        return $this->hasOne(Job::class, 'id', 'job_id');
    }

    public function getDateHourAttribute()
    {
        if (!empty($this->created_at)) {
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function getStatusPaymentValueAttribute()
    {
        return isset($this->status_payment) ? config('constant.status_payments.' .  app()->getLocale() . '.' . $this->status_payment) : '';
    }

    public function getStatusComplainValueAttribute()
    {
        return  !empty($this->status_complain) ? config('constant.status_complain.' .  app()->getLocale() . '.' . $this->status_complain) : (app()->getLocale() == 'en' ? 'No complaints' : 'Không có khiếu nại');
    }

    public function getStatusRecruitmentValueAttribute()
    {
        return !empty($this->status_recruitment) ? config('constant.status_recruitment.' .  app()->getLocale() . '.' . $this->status_recruitment) : '';
    }

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i') : null;
    }

    public function getComplainDeadlineAttribute()
    {
        $now = Carbon::now();
        if ($this->created_at) {
            $deadline = $this->created_at->addMinutes(config('constant.buy_cv_success'));
            if ($deadline->timestamp < $now->timestamp) {
                return  false;
            } else {
                return true;
            }
        }
        return true;
    }

    public function getCanComplainAttribute()
    {
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuyBookRepository = app(WareHouseCvSellingBuyBookRepository::class);
        $wareHouseCvSellingBuyOnboardRepository = app(WareHouseCvSellingBuyOnboardRepository::class);
        $now = Carbon::now();
        if ($this->status_complain != 0 && $this->status_complain != 5) {
            return false;
        }

        if ($this->wareHouseCvSelling->type_of_sale == 'interview') {
            if ($this->status_recruitment == 7 && $this->count_complain < 1) {
                $book = $wareHouseCvSellingBuyBookRepository->getWaitingInterviewBook($this->id);
                if (!empty($book)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book)
                        ->startOfDay()
                        ->addMinutes($book->book_time_minute + 7 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        if ($this->wareHouseCvSelling->type_of_sale == 'onboard') {
            if ($this->status_recruitment == 7 && $this->count_complain < 1) {
                // return true;
                $book = $wareHouseCvSellingBuyBookRepository->getWaitingInterviewBook($this->id);
                if (!empty($book)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book)
                        ->startOfDay()
                        ->addMinutes($book->book_time_minute + 7 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
            if ($this->status_recruitment == 14 && $this->count_complain < 2) {
                $onboard = $wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($this->id);
                if (!empty($onboard)) {
                    $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book)
                        ->startOfDay()
                        ->addMinutes($onboard->book_time_minute + 67 * 24 * 60);
                    if ($timeInterval->timestamp < $now->timestamp) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }

        // if ($this->wareHouseCvSelling->type_of_sale == 'cv' && $this->status_recruitment == 18){
        //     if ($this->created_at){
        //         if ($this->created_at->addMinutes(7 * 24 * 60)->timestamp < $now->timestamp){
        //             return false;
        //         }else{
        //             return true;
        //         }
        //     }
        // }

        return false;
    }
}
