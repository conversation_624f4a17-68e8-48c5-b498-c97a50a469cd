---
description: 
globs: 
alwaysApply: false
---
# Controllers Guide

Controllers trong RecLand được tổ chức theo nhóm chức năng:

## Frontend Controllers

- [HomeController](mdc:app/Http/Controllers/HomeController.php) - <PERSON><PERSON> lý trang chủ và trang tĩnh
- [JobController](mdc:app/Http/Controllers/JobController.php) - Hiển thị và tìm kiếm công việc
- [ProfileController](mdc:app/Http/Controllers/ProfileController.php) - Quản lý profile người dùng
- [ResumeController](mdc:app/Http/Controllers/ResumeController.php) - Quản lý CV của ứng viên
- [ApplicationController](mdc:app/Http/Controllers/ApplicationController.php) - Quản lý đơn ứng tuyển
- [PaymentController](mdc:app/Http/Controllers/PaymentController.php) - <PERSON><PERSON> lý thanh toán

## Admin Controllers

- [Admin/DashboardController](mdc:app/Http/Controllers/Admin/DashboardController.php) - Dashboard admin
- [Admin/UserController](mdc:app/Http/Controllers/Admin/UserController.php) - Quản lý người dùng
- [Admin/JobController](mdc:app/Http/Controllers/Admin/JobController.php) - Quản lý công việc
- [Admin/ReportController](mdc:app/Http/Controllers/Admin/ReportController.php) - Quản lý báo cáo
- [Admin/SettingController](mdc:app/Http/Controllers/Admin/SettingController.php) - Cấu hình hệ thống

## API Controllers

- [Api/AuthController](mdc:app/Http/Controllers/Api/AuthController.php) - Xử lý authentication API
- [Api/JobController](mdc:app/Http/Controllers/Api/JobController.php) - API công việc
- [Api/ResumeController](mdc:app/Http/Controllers/Api/ResumeController.php) - API CV

## Tổ chức Controller

Controllers tuân thủ các nguyên tắc:
1. Sử dụng Form Requests cho validation
2. Gọi đến Service layer cho business logic
3. Trả về View cho web routes hoặc JSON cho API routes
4. Sử dụng middleware để kiểm soát quyền truy cập
5. Sử dụng dependency injection cho các dependencies
