<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\RoleRepository;

class RoleService
{
    protected $roleRepository;

    public function __construct(RoleRepository $roleRepository)
    {
        $this->roleRepository = $roleRepository;
    }

    public function getRole()
    {
        return $this->roleRepository->getListRole([]);
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name',
                ),
                'value' => 'Tên role'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('role-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }


    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->roleRepository->getListRole($params, $order, $paginate);
    }


    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function createService(array $request)
    {
        $permissionRequest = $request['permission'];
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        $permissions = $this->getGroupPermission($permissionRequest);
        $request['permission'] = json_encode($permissions);
        return $this->roleRepository->create($request);
    }

    public function detailService($id)
    {
        return $this->roleRepository->find($id);
    }

    public function updateService(array $request, $id)
    {
        $permissionRequest = $request['permission'];
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        $permissions = $this->getGroupPermission($permissionRequest);
        $request['permission'] = json_encode($permissions);
        return $this->roleRepository->update($id, [], $request);
    }

    protected function getGroupPermission(array $permissionRequest)
    {
        $rolePermission = config('constant.role_permission');
        $permissions = [];
        foreach ($rolePermission as $items) {
            foreach ($permissionRequest as $value) {
                $permissions[] = $value;
                if (!empty($items[$value]['action_save'])) {
                    $permissions = array_merge($permissions, $items[$value]['action_save']);
                }
            }
        }
        $permissions = array_unique($permissions);
        return $permissions;
    }

    public function findByName($name)
    {
        return $this->roleRepository->findByName($name);
    }


}
