<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Contracts\Auditable;
use Zoha\Metable;

class User extends Authenticatable implements Auditable
{
    use HasApiTokens, HasFactory, Notifiable;
    use CrudTrait;
    use Metable;
    use \OwenIt\Auditing\Auditable;
    //    protected $fillable = [
    //        'name',
    //        'email',
    //        'mobile',
    //        'password',
    //        'referral_code',
    //        'company_name',
    //        'mst',
    //    ];

    public $timestamps = true;
    protected $guarded = ['id'];
    protected $audit_tags = [];

    protected $appends = [
        'verify_status',
        'source_value',
        'birthday_value',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
    ];

    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }
    public function userRole()
    {
        return $this->hasMany(UserRole::class, 'user_id', 'id');
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class, 'user_role', 'user_id', 'role_id');
    }

    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function userInfo()
    {
        return $this->hasOne(UserInfo::class, 'user_id', 'id');
    }


    public function wallet()
    {
        return $this->hasOne(Wallet::class, 'user_id', 'id');
    }

    public function userEmployerType()
    {
        return $this->hasOne(EmployerType::class, 'user_id', 'id');
    }

    public function lastJobEmployer()
    {
        return $this->hasOne(Job::class, 'employer_id', 'id')->orderBy('id', 'desc');
    }

    public function jobs()
    {
        return $this->hasMany(Job::class, 'employer_id', 'id');
    }

    // relation morph to meta data
    public function metaData()
    {
        return $this->morphMany(MetaData::class, 'object');
    }

    public function bonus()
    {
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;
        return $this->hasOne(Bonus::class, 'user_id', 'id')
            ->where('month', $month)->where('year', $year);
    }

    public function storageRecharge()
    {
        return $this->hasMany(ZalopayTransaction::class);
    }

    public function getBonusValueAttribute()
    {
        return isset($this->bonus->money) ? ($this->bonus->money + $this->bonus->money_kpi) : 0;
    }


    public function getVerifyStatusAttribute()
    {
        return $this->company_id > 0 ? 'Yes' : 'No';
    }

    public function getCompanyValueAttribute()
    {
        if ($this->company_id > 0 && $this->company) {
            return $this->company->name;
        } else {
            return $this->company_name;
        }
    }

    public function getSourceValueAttribute()
    {
        if ($this->source) {
            return config('constant.source.' . $this->source);
        }
        return '';
    }

    public function getBirthdayValueAttribute()
    {
        if ($this->birthday) {
            $data = Carbon::createFromFormat('Y-m-d', $this->birthday);
            return $data->format('d/m/Y');
        }
        return '';
    }

    public function getAvatarUrlAttribute()
    {
        return gen_url_file_s3($this->avatar, '', false);
    }

    public function getAvatarRenderAttribute()
    {
        return view('frontend.layouts.user.avatar', [
            'avatar' => $this->avatar,
            'avatarUrl' => $this->avatar_url,
            'name' => $this->name,
        ]);
    }

    public function getFirstCharterOfNameAttribute()
    {
        $name = explode(' ', trim($this->name));
        $name = end($name);
        return mb_substr($name, 0, 1);
    }

    public function getSubNameAttribute()
    {
        $name = explode(' ', trim($this->name));
        $name = end($name);
        return $name;
    }

    public function checkConfirm()
    {
        $metaData = $this->metaData()->where('key', 'employer_confirmed_at')->first();
        if ($metaData) {
            return true;
        }
        return false;
    }
}
