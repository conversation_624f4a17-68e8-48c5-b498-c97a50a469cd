<?php

namespace App\Http\Resources\Frontend\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class AboutUsAlbumResource extends JsonResource
{
    /**
     * @param $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $bigImage1 = $this->photos->where('type', 1)->sortByDesc('id')->first();
        $bigImage2 = $this->photos->where('type', 1)->first();
        return [
            'name'        => $this->name,
            'photos'      => AboutUsPhotoResource::collection($this->photos()->where('type', 0)->limit(4)->get()),
            'image_big_1' => $bigImage1 ? $bigImage1->url_image : null,
            'image_big_2' => $bigImage1 ? $bigImage2->url_image : null,
        ];
    }

}
