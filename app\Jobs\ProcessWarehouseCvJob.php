<?php

namespace App\Jobs;

use App\Models\WareHouseCv;
use App\Services\FileServiceS3;
use App\Services\HideService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class ProcessWarehouseCvJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    protected $warehouseCvId;

    /**
     * Create a new job instance.
     */
    public function __construct($warehouseCvId)
    {
        $this->warehouseCvId = $warehouseCvId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $warehouseCv = WareHouseCv::find($this->warehouseCvId);
            if (!$warehouseCv) {
                Log::error("WareHouseCv not found with ID: {$this->warehouseCvId}");
                return;
            }

            // // Update status to processing
            // $warehouseCv->status = 'processing';
            // $warehouseCv->save();

            // Get CV file path from cv_public
            $cvPath = $warehouseCv->cv_public;
            if (empty($cvPath) || !str_contains($cvPath, 'pending_process::')) {
                Log::error("CV file not found for WareHouseCv ID: {$this->warehouseCvId}");
                // $cvPath = str_replace('pending_process::', 'file_not_found::', $cvPath);
                // $warehouseCv->cv_public = $cvPath;
                // $warehouseCv->save();
                return;
            }

            // Check if the path has the correct format with pending_process:: prefix
            if (!str_contains($cvPath, 'pending_process::')) {
                Log::error("CV path format is invalid for WareHouseCv ID: {$this->warehouseCvId}. Expected format: pending_process::path/to/file");
                $cvPath = str_replace('pending_process::', 'path_invalid::', $cvPath);
                $warehouseCv->cv_public = $cvPath;
                $warehouseCv->save();
                return;
            }

            // Extract the local path after pending_process::
            $localPath = str_replace('pending_process::', '', $cvPath);

            // Get the full local path
            $fullPath = storage_path('app/public/' . $localPath);

            // Check if file exists locally
            if (!file_exists($fullPath)) {
                Log::error("CV file not found at local path: {$fullPath} for WareHouseCv ID: {$this->warehouseCvId}");
                $cvPath = str_replace('pending_process::', 'file_not_found::', $cvPath);
                $warehouseCv->cv_public = $cvPath;
                $warehouseCv->save();
                return;
            }

            // Call the CV parser API
            $response = Http::attach(
                'file',
                file_get_contents($fullPath),
                basename($fullPath)
            )->withHeaders([
                'match-cv-api-key' => config('app.match_cv_api_key')
            ])->post(config('app.n8n_webhook_url') . '/webhook/cv-parser-with-markdown');

            if ($response->successful()) {
                $cvData = $response->json();

                // Save the CV data as meta
                $warehouseCv->setMeta('cv_parsed_data', $cvData);

                // Cập nhật dữ liệu CV từ parsed data
                $this->updateWarehouseCvData($warehouseCv, $cvData);

                // Update status to completed
                // $warehouseCv->status = 'completed';
                // $warehouseCv->save();

                Log::info("WareHouseCv processed successfully: {$this->warehouseCvId}");
            } else {
                Log::error("API call failed for WareHouseCv {$this->warehouseCvId}: " . $response->body());
            }
        } catch (\Exception $e) {
            Log::error("Error processing WareHouseCv {$this->warehouseCvId}: " . $e->getMessage());

            // Update status to failed
            if ($warehouseCv ?? null) {
                // $warehouseCv->status = 'failed';
                // $warehouseCv->save();
            }

            throw $e;
        }
    }

    /**
     * Cập nhật dữ liệu WareHouseCv từ parsed CV data
     */
    private function updateWarehouseCvData(WareHouseCv $warehouseCv, array $cvData)
    {
        try {
            $cvDataContent = data_get($cvData, 'cv_data', []);
            $pdfUrl = data_get($cvData, 'pdf_url', '');

            // Chuẩn bị dữ liệu cập nhật
            $updateData = [];

            // Cập nhật thông tin cơ bản
            if (!empty($cvDataContent['full_name'])) {
                $updateData['candidate_name'] = $cvDataContent['full_name'];
            }

            if (!empty($cvDataContent['email'])) {
                $updateData['candidate_email'] = $cvDataContent['email'];
            }

            if (!empty($cvDataContent['phone'])) {
                $updateData['candidate_mobile'] = $cvDataContent['phone'];
            }

            if (!empty($cvDataContent['address'])) {
                $updateData['candidate_address'] = $cvDataContent['address'];
            }

            if (!empty($cvDataContent['job_title'])) {
                $updateData['candidate_job_title'] = $cvDataContent['job_title'];
            }

            if (isset($cvDataContent['years_of_experience'])) {
                $updateData['year_experience'] = (int)$cvDataContent['years_of_experience'];
            }

            if (!empty($cvDataContent['self_pr'])) {
                $updateData['assessment'] = $cvDataContent['self_pr'];
            }

            // Xử lý skills
            if (!empty($cvDataContent['skills']) && is_array($cvDataContent['skills'])) {
                $arrSkillDescription = [];
                foreach ($cvDataContent['skills'] as $skill) {
                    if (!empty($skill)) {
                        $arrSkillDescription[$skill] = '';
                    }
                }
                $updateData['main_skill'] = json_encode($arrSkillDescription);
            }

            // Xử lý địa chỉ/location
            if (!empty($cvDataContent['address'])) {
                $updateData['candidate_location'] = $cvDataContent['address'];
            }

            // Cập nhật career - mặc định IT nếu không có
            if (empty($warehouseCv->career)) {
                // $updateData['career'] = '30,31'; // IT phan mem, IT phan cung mang
                $updateData['career'] = ''; // IT phan mem, IT phan cung mang
            }

            // Xử lý upload CV lên S3 nếu có pdf_url
            if (!empty($pdfUrl)) {
                try {
                    // Upload cv_public lên S3
                    $cvPublicS3Url = FileServiceS3::getInstance()->uploadToS3FromLink(
                        $pdfUrl,
                        config('constant.sub_path_s3.cv')
                    );
                    $updateData['cv_public'] = $cvPublicS3Url;

                    // Tạo cv_private bằng HideService và upload lên S3
                    $hideCvService = new HideService();
                    $privateCvContent = $hideCvService->hideCv($pdfUrl);

                    if (!empty($privateCvContent)) {
                        $cvPrivateS3Url = FileServiceS3::getInstance()->uploadToS3FromLink(
                            $privateCvContent,
                            config('constant.sub_path_s3.cv')
                        );
                        $updateData['cv_private'] = $cvPrivateS3Url;

                        Log::info("Created cv_private for WareHouseCv ID: {$warehouseCv->id}");
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing CV files for WareHouseCv {$warehouseCv->id}: " . $e->getMessage());
                }
            } elseif (!empty($warehouseCv->cv_public) && str_contains($warehouseCv->cv_public, 'pending_process::')) {
                // Nếu không có pdfUrl nhưng có file local pending_process
                try {
                    // Extract the local path after pending_process::
                    $localPath = str_replace('pending_process::', '', $warehouseCv->cv_public);
                    $fullPath = storage_path('app/public/' . $localPath);

                    if (file_exists($fullPath)) {
                        // Upload cv_public từ file local lên S3
                        $cvPublicS3Url = FileServiceS3::getInstance()->uploadFromStorageToS3(
                            $localPath,
                            'public',
                            config('constant.sub_path_s3.cv')
                        );
                        $updateData['cv_public'] = $cvPublicS3Url;

                        // Tạo cv_private bằng HideService với URL S3 vừa upload
                        $hideCvService = new HideService();
                        $privateCvContent = $hideCvService->hideCv(Storage::disk('s3')->url($cvPublicS3Url));

                        if (!empty($privateCvContent)) {
                            $cvPrivateS3Url = FileServiceS3::getInstance()->uploadToS3FromLink(
                                $privateCvContent,
                                config('constant.sub_path_s3.cv')
                            );
                            $updateData['cv_private'] = $cvPrivateS3Url;

                            Log::info("Created cv_private from local file for WareHouseCv ID: {$warehouseCv->id}");
                        }

                        Log::info("Uploaded local CV to S3 for WareHouseCv ID: {$warehouseCv->id}");
                        // Xóa file local sau khi upload thành công
                        // if (file_exists($fullPath)) {
                        Storage::disk('public')->delete($localPath);
                        Log::info("Deleted local CV file: {$fullPath} for WareHouseCv ID: {$warehouseCv->id}");
                        // }
                    } else {
                        Log::error("Local CV file not found: {$fullPath} for WareHouseCv ID: {$warehouseCv->id}");
                    }
                } catch (\Exception $e) {
                    Log::error("Error processing local CV file for WareHouseCv {$warehouseCv->id}: " . $e->getMessage());
                }
            }

            // Cập nhật các trường timestamp
            $updateData['updated_at'] = now();

            // Thực hiện cập nhật
            if (!empty($updateData)) {
                $warehouseCv->update($updateData);
                Log::info("Updated WareHouseCv data for ID: {$warehouseCv->id}");
            }

            // Lưu thêm meta data chi tiết
            // $this->saveAdditionalMetaData($warehouseCv, $cvDataContent);
        } catch (\Exception $e) {
            Log::error("Error updating WareHouseCv data for ID {$warehouseCv->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Lưu thêm meta data chi tiết từ parsed CV
     */
    private function saveAdditionalMetaData(WareHouseCv $warehouseCv, array $cvDataContent)
    {
        try {
            // Lưu thông tin học vấn
            if (!empty($cvDataContent['education_certifications'])) {
                $warehouseCv->setMeta('education_certifications', $cvDataContent['education_certifications']);
            }

            // Lưu thông tin kinh nghiệm làm việc
            if (!empty($cvDataContent['work_experience'])) {
                $warehouseCv->setMeta('work_experience', $cvDataContent['work_experience']);
            }

            // Lưu thông tin dự án
            if (!empty($cvDataContent['projects'])) {
                $warehouseCv->setMeta('projects', $cvDataContent['projects']);
            }

            // Lưu thông tin ngôn ngữ
            if (!empty($cvDataContent['languages'])) {
                $warehouseCv->setMeta('languages', $cvDataContent['languages']);
            }

            // Lưu thông tin bổ sung
            $additionalInfo = [
                'date_of_birth' => data_get($cvDataContent, 'date_of_birth'),
                'gender' => data_get($cvDataContent, 'gender'),
                'university' => data_get($cvDataContent, 'university'),
                'old_company' => data_get($cvDataContent, 'old_company'),
                'salary_current' => data_get($cvDataContent, 'salary_current'),
                'facebook' => data_get($cvDataContent, 'facebook'),
                'linkedin' => data_get($cvDataContent, 'linkedin'),
                'level' => data_get($cvDataContent, 'level'),
                'academic_level' => data_get($cvDataContent, 'academic_level'),
                'cv_language' => data_get($cvDataContent, 'cv_language'),
            ];

            // Lọc bỏ các giá trị null/empty
            $additionalInfo = array_filter($additionalInfo, function ($value) {
                return !empty($value);
            });

            if (!empty($additionalInfo)) {
                $warehouseCv->setMeta('additional_info', $additionalInfo);
            }
        } catch (\Exception $e) {
            Log::error("Error saving additional meta data for WareHouseCv {$warehouseCv->id}: " . $e->getMessage());
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ProcessWarehouseCvJob failed for ID {$this->warehouseCvId}: " . $exception->getMessage());

        // Mark the record as failed
        $warehouseCv = WareHouseCv::find($this->warehouseCvId);
        if ($warehouseCv) {
            // $warehouseCv->status = 'failed';
            // $warehouseCv->save();
        }
    }
}
