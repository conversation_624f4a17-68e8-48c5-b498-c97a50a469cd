<?php

namespace App\Http\Resources\Frontend\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class SubmitCvResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'job_id' => $this->job_id,
            'status' => $this->status,
            'status_str' => $this->status_recruitment_value,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user' => new UserResource($this->whenLoaded('user')),
            'job' => new JobResource($this->whenLoaded('job')),
            'warehouse_cv' => new WarehouseCvResource($this->whenLoaded('warehouseCv')),
            'submit_cv_meta' => new SubmitCvMetaResource($this->whenLoaded('submitCvMeta')),
            'bonus' => $this->bonus,
            'bonus_type' => $this->bonus_type,
            'bonus_str' => \App\Helpers\Common::formatNumber($this->bonus) . ' đ',
            // test
        ];
    }
}
