<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserJobCare extends BaseModel
{
    protected $fillable = [
        'user_id',
        'job_id',
        'type',
    ];

    /**
     * Relationship với Job
     */
    public function job()
    {
        return $this->belongsTo(Job::class, 'job_id', 'id');
    }

    /**
     * Relationship với User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
