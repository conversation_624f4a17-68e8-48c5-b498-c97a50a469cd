<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DownloadFileController extends Controller
{
    //

    public function download(Request $request)
    {
        $pathFile = $request->url;

//        $myFile = Storage::disk('s3')->url($pathFile);

//        return response()->download($myFile);

        $ch = curl_init($pathFile);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_AUTOREFERER, false);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $output = curl_exec($ch);
        header("Content-Type: application/octet-stream");
        header("Content-Disposition: attachment; filename=" . basename($pathFile));
        $fp = fopen("php://output", 'w');
        fwrite($fp, $output );
        fclose($fp);
    }
}
