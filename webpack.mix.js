const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */
//
// mix.js('resources/js/app.js', 'public/js')
//     .postCss('resources/css/app.css', 'public/css', [
//         //
//     ]);


mix
    .sass('public/frontend/asset/css/style.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/company.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/candidate.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/collaborator-member-manager.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/introducation.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/common.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/side-modals.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/cv-on-sale.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/cv-sale.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/cv-bought.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/employer-cv-submit.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/wallet.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/after-recruitment.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/modal-system-report.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/asset/css/saved-jobs.scss', 'public/frontend/asset/css/')
    .sass('public/frontend/assets_v2/css/scss/style.scss', 'public/frontend/assets_v2/css/')
    .options({
        processCssUrls: false
    })
    .styles(
        [
            'public/frontend/asset/css/bootstrap.min.css',
            'public/frontend/asset/OwlCarousel2-2.3.4/assets/owl.carousel.css',
            'public/frontend/asset/OwlCarousel2-2.3.4/assets/owl.theme.default.css',
            'public/frontend/asset/css/slick.css',
            'public/frontend/asset/css/slick-theme.css',
            'public/frontend/asset/css/reset.css',
            'public/frontend/asset/css/font-awesome.min.css',
            'public/frontend/asset/css/select2.min.css',
            'public/backend/assets/css/toast.css',
            'public/frontend/asset/css/animate.css',
            'public/frontend/asset/css/side-modals.css',
            'public/frontend/asset/css/style.css',
            'public/frontend/asset/css/modal-system-report.css',
            'public/frontend/asset/css/basicLightbox.min.css',
            'public/packages/sweet-alert/sweetalert.css',
            'public/packages/sweet-alert/jquery.sweet-modal.min.css',
        ], 'public/frontend/asset/css/all_v1.css')
    .styles(
        [
            'public/frontend/assets_v2/css/bootstrap.min.css',
            'public/frontend/assets_v2/css/select2.min.css',
            'public/packages/sweet-alert/sweetalert.css',
            'public/packages/sweet-alert/jquery.sweet-modal.min.css',
            'public/frontend/assets_v2/css/style.css',
            'public/frontend/assets_v2/css/toastr.css',
            'public/frontend/asset/css/side-modals.css'

        ], 'public/frontend/assets_v2/css/all.css')
    .scripts([
        'public/frontend/asset/js/jquery.min.js',
        'public/frontend/asset/js/slider-customer.js',
        'public/frontend/asset/js/bootstrap.min.js',
        'public/frontend/asset/js/jquery-ui.js',
        'public/frontend/asset/OwlCarousel2-2.3.4/owl.carousel.js',
        'public/frontend/asset/js/slick.js',
        'public/frontend/asset/js/toast.js',
        'public/frontend/asset/js/jquery.validate.min.js',
        'public/frontend/asset/js/select2.full.min.js',
        'public/frontend/asset/js/highcharts.js',
        'public/frontend/asset/js/wow.js',
        'public/frontend/asset/js/script.js',
        'public/frontend/asset/js/bug-report.js',
        'public/frontend/asset/js/jquery-cookie.min.js',
        'public/frontend/asset/js/basicLightbox.min.js',
        'public/packages/sweet-alert/sweetalert.min.js',
        'public/packages/sweet-alert/jquery.sweet-modal.min.js',
    ], 'public/frontend/asset/js/all_v1.js')
    .scripts([
        'public/frontend/assets_v2/js/bootstrap.bundle.min.js',
        // 'public/frontend/asset/js/bootstrap.min.js',
        'public/frontend/assets_v2/js/jquery-2.1.4.min.js',
        'public/frontend/asset/js/jquery.validate.min.js',
        'public/frontend/asset/js/select2.full.min.js',
        'public/frontend/assets_v2/js/aos.js',
        'public/frontend/assets_v2/js/numscroller-1.0.js',
        'public/frontend/assets_v2/js/common.js',
        'public/frontend/assets_v2/js/toastr.js',
        'public/backend/assets/plugins/modal-datepicker/datepicker.js',
        'public/frontend/asset/js/wow.js',
        'public/frontend/asset/js/script.js',
        'public/frontend/asset/js/bug-report.js',
        'public/packages/sweet-alert/sweetalert.min.js',
        'public/packages/sweet-alert/jquery.sweet-modal.min.js',
    ], 'public/frontend/assets_v2/js/all.js')
    .js('resources/js/app.js', 'public/js')
    .js('resources/js/admin/submit-cv-discuss.js', 'public/js/admin')
    .js('resources/js/admin/box-job-comments.js', 'public/js/admin')
    .js('resources/js/jscomponents/login-to-buy-cv.js', 'public/js/jscomponents')
    .js('resources/js/jscomponents/rec/apply-in-team.js', 'public/js/jscomponents/rec')
    .vue().version();

// mix.js('resources/js/admin/submit-cv-discuss.js', 'public/js/admin').vue();

