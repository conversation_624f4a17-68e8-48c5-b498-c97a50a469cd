<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingEvaluate;

class WareHouseCvSellingEvaluateRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingEvaluate::class;

    public function getWithCvSelling($idCvSelling, $params = [])
    {
        $query = $this->query();

        $query->where('warehouse_cv_selling_id', $idCvSelling);

        if (isset($params['star']) && $params['star'] != 'all') {
            $query->where('star', $params['star']);
        }

        $query->with('user.company');

        return $query->orderBy('id', 'desc')->get();
    }
}
