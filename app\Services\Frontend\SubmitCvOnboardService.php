<?php

namespace App\Services\Frontend;

use App\Jobs\ChangeToRejectOffer;
use App\Jobs\ChangeToRejectOfferSubmit;
use App\Jobs\ChangeToTrailWork;
use App\Jobs\ChangeToTrailWorkSubmit;
use App\Jobs\RejectBookExpire;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\EmployerScheduleOnboardAdmin;
use App\Notifications\EmployerScheduleOnboardAdminSubmit;
use App\Notifications\EmployerScheduleOnboardRec;
use App\Notifications\EmployerScheduleOnboardRecSubmit;
use App\Notifications\RecRefuseComplain;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class SubmitCvOnboardService
{

    protected $submitCvOnboardRepository;
    protected $submitCvRepository;
    protected $submitCvHistoryStatusRepository;
    public function __construct(
                                SubmitCvOnboardRepository $submitCvOnboardRepository,
                                SubmitCvRepository $submitCvRepository,
                                SubmitCvHistoryStatusRepository $submitCvHistoryStatusRepository
    )
    {
        $this->submitCvOnboardRepository = $submitCvOnboardRepository;
        $this->submitCvRepository = $submitCvRepository;
        $this->submitCvHistoryStatusRepository = $submitCvHistoryStatusRepository;
    }

    public function scheduleOnboard($data){
        $submitCv = $this->submitCvRepository->find($data['submit_id']);
        if ($submitCv->bonus_type == 'onboard'){
            if (!in_array($submitCv->status,[11])){
                //3 => 'Waiting setup interview',
                throw new \Exception('Không đủ điều kiện đặt lịch onboard');
            }
            $user = auth('client')->user();
            $data = [
                'ntd_id'        => $user->id,
                'address'       => $data['address'],
                'name'          => $data['name'],
                'submit_cvs_id' => $data['submit_id'],
                'date_book'     => Carbon::createFromFormat('d/m/Y',$data['date']),
                'time_book'     => $data['hour'].':'.$data['minute'],
            ];
            $book = $this->submitCvOnboardRepository->create($data);
            $book->load('rec');
            $submitCv->update([
                'status' => 20
            ]);

            if ($submitCv->authorize > 0){
                Mail::to(config('settings.global.email_admin'))->send(new EmployerScheduleOnboardAdminSubmit($submitCv, $submitCv->rec,$book));
            }else{
                $submitCv->rec->notify(new EmployerScheduleOnboardRecSubmit($submitCv, $submitCv->rec,$book));
            }
            $this->submitCvHistoryStatusRepository->logStatus($submitCv,$user);
            $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book);
            ChangeToTrailWorkSubmit::dispatch($submitCv->id,$user)->delay($carbonDate->addMinutes(7 * 24 * 60));
            ChangeToRejectOfferSubmit::dispatch($submitCv->id,$book->id,auth('client')->user())->delay(now()->addMinutes(2 * 24 * 60));
        }

        //log
        return $book;
    }

    public function getBySubmitCvId($submit_id)
    {
        return $this->submitCvOnboardRepository->getBySubmitCvId($submit_id);
    }





}
