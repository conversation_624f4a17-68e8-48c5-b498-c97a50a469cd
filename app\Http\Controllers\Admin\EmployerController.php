<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ChagePasswordRequest;
use App\Http\Requests\Admin\DepositRequest;
use App\Http\Requests\Admin\EmployerRequest;
use App\Services\Admin\CompanyService;
use App\Services\Admin\DepositService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;



class EmployerController extends Controller
{
    protected $userService;
    protected $companyService;
    protected $depositService;


    public function __construct(UserService $userService, CompanyService $companyService,DepositService $depositService)
    {
        $this->userService = $userService;
        $this->companyService = $companyService;
        $this->depositService = $depositService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->userService->buildDatatableEmployer();
        $total = $this->userService->totalEmployer();
        $totalCurrentMonth = $this->userService->totalEmployerCurrentMonth();
        return view('admin.pages.employer.index', compact('datatable', 'total', 'totalCurrentMonth'));
    }

    /**
     * @param Request $request
     * @return Application|Factory|View
     */
    public function create(Request $request): View|Factory|Application
    {
        $company_id = $request->old('company_id') !== null ? $request->old('company_id') : 0;
        $companies = [];
        if ($company_id) {
            $data = $this->companyService->detail($company_id);
            $companies[] = $data[0];
        }
        return view('admin.pages.employer.create', compact('companies'));
    }

    /**
     * @param EmployerRequest $request
     * @return RedirectResponse
     */
    public function store(EmployerRequest $request)
    {
        $data = $this->userService->createEmployer($request->except('_token'));
        Toast::success(__('message.edit_success'));
        return redirect()->route('employer.edit', ['employer' => $data->id]);
    }

    /**
     * @param $id
     * @param Request $request
     * @return Application|Factory|View
     */
    public function edit($id, Request $request)
    {
        $data = $this->userService->detailService($id);

        $dataEmployerType = $this->userService->findEmployerTypeUserId($id);

        $dataRoleCompany = $this->roleCompany($data->company_id);
        $datatable = $this->depositService->buildDatatable($id);
        $company_id = $request->old('company_id') !== null ? $request->old('company_id') : $data->company_id;
        $companies = [];
        if ($company_id) {
            $dataTemp = $this->companyService->detail($company_id);
            $companies[] = $dataTemp[0];
        }
        return view('admin.pages.employer.edit', compact('id','companies', 'data', 'dataEmployerType', 'dataRoleCompany','datatable'));
    }

    /**
     * @param EmployerRequest $request
     * @param $id
     * @return RedirectResponse
     */
    public function update(EmployerRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $this->userService->updateEmployer($request->all(), $id);
            Toast::success(__('message.edit_success'));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log update employer: ', [
                'content: ' => $e->getMessage()
            ]);
        }
        return redirect()->route('employer.edit', ['employer' => $id]);
    }

    public function datatable(Request $request)
    {
        $data = $this->userService->datatableEmployer($request->all());
        return response($data);
    }

    public function datatableDeposit(Request $request,$employer){
        $data = $this->depositService->datatable($request->all(),$employer);
        return response($data);
    }

    public function deposit(DepositRequest $request){
        $data = $this->depositService->deposit($request->all());
        return response($data);
//        Toast::success('Nạp tiền thành công');
//        session()->flash('action', 'deposits');
//        return redirect()->route('employer.edit', ['employer' => $request->user_id]);
    }

    public function changePassword(ChagePasswordRequest $request, $id)
    {
        $this->userService->changePasswordById($id, $request->password);
        Toast::success(__('message.change_password_success'));
        return redirect()->route('employer.edit', ['employer' => $id]);
    }

    public function changeCompany(Request $request)
    {
        $request = $request->all();
        $search = [
            'size' => config('constant.limit_search_ajax_selectbox'),
        ];
        if (isset($request['searchTerm']) && $request['searchTerm'] != '') $search['search'] = $request['searchTerm'];
        $companies = $this->companyService->indexService($search, [], true, 'id', 'desc', ['id', 'name', 'mst']);
        $response = [];
        if ($companies) {
            foreach ($companies as $key => $value) {
                $response[$key]['id'] = $value->id;
                $response[$key]['text'] = $value->name . ' - ' . $value->mst;
            }
        }

        return json_encode($response);
    }

    public function roleCompany($id)
    {
        $result = $this->companyService->getRoleCompany($id);
        return $result;
    }

}
