<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Job;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ShowJobViewsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job:show-views';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Hiển thị danh sách job từ ngày 17/6/2025 kèm theo số lượt xem';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = '2025-06-17';
        $this->info('Đang tải danh sách job từ ngày ' . $date . '...');

        // L<PERSON>y danh sách job từ ngày 17/6/2025
        $jobs = Job::where('created_at', '>=', $date)
            ->where('is_real', 1)
            ->orderBy('created_at', 'desc')
            ->get();

        if ($jobs->isEmpty()) {
            $this->warn('Không có job nào được tạo từ ngày ' . $date);
            return Command::SUCCESS;
        }

        $this->info('Tìm thấy ' . $jobs->count() . ' job(s)');
        $this->line('');

        // Tạo bảng hiển thị
        $headers = ['ID', 'Tiêu đề', 'Slug', 'Lượt xem'];
        $rows = [];

        foreach ($jobs as $job) {
            // Đếm số lượt xem từ bảng shetabit_visits
            $viewCount = DB::table('shetabit_visits')
                ->where('url', 'LIKE', '%' . $job->slug . '%')
                ->where('created_at', '>=', $date)
                ->count();

            $rows[] = [
                $job->id,
                $this->truncateString($job->name, 50),
                $job->slug,
                $viewCount
            ];
        }

        $this->table($headers, $rows);
        $this->line('');
        $this->info('Hoàn thành!');

        return Command::SUCCESS;
    }

    /**
     * Cắt ngắn chuỗi nếu quá dài
     */
    private function truncateString($string, $length = 50)
    {
        if (strlen($string) <= $length) {
            return $string;
        }
        return substr($string, 0, $length - 3) . '...';
    }
}
