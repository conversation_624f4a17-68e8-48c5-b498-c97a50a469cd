<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailForgotPasswordRule;
use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;

class CheckEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => ['required','email',
                new CheckEmailRule(config('constant.role.rec'), null, 1),
                new CheckEmailForgotPasswordRule(config('constant.role.rec'))
            ],
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'email.email'    => __('message.email'),
        ];
    }
}
