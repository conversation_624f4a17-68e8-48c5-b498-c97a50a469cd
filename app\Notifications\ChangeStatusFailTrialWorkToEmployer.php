<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailTrialWorkToEmployer extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $point;

    public function __construct($wareHouseCvSellingBuy,$point)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->point = $point;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $url = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);
        $employerWallet = route('employer-wallet');
        $linkMarket = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusFailTrialWorkToEmployer', [
                'employerName'   => $employerName,
                'candidateName'  => $candidateName,
                'companyName'    => $companyName,
                'position'       => $position,
                'url'            => $url,
                'employerWallet' => $employerWallet,
                'linkMarket'     => $linkMarket,
                'point'          => $this->point,
            ])
            ->subject('Nhà tuyển dụng đã cập nhật trạng thái “Fail trial work” ứng viên '.$candidateName.' vị trí '.$position);

    }




}
