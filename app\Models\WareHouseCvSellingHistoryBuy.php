<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class WareHouseCvSellingHistoryBuy extends BaseModel implements Auditable 
{
    use HasFactory, Notifiable;
    use \OwenIt\Auditing\Auditable;

    protected $table = 'warehouse_cv_selling_history_buys';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = [
        'status_payment',
    ];
    protected $audit_tags = [];
    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }

    public function getStatusPaymentAttribute(){
        //type = 0 tru tien, percent
        //NTD
        /*1 => 'Đã cọc',
        2 => 'Hoàn cọc',
        3 => 'Hoàn tiền',
        4 => 'NTD Đã thanh toán',*/
        if ($this->type == 0 && $this->percent == 10){
            return 1;//Đã cọc 10%
        }
        if ($this->type == 0 && $this->percent == 90){
            return 4;//NTD Đã thanh toán
        }
        if ($this->type == 0 && $this->percent == 100){
            return 4;//NTD Đã thanh toán
        }

        if ($this->type == 1 && $this->percent == 10){
            return 2;//Hoàn cọc
        }
        if ($this->type == 1 && $this->percent == 90){
            return 3;//Hoàn tiền
        }
        if ($this->type == 1 && $this->percent == 100){
            return 3;//Hoàn tiền
        }
        //type = 3 hoan tien, percent
        return 3;
    }


    public function wareHouseCvSelling()
    {
        return $this->belongsTo(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_buy_id', 'id');
    }

    public function wareHouseCvSellingBuy()
    {
        return $this->belongsTo(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_buy_id', 'id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('d/m/Y, g:i A');
        }
        return null;
    }

    public function getPointTypeAttribute(){
        if ($this->type === 0){
            $point = '- '. number_format($this->point);
        }else{
            $point = '+ '. number_format($this->point);
        }
        return $point;
    }


}
