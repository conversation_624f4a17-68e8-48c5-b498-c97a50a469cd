<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingBuyDiscuss;

class WareHouseCvSellingBuyDiscussRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingBuyDiscuss::class;

    public function getByWarehouseCvBuyId($warehouseCvBuyId){
        $query = $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId)
            ->with(['employer','rec']);

        return $query->get();
    }
}
