<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentTrialWorkSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $number;
    protected $price;
    protected $percent;

    public function __construct($submitCv,$number,$price,$percent)
    {
        $this->submitCv = $submitCv;
        $this->number   = $number;
        $this->price    = $price;
        $this->percent  = $percent;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->bonus_type;
        $recName       = $this->submitCv->rec->name;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id]);
        $recTurnovers  = route('rec-turnovers');

        $subject = '[Recland] Thanh toán đợt '.$this->number.' ứng viên Onboard - Giới thiệu ứng viên';
        if ($this->number == 3){
            $subject = '[Recland] Hoàn tất thanh toán ứng viên Onboard - Giới thiệu ứng viên';
        }

        return (new MailMessage)
            ->view('email.paymentTrialWorkSubmit', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'url'           => $url,
                'recTurnovers'  => $recTurnovers,
                'number'        => $this->number,
                'price'         => $this->price,
                'percent'       => $this->percent,
                'totalPrice'    => $this->submitCv->price,
            ])
            ->subject($subject);

    }




}
