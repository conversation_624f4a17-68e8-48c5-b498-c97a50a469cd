---
description:
globs:
alwaysApply: false
---
# Routing Guide

H<PERSON> thống routing của RecLand được chia thành các nhóm chính:

## Frontend Routes

File [routes/web.php](mdc:routes/web.php) chứa routes cho phần frontend của hệ thống:

- Routes cho authentication: đăng nhập, đăng ký, quên mật khẩu
- Routes cho trang chủ và trang tĩnh
- Routes cho tìm kiếm và hiển thị công việc
- Routes cho profile người dùng
- Routes cho quản lý CV của ứng viên
- Routes cho trang blog và bài viết
- Routes cho xử lý thanh toán
- Routes cho nhà tuyển dụng

## Admin Routes

File [routes/admin.php](mdc:routes/admin.php) chứa các routes cho phần quản trị:

- Routes cho dashboard admin
- Routes cho quản lý người dùng
- Routes cho quản lý công việc
- Routes cho quản lý CV
- Routes cho quản lý thanh toán và giao dịch
- Routes cho cấu hình hệ thống
- Routes cho báo cáo và thống kê

## API Routes

File [routes/api.php](mdc:routes/api.php) chứa các endpoints API:

- API authentication
- API cho quản lý CV
- API cho báo cáo
- API tích hợp với hệ thống khác

## Middleware

Các routes được bảo vệ bởi các middleware:

- `auth` - Kiểm tra đăng nhập
- `role` - Kiểm tra quyền người dùng
- `verified` - Kiểm tra xác thực email
- `api` - Middleware cho API
