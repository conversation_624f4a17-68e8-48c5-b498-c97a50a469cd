<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $id = $this->route('role');
        return [
            'name' => 'required|unique:roles,name,'.$id.',id',
            'permission' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'unique' => __('message.role_name_unique'),
            'permission.required' => __('message.permission_required'),
        ];
    }
}
