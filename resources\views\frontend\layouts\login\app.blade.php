<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {!! SEOMeta::generate() !!}
    {!! OpenGraph::generate() !!}

    <link rel="icon" href="{{ gen_url_file_s3(config('settings.global.img_favicon')) }}">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;600;800&family=Noto+Sans&display=swap"
        rel="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="stylesheet" href="{{ asset2('frontend/asset/css/all_v1.css') }}">
    {!! config('settings.global.script-head-textarea') !!}
    @yield('css_custom')
    @stack('before_styles')
    @include('frontend.inc_layouts.v2.header_script')
</head>

<body>
    {!! config('settings.global.script-body-textarea') !!}
    <div id="page">
        <div id="fb-root"></div>
        <script async defer crossorigin="anonymous"
            src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v14.0&appId=936311346749549&autoLogAppEvents=1"
            nonce="3sLgPOQZ"></script>
        <!-- Header -->
        @include('frontend.inc_layouts.login.header')
        <!-- End Header -->
        @yield('content')
        <!-- Footer -->
        {{--    @include('frontend.inc_layouts.login.footer') --}}
        @include('frontend.inc_layouts.login.footer_v2')
        @include('frontend.inc_layouts.login.modal_report')
        {{-- @include('frontend.layouts.modal.modal-intro') --}}
        <!-- End Footer -->

        <!-- Bug Report Modal -->
        @auth('client')
        @include('frontend.partials.bug_report_modal')
        @endauth
    </div>
    @if (use_vue_in_route())
        <script src="{{ mix('js/app.js') }}"></script>
    @endif
    
    <script src="{{ asset2('frontend/asset/js/all_v1.js') }}"></script>
    <script src="{{ asset2('frontend/asset/js/additional-methods.min.js') }}"></script>
    {!! App\Services\Admin\Toast\Facades\Toast::message() !!}
    @yield('scripts')
    @stack('after_styles')
    @stack('after_scripts')

    {{-- <script src="http://localhost:35729/livereload.js?snipver=1"></script> --}}
</body>

</html>
