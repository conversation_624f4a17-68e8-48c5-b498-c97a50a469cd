<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerScheduleInterviewRec extends Notification implements ShouldQueue
{
    use Queueable;


    protected $wareHouseCvSellingBuy;
    protected $rec;
    protected $wareHouseCvSellingBuyBook;

    public function __construct($wareHouseCvSellingBuy,$rec,$wareHouseCvSellingBuyBook)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->rec = $rec;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName = $this->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;

        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $timeInterview = $this->wareHouseCvSellingBuyBook->date_time_book_format;
        $address = $this->wareHouseCvSellingBuyBook->address;
        $type_of_sale = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $link = route('rec-cv-sold') . '?cv_sold=' . $this->wareHouseCvSellingBuy->id . '&open_comment=1';

        return (new MailMessage)
            ->view('email.employerScheduleInterviewRec', [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'timeInterview' => $timeInterview,
                'address' => $address,
                'type_of_sale' => $type_of_sale,
                'link' => $link,
            ])
            ->subject('[RECLAND]  Thông báo lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);

    }

}

