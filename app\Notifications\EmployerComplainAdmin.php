<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerComplainAdmin extends Mailable
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $cvSellingBuy;

    public function __construct($rec,$employer,$cvSellingBuy)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->cvSellingBuy = $cvSellingBuy;
    }

    public function content(): Content
    {
        $candidateName = '';
        $position = '';
        $type = '';
        $content = $this->cvSellingBuy->txt_complain;
        $image = $this->cvSellingBuy->img_complain;
        $companyName = $this->employer->name;
        if (!empty($this->cvSellingBuy->wareHouseCvSelling)){
            $type = $this->cvSellingBuy->wareHouseCvSelling->type_of_sale;
        }
        if (!empty($this->cvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->cvSellingBuy->job)){
                $position = $this->cvSellingBuy->job->name;
            }
        }
        $url = route('luot-ban.show',['luot_ban' => $this->cvSellingBuy->id,'tab' => 'khieunai']);

        return new Content(
            view: 'email.employerComplainAdmin',
            with: [
                'name' => $this->rec->name,
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type,
                'content' => $content,
                'image' => gen_url_file_s3($image),
                'url' => $url,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $companyName = $this->employer->name;
        $candidateName = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $message->subject('[Uỷ Quyền][Recland][Case #'.$this->cvSellingBuy->id.'] Thông báo khiếu nại của công ty '.$companyName.' đối với Ứng viên '.$candidateName);
        return $this;
    }

}
