<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateBankInfo extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'bank_account' => 'required',
            'bank_name' => 'required',
            'bank_account_number' => 'required',
            'bank_branch' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required'     => __('frontend/validation.required'),
        ];
    }
}
