<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\JobComment;
use App\Models\Job;
use App\Models\User;

class NewJobCommentReply extends Mailable
{
    use Queueable, SerializesModels;

    public $reply;
    public $parentComment;
    public $job;
    public $replier;

    public function __construct(JobComment $reply, JobComment $parentComment, Job $job, User $replier)
    {
        $this->reply = $reply;
        $this->parentComment = $parentComment;
        $this->job = $job;
        $this->replier = $replier;
    }

    public function build()
    {
        return $this->subject('<PERSON><PERSON> phản hồi mới cho câu hỏi của bạn trên job "'.$this->job->name.'"')
                    ->view('emails.new-comment-reply');
    }
} 