<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\WareHouseCvSellingService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class WareHouseCvSellingController extends Controller
{

    protected $wareHouseCvSellingService;

    public function __construct(
        WareHouseCvSellingService $wareHouseCvSellingService
    ) {
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->wareHouseCvSellingService->buildDatatable();
        $total = $this->wareHouseCvSellingService->total();
        //        $totalCurrentMonth = $this->wareHouseCvSellingService->totalCurrentMonth();
        return view('admin.pages.cv-selling.index', compact('datatable', 'total'));
    }

    public function datatable(Request $request)
    {
        $data = $this->wareHouseCvSellingService->datatable($request->all());
        return response($data);
    }

    public function updateAuthority(Request $request)
    {
        $data = $this->wareHouseCvSellingService->updateAuthority($request->all());
        return $data;
    }

    public function createViewTokenUrl(Request $request)
    {
        $url = $this->wareHouseCvSellingService->createViewTokenUrl($request->all());
        if ($url) {
            return response()->json(['status' => true, 'url' => $url]);
        } else {
            return response()->json(['status' => false, 'message' => 'Lỗi tạo link xem']);
        }
    }
}
