# Workflow: <PERSON><PERSON><PERSON> nhật created_at cho warehouse_cvs từ itnavi_search_cvs

**Ng<PERSON>y tạo**: 14/01/2025  
**<PERSON><PERSON> tả**: Tạo command để cập nhật created_at cho các CV trong warehouse_cvs có source = 'itnavi_import_tructiep' và created_at IS NULL, dựa trên email matching với bảng itnavi_search_cvs.

## Vấn đề cần giải quyết

Có 31,649 CV trong bảng `warehouse_cvs` với điều kiện:
- `created_at IS NULL`
- `source = 'itnavi_import_tructiep'`

Cần cập nhật `created_at` cho các CV này dựa trên:
- `warehouse_cvs.candidate_email = itnavi_search_cvs.email`
- `warehouse_cvs.created_at = itnavi_search_cvs.created_at`

## Command đã tạo

### File: `app/Console/Commands/UpdateWarehouseCvCreatedAt.php`

**Signature**: `warehouse-cv:update-created-at`

**Options**:
- `--dry-run`: Ch<PERSON><PERSON> thử không thực sự cập nhật dữ liệu
- `--limit=100`: Số lượng CV xử lý mỗi batch (mặc định: 100)

**Chức năng**:
1. Lấy danh sách CV có `created_at IS NULL` và `source = 'itnavi_import_tructiep'`
2. Với mỗi CV, tìm email tương ứng trong bảng `itnavi_search_cvs`
3. Cập nhật `created_at` từ `itnavi_search_cvs.created_at`
4. Xử lý theo batch để tránh timeout
5. Hiển thị progress và thống kê

## Cách sử dụng

### 1. Chạy thử (Dry Run)
```bash
php artisan warehouse-cv:update-created-at --dry-run --limit=10
```

### 2. Chạy thực tế
```bash
php artisan warehouse-cv:update-created-at --limit=100
```

### 3. Chạy với batch lớn hơn
```bash
php artisan warehouse-cv:update-created-at --limit=500
```

## Kết quả test

**Tổng số CV cần cập nhật**: 31,649 CV

**Test với --dry-run --limit=5**:
- Tất cả CV đều tìm thấy email matching trong `itnavi_search_cvs`
- Dữ liệu `created_at` từ `itnavi_search_cvs` hợp lệ
- Command hoạt động ổn định

**Ví dụ output**:
```
Bắt đầu cập nhật created_at cho warehouse_cvs...
Dry run: Có
Limit: 5

Tổng số CV cần cập nhật: 31649
Xử lý CV #13642 - Email: <EMAIL>
  → Tìm thấy trong itnavi_search_cvs: 2021-05-25 16:59:26
  → [DRY RUN] Sẽ cập nhật created_at = 2021-05-25 16:59:26
```

## Logic xử lý

1. **Query CV cần cập nhật**:
   ```sql
   SELECT * FROM warehouse_cvs 
   WHERE created_at IS NULL 
   AND source = 'itnavi_import_tructiep'
   ```

2. **Tìm email matching**:
   ```sql
   SELECT * FROM itnavi_search_cvs 
   WHERE email = ? 
   ORDER BY created_at DESC 
   LIMIT 1
   ```

3. **Cập nhật created_at**:
   ```sql
   UPDATE warehouse_cvs 
   SET created_at = ?, updated_at = NOW() 
   WHERE id = ?
   ```

## Tính năng

- ✅ **Batch processing**: Xử lý theo batch để tránh timeout
- ✅ **Dry run mode**: Chạy thử không thay đổi dữ liệu
- ✅ **Progress tracking**: Hiển thị tiến độ xử lý
- ✅ **Error handling**: Xử lý trường hợp không tìm thấy email
- ✅ **Statistics**: Thống kê số lượng CV đã xử lý/cập nhật
- ✅ **Configurable limit**: Có thể điều chỉnh số lượng xử lý mỗi batch

## Lưu ý quan trọng

1. **Backup dữ liệu** trước khi chạy command thực tế
2. **Chạy dry-run** trước để kiểm tra
3. **Monitor server resources** khi chạy với batch lớn
4. **Có thể chạy nhiều lần** - command sẽ chỉ xử lý CV có `created_at IS NULL`

## Command registration

Command đã được tự động đăng ký thông qua:
```php
// app/Console/Kernel.php
$this->load(__DIR__ . '/Commands');
```

## Thời gian ước tính

Với 31,649 CV và batch size 100:
- Số batch: ~317 batch
- Thời gian ước tính: 10-30 phút (tùy thuộc vào hiệu suất server)

## Hoàn thành

✅ Command đã được tạo và test thành công  
✅ Sẵn sàng để chạy thực tế  
✅ Documentation đầy đủ
