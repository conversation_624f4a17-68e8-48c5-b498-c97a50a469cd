<?php
namespace App\Jobs;

use App\Notifications\ChangeStatusCancelOnboardToAdmin;
use App\Notifications\ChangeStatusCancelOnboardToAdminSubmit;
use App\Notifications\ChangeStatusCancelOnboardToRec;
use App\Notifications\ChangeStatusCancelOnboardToRecSubmit;
use App\Notifications\EmailRejectOnboard;
use App\Notifications\EmailRejectOnboardSubmit;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class ChangeToRejectOfferSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $submitCvId;
    protected $user;
    protected $onboardId;

    public function __construct($submitCvId,$onboardId,$user)
    {
        $this->submitCvId = $submitCvId;
        $this->user = $user;
        $this->onboardId = $onboardId;
    }

    public function handle()
    {
        $submitCvOnboardRepository = app(SubmitCvOnboardRepository::class);
        $submitCvRepository = app(SubmitCvRepository::class);
        $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        if($submitCv->status != 20) return false;

        $onboard = $submitCvOnboardRepository->find($this->onboardId);
        $onboard->update([
            'status' => 2
        ]);
        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
        //12 => 'Reject Offer',
        $submitCv->update([
            'status' => 12,
        ]);
        $submitCvHistoryStatusRepository->logStatus($submitCv,$this->user);

        $employer = $submitCv->employer;
        $employer->notify(new EmailRejectOnboardSubmit($employer,$onboard,$submitCv));
        if ($submitCv->wareHouseCvSelling->authority > 0){
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdminSubmit($submitCv));
        }else{
            $submitCv->rec->notify(new ChangeStatusCancelOnboardToRecSubmit($submitCv));
        }
        //todo hoàn cọc
        DepositRefundRejectOfferSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(48 * 60));;
    }

}
