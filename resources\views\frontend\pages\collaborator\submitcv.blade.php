@extends('frontend.layouts.collaborator.app')
@section('css_custom')
<link rel="stylesheet" href="{{ asset2('frontend/asset/css/introducation.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
    .modal-candidate-cancel .modal-content {
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .modal-candidate-cancel .modal-header {
        border-radius: 12px 12px 0 0;
    }

    .modal-candidate-cancel .btn {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .modal-candidate-cancel .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .modal-candidate-cancel .alert {
        border-radius: 8px;
    }

    .modal-candidate-cancel .candidate-name-box {
        background: linear-gradient(135deg, #62adbf 0%, #17677b 100%);
        color: white;
        border-radius: 8px;
        padding: 12px 16px;
        font-weight: 600;
    }
</style>
{{--
<link rel="stylesheet" href="{{asset2('frontend/asset/css/cv-sale.css')}}"> --}}
@endsection
@section('content-collaborator')
<div id="candidate-introduction-manager">
    <div class="header">
        <div class="search">
            <form id="form-search" action="{{ route('rec-submitcv') }}">
                <div class="input-search">
                    <input class=""
                        placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.placeholder_search') }}"
                        name="search" value="{{ isset($_GET['search']) ? $_GET['search'] : '' }}">
                </div>
                <input type="text" name="page" value="{{ isset($_GET['page']) ? $_GET['page'] : '' }}" hidden>
                <input type="hidden" name="status" value="{{ isset($_GET['status']) ? $_GET['status'] : '' }}">
            </form>
            <button class="button-search button-search-modify">{{ config('settings.' . app()->getLocale() .
                '.rec_submitcv.tim') }}</button>
        </div>
        <div class="button-header">
            <a href="javascript:void(0)" data-toggle="open-popup">{{ config('settings.' . app()->getLocale() .
                '.rec_submitcv.gioithieuungvien') }}</a>
        </div>
    </div>

    <?php
        $arrStatus = isset($_GET['status']) ? explode(',', $_GET['status']) : [];
        $arrStatus = array_flip($arrStatus);
        ?>

    <div class="select-option-group">
        <select name="select-status[]" id="status-select" data-select2-id="select2-data-status-select" multiple
            tabindex="-1" class="select2-hidden-accessible" aria-hidden="true">
            <option {{ isset($arrStatus['all']) ? 'selected' : '' }} value="all">
                {{ config('settings.' . app()->getLocale() . '.rec_submitcv.tatcatrangthai') }}</option>
            @foreach ($submitCvsStatus as $k => $status)
            <option value="{{ $k }}" {{ isset($arrStatus[$k]) ? 'selected' : '' }}>
                {{ ucfirst($status) }}</option>
            @endforeach
        </select>
    </div>

    <div class="group-item">
        @if (count($arrSubmitCv))
        @foreach ($arrSubmitCv as $k => $item)
        <div class="item" style="position: relative;">
            <div class="ava">
                <img src="{{ $item->company->path_logo }}">
            </div>
            <div class="content">
                <div class="wrapper-content-item">
                    <div class="header-content-item">
                        <div class="name" data-id="21" data-bs-toggle="tooltip" data-bs-placement="top" title=""
                            style="cursor: pointer"
                            data-bs-original-title="{{ optional($item->submitCvMeta)->candidate_name }}">
                            {{ optional($item->submitCvMeta)->candidate_name }}</div>
                        <div class="list-info list-info-1">
                            <ul>
                                <li><span class="icon"><img
                                            src="{{ asset2('frontend/asset/images/cv-on-sale/icon-item-1.svg') }}"></span>
                                    <span data-bs-toggle="tooltip" data-bs-placement="top" title="" class="text-over"
                                        data-bs-original-title="{{ optional($item->submitCvMeta)->candidate_email }}">{{
                                        optional($item->submitCvMeta)->candidate_email }}</span>
                                </li>
                                <li><span class="icon"><img
                                            src="{{ asset2('frontend/asset/images/cv-on-sale/icon-item-2.svg') }}"></span>
                                    <span class="text-over">{{ optional($item->submitCvMeta)->candidate_mobile }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="buyer">
                        <span class="label">Vị trí ứng tuyển: </span>
                        <span class="name"><a href="{{ route('job-detail', ['slug' => $item->job->slug]) }}"
                                target="_blank" class="text-black">{{ $item->job->name }}</a></span>
                        {{-- <span class="label">Công ty: </span> --}}
                        {{-- <span class="name">{{$item->job->name}}</span> --}}
                        {{-- <span class="date">12:47 PM, 18/04/2024</span> --}}
                    </div>
                    <div class="main-content-item">
                        <div class="object-main-content-item">
                            <div class="label-object">
                                Hình thức ứng tuyển
                            </div>
                            <div class="main-object">
                                {{ \Str::ucfirst($item->bonus_type) }}
                            </div>
                        </div>
                        <div class="object-main-content-item object-price">
                            <div class="label-object">
                                Số tiền bonus
                            </div>
                            <div class="main-object">
                                <span>
                                    {{ \App\Helpers\Common::formatNumber($item->bonus) }}
                                </span>
                                <span class="currency">VNĐ</span>
                            </div>
                        </div>
                        <div class="object-main-content-item ">
                            <div class="label-object">
                                Trạng thái thanh toán
                            </div>
                            <div class="main-object">
                                <span class="span-st1">{{ $item->status_payment_ctv ? config('constant.status_payments.'
                                    . app()->getLocale())[$item->status_payment_ctv] : 'Chưa thanh toán' }}</span>
                            </div>
                        </div>
                        <div class="object-main-content-item ">
                            <div class="label-object">
                                Trạng thái tuyển dụng
                            </div>
                            <div class="main-object">
                                <span class="span-st1">{{ $item->status_recruitment_value }}</span>
                            </div>
                        </div>
                        <div class="object-main-content-item">
                            <div class="label-object">
                                Trạng thái khiếu nại
                            </div>
                            <div class="main-object">
                                <span class="{{ $item->status_complain ? 'span-st2' : 'text-gray' }}">{{
                                    $item->status_complain_value }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="status-group">
                <div class="button">
                    <a class="detail-cv" data-id="{{ $item->id }}" href="javascript:void(0)">Chi tiết</a>
                    @if ($item->status == config('constant.status_recruitment_revert.CandidateCancelApply'))
                    <span class="cancelled-text"
                        style="color: #dc3545; font-weight: bold; padding: 4px 12px;  border-radius: 4px; font-size: 14px;">Đã
                        hủy</span>
                    @elseif ((!$item->status_payment_ctv || $item->status_payment_ctv == 0) && $item->can_cancel_apply)
                    <a class="cancel-submit-btn" data-toggle="candidate-cancel-confirm" data-id="{{ $item->id }}"
                        data-name="{{ optional($item->submitCvMeta)->candidate_name }}" href="javascript:void(0)"
                        style="color: #dc3545; border: 1px solid #dc3545; padding: 4px 12px; border-radius: 30px; text-decoration: none; background-color: #fff; transition: all 0.3s;"
                        onmouseover="this.style.backgroundColor='#dc3545'; this.style.color='#fff';"
                        onmouseout="this.style.backgroundColor='#fff'; this.style.color='#dc3545';">Hủy ứng tuyển</a>
                    @endif
                </div>
            </div>
        </div>
        {{-- <div class="item">
            <div class="ava">
                <img src="{{$item->company->path_logo}}">
            </div>
            <div class="content">
                <div class="title-item">
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.thongtincanhan') }}
                </div>
                <div class="name detail-submitcv" data-id="{{$item->id}}" style="cursor: pointer">
                    {{optional($item->submitCvMeta)->candidate_name}}
                </div>
                <div class="list-info list-info-1">
                    <ul>
                        <li><span class="icon"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-1.svg')}}"></span>
                            <span data-bs-toggle="tooltip" data-bs-placement="top"
                                title="{{optional($item->submitCvMeta)->candidate_email}}"
                                class="text-over">{{optional($item->submitCvMeta)->candidate_email}}</span>
                        </li>
                        <li><span class="icon"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-2.svg')}}"></span>
                            <span class="text-over">{{optional($item->submitCvMeta)->candidate_mobile}}</span>
                        </li>
                    </ul>
                </div>
                <div class="title-item">
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.thongtinungtuyen') }}
                </div>
                <div class="list-info list-info-2">
                    <ul>
                        <li><span class="icon"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-3.svg')}}"></span>
                            <span data-bs-toggle="tooltip" data-bs-placement="top"
                                title="{{optional($item->submitCvMeta)->candidate_job_title}}"
                                class="text-over">{{optional($item->submitCvMeta)->candidate_job_title}}</span>
                        </li>
                        <li><span class="icon"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-4.svg')}}"></span>
                            @if (isset($item->job))
                            <span class="text-over">
                                <a data-bs-toggle="tooltip" data-bs-placement="top" title="{{$item->job->name}}"
                                    target="_blank"
                                    href="{{route('job-detail', ['slug' => $item->job->slug])}}">{{$item->job->name}}</a>
                            </span>
                            @endif
                        </li>
                        <li><span class="icon"><img
                                    title="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.mucluongmongmuon') }}"
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-5.svg')}}"></span>
                            <span
                                title="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.mucluongmongmuon') }}"
                                class="text-over candidate-currency">{{\App\Helpers\Common::formatNumber((int)
                                $item->candidate_salary_expect)}} </span>
                            <sub>{{$item->candidate_currency_value}}</sub>
                        </li>
                        <li><span class="icon"><img
                                    src="{{asset2('frontend/asset/images/dashboard-ctv/icon-item-6.svg')}}"></span>
                            <span
                                class="text-over">{{\Carbon\Carbon::parse($item->expected_date)->format('d/m/Y')}}</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="status-group">
                @if (in_array($item->status, [0, 9, 10]))
                <div class="status pending-review">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                <div class="button">
                    <a data-toggle="cancel-confirm" data-id="{{$item->id}}"
                        data-name="{{optional($item->submitCvMeta)->candidate_name}}" href="javascript:void(0)">{{
                        config('settings.' . app()->getLocale() . '.rec_submitcv.huyungtuyen') }}</a>
                </div>
                @elseif($item->status == 1)
                <div class="status accepted">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @elseif($item->status == 2)
                <div class="status rejected">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @elseif($item->status == 3)
                <div class="status pass-interview">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @elseif($item->status == 4)
                <div class="status fail-interview">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @elseif($item->status == 5)
                <div class="status onboarded">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @elseif($item->status == 6)
                <div class="status cancel">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @else
                <div class="status draft">{{config('constant.submit_cvs_status.'.$item->status)}}</div>
                @endif

                @if ($item->authorize == 1)
                <div class="authorize">
                    @if ($item->authorize_status == 0)
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.choxacnhanuyquyen') }}
                    @endif
                    @if ($item->authorize_status == 1)
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.dongyuyquyen') }}
                    @endif
                    @if ($item->authorize_status == 2)
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.tuchoiuyquyen') }}
                    @endif
                </div>
                @endif
            </div>
        </div> --}}
        @endforeach
        @endif
    </div>

    <div class="group-paginate">
        <div class="paginate">
            @if (!$arrSubmitCv->onFirstPage())
            <div class="pre paginate_prev"
                data-paginate="{{ $arrSubmitCv->currentPage() - 1 <= 1 ? 1 : $arrSubmitCv->currentPage() - 1 }}">
            </div>
            @endif
            <div class="number">
                <span class="paginate_current" data-value="{{ $arrSubmitCv->currentPage() }}">{{
                    $arrSubmitCv->currentPage() }}</span>
                /
                <span class="paginate_total" data-value="{{ $arrSubmitCv->lastPage() }}">{{ $arrSubmitCv->lastPage()
                    }}</span>
            </div>
            @if (!$arrSubmitCv->onLastPage())
            <div class="next paginate_next"
                data-paginate="{{ $arrSubmitCv->currentPage() + 1 >= $arrSubmitCv->lastPage() ? $arrSubmitCv->lastPage() : $arrSubmitCv->currentPage() + 1 }}">
            </div>
            @endif
        </div>
        <div class="goto">
            {{ config('settings.' . app()->getLocale() . '.rec_submitcv.didentrang') }}:
            <input type="number" id="gotoPage" min="1" oninput="validity.valid||(value='');"
                value="{{ $arrSubmitCv->currentPage() }}">
        </div>
    </div>

</div>
<div class="modal fade" id="modal-confirm" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered " role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="header">{{ config('settings.' . app()->getLocale() . '.rec_submitcv.huyungtuyen') }}</div>
                <div class="content">
                    <p class="p-confirm">
                        {{ config('settings.' . app()->getLocale() . '.rec_submitcv.xacnhanhuyungvien') }} </p>
                    <p class="p-name"></p>
                    <input class="p-id" type="hidden" value="">
                </div>
                <div class="footer">
                    <button class="button button-back" data-bs-dismiss="modal">{{ config('settings.' .
                        app()->getLocale() . '.rec_submitcv.quaylai') }}</button>
                    <button class="button button-confirm">{{ config('settings.' . app()->getLocale() .
                        '.rec_submitcv.xacnhan') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade modal-confirm" id="modal-agree" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered " role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="header">{{ config('settings.' . app()->getLocale() . '.rec_submitcv.dongyungtuyen') }}
                </div>
                <div class="content">
                    <p class="p-confirm">
                        {{ config('settings.' . app()->getLocale() . '.rec_submitcv.xacnhandongyungvien') }} </p>
                    <p class="p-name"></p>
                    <input class="p-id" type="hidden" value="">
                </div>
                <div class="footer">
                    <button class="button button-back" data-bs-dismiss="modal">{{ config('settings.' .
                        app()->getLocale() . '.rec_submitcv.quaylai') }}</button>
                    <button class="button button-confirm1">{{ config('settings.' . app()->getLocale() .
                        '.rec_submitcv.xacnhan') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="detail-cv">

</div>

<!-- Modal CV -->
<div class="modal fade" id="modal-cv">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div class="header back-header" style="cursor: pointer"><img
                        src="{{ asset2('frontend/asset/images/dashboard-ctv/arrow-right.svg') }}">
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.chitietgioithieuungvien') }}
                </div>
                <div class="content">
                    <iframe class="iframe" src="" style="width: 100%">

                    </iframe>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Modal CV -->

<!-- Modal gioithieuungvien -->
<div class="modal fade modal-job" id="modal-introduction" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h3 class="title text-center">
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.gioithieuungvien') }}
                </h3>
                <ul class="nav nav-pills header-tab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a href="javascript:void(0)" class="item-header-tab from-storage active" data-bs-toggle="pill"
                            data-bs-target="#tab-introduction-from-storage">
                            <span>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.chontukho') }}</span>
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a href="javascript:void(0)" class="item-header-tab new" data-bs-toggle="pill"
                            data-bs-target="#tab-introduction-new">
                            <span>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.gioithieumoi') }}</span>
                        </a>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane tab-pane-form fade show active" id="tab-introduction-from-storage">
                        <div class="form-introduction-from-storage">
                            <form action="{{ route('user-save-cv-submit') }}" id="chontukho" method="post"
                                autocomplete="off">
                                @csrf
                                <div class="item-form">
                                    <label>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.chonungvien') }}
                                        <span>*</span></label>
                                    <select style="width: 100%" id="list_cv"
                                        class="select2-custom select2-style select2-modal" name="ware_house_cv">
                                        <option value="">
                                            {{ config('settings.' . app()->getLocale() . '.rec_submitcv.chonungvien') }}
                                        </option>
                                        {{-- @if ($arrWareHouseCv) --}}
                                        {{-- @foreach ($arrWareHouseCv as $item) --}}
                                        {{-- <option value="{{$item->id}}">{{$item->candidate_name}} -
                                            {{$item->candidate_job_title}} - {{$item->candidate_email}}</option> --}}
                                        {{-- @endforeach --}}
                                        {{-- @endif --}}
                                    </select>
                                    @if ($errors->has('ware_house_cv'))
                                    <label class="error-message">{{ $errors->first('ware_house_cv') }}</label>
                                    @endif
                                </div>
                                <div class="item-form">
                                    <label>{{ config('settings.' . app()->getLocale() .
                                        '.rec_submitcv.ngaydukienvaolam') }}
                                        <span>*</span></label>
                                    <input class="field-item fc-datepicker" type="text"
                                        placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.chon') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.ngaydukienvaolam')) }}"
                                        name="expected_date">
                                </div>
                                <div class="row">
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.loaiungtuyen') }}
                                            <span>*</span></label>
                                        <select style="width: 100%" class="select2-custom select2-style"
                                            name="is_self_apply">
                                            <option value="0">
                                                {{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.gioithieuungvien') }}
                                            </option>
                                            <option value="1">
                                                {{ config('settings.' . app()->getLocale() . '.rec_submitcv.tuungtuyen')
                                                }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.congviecmuonungtuyen') }}
                                            <span>*</span></label>
                                        <select style="width: 100%" class="select2-custom select2-style list-job"
                                            id="job_chontukho" name="job">
                                            <option value="">
                                                {{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.choncongviec') }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="item-form">
                                            <label>{{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.mucluongmongmuon') }}
                                                <span>*</span></label>
                                            <input data-toggle="current_mask" data-target="#candidate_salary_expect_1"
                                                class="field-item " type="text"
                                                placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.mucluongmongmuon')) }}"
                                                name="candidate_salary_expect">
                                            <input id="candidate_salary_expect_1" type="hidden" value=""
                                                name="candidate_salary_expect">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="item-form">
                                            <label>{{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.loaitien') }}</label>
                                            <select style="width: 100%" class="select2-custom select2-style"
                                                name="candidate_currency">
                                                @foreach ($currency as $item)
                                                ;
                                                <option value="{{ $item }}">{{ $item }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="tab" value="chontukho">
                                {{-- <input type="hidden" name="slug" value="{{$arrJob[0]->slug}}"> --}}
                            </form>
                            <div class="bottom-form">
                                <button type="button" class="btn-draft">{{ config('settings.' . app()->getLocale() .
                                    '.rec_submitcv.luubannhap') }}</button>
                                <button type="button" class="btn-save">{{ config('settings.' . app()->getLocale() .
                                    '.rec_submitcv.xacnhan') }}</button>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane tab-pane-form fade" id="tab-introduction-new">
                        <div class="form-introduction-new">
                            <div class="step">
                                <ul>
                                    <li>
                                        <span data-step="1" class="step-item step-item1 active"></span>
                                    </li>
                                    <li>
                                        <span data-step="2" class="step-item step-item2"></span>
                                    </li>
                                    <li>
                                        <span data-step="3" class="step-item step-item2"></span>
                                    </li>
                                </ul>
                            </div>

                            <form action="{{ route('user-save-cv-submit') }}" id="themmoi_cv" method="post"
                                enctype="multipart/form-data" autocomplete="off">
                                @csrf
                                <input type="hidden" name="tab" value="themmoi_cv">
                                {{-- <input type="hidden" name="slug" value="{{$arrJob[0]->slug}}"> --}}
                                {{-- <input type="hidden" name="status" value="0"> --}}

                                <div class="form-step active" data-step="1">
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.hovaten') }}
                                            <span>*</span></label>
                                        <input class="field-item " type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.hovaten')) }}"
                                            name="candidate_name">
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.ngaydukienvaolam') }}
                                            <span>*</span></label>
                                        <input class="field-item fc-datepicker" type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.chon') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.ngaydukienvaolam')) }}"
                                            name="expected_date">
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.sodienthoai')
                                            }}
                                            <span>*</span></label>
                                        <input class="field-item " type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.sodienthoai')) }}"
                                            name="candidate_mobile">
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() . '.rec_submitcv.email') }}
                                            <span>*</span></label>
                                        <input class="field-item " type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.email')) }}"
                                            name="candidate_email">
                                        @if ($errors->has('candidate_email'))
                                        <label class="error-message">{{ $errors->first('candidate_email') }}</label>
                                        @endif
                                    </div>

                                    <div class="bottom-form">
                                        <button type="button" data-bs-dismiss="modal" class="btn-draft1">{{
                                            config('settings.' . app()->getLocale() . '.rec_submitcv.dong') }}</button>
                                        <button type="button" data-toggle="next"
                                            class="btn-save-clone next btn-save1">{{ config('settings.' .
                                            app()->getLocale() . '.rec_submitcv.tieptheo') }}</button>
                                    </div>
                                </div>

                                <div class="form-step" data-step="2">
                                    <div class="row">
                                        <div class="item-form">
                                            <label>{{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.loaiungtuyen') }}
                                                <span>*</span></label>
                                            <select style="width: 100%" class="select2-custom select2-style"
                                                name="is_self_apply">
                                                <option value="0">
                                                    {{ config('settings.' . app()->getLocale() .
                                                    '.rec_submitcv.gioithieuungvien') }}
                                                </option>
                                                <option value="1">
                                                    {{ config('settings.' . app()->getLocale() .
                                                    '.rec_submitcv.tuungtuyen') }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="item-form">
                                            <label>{{ config('settings.' . app()->getLocale() .
                                                '.rec_submitcv.congviecmuonungtuyen') }}
                                                <span>*</span></label>
                                            <select style="width: 100%" class="select2-custom select2-style list-job"
                                                name="job" id="job_themmoi_cv">
                                                <option value="">
                                                    {{ config('settings.' . app()->getLocale() .
                                                    '.rec_submitcv.choncongviec') }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.vitricongviec') }}
                                            <span>*</span></label>
                                        <input class="field-item " type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.vitricongviec')) }}"
                                            name="candidate_job_title">
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="item-form">
                                                <label>{{ config('settings.' . app()->getLocale() .
                                                    '.rec_submitcv.mucluongmongmuon') }}
                                                    <span>*</span></label>
                                                <input data-toggle="current_mask"
                                                    data-target="#candidate_salary_expect_2" class="field-item "
                                                    type="text"
                                                    placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.mucluongmongmuon')) }}"
                                                    name="candidate_salary_expect">
                                                <input id="candidate_salary_expect_2" type="hidden" value=""
                                                    name="candidate_salary_expect">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="item-form">
                                                <label>{{ config('settings.' . app()->getLocale() .
                                                    '.rec_submitcv.loaitien') }}</label>
                                                <select style="width: 100%" class="select2-custom select2-style"
                                                    name="candidate_currency">
                                                    {{-- <option value="VND">VND</option> --}}
                                                    {{-- <option value="USD">VND</option> --}}
                                                    @foreach ($currency as $item)
                                                    ;
                                                    <option value="{{ $item }}">{{ $item }}
                                                    </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bottom-form">
                                        <button type="button" data-toggle="back" class="btn-draft2">{{
                                            config('settings.' . app()->getLocale() . '.rec_submitcv.dong') }}</button>
                                        <button type="button" data-toggle="next"
                                            class="btn-save-clone next btn-save2">{{ config('settings.' .
                                            app()->getLocale() . '.rec_submitcv.tieptheo') }}</button>
                                    </div>
                                </div>

                                <div class="form-step " data-step="3">
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.cvpublicdaydu') }}
                                            <span>*</span></label>
                                        <div class="input-file">
                                            <div class="input-group file-browser">
                                                <input type="text" class="file-browser-mask" placeholder="choose"
                                                    readonly="">
                                                <label class="input-group-append">
                                                    <span class="btn-file">
                                                        {{ config('settings.' . app()->getLocale() .
                                                        '.rec_submitcv.browse') }}
                                                        <input type="file" class="file-browserinput"
                                                            style="display: none;" name="cv_public">
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.cvprivatedaydu') }}
                                            <span>*</span></label>
                                        <div class="input-file">
                                            <div class="input-group file-browser">
                                                <input type="text" class="file-browser-mask" placeholder="choose"
                                                    readonly="">
                                                <label class="input-group-append">
                                                    <span class="btn-file">
                                                        {{ config('settings.' . app()->getLocale() .
                                                        '.rec_submitcv.browse') }}
                                                        <input type="file" class="file-browserinput"
                                                            style="display: none;" name="cv_private">
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item-form">
                                        <label>{{ config('settings.' . app()->getLocale() .
                                            '.rec_submitcv.linkportfolio') }}</label>
                                        <input class="field-item " type="text"
                                            placeholder="{{ config('settings.' . app()->getLocale() . '.rec_submitcv.nhap') . ' ' . strtolower(config('settings.' . app()->getLocale() . '.rec_submitcv.linkportfolio')) }}"
                                            name="candidate_portfolio">
                                    </div>

                                    <div class="bottom-form">
                                        <button type="button" data-toggle="back" class="btn-draft3">{{
                                            config('settings.' . app()->getLocale() . '.rec_submitcv.quaylai')
                                            }}</button>
                                        <button type="submit" class="btn-save-clone next btn-save3">{{
                                            config('settings.' . app()->getLocale() . '.rec_submitcv.xacnhan')
                                            }}</button>
                                    </div>
                                </div>
                            </form>

                            <input type="hidden" name="detail-id" class="detail-id">
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<!-- End Modal gioithieuungvien -->

@include('frontend.layouts.modal.modal-introduction')

<!-- Modal candidate cancel confirm -->
<div class="modal fade modal-candidate-cancel" id="modal-candidate-cancel-confirm" tabindex="-1"
    aria-labelledby="modalCandidateCancelLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header bg-danger text-white border-0">
                <h5 class="modal-title fw-bold d-flex align-items-center" id="modalCandidateCancelLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ config('settings.' . app()->getLocale() . '.rec_submitcv.huyungtuyen') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center">
                    <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                        style="width: 80px; height: 80px;">
                        <i class="fas fa-user-times text-danger" style="font-size: 2.5rem;"></i>
                    </div>
                    <h5 class="fw-bold text-dark mb-3">Xác nhận hủy ứng tuyển</h5>
                    <p class="text-muted mb-4 fs-6">
                        Bạn có chắc chắn muốn hủy ứng tuyển cho ứng viên này không?
                    </p>
                    <div class="candidate-name-box mb-4">
                        <i class="fas fa-user me-2"></i>
                        <span class="p-name fw-bold"></span>
                    </div>
                    {{-- <div class="alert alert-warning border-0 bg-warning bg-opacity-15">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-info-circle text-warning me-3 mt-1 fs-5"></i>
                            <div class="text-start">
                                <div class="fw-bold text-warning mb-1">Lưu ý quan trọng</div>
                                <small class="text-muted">Hệ thống sẽ tự động hoàn tiền cho nhà tuyển dụng trong vòng 24
                                    giờ.</small>
                            </div>
                        </div>
                    </div> --}}
                </div>
                <input class="p-id" type="hidden" value="">
            </div>
            <div class="modal-footer border-0 pt-0 px-4 pb-4">
                <div class="d-flex gap-3 w-100">
                    <button type="button" class="btn btn-outline-secondary flex-fill py-2" data-bs-dismiss="modal">
                        <i class="fas fa-arrow-left me-2"></i>
                        {{ config('settings.' . app()->getLocale() . '.rec_submitcv.quaylai') }}
                    </button>
                    <button type="button" class="btn btn-danger flex-fill py-2 button-candidate-cancel-confirm">
                        <i class="fas fa-times me-2"></i>
                        {{ config('settings.' . app()->getLocale() . '.rec_submitcv.xacnhan') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal right fade " id="modal-cv-detail" tabindex="-1" role="dialog" aria-labelledby="left_modal">
    <div class="modal-dialog " role="document">
        <div class="modal-content">
            <div class="close" data-bs-dismiss="modal"></div>

            <div class="row-modal" id="main-modal-cv-detail">

            </div>


        </div>
    </div>
</div>

@endsection
@section('scripts')
<script src="{{ asset2('frontend/asset/js/additional-methods.min.js') }}"></script>
<script src="{{ asset2('frontend/asset/js/introduction.js') }}"></script>
<script>
    $(document).ready(function() {

            $('body').on('click', '[data-toggle="close-site-col"]', function() {
                $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog');
            });

            $('body').on('change', '[data-toggle="submit_private_cv_upload"]', function() {
                let validate = validateSizeFile(this);
                let validateExt = validateExtension(this);
                $('.wapper-iframe .error').html('').hide();
                let flagValidate = true;
                if (validate === false) {
                    flagValidate = false;
                    $('.private_cv_upload-error').html($(this).data('mess-filesize')).show();
                } else if (validateExt === false) {
                    flagValidate = false;
                    $('.private_cv_upload-error').html($(this).data('mess-extension')).show();
                }
                let submit_id = $(this).data('submit-id');
                if (flagValidate) {
                    let fileData = $('[data-toggle="submit_private_cv_upload"]').prop('files')[0];
                    let formData = new FormData();
                    formData.append('submit_id', submit_id);
                    formData.append('private_cv', fileData);
                    $.ajax({
                        url: $(this).data('url'),
                        type: 'POST',
                        data: formData,
                        contentType: false,
                        processData: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(result) {
                            if (result.status === 'success') {
                                // check result.cv_private_link has ".pdf"
                                // $(".wapper-iframe iframe").attr('src','https://docs.google.com/gview?embedded=true&url='+result.cv_private_link);
                                if (result.cv_private_link.includes('.pdf')) {
                                    $("#iframe-private-cv iframe").attr('src', result
                                        .cv_private_link);
                                } else {
                                    $("#iframe-private-cv iframe").attr('src',
                                        "https://view.officeapps.live.com/op/embed.aspx?src=" +
                                        encodeURIComponent(result.cv_private_link));
                                }
                                $("#cv_private_value").val(result.cv_private);
                                $.toast({
                                    title: 'Success',
                                    content: 'Đã cập nhật file CV che thông tin mới',
                                    type: 'success',
                                    delay: 5000
                                });
                            }
                        },
                        beforeSend: function() {
                            // $('#btn_private_cv_re_upload').prop('disabled', true).html('Đang cập nhật...');
                            $('#loading-private-cv-re-upload').show();
                            $('.drop-upload-file').addClass('drop-upload-loading');
                        },
                        complete: function() {
                            // $('#btn_private_cv_re_upload').prop('disabled', false).html('Tải CV che thông tin');
                            $('#loading-private-cv-re-upload').hide();
                            $('.drop-upload-file').removeClass('drop-upload-loading');
                        },
                    });

                }
            });
            $('body').on('click', '[data-toggle="view-detail-cv"]', function() {
                // $(this).parents('.modal-dialog').toggleClass('view-more-cv');

                if ($('#modal-cv-detail .modal-dialog').hasClass('open-col-info')) {
                    $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog');
                } else {
                    $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog').addClass(
                        'open-col-info');
                }
            });
            $('body').on('click', '[data-toggle="view-public-cv"]', function() {
                $('.col-info').toggleClass('show-public-cv');
            });

            $('.detail-cv').click(function() {
                let id = $(this).data('id');
                openCvSubmitDetail(id);
            });

            @if (!empty($_GET['submit_id']))
                openCvSubmitDetail({{ $_GET['submit_id'] }});
            @endif

            function openCvSubmitDetail(id) {
                let url = '{{ route('rec-detail-cv-submit', ':id') }}'
                url = url.replace(':id', id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (!res) {
                            // window.location.href = "{{ route('rec-cv-sold') }}";
                            return false;
                        }
                        $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog');
                        $('#main-modal-cv-detail').html(res);
                        $("#modal-cv-detail").modal('show');
                        @if (!empty($_GET['open_comment']) && $_GET['open_comment'] == 1 && !empty($_GET['submit_id']))
                            $('[data-toggle="view-discuss"][data-id="{{ $_GET['submit_id'] }}"]')
                                .trigger('click');
                        @endif

                        @if (!empty($_GET['open_complain']) && $_GET['open_complain'] == 1 && !empty($_GET['submit_id']))
                            $('[data-toggle="complain"][data-id="{{ $_GET['submit_id'] }}"]').trigger(
                                'click');
                        @endif

                        $('[data-toggle="upload-file"]').click(function() {
                            $('[data-toggle="submit_private_cv_upload"]').trigger('click');
                        });
                    },
                });
            };

            $('body').on('click', '[data-toggle="complain"]', function() {
                if ($('#modal-cv-detail .modal-dialog').hasClass('open-col-site')) {
                    $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog');
                } else {
                    $("#modal-cv-detail .modal-dialog").attr('class', 'modal-dialog').addClass(
                        'open-col-site');
                }
            });

            $('body').on('submit', '#form-discuss', function(e) {
                e.preventDefault();
                $(this).find('[type="submit"]').prop('disabled', true);
                let comment = $(this).find('[name="comment"]').val();
                let submit_id = $(this).find('[name="submit_id"]').val();
                if (comment) {
                    console.log(comment);
                    $.ajax({
                        method: 'POST',
                        url: '{{ route('rec-send-discusses-submit') }}',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        data: {
                            'comment': comment,
                            'submit_id': submit_id,
                        },
                        success: function(res) {
                            if (res.success == true) {
                                $("#form-discuss").find('[name="comment"]').val('');
                                let html = '<div class="item-discuss">' +
                                    '<div class="header"><span>' + res.data.rec.name +
                                    '</span> ' + res.data.date_hour + '</div>' +
                                    '<div class="content">' + res.data.comment + '</div>' +
                                    '</div>';
                                $("#list-discuss p:first-child").remove();
                                $("#list-discuss").append(html);
                                $.toast({
                                    title: 'Success',
                                    content: 'Phản hồi thành công',
                                    type: 'success',
                                    delay: 300
                                });
                                $('#wrap-list-discuss').scrollTop($('#wrap-list-discuss').prop(
                                    'scrollHeight'));
                            } else {
                                $.toast({
                                    title: 'Error',
                                    content: 'Phản hồi Thất bại',
                                    type: 'error',
                                    delay: 300
                                });
                            }

                        },
                        complete: function(data) {
                            $("#form-discuss").find('[type="submit"]').prop('disabled', false);
                        }
                    })


                } else {
                    $(this).find('[type="submit"]').prop('disabled', false);
                }
            });

            $('body').on('click', '[data-toggle="view-discuss"]', function() {
                if ($('#modal-cv-detail .modal-dialog').hasClass('open-col-discus')) {
                    $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog');
                } else {
                    $("#list-discuss").html('');
                    let id = $(this).data('id');
                    let authority = $(this).data('authority');
                    $("#modal-discuss").modal('show');
                    let url = '{{ route('rec-discusses-submit', ':id') }}'
                    url = url.replace(':id', id);
                    $("#submit_id").val(id);
                    $.ajax({
                        method: 'GET',
                        url: url,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(res) {
                            let html = '';
                            if (res.discuss.length === 0 && res.book.length === 0) {
                                let content =
                                    '<p style="color: red; text-align: center">Hiện tại bạn chưa có thảo luận nào</p>'
                                $("#list-discuss").html(content);
                            } else {
                                for (let i = 0; i < res.discuss.length; i++) {
                                    let item = res.discuss[i];
                                    if (item.ntd_id > 0 && item.employer) {
                                        html += '<div class="item-discuss item-discuss-ntd">' +
                                            '<div class="header"><span>Nhà tuyển dụng #' + item
                                            .employer.id +
                                            '</span> ' + item.date_hour + '</div>' +
                                            '<div class="content">' + item.comment + '</div>' +
                                            '</div>';
                                    } else if (item.ctv_id > 0 && item.rec) {
                                        html += '<div class="item-discuss">' +
                                            '<div class="header"><span>Cộng tác viên #' + item
                                            .rec.id +
                                            '</span> ' + item.date_hour + '</div>' +
                                            '<div class="content">' + item.comment + '</div>' +
                                            '</div>';
                                    }
                                }
                                let htmlScheduleInterview = '';
                                if (res.book.length > 0) {
                                    for (let i = 0; i < res.book.length; i++) {
                                        let item = res.book[i];
                                        if (item.ntd_id > 0 && item.employer) {
                                            htmlScheduleInterview +=
                                                '<div class="item-discuss">' +
                                                '<div class="header"><span>' + item.employer
                                                .name + '</span> ' + item.date_hour + '</div>' +
                                                '<div class="content">Nhà tuyển dụng đã đặt lịch phỏng vấn ứng viên <b>(' +
                                                item.name + ')</b><br> lúc <b>' + item
                                                .time_book_format + '</b> ngày <b>' + item
                                                .date_book_format + '</b><br> tại địa chỉ <b>(' + item
                                                .address + ')</b><br>số điện thoại: <b>' + item.phone + '</b></div>';
                                            if (item.status == 0 && authority == 0) {
                                                htmlScheduleInterview +=
                                                    '<form method="post" action="{{ route('rec-book-status-submit') }}">' +
                                                    '<input type="hidden" name="_token" value="{{ csrf_token() }}" />' +
                                                    '<input type="hidden" name="submit_book_id" value="' +
                                                    item.id + '" />' +
                                                    '<div class="footer-comment">' +
                                                    '<button name="status_recruitment" value="{{ config('constant.status_recruitment_revert.CandidateCancelApply') }}" class="btn-st3">Từ chối ứng tuyển</button>' +
                                                    '<button name="status_recruitment" value="{{ config('constant.status_recruitment_revert.RejectInterviewschedule') }}" class="btn-st3">Yêu cầu đổi lịch</button>' +
                                                    '<button name="status_recruitment" value="{{ config('constant.status_recruitment_revert.WaitingInterview') }}" class="btn-st2">Đồng ý lịch</button>' +
                                                    '</div>' +
                                                    '</form>';
                                            }
                                            htmlScheduleInterview += '</div>';
                                        }
                                    }

                                }
                                if (res.onboard.length > 0) {
                                    for (let i = 0; i < res.onboard.length; i++) {
                                        let item = res.onboard[i];
                                        if (item.ntd_id > 0 && item.employer) {
                                            htmlScheduleInterview +=
                                                '<div class="item-discuss">' +
                                                '<div class="header"><span>' + item.employer
                                                .name + '</span> ' + item.date_hour + '</div>' +
                                                '<div class="content">Nhà tuyển dụng đã đặt lịch onboard ứng viên <b>(' +
                                                item.name + ')</b> lúc <b>' + item
                                                .time_book_format + '</b> ngày <b>' + item
                                                .date_book_format + '</b>  tại <b>(' + item
                                                .address + ')</b> </div>';
                                            if (item.status == 0 && authority == 0) {
                                                htmlScheduleInterview +=
                                                    '<form method="post" action="{{ route('rec-onboard-status-submit') }}">' +
                                                    '<input type="hidden" name="_token" value="{{ csrf_token() }}" />' +
                                                    '<input type="hidden" name="submit_onboard_id" value="' +
                                                    item.id + '" />' +
                                                    '<div class="footer-comment">' +
                                                    '<button name="status_recruitment" value="12" class="btn-st3">Từ chối</button>' +
                                                    '<button name="status_recruitment" value="13" class="btn-st2">Xác nhận</button>' +
                                                    '</div>' +
                                                    '</form>';
                                            }
                                            htmlScheduleInterview += '</div>';
                                        }
                                    }
                                }

                                $("#list-discuss").html(html);
                                $("#list-schedule-interview").html(htmlScheduleInterview);

                            }
                            $('#modal-cv-detail .modal-dialog').attr('class', 'modal-dialog')
                                .addClass('open-col-discus');
                        },
                        error: function(xhr, status, error) {

                        },
                    });
                }
            });
            // $('[data-toggle="cancel-confirm"]').click(function (){
            $(document).on('click', '[data-toggle="cancel-confirm"]', function() {
                $("#modal-candidate-introduction-details").modal('hide');
                $("#modal-confirm").modal('show');
                let name = $(this).data('name');
                let id = $(this).data('id');
                $('.p-name').html(name);
                $('.p-id').val(id);
            });

            $(document).on('click', '[data-toggle="agree-confirm"]', function() {
                $("#modal-agree").modal('show');
                let name = $(this).data('name');
                let id = $(this).data('id');
                $('.p-name').html(name);
                $('.p-id').val(id);
            })


            jQuery(function($) {
                $.fn.select2.amd.require([
                    'select2/selection/single',
                    'select2/selection/placeholder',
                    'select2/selection/allowClear',
                    'select2/dropdown',
                    'select2/dropdown/search',
                    'select2/dropdown/attachBody',
                    'select2/utils'
                ], function(SingleSelection, Placeholder, AllowClear, Dropdown, DropdownSearch,
                    AttachBody, Utils) {

                    var SelectionAdapter = Utils.Decorate(
                        SingleSelection,
                        Placeholder
                    );

                    SelectionAdapter = Utils.Decorate(
                        SelectionAdapter,
                        AllowClear
                    );

                    var DropdownAdapter = Utils.Decorate(
                        Utils.Decorate(
                            Dropdown,
                            DropdownSearch
                        ),
                        AttachBody
                    );

                    var base_element = $('#status-select')
                    $(base_element).select2({
                        // containerCssClass: "errosssssssr",
                        dropdownCssClass: "select2-status-dropdown",
                        placeholder: '{{ config('settings.' . app()->getLocale() . '.rec_submitcv.tatcatrangthai') }}',
                        // dropdownAdapter: DropdownAdapter,
                        selectionAdapter: SelectionAdapter,
                        minimumResultsForSearch: -1,
                        allowClear: true,
                        templateResult: function(data) {
                            if (!data.id) {
                                return data.text;
                            }
                            var id = 'state' + data.id;
                            var $res = $('<div class="checkbox select-status-' + data
                                .id + '"><label "><input id="' + id +
                                '" name="status-checkbox" value="' + data.id +
                                '" type="checkbox" ' + (data.selected ? 'checked' :
                                    '') + '>' + data.text + '</label></div>', {
                                    id: id
                                });
                            return $res;

                        },
                        templateSelection: function(data) {
                            // if (!data.id) { return data.text; }
                            // var selected = ($(base_element).val() || []).length;
                            // var total = $('option', $(base_element)).length;
                            return '{{ config('settings.' . app()->getLocale() . '.rec_submitcv.tatcatrangthai') }}';
                        }
                    })
                });
            });


            $('.button-search').click(function() {
                $('#form-search').submit();
            });

            $("input[name='search']").keyup(function(e) {
                if (e.keyCode == 13) {
                    $('#form-search').submit();
                }
            });

            $("#status-select").change(function() {
                var valCheck = $(this).val();
                $("input[name=status]").val(valCheck.toString());
                $('#form-search').submit();
            });

            $('.button-confirm').click(function() {
                let id = $('.p-id').val();
                let url = '{{ route('rec-submitcv-cancel', ':id') }}';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        location.reload();
                    },
                });
            });

            $('.button-confirm1').click(function() {
                let id = $('.p-id').val();
                let url = '{{ route('ajax-update-submit-cv', ':id') }}';
                url = url.replace(':id', id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        location.reload();
                    },
                });
            });

            // Xử lý click nút X - candidate cancel
            $(document).on('click', '[data-toggle="candidate-cancel-confirm"]', function() {
                $("#modal-candidate-cancel-confirm").modal('show');
                let name = $(this).data('name');
                let id = $(this).data('id');
                $('#modal-candidate-cancel-confirm .p-name').html(name);
                $('#modal-candidate-cancel-confirm .p-id').val(id);
            });

            // Xử lý xác nhận candidate cancel
            $('.button-candidate-cancel-confirm').click(function() {
                let id = $('#modal-candidate-cancel-confirm .p-id').val();
                let url = '{{ route('rec-candidate-cancel-submit', ':id') }}';
                url = url.replace(':id', id);
                
                $(this).prop('disabled', true).text('Đang xử lý...');
                
                $.ajax({
                    method: 'POST',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (res.success) {
                            $('#modal-candidate-cancel-confirm').modal('hide');
                            location.reload();
                        } else {
                            alert(res.message || 'Có lỗi xảy ra, vui lòng thử lại');
                        }
                    },
                    error: function(xhr) {
                        alert('Có lỗi xảy ra, vui lòng thử lại');
                        console.error(xhr);
                    },
                    complete: function() {
                        $('.button-candidate-cancel-confirm').prop('disabled', false).text('{{ config('settings.' . app()->getLocale() . '.rec_submitcv.xacnhan') }}');
                    }
                });
            });

            $('.detail-submitcv').click(function() {
                let id = $(this).data('id');
                let url = '{{ route('rec-submitcv-detail', ':id') }}';
                url = url.replace(':id', id);
                $('.detail-id').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#detail-cv').html(res);
                        $("#modal-candidate-introduction-details").modal('show');
                    },
                });
            });

            $(document).on('click', '.show-iframe', function() {
                let url = $(this).data('url');
                $(".iframe").attr('src', url);
                $("#modal-candidate-introduction-details").modal('hide');
                $("#modal-cv").modal('show');

                let detailId = $('.detail-id').val();
            });

            $(document).on('click', '.back-header', function() {
                let id = $('.detail-id').val();
                let url = '{{ route('rec-submitcv-detail', ':id') }}';
                url = url.replace(':id', id);
                $('.detail-id').val(id);
                $.ajax({
                    method: 'GET',
                    url: url,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        $('#detail-cv').html(res);
                        $("#modal-cv").modal('hide');
                        $("#modal-candidate-introduction-details").modal('show');
                    },
                });
            })

            $(document).on('click', '[data-toggle="open-popup"]', function() {
                $("#modal-introduction-v2").modal('show');


                {{-- let type = $(this).data('type'); --}}
                {{-- if (type == 0) { --}}
                {{--    $('.item-header-tab').removeClass('active'); --}}
                {{--    $('.from-storage').addClass('active') --}}

                {{--    $('.tab-pane-form').removeClass('show active') --}}
                {{--    $('#tab-introduction-from-storage').addClass('show active') --}}
                {{-- } else { --}}
                {{--    $('.item-header-tab').removeClass('active'); --}}
                {{--    $('.new').addClass('active') --}}

                {{--    $('.tab-pane-form').removeClass('show active') --}}
                {{--    $('#tab-introduction-new').addClass('show active') --}}
                {{-- } --}}

                {{-- var urlCv = '{{route('get-cv-by-user-submit')}}'; --}}
                {{-- $('.select2-modal').select2({ --}}
                {{--    dropdownParent: "#modal-introduction", --}}
                {{--    ajax: { --}}
                {{--        url: urlCv, --}}
                {{--        type: "get", --}}
                {{--        dataType: 'json', --}}
                {{--        delay: 550, --}}
                {{--        headers: { --}}
                {{--            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') --}}
                {{--        }, --}}
                {{--        data: function (params) { --}}
                {{--            return { --}}
                {{--                searchTerm: params.term // search term --}}
                {{--            }; --}}
                {{--        }, --}}
                {{--        processResults: function (response) { --}}
                {{--            return { --}}
                {{--                results: response --}}
                {{--            }; --}}
                {{--        }, --}}
                {{--        cache: true --}}
                {{--    } --}}
                {{-- }); --}}

                {{-- var urlListJob = '{{route('ajax-list-job')}}'; --}}
                {{-- $('.list-job').select2({ --}}
                {{--    dropdownParent: "#modal-introduction", --}}
                {{--    ajax: { --}}
                {{--        url: urlListJob, --}}
                {{--        type: "get", --}}
                {{--        dataType: 'json', --}}
                {{--        delay: 550, --}}
                {{--        headers: { --}}
                {{--            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') --}}
                {{--        }, --}}
                {{--        data: function (params) { --}}
                {{--            return { --}}
                {{--                searchTerm: params.term // search term --}}
                {{--            }; --}}
                {{--        }, --}}
                {{--        processResults: function (response) { --}}
                {{--            return { --}}
                {{--                results: response --}}
                {{--            }; --}}
                {{--        }, --}}
                {{--        cache: true --}}
                {{--    } --}}
                {{-- }); --}}

                {{-- $("#modal-introduction").modal({backdrop: 'static', keyboard: false}); --}}
                {{-- $("#modal-introduction").modal('show'); --}}
                {{-- $("#chontukho").validate().resetForm(); --}}
                {{-- $("#themmoi_cv").validate().resetForm(); --}}
            });

            $(document).on('click', '[data-toggle="next"]', function() {
                if (v.form()) {
                    let form = $(this).parents('.form-step');
                    let index = form.data('step');
                    form.removeClass('active');
                    let nextStep = parseInt(index) + 1
                    $('.form-step[data-step="' + nextStep + '"]').addClass('active');

                    let x = nextStep;
                    $('.step-item').removeClass('active');
                    while ($('.step-item[data-step="' + x + '"]').length > 0) {
                        $('.step-item[data-step="' + x + '"]').addClass('active');
                        x--;
                    }

                    $(document).on('click', '.btn-save3', function() {
                        $("#themmoi_cv").validate().settings.ignore = "";
                        $('<input>').attr({
                            type: 'hidden',
                            name: 'status',
                            value: 'pending-review'
                        }).appendTo($('#themmoi_cv'));
                        if (v.form()) {
                            $('#themmoi_cv').submit();
                        }
                    })

                }
            })
            $('[data-toggle="back"]').click(function() {
                let form = $(this).parents('.form-step');
                let index = form.data('step');
                form.removeClass('active');
                let nextStep = parseInt(index) - 1
                $('.form-step[data-step="' + nextStep + '"]').addClass('active');
                $("#themmoi_cv").validate().settings.ignore = ":hidden";
                let x = nextStep;
                $('.step-item').removeClass('active');
                while ($('.step-item[data-step="' + x + '"]').length > 0) {
                    $('.step-item[data-step="' + x + '"]').addClass('active');
                    x--;
                }
            });

            $(document).on('click', '.btn-save', function() {
                $('<input>').attr({
                    type: 'hidden',
                    name: 'status',
                    value: 'pending-review'
                }).appendTo($('#chontukho'));
                if ($("#chontukho").valid()) {
                    $('#chontukho').submit();
                }
            });

            $(document).on('click', '.btn-draft', function() {
                $('<input>').attr({
                    type: 'hidden',
                    name: 'status',
                    value: 'draft'
                }).appendTo($('#chontukho'));
                if ($("#chontukho").valid()) {
                    $('#chontukho').submit();
                }
            });

            $("#chontukho").validate({
                rules: {
                    ware_house_cv: {
                        required: true,
                        check_warehouse_cv: true,
                        check_duplicate_submit_by_cv: true
                    },
                    expected_date: {
                        required: true,
                    },
                    candidate_salary_expect: {
                        required: true,
                        // number: true,
                        custom_number: true
                    },
                    job: {
                        required: true,
                    }
                },
                messages: {
                    ware_house_cv: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expected_date: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_salary_expect: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                        custom_number: '{{ __('frontend/validation.number') }}',
                    },
                    job: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                },

                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else {
                        elem.addClass(errorClass);
                    }
                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        console.log(errorClass);
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                }
            });

            var v = $("#themmoi_cv").validate({
                rules: {
                    candidate_name: {
                        required: true,
                    },
                    expected_date: {
                        required: true
                    },
                    candidate_mobile: {
                        required: true,
                        minlength: 10,
                        maxlength: 16,
                        regex: true,
                    },
                    candidate_email: {
                        required: true,
                        email: true,
                        remote: {
                            url: "{{ route('ajax-check-email-candidate') }}",
                            type: "post",
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            data: {
                                email: function() {
                                    return $('input[name="candidate_email"]').val();
                                }
                            },
                            dataFilter: function(data) {
                                var json = JSON.parse(data);
                                if (json.success === "true" || json.success === true) {
                                    let message =
                                        '{{ __('frontend/validation.check_candidate_email') }}';
                                    let user = json.email;
                                    let status = json.status;

                                    let message1 = message.replace(':user', user);
                                    let message2 = message1.replace(':status', status);
                                    return '"' + message2 + '"';
                                } else {
                                    return '"true"';
                                }
                            }
                        }
                    },
                    candidate_job_title: {
                        required: true
                    },
                    candidate_salary_expect: {
                        required: true,
                        // number: true,
                        custom_number: true,
                    },
                    job: {
                        required: true,
                        check_duplicate_submit_by_info: true,
                    },
                    cv_public: {
                        required: true,
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    cv_private: {
                        required: true,
                        extension: "pdf,docx",
                        filesize: 5, // <- 5 MB
                    },
                    // candidate_portfolio: {
                    //     required: true
                    // },
                },
                messages: {
                    candidate_name: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    expected_date: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_mobile: {
                        required: '{{ __('frontend/validation.required') }}',
                        minlength: '{{ __('frontend/validation.min', ['min' => 10]) }}',
                        maxlength: '{{ __('frontend/validation.max', ['max' => 16]) }}',
                        regex: '{{ __('frontend/validation.regex_phone') }}',
                    },
                    candidate_email: {
                        required: '{{ __('frontend/validation.required') }}',
                        email: '{{ __('frontend/validation.email') }}',
                    },
                    candidate_job_title: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    job: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                    candidate_salary_expect: {
                        required: '{{ __('frontend/validation.required') }}',
                        number: '{{ __('frontend/validation.number') }}',
                        custom_number: '{{ __('frontend/validation.number') }}',
                    },
                    cv_public: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    cv_private: {
                        required: '{{ __('frontend/validation.required') }}',
                        extension: '{{ __('frontend/validation.format_cv') }}',
                        filesize: '{{ __('frontend/validation.file_max') }}',
                    },
                    candidate_portfolio: {
                        required: '{{ __('frontend/validation.required') }}',
                    },
                },
                highlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        elem.next().parent().addClass(errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').addClass(errorClass)
                    } else {
                        elem.addClass(errorClass);
                    }


                },
                unhighlight: function(element, errorClass, validClass) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        $("#select2-" + elem.attr("id") + "-container").parent().removeClass(
                            errorClass);
                    } else if (elem.hasClass("file-browserinput")) {
                        elem.parents('.file-browser').find('.file-browser-mask').removeClass(errorClass)
                    } else {
                        elem.removeClass(errorClass);
                    }
                },
                errorPlacement: function(error, element) {
                    var elem = $(element);
                    if (elem.hasClass("select2-hidden-accessible")) {
                        element = elem.next();
                        error.insertAfter(element);
                    } else if (elem.hasClass("file-browserinput")) {
                        element = elem.parents('.file-browser').find('.file-browser-mask');
                        error.insertAfter(element);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    if (!this.beenSubmitted) {
                        this.beenSubmitted = true;
                        form.submit();
                    }
                }
            });

            $.validator.addMethod('regex', function(value) {
                var regex = /^[0-9()+.-]*$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod("custom_number", function(value, element) {
                var regex = /^-?(?:\d+|\d{1,3}(?:[\s\.,]\d{3})+)(?:[\.,]\d+)?$/;
                return value.trim().match(regex);
            });

            $.validator.addMethod('filesize', function(value, element, param) {
                return this.optional(element) || (element.files[0].size <= param * 1000000)
            }, 'File size must be less than {0} MB');

            $.validator.addMethod('check_warehouse_cv', function(value, element) {
                var isSuccess = false;
                var user = '';
                var status = '';
                $.ajax({
                    url: '{{ route('ajax-check-candidate') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        cv: value
                    },
                    success: function(data) {
                        isSuccess = data.success;
                        user = data.email;
                        status = data.status;
                    }
                });


                if (isSuccess == true) {
                    let message = '{{ __('frontend/validation.check_candidate_email') }}';

                    let message1 = message.replace(':user', user);
                    let message2 = message1.replace(':status', status);

                    $.validator.messages.check_warehouse_cv = message2;
                    return false;
                } else {
                    return true;
                }
            });

            $.validator.addMethod('check_duplicate_submit_by_cv', function(value, element) {
                var isSuccess = false;
                $.ajax({
                    url: '{{ route('ajax-check-duplicate-submit-by-cv') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        cv: value,
                        job_id: $("#job_chontukho").val(),
                    },
                    success: function(data) {
                        isSuccess = data.success;
                    }
                });

                if (isSuccess == true) {
                    let message = '{{ __('frontend/job/message.error_duplicate_job') }}';
                    $.validator.messages.check_duplicate_submit_by_cv = message;
                    return false;
                } else {
                    return true;
                }
            });

            $.validator.addMethod('check_duplicate_submit_by_info', function(value, element) {
                var isSuccess = false;
                $.ajax({
                    url: '{{ route('check-duplicate-submit-by-new-info') }}',
                    type: 'POST',
                    async: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        email: $("input[name='candidate_email']").val(),
                        phone: $("input[name='candidate_mobile']").val(),
                        job_id: $("#job_themmoi_cv").val(),
                    },
                    success: function(data) {
                        isSuccess = data.success;
                    }
                });

                if (isSuccess == true) {
                    let message = '{{ __('frontend/job/message.error_duplicate_job') }}';
                    $.validator.messages.check_duplicate_submit_by_info = message;
                    return false;
                } else {
                    return true;
                }
            });

        })
</script>
@endsection