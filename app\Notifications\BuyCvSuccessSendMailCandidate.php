<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class BuyCvSuccessSendMailCandidate extends Mailable
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $token;
    protected $job;

    public function __construct($wareHouseCvSellingBuy, $token, $job)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->token = $token;
        $this->job = $job;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $nameCTV = $this->wareHouseCvSellingBuy->wareHouseCvSelling->user->name; //name ctv
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $jobName = $this->job->name;
        // $link = $this->job->path_file_jd;
        $link = route('download.pdf', ['slug' => $this->job->slug]);
        $linkAccept = route('verify-email-candidate') . '?token=' . $this->token . '&type=1';
        $linkReject = route('verify-email-candidate') . '?token=' . $this->token . '&type=2';

        return new Content(
            view: 'email.mua_cv_thanhcong_ungvien',
            with: [
                'candidateName' => $candidateName,
                'nameCTV' => $nameCTV,
                'position' => $position,
                'companyName'  => $companyName,
                'link' => $link,
                'linkAccept' => $linkAccept,
                'linkReject' => $linkReject,
                'jobName' => $jobName,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $message->subject('[Recland] Xác nhận ứng tuyển vị trí '. $position .' tại công ty ' . $companyName);
        return $this;
    }

}
