<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerComplainAdminSubmit extends Mailable
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $submitCv;

    public function __construct($rec,$employer,$submitCv)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->submitCv = $submitCv;
    }

    public function content(): Content
    {
        $candidateName = '';
        $position      = '';
        $type          = '';
        $content       = $this->submitCv->txt_complain;
        $image         = $this->submitCv->img_complain;
        $companyName   = $this->employer->name;
        if (!empty($this->submitCv)){
            $type = $this->submitCv->bonus_type;
        }
        if (!empty($this->submitCv->warehouseCv)){
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->submitCv->warehouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
                $position = $this->submitCv->job->name;
            }
        }
        // $url = route('luot-ban.show',['luot_ban' => $this->submitCv->id,'tab' => 'khieunai']);
        $url = route('submit-cv.edit', ['id' => $this->submitCv->id, 'tab' => 'khieunai']);

        return new Content(
            view: 'email.employerComplainAdminSubmit',
            with: [
                'name'          => $this->rec->name,
                'companyName'   => $companyName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'type'          => $type,
                'content'       => $content,
                'image'         => gen_url_file_s3($image),
                'url'           => $url,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $companyName = $this->employer->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $message->subject('[Uỷ Quyền][Recland][Case #'.$this->submitCv->id.'] Thông báo khiếu nại của công ty '.$companyName.' đối với Ứng viên '.$candidateName);
        return $this;
    }

}
