# SRS - F01.2: <PERSON><PERSON> Interview từ Marketplace

**Phiên bản:** 6.0
**Ngày:** 2025-07-08
**Tác giả:** Gemini

---

### 1. <PERSON><PERSON> tả

Sau khi NTD đã mua CV Data, họ có thể trả thêm phí (bằng **Point**) để tiến hành mời ứng viên phỏng vấn. Phí sẽ được trừ ngay lập tức từ NTD. Sau một khoảng thời gian và nếu không có khiếu nại, hoa hồng sẽ được trả cho CTV. Luồng này được điều phối bởi `WareHouseCvSellingBuyService` và các Job bất đồng bộ (`PayInterview`, `SendemailBook`, `CandidateCancelInterview`, `DepositRefundRejectOffer`, `OutOfDateBookInterview`).

### 2. <PERSON><PERSON><PERSON> tượng tham gia

*   **<PERSON>hà tuyển dụng (NTD):** <PERSON><PERSON>ời khởi tạo và trả phí.
*   **Ứng viên (UV):** Người nhận lời mời.
*   **Cộng tác viên (CTV):** Người nhận hoa hồng.
*   **Hệ thống:** Tự động hóa quy trình.

### 3. Điều kiện tiên quyết

*   Tồn tại một bản ghi `warehouse_cv_selling_buys` xác nhận NTD đã mua CV Data.
*   Trạng thái tuyển dụng (`status_recruitment`) của bản ghi trên đang ở giai đoạn phù hợp (ví dụ: 0 - Mới mua, 2 - Đã xem).
*   Ví của NTD có đủ số dư Point cho phí "Interview".

### 4. Luồng sự kiện chính (Happy Path)

1.  **Bước 1 (HTTP Request):** NTD nhấn nút "Mời phỏng vấn". Frontend gửi request đến một phương thức trong `WareHouseCvSellingBuyService` (ví dụ: `updateScheduleInterview`), kèm theo ID của `warehouse_cv_selling_buys` và trạng thái `7` (Chờ phỏng vấn).

2.  **Bước 2 (Service xử lý logic):** Toàn bộ logic được bao bọc trong một **Database Transaction**.
    *   **a. Lấy dữ liệu & Kiểm tra:** Service lấy các đối tượng `WareHouseCvSellingBuyBook` (lịch đặt phỏng vấn), `WareHouseCvSellingBuy` và `Wallet` của NTD. Kiểm tra `wareHouseCvSellingBuyBook->status` phải là `0` (vừa đặt) để tránh xử lý trùng lặp.
    *   **b. Cập nhật trạng thái đặt lịch:** `wareHouseCvSellingBuyBook->status` được cập nhật thành `1` (đã được xác nhận).
    *   **c. Cập nhật trạng thái tuyển dụng:** `wareHouseCvSellingBuy->status_recruitment` được cập nhật thành `7` (Chờ phỏng vấn).
    *   **d. Ghi lịch sử:** Service ghi lại sự kiện thay đổi trạng thái này vào `ware_house_cv_selling_buy_history_status`.

3.  **Bước 3 (Commit & Phản hồi):** DB Transaction được commit. Service trả về kết quả thành công cho Controller. Controller trả về HTTP `200 OK` cho frontend.

4.  **Bước 4 (Gửi thông báo & Lên lịch trả hoa hồng):**
    *   **Notification `EmailConfirmInterView` (gửi cho NTD):** Thông báo xác nhận lịch phỏng vấn.
    *   **Job `PassInterview`:** Nếu `type_of_sale` là 'interview', job `PassInterview` được dispatch với độ trễ được tính từ thời gian phỏng vấn (`wareHouseCvSellingBuyBook->date_book`) cộng thêm 7 ngày. Job này sẽ tự động chuyển trạng thái sang "Pass Interview" nếu NTD không cập nhật trạng thái sau thời gian này.

### 5. Luồng sự kiện phụ & Xử lý lỗi (Sad Path)

*   **5.1. Lỗi lịch phỏng vấn đã được xử lý:**
    *   **Nguyên nhân:** Tại **Bước 2a**, `wareHouseCvSellingBuyBook->status` không phải là `0`.
    *   **Xử lý:** Service ném ra một Exception. Controller bắt được và trả về thông báo lỗi "Lịch phỏng vấn đã được xử lý."

*   **5.2. Ứng viên từ chối / không phản hồi (NTD cập nhật trạng thái):**
    *   **Nguyên nhân:** NTD cập nhật trạng thái thành `5` (Từ chối lịch phỏng vấn).
    *   **Xử lý:**
        *   `wareHouseCvSellingBuyBook->status` được cập nhật thành `2` (bị từ chối).
        *   NTD nhận được thông báo (`EmailRejectInterView`).
        *   Hệ thống đếm số lần từ chối (`countBookReject`).
        *   **Nếu số lần từ chối >= 3:**
            *   `wareHouseCvSellingBuy->status_recruitment` được cập nhật thành `9` (Candidate Cancel Interview).
            *   **Hoàn tiền:**
                *   Nếu `type_of_sale` là 'interview', 100% phí được hoàn lại vào ví NTD. `wareHouseCvSellingBuy->status_payment` được đặt thành `3` (Hoàn tiền).
                *   Nếu `type_of_sale` là 'onboard', tiền cọc được hoàn lại vào ví NTD. `wareHouseCvSellingBuy->status_payment` được đặt thành `2` (Hoàn cọc).
            *   Ghi log hoàn tiền vào `WareHouseCvSellingHistoryBuy`.
            *   Thông báo được gửi đến NTD, Admin (nếu ủy quyền) và CTV.
        *   **Nếu số lần từ chối < 3:**
            *   `wareHouseCvSellingBuy->status_recruitment` được cập nhật thành `5` (Reject Interview schedule).
            *   Job `OutOfDateBookInterview` được dispatch với độ trễ 7 ngày, để tự động hủy nếu không có hành động nào khác.
        *   Lịch sử trạng thái được ghi lại.

*   **5.3. NTD khiếu nại:**
    *   **Nguyên nhân:** NTD không hài lòng với chất lượng CV và tạo khiếu nại (`status_complain` = 1).
    *   **Xử lý:** Job `PayInterview` sẽ kiểm tra thấy `status_complain` khác 0 và sẽ không thực hiện việc trả hoa hồng cho CTV cho đến khi khiếu nại được Admin giải quyết.

*   **5.4. Lời mời hết hạn (Job `OutOfDateBookInterview`):**
    *   **Mô tả:** Xử lý các lời mời phỏng vấn đã hết hạn mà không có phản hồi.
    *   **Khi nào được dispatch:** Thường được chạy theo lịch định kỳ (ví dụ: hàng ngày) để quét các bản ghi `warehouse_cv_selling_buys` hoặc được dispatch bởi `updateScheduleInterview` khi số lần từ chối < 3.
    *   **Happy Path:** Job nhận các tham số `wareHouseCvSellingBuy` và `user`. Nếu `warehouse_cv_selling_buy->status_recruitment` là 5 (Trạng thái nội bộ cho lời mời hết hạn), job sẽ cập nhật `status_recruitment` thành 6 (Hủy phỏng vấn), ghi log lịch sử trạng thái (với thông tin `user`), và gửi thông báo cho NTD, Admin, và CTV.
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không phải là 5, job sẽ bỏ qua bản ghi đó.

*   **5.5. Ứng viên từ chối lời mời qua email (`verifyEmailCandidate`):**
    *   **Mô tả:** Ứng viên từ chối lời mời phỏng vấn thông qua liên kết trong email.
    *   **Khi nào xảy ra:** Khi ứng viên click vào link từ chối trong email.
    *   **Xử lý:**
        *   `wareHouseCvSellingBuy->status_recruitment` được cập nhật thành `2` (Candidate Cancel Apply).
        *   `wareHouseCvSellingBuy->status_payment` được đặt thành `3` (Hoàn tiền).
        *   Ghi log hoàn tiền vào `WareHouseCvSellingHistoryBuy`.
        *   Tiền được hoàn lại vào ví NTD.
        *   Thông báo được gửi đến CTV, NTD và Admin.
        *   Lịch sử trạng thái được ghi lại.

### 6. Yêu cầu phi chức năng

*   **Tính nhất quán:** Việc kiểm tra lại trạng thái và khiếu nại bên trong các Job (`PayInterview`, `SendemailBook`) trước khi thực hiện hành động là bắt buộc để đảm bảo tính nhất quán của hệ thống.
*   **Độ tin cậy:** Các job liên quan đến tài chính (`PayInterview`, `DepositRefundRejectOffer`) phải được giám sát chặt chẽ để đảm bảo chúng được thực thi thành công hoặc được xử lý lại nếu có lỗi.

### 7. Mô hình dữ liệu liên quan

*   **Models:** `WarehouseCvSellingBuy`, `WarehouseCvSellingBuyHistoryStatus`, `Wallet`, `WalletTransaction`, `WareHouseCvSellingHistoryBuy`, `WareHouseCvSellingBuyBook`.
*   **Repositories:** `WareHouseCvSellingBuyRepository`, `WalletRepository`, `WareHouseCvSellingBuyHistoryStatusRepository`, `WareHouseCvSellingHistoryBuyRepository`, `WareHouseCvSellingBuyBookRepository`.
*   **Services:** `WareHouseCvSellingBuyService`, `WalletService`.
*   **Controller:** `WareHouseCvSellingBuyController` (Frontend).

### 8. Các Job liên quan (Background Processes)

*   **`SendemailBook`**
    *   **Mô tả:** Gửi email nhắc nhở lịch phỏng vấn cho ứng viên và các bên liên quan.
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành **7 (Chờ phỏng vấn)**.
    *   **Happy Path:** Nếu `warehouse_cv_selling_buy->status_recruitment` là 7, job sẽ gửi `RemindScheduleInterview` notification đến `user` (ứng viên).
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không phải là 7, job sẽ không gửi email, tránh gửi thông báo không cần thiết hoặc sai lệch.

*   **`PayInterview`**
    *   **Mô tả:** Xử lý việc thanh toán hoa hồng phỏng vấn cho CTV.
    *   **Khi nào được dispatch:** Với một độ trễ sau khi NTD mời phỏng vấn và UV đồng ý (trạng thái 8 hoặc 10).
    *   **Happy Path:** Nếu `warehouse_cv_selling_buy->status_recruitment` là 8 (Đồng ý phỏng vấn) hoặc 10 (Pass phỏng vấn) VÀ `status_complain` là 0 (Không khiếu nại) hoặc 5 (Admin từ chối khiếu nại), job sẽ gọi `WareHouseCvSellingBuyService->payCtv()` để cộng tiền vào ví CTV và ghi log giao dịch.
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không hợp lệ hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thanh toán. Lỗi trong quá trình thanh toán (ví dụ: lỗi CSDL) sẽ khiến job thất bại và có thể được thử lại.

*   **`CandidateCancelInterview`**
    *   **Mô tả:** Xử lý khi NTD hủy phỏng vấn hoặc lịch phỏng vấn hết hạn. Job này sẽ cập nhật trạng thái và gửi thông báo cho các bên liên quan.
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành **6 (Hủy phỏng vấn)** hoặc khi ứng viên từ chối lời mời phỏng vấn qua email.
    *   **Happy Path:** Job nhận các tham số `wareHouseCvSellingBuyId` và `user`. Nếu `warehouse_cv_selling_buy->status_recruitment` là 3 (Chờ book lịch phỏng vấn), job sẽ:
        *   Cập nhật `status_recruitment` thành 6 (Hủy phỏng vấn).
        *   Ghi log lịch sử trạng thái vào `warehouse_cv_selling_buy_history_status`.
        *   Gửi thông báo cho NTD (`ChangeStatusRecruiterCancelInterviewToEmployer`).
        *   Nếu CV có `authority > 0`, gửi thông báo cho Admin (`ChangeStatusRecruiterCancelInterviewToAdmin`).
        *   Nếu CV không có `authority > 0`, gửi thông báo cho CTV (`ChangeStatusRecruiterCancelInterviewToRec`).
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không phải là 3, job sẽ không thực hiện thay đổi trạng thái hoặc gửi thông báo.

*   **`DepositRefundRejectOffer`**
    *   **Mô tả:** Hoàn lại Point cho NTD khi một giao dịch bị hủy hoặc từ chối (ví dụ: ứng viên hủy phỏng vấn, NTD từ chối ứng viên).
    *   **Khi nào được dispatch:** Khi một sự kiện hủy/từ chối xảy ra sau khi NTD đã trả phí (ví dụ: NTD cập nhật trạng thái `warehouse_cv_selling_buys` thành 6 hoặc 9, hoặc ứng viên từ chối lời mời qua email).
    *   **Happy Path:** Job tìm các bản ghi thanh toán trước đó (`WareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus`), đánh dấu chúng là đã hoàn tiền, tạo các bản ghi log hoàn tiền mới, cập nhật `status_payment` của `warehouse_cv_selling_buys` thành 2 (Hoàn cọc) hoặc 3 (Hoàn tiền), và cộng Point vào ví của NTD thông qua `WalletService`.
    *   **Sad Path / Xử lý lỗi:** Nếu không tìm thấy bản ghi thanh toán nào để hoàn tiền hoặc nếu đã được hoàn tiền trước đó, job sẽ không thực hiện hành động hoàn tiền. Lỗi trong quá trình hoàn tiền sẽ khiến job thất bại và có thể được thử lại.

*   **`OutOfDateBookInterview`**
    *   **Mô tả:** Xử lý các lời mời phỏng vấn đã hết hạn mà không có phản hồi.
    *   **Khi nào được dispatch:** Thường được chạy theo lịch định kỳ (ví dụ: hàng ngày) để quét các bản ghi `warehouse_cv_selling_buys` hoặc được dispatch bởi `updateScheduleInterview` khi số lần từ chối < 3.
    *   **Happy Path:** Job nhận các tham số `wareHouseCvSellingBuy` và `user`. Nếu `warehouse_cv_selling_buy->status_recruitment` là 5 (Trạng thái nội bộ cho lời mời hết hạn), job sẽ cập nhật `status_recruitment` thành 6 (Hủy phỏng vấn), ghi log lịch sử trạng thái (với thông tin `user`), và gửi thông báo cho NTD, Admin, và CTV.
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không phải là 5, job sẽ bỏ qua bản ghi đó.

*   **`PassInterview`**
    *   **Mô tả:** Xử lý khi ứng viên "Đạt phỏng vấn".
    *   **Khi nào được dispatch:** Khi NTD cập nhật trạng thái ứng viên thành "Đạt phỏng vấn" (status `8`), hoặc tự động sau 7 ngày kể từ thời gian phỏng vấn nếu NTD không cập nhật trạng thái.
    *   **Happy Path:** Job nhận các tham số `wareHouseCvSellingBuyId` và `user`. Nếu `warehouse_cv_selling_buy->status_recruitment` là 7 ("Chờ phỏng vấn") và không có khiếu nại hoặc khiếu nại đã được giải quyết, job sẽ cập nhật `status_recruitment` thành 8 ("Đạt phỏng vấn"), ghi log lịch sử trạng thái (với thông tin `user`), và gửi thông báo cho Admin (nếu ủy quyền) hoặc CTV. Nếu loại hình dịch vụ là "Interview", job `PayInterview` sẽ được kích hoạt và trì hoãn 24 giờ để thanh toán hoa hồng cho CTV. Nếu là "Onboard", trạng thái sẽ chuyển sang `11` ("Offering").
    *   **Sad Path / Xử lý lỗi:** Nếu `status_recruitment` không hợp lệ hoặc có khiếu nại đang chờ xử lý, job sẽ không thực hiện thay đổi trạng thái hoặc gửi thông báo).

*   **`RejectBookExpire`**
    *   **Mô tả:** Xử lý khi lịch phỏng vấn đã hết hạn hoặc bị từ chối.
    *   **Khi nào được dispatch:** Khi một lịch phỏng vấn được tạo nhưng không có hành động nào được thực hiện trong một khoảng thời gian nhất định, hoặc khi NTD từ chối lịch phỏng vấn.
    *   **Happy Path:** Job nhận các tham số `wareHouseCvSellingBuyBook` và `user`. Nếu `wareHouseCvSellingBuyBook->status` là 0 (vừa đặt), job sẽ:
        *   Cập nhật `wareHouseCvSellingBuyBook->status` thành 2 (bị từ chối).
        *   Gửi thông báo cho NTD (`EmailRejectInterView`).
        *   Đếm số lần từ chối (`countBookReject`).
        *   **Nếu số lần từ chối >= 3:**
            *   Cập nhật `wareHouseCvSellingBuy->status_recruitment` thành 9 (Candidate Cancel Interview).
            *   **Hoàn tiền:**
                *   Nếu `type_of_sale` là 'interview', 100% phí được hoàn lại vào ví NTD. `wareHouseCvSellingBuy->status_payment` được đặt thành 3 (Hoàn tiền).
                *   Nếu `type_of_sale` là 'onboard', tiền cọc được hoàn lại vào ví NTD. `wareHouseCvSellingBuy->status_payment` được đặt thành 2 (Hoàn cọc).
            *   Ghi log hoàn tiền vào `WareHouseCvSellingHistoryBuy`.
            *   Cộng Point vào ví của NTD (`employer->wallet->addAmount`) và ghi audit tag.
            *   Thông báo được gửi đến NTD, Admin (nếu `authority == 2`) và CTV.
        *   **Nếu số lần từ chối < 3:**
            *   Cập nhật `wareHouseCvSellingBuy->status_recruitment` thành 5 (Reject Interview schedule).
            *   Dispatch job `OutOfDateBookInterview` với độ trễ 7 ngày.
        *   Ghi log lịch sử trạng thái vào `ware_house_cv_selling_buy_history_status`.
    *   **Sad Path / Xử lý lỗi:** Nếu `wareHouseCvSellingBuyBook->status` không phải là 0, job sẽ không thực hiện hành động nào.

### 9. Các thông báo liên quan

*   **`CandidateConfirmRecruitment` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên đã đồng ý lời mời phỏng vấn.
    *   **Nội dung chính:** Tên ứng viên, tên công ty, vị trí, link đến trang quản lý CV đã mua.
    *   **Kênh:** Mail, Database.
*   **`CandidateRejectRecruitmentAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo ứng viên từ chối lời mời phỏng vấn (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên ứng viên, tên công ty, vị trí, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`CandidateRejectRecruitmentEmployer` (gửi cho NTD):**
    *   **Mô tả:** Thông báo ứng viên từ chối lời mời phỏng vấn.
    *   **Nội dung chính:** Tên ứng viên, vị trí, link đến ví và marketplace.
    *   **Kênh:** Mail, Database.
*   **`CandidateRejectRecruitmentRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo ứng viên do CTV giới thiệu đã từ chối lời mời phỏng vấn.
    *   **Nội dung chính:** Tên CTV, tên ứng viên, tên công ty, vị trí, link đến trang CV đã bán.
    *   **Kênh:** Mail, Database.
*   **`ChangeStatusRecruiterCancelInterviewToAdmin` (gửi cho Admin):**
    *   **Mô tả:** Thông báo NTD hủy phỏng vấn hoặc lịch phỏng vấn hết hạn (áp dụng cho CV ủy quyền).
    *   **Nội dung chính:** Tên CTV, tên ứng viên, công ty NTD, vị trí, link đến trang quản lý của Admin.
    *   **Kênh:** Mail.
*   **`ChangeStatusRecruiterCancelInterviewToEmployer` (gửi cho NTD):**
    *   **Mô tả:** Thông báo lịch phỏng vấn đã hết hạn hoặc bị hủy.
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, công ty, link đến trang CV đã mua và marketplace.
    *   **Kênh:** Mail.
*   **`ChangeStatusRecruiterCancelInterviewToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo lịch phỏng vấn đã hết hạn hoặc bị hủy cho ứng viên do CTV giới thiệu.
    *   **Nội dung chính:** Tên CTV, tên NTD, tên ứng viên, vị trí, công ty, link đến trang CV đã bán.
    *   **Kênh:** Mail.
*   **`PaymentInterviewToRec` (gửi cho CTV):**
    *   **Mô tả:** Thông báo thanh toán hoa hồng phỏng vấn thành công.
    *   **Nội dung chính:** Tên ứng viên, tên CTV, tên công ty, số tiền hoa hồng, link đến trang CV đã bán và doanh thu.
    *   **Kênh:** Mail.
*   **`RemindScheduleInterview` (gửi cho NTD):**
    *   **Mô tả:** Nhắc nhở NTD cập nhật kết quả phỏng vấn.
    *   **Nội dung chính:** Tên NTD, tên ứng viên, vị trí, thời gian, địa điểm phỏng vấn, link cập nhật trạng thái.
    *   **Kênh:** Mail.