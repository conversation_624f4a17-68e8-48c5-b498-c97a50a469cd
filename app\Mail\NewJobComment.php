<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\JobComment;
use App\Models\Job;
use App\Models\User;

class NewJobComment extends Mailable
{
    use Queueable, SerializesModels;

    public $comment;
    public $job;
    public $commenter;

    public function __construct(JobComment $comment, Job $job, User $commenter)
    {
        $this->comment = $comment;
        $this->job = $job;
        $this->commenter = $commenter;
    }

    public function build()
    {
        return $this->subject('C<PERSON> câu hỏi mới trên tin tuyển dụng "'.$this->job->name.'"')
                    ->view('emails.new-job-comment');
    }
} 