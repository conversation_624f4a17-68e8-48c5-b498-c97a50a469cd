<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\WalletTopupRequest;
use App\Models\User;
use App\Models\Wallet;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WalletTopupController extends Controller
{
    /**
     * Hiển thị form nạp tiền
     */
    public function index()
    {
        return view('admin.pages.wallet-topup.index');
    }

    /**
     * Tìm kiếm user theo email
     */
    public function searchUser(Request $request)
    {
        $email = $request->input('email');

        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng nhập email'
            ]);
        }

        $users = User::whereIn('type', ['admin', 'employer', 'rec'])
            ->where('email', $email)
            ->get();

        if ($users->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy người dùng với email này'
            ]);
        }

        $userOptions = [];
        foreach ($users as $user) {
            // Tạo ví nếu chưa có (chỉ cho employer và rec)
            $wallet = $user->wallet;
            if (!$wallet && in_array($user->type, ['employer', 'rec'])) {
                $wallet = Wallet::create([
                    'user_id' => $user->id,
                    'type' => $user->type,
                    'point' => 0,
                    'amount' => 0,
                    'price' => 0,
                ]);
            }

            $currentBalance = 0;
            $balanceField = '';
            if ($user->type === 'employer' && $wallet) {
                $currentBalance = $wallet->amount;
                $balanceField = 'amount';
            } elseif ($user->type === 'rec' && $wallet) {
                $currentBalance = $wallet->price;
                $balanceField = 'price';
            }

            $typeText = '';
            switch ($user->type) {
                case 'admin':
                    $typeText = 'Quản trị viên';
                    break;
                case 'employer':
                    $typeText = 'Nhà tuyển dụng';
                    break;
                case 'rec':
                    $typeText = 'Cộng tác viên';
                    break;
            }

            $userOptions[] = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'type' => $user->type,
                'type_text' => $typeText,
                'current_balance' => $currentBalance,
                'current_balance_formatted' => number_format($currentBalance, 0, ',', '.') . ' VNĐ',
                'balance_field' => $balanceField,
                'has_wallet' => (bool) $wallet
            ];
        }

        return response()->json([
            'success' => true,
            'users' => $userOptions,
            'multiple' => count($userOptions) > 1
        ]);
    }

    /**
     * Xử lý nạp tiền
     */
    public function topup(WalletTopupRequest $request)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($request->user_id);
            $wallet = $user->wallet;

            if (!$wallet) {
                $wallet = Wallet::create([
                    'user_id' => $user->id,
                    'type' => $user->type,
                    'point' => 0,
                    'amount' => 0,
                    'price' => 0,
                ]);
            }

            $transaction_type = $request->transaction_type; // 'add' hoặc 'subtract'
            $amount = $request->amount;
            $note = $request->note ?: ($transaction_type === 'add' ? 'Nạp tiền từ Admin' : 'Trừ tiền từ Admin');

            // Xử lý theo loại user
            if ($user->type === 'employer') {
                // Nạp vào amount cho employer
                if ($transaction_type === 'add') {
                    $wallet->addAmount($amount, null, $note, 'admin_topup');
                } else {
                    // Kiểm tra số dư trước khi trừ
                    if ($wallet->amount < $amount) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Số dư không đủ để thực hiện giao dịch trừ tiền'
                        ]);
                    }
                    $wallet->subtractAmount($amount, null, $note, 'admin_subtract');
                }
            } else if ($user->type === 'rec') {
                // Nạp vào price cho rec
                if ($transaction_type === 'add') {
                    $wallet->addPrice($amount, null, $note, 'admin_topup');
                } else {
                    // Kiểm tra số dư trước khi trừ
                    if ($wallet->price < $amount) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Số dư không đủ để thực hiện giao dịch trừ tiền'
                        ]);
                    }
                    $wallet->subtractPrice($amount, null, $note, 'admin_subtract');
                }
            } else if ($user->type === 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Tài khoản Admin không hỗ trợ chức năng ví'
                ]);
            }

            DB::commit();

            $newBalance = $user->type === 'employer' ? $wallet->amount : $wallet->price;

            return response()->json([
                'success' => true,
                'message' => ($transaction_type === 'add' ? 'Nạp tiền' : 'Trừ tiền') . ' thành công!',
                'new_balance' => $newBalance,
                'new_balance_formatted' => number_format($newBalance, 0, ',', '.') . ' VNĐ'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Lỗi nạp tiền ví: ' . $e->getMessage(), [
                'user_id' => $request->user_id ?? null,
                'amount' => $request->amount ?? null,
                'transaction_type' => $request->transaction_type ?? null,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ]);
        }
    }
}
