<?php

namespace App\Services\Frontend;

use App\Jobs\CandidateCancelInterviewSubmitCv;
use App\Jobs\DepositRefundRejectOfferSubmit;
use App\Jobs\PayInterviewSubmit;
use App\Jobs\PayOnboardSubmit;
use App\Jobs\RecSumExpiredPoint;
use App\Jobs\RecSumExpiredPointSubmit;
use App\Jobs\SuccessRecuitmentSubmit;
use App\Notifications\CandidateConfirmRecruitmentSubmitCv;
use App\Notifications\CandidateRejectRecruitmentAdminSubmit;
use App\Notifications\CandidateRejectRecruitmentEmployerSubmit;
use App\Notifications\CandidateRejectRecruitmentRecSubmit;
use App\Notifications\ChangeStatusCancelInterviewToAdminSubmit;
use App\Notifications\ChangeStatusCancelInterviewToEmployerSubmit;
use App\Notifications\ChangeStatusCancelInterviewToRecSubmit;
use App\Notifications\ChangeStatusCancelOnboardToAdminSubmit;
use App\Notifications\ChangeStatusCancelOnboardToEmployerSubmit;
use App\Notifications\ChangeStatusCancelOnboardToRecSubmit;
use App\Notifications\ChangeStatusFailIInterviewSubmit;
use App\Notifications\ChangeStatusFailIInterviewToAdminSubmit;
use App\Notifications\ChangeStatusFailTrialWorkToAdminSubmit;
use App\Notifications\ChangeStatusFailTrialWorkToEmployerSubmit;
use App\Notifications\ChangeStatusFailTrialWorkToRecSubmit;
use App\Notifications\ChangeStatusPassInterviewAdminSubmit;
use App\Notifications\ChangeStatusPassInterviewSubmit;
use App\Notifications\ChangeStatusSuccessRecruitmentToAdminSubmit;
use App\Notifications\ChangeStatusSuccessRecruitmentToRecSubmit;
use App\Notifications\ChangeStatusTrailWorkToAdminSubmit;
use App\Notifications\ChangeStatusTrailWorkToAuthorityRecSubmit;
use App\Notifications\ChangeStatusTrailWorkToRecSubmit;
use App\Notifications\EmailConfirmOnboardSubmit;
use App\Notifications\EmailRejectOnboardSubmit;
use App\Notifications\EmployerComplainAdminSubmit;
use App\Notifications\EmployerComplainSubmit;
use App\Notifications\PaymentInterviewToRecSubmit;
use App\Notifications\PaymentTrialWorkSubmit;
use App\Notifications\RecAgreeComplainEmployerSubmit;
use App\Notifications\RecAgreeComplainSubmit;
use App\Notifications\RecRefuseComplainSubmit;
use App\Notifications\RemindExpireTrailWorkSubmit;
use App\Notifications\RemindPaymentDebitSubmit;
use App\Repositories\BonusRepository;
use App\Repositories\LevelByJobTopRepository;
use App\Repositories\LevelBySkillMainRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\SubmitCvHistoryPaymentRepository;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\SubmitCvOnboardRepository;
use App\Repositories\SubmitCvPaymentDebitRepository;
use App\Services\FileServiceS3;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SubmitCvService
{

    protected $submitCvRepository;
    protected $submitCvHistoryStatusRepository;
    protected $submitCvOnboardRepository;
    protected $submitCvPaymentDebitRepository;
    protected $submitCvHistoryPaymentRepository;

    public function __construct(
        SubmitCvRepository $submitCvRepository,
        SubmitCvHistoryStatusRepository $SubmitCvHistoryStatusRepository,
        SubmitCvPaymentDebitRepository $submitCvPaymentDebitRepository,
        SubmitCvHistoryPaymentRepository $submitCvHistoryPaymentRepository,
        SubmitCvOnboardRepository $submitCvOnboardRepository,
    ) {
        $this->submitCvHistoryStatusRepository = $SubmitCvHistoryStatusRepository;
        $this->submitCvRepository = $submitCvRepository;
        $this->submitCvOnboardRepository = $submitCvOnboardRepository;
        $this->submitCvPaymentDebitRepository = $submitCvPaymentDebitRepository;
        $this->submitCvHistoryPaymentRepository = $submitCvHistoryPaymentRepository;
    }
    public function find($id)
    {
        return $this->submitCvRepository->find($id);
    }

    /**
     * Ứng viên đồng ý hoặc từ chối khi NTD mua CV
     */
    public function verifyEmailCandidate($token, $type)
    {
        $submitCvHistoryPaymentRepository = resolve(SubmitCvHistoryPaymentRepository::class);
        $submitCvHistoryStatusRepository = resolve(SubmitCvHistoryStatusRepository::class);
        $submitCv = $this->submitCvRepository->findToken($token);
        $statusRecruitment = $type == 1 ? config('constant.status_recruitment_revert.WaitingPayment') : config('constant.status_recruitment_revert.CandidateCancelApply');   //type = 1 dong y, type = 2 tu choi
        //3 => 'Waiting setup interview',
        //2 => 'Candidate Cancel Apply',
        //1 => 'Waiting candidate confirm',
        if ($submitCv && $submitCv->status == config('constant.status_recruitment_revert.Waitingcandidateconfirm')) {
            //dong y
            if ($statusRecruitment == config('constant.status_recruitment_revert.WaitingPayment')) {
                // Chuyen sang trang thai cho thanh toan
                // $statusRecruitment = config('constant.status_recruitment_revert.WaitingPayment');
                $update = [
                    'status' => $statusRecruitment,
                    'confirm_token' => null
                ];
                $submitCv->update($update);
                //gui mail cho ntd
                $submitCv->employer->notify(new CandidateConfirmRecruitmentSubmitCv($submitCv));
                $historyStatus = $this->submitCvHistoryStatusRepository->create([
                    'user_id'            => $submitCv->employer->id,
                    'submit_cvs_id'      => $submitCv->id,
                    'status_recruitment' => $statusRecruitment,
                    'candidate_name'     => $submitCv->warehouseCv->candidate_name,
                    'type'               => 'rec',
                    'authority'          => $submitCv->authorize
                ]);
                //queue check sau 7 ngay chua xac nhan thi tu dong tu choi
                if ($submitCv->bonus_type != 'cv') {
                    // CandidateCancelInterviewSubmitCv::dispatch($submitCv->id)->delay(now()->addDays(7));
                }
            }
            //tu choi
            else {
                //gui mail cho ctv
                $submitCv->user->notify(new CandidateRejectRecruitmentRecSubmit($submitCv));

                //gui mail cho ntd
                // $submitCv->employer->notify(new CandidateRejectRecruitmentEmployerSubmit($submitCv));

                //gui mail cho admin
                Mail::to(config('settings.global.email_admin'))->send(new CandidateRejectRecruitmentAdminSubmit($submitCv));

                //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                $submitCv->status = config('constant.status_recruitment_revert.CandidateCancelApply');
                // $submitCv->status_payment = 3; //3 => 'Hoàn tiền'
                $submitCv->confirm_token = null;
                $submitCv->save();
                //Log thay đổi trang thái
                $historyStatus = $submitCvHistoryStatusRepository->create([
                    'user_id'            => $submitCv->employer->id,
                    'submit_cvs_id'      => $submitCv->id,
                    'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'),   //2 => 'Candidate Cancel Apply',
                    'candidate_name'     => $submitCv->warehouseCv->candidate_name,
                    'type'               => 'rec',
                    'authority'          => $submitCv->authorize,
                ]);

                //interview
                if (false) {
                    # xử lý cho cv/interview/onboard giống nhau nên bỏ qua đoạn này
                    if ($submitCv->bonus_type == 'interview') {
                        // Log::info('interview $submitCv 222222: ', [
                        //     'xxxx: ' => $submitCv
                        // ]);
                        //ghi log hoan tien
                        // Khong hoan tien nua vi chua thanh toan
                        // $submitCvHistoryPaymentRepository->create([
                        //     'user_id'      => $submitCv->employer->id,
                        //     'submit_cv_id' => $submitCv->id,
                        //     'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                        //     'percent'      => 100,                                                           //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        //     'type_of_sale' => $submitCv->job->bonus_type,
                        //     'amount'       => $submitCv->bonus_point,
                        //     'balance'      => $submitCv->employer->wallet->amount + $submitCv->bonus_point,
                        //     'status'       => 0
                        // ]);
                        //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                        $submitCv->status = config('constant.status_recruitment_revert.CandidateCancelApply');
                        // $submitCv->status_payment = 3; //3 => 'Hoàn tiền'
                        $submitCv->confirm_token = null;
                        $submitCv->save();
                        //Log thay đổi trang thái
                        $historyStatus = $submitCvHistoryStatusRepository->create([
                            'user_id'            => $submitCv->employer->id,
                            'submit_cvs_id'      => $submitCv->id,
                            'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'),   //2 => 'Candidate Cancel Apply',
                            'candidate_name'     => $submitCv->warehouseCv->candidate_name,
                            'type'               => 'rec',
                            'authority'          => $submitCv->authorize,
                        ]);
                        //Cộng point của NTD
                        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->job->bonus;
                        // $submitCv->employer->wallet->save();
                        // Log::info('interview $wareHouseCvSellingBuy 3333333355555: ', [
                        //     'xxxx: ' => $submitCv
                        // ]);
                    }
                    //onboard
                    if ($submitCv->job->bonus_type == 'onboard') {
                        //2 => 'Candidate Cancel Apply',
                        // $submitCvHistoryStatusData = $submitCvHistoryStatusRepository->finByTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                        // Log::info('onboard $wareHouseCvSellingBuy 222222: ', [
                        //     'xxxx: ' => $submitCvHistoryStatusData
                        // ]);
                        // //hoàn bao nhiêu point
                        // $bonus = 0;
                        // if ($submitCvHistoryStatusData) {
                        //     foreach ($submitCvHistoryStatusData as $key => $value) {
                        //         //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        //         $value->status = 1;
                        //         $value->save();
                        //         //ghi log hoan tien
                        //         $submitCvHistoryPaymentRepository->create([
                        //             'user_id'      => $submitCv->employer->id,
                        //             'submit_cv_id' => $submitCv->id,
                        //             'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                        //             'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        //             'type_of_sale' => $submitCv->job->bonus_type,
                        //             'amount'       => $value->amount,
                        //             'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                        //             'status'       => 0
                        //         ]);
                        //         $bonus += $value->amount;
                        //     }
                        // }

                        // //Tự động update reject khi Ứng viên ko xác nhận => update status_recruitment  = CandidateCancelApply
                        $submitCv->status = config('constant.status_recruitment_revert.CandidateCancelApply');
                        // $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                        $submitCv->confirm_token = null;
                        $submitCv->save();
                        //Log thay đổi trang thái
                        $historyStatus = $submitCvHistoryStatusRepository->create([
                            'user_id'            => $submitCv->employer->id,
                            'submit_cvs_id'      => $submitCv->id,
                            'status_recruitment' => config('constant.status_recruitment_revert.CandidateCancelApply'),   //2 => 'Candidate Cancel Apply',
                            'candidate_name'     => $submitCv->warehouseCv->candidate_name,
                            'type'               => 'rec',
                            'authority'          => $submitCv->authorize,
                        ]);
                        // //Cộng point của NTD
                        // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                        // $submitCv->employer->wallet->save();
                        // Log::info('onboard $wareHouseCvSellingBuy 3333333355555: ', [
                        //     'xxxx: ' => $submitCv
                        // ]);
                    }
                }
            }
            return true;
        }
        return false;
    }
    public function getMessageRejectInterview($submitCv, $bock)
    {
        if ($submitCv->status != 5) {
            return null;
        }
        return $bock->last();
    }

    /**
     * @param $id
     * @param $statusRecruitment
     * @return mixed
     * Thanh toan tien cho CTV : Interview
     *
     */
    public function payCtv($submitCv)
    {
        //Cộng tiền trả CTV
        //1. payins
        $bonusRepository = resolve(BonusRepository::class);
        $payinMonthRepository = resolve(PayinMonthRepository::class);
        $payinMonth = $payinMonthRepository->findBySubmitCvId($submitCv->id);
        //nếu thanh toán rồi , thì ko thanh toán lại
        // Log::info('start pay ', [
        //     'xxxx: ' => $submitCv
        // ]);
        if (!$payinMonth) {
            // Log::info('start 11111111111 pay ', [
            //     'yyyy: ' => $submitCv
            // ]);
            $now = \Carbon\Carbon::now();
            $year = $now->year;
            $month = $now->month;
            $bonusCheck = $bonusRepository->getBonusByUser($submitCv->rec->id, $month, $year);
            //nếu ủy quyền thì thanh toán 20% price
            $price = $submitCv->getSubmitBonusForCtv();

            if (!$bonusCheck) {
                //nếu tháng/năm chưa có thì insert mới
                $bonusRepository->create(
                    [
                        'user_id'   => $submitCv->rec->id,
                        'year'      => $year,
                        'month'     => $month,
                        'price'     => $price,
                    ]
                );
            } else {
                //nếu có doanh thu từ trước thì cộng dồn
                $bonusCheck->price += $price;
                $bonusCheck->save();
            }
            //từng giao dịch trong tháng
            $payinMonthRepository->create(
                [
                    'user_id'      => $submitCv->rec->id,
                    'submit_cv_id' => $submitCv->id,
                    'year'         => $year,
                    'month'        => $month,
                    'price'        => $price,
                    'status'       => 8                    //8 => 'Hoàn tất thanh toán',
                ]
            );
            //2. wallets
            // $submitCv->rec->wallet->price  = $submitCv->rec->wallet->price + $price;
            $submitCv->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
            $submitCv->rec->wallet->addPrice($price, $submitCv, 'Trả định kỳ cho CTV', 'pay_ctv');
            // $submitCv->rec->wallet->save();
            //3. update status_complain =4 để không hoàn tiền lần nữa
            if ($submitCv->status_complain) {
                // $submitCv->status_complain = 4;
                $submitCv->save();
            }
            $submitCv->rec->notify(new PaymentInterviewToRecSubmit($submitCv, $price));
        }
    }

    /**
     * @param $request
     * @return mixed
     * Popup thay doi trang thai
     */
    public function submitCvChangeStatus($request)
    {
        $submitCv = $this->submitCvRepository->find($request['submit_id']);
        $assessment = isset($request['assessment']) ? $request['assessment'] : '';
        $submitCvHistoryPaymentRepository = resolve(SubmitCvHistoryPaymentRepository::class);
        //interview
        if ($submitCv->bonus_type == 'interview') {
            //10 => 'Fail Interview',
            if ($request['status_recruitment'] == config('constant.status_recruitment_revert.FailInterview')) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(mailable: new ChangeStatusFailIInterviewToAdminSubmit($submitCv, ['note' => $assessment]));
                } else {
                    $submitCv->rec->notify(new ChangeStatusFailIInterviewSubmit($submitCv, ['note' => $assessment]));
                    Mail::to(config('settings.global.email_admin'))->send(mailable: new ChangeStatusFailIInterviewToAdminSubmit($submitCv, ['note' => $assessment]));
                }
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
                //sau 24h thi Thanh toan tien tra CTV -> interview
                PayInterviewSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(24 * 60));
            }
            //            8 => 'Pass Interview',
            if ($request['status_recruitment'] == config('constant.status_recruitment_revert.PassInterview')) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdminSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusPassInterviewSubmit($submitCv));
                }
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
                //sau 24h thi Thanh toan tien tra CTV -> interview
                PayInterviewSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(24 * 60));
            }
            //9 => 'Candidate Cancel Interview',
            if ($request['status_recruitment'] == config('constant.status_recruitment_revert.CandidateCancelInterview')) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                //line số 52, 96 TODO dũng
                //ghi log hoan tien

                $bonus = $submitCv->bonus_ntd;
                $submitCvHistoryPaymentRepository->create([
                    'user_id'      => $submitCv->employer->id,
                    'submit_cv_id' => $submitCv->id,
                    'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                    'percent'      => 100,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale' => $submitCv->job->bonus_type,
                    'amount'       => $bonus,
                    'balance'      => $submitCv->employer->wallet->amount + $bonus,
                    'status'       => 3
                ]);
                //update status_payment
                $submitCv->status_payment = 3; //Hoan tien
                $submitCv->save();
                //Cộng point của NTD
                // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Candidate Cancel Interview', 'update_status_recruitment');
                // $submitCv->employer->wallet->save();

                $submitCv->employer->notify(new ChangeStatusCancelInterviewToEmployerSubmit($submitCv, $bonus));
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdminSubmit($submitCv, $bonus));
                } else {
                    $submitCv->rec->notify(new ChangeStatusCancelInterviewToRecSubmit($submitCv, $bonus));
                }
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
            }
        }

        // dd($request);
        // onboard
        if ($submitCv->bonus_type == 'onboard') {

            //Pass Interview
            if ($request['status_recruitment'] == 8) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);

                //offering 11
                $submitCv->update([
                    'status' => 11
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);

                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdminSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusPassInterviewSubmit($submitCv));
                }
            }
            //10 => 'Fail Interview',
            if ($request['status_recruitment'] == 10) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailIInterviewToAdminSubmit($submitCv, ['note' => $assessment]));
                } else {
                    $submitCv->rec->notify(new ChangeStatusFailIInterviewSubmit($submitCv, ['note' => $assessment]));
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailIInterviewToAdminSubmit($submitCv, ['note' => $assessment]));
                }
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);

                $submitCvHistoryPaymentData = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $bonus = 0;
                if ($submitCvHistoryPaymentData) {
                    foreach ($submitCvHistoryPaymentData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $submitCvHistoryPaymentRepository->create([
                            'user_id'      => $submitCv->employer->id,
                            'submit_cv_id' => $submitCv->id,
                            'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                            'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale' => $submitCv->job->bonus_type,
                            'amount'       => $value->amount,
                            'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                            'status'       => 0
                        ]);
                        $bonus += $value->amount;
                    }
                    $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                    $submitCv->save();
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn cọc', 'update_status_recruitment');
                    // $submitCv->employer->wallet->save();
                }
            }

            //9 => 'Candidate Cancel Interview',
            if ($request['status_recruitment'] == 9) {
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);                //cv onboard
                $submitCvHistoryPaymentData = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $bonus = 0;
                if ($submitCvHistoryPaymentData) {
                    foreach ($submitCvHistoryPaymentData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $submitCvHistoryPaymentRepository->create([
                            'user_id'      => $submitCv->employer->id,
                            'submit_cv_id' => $submitCv->id,
                            'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                            'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale' => $submitCv->job->bonus_type,
                            'amount'       => $value->amount,
                            'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                            'status'       => 0
                        ]);
                        $bonus += $value->amount;
                    }
                    $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                    $submitCv->save();
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn cọc', 'update_status_recruitment');
                    // $submitCv->employer->wallet->save();
                }
                $submitCv->employer->notify(new ChangeStatusCancelInterviewToEmployerSubmit($submitCv, $bonus));
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdminSubmit($submitCv, $bonus));
                } else {
                    $submitCv->rec->notify(new ChangeStatusCancelInterviewToRecSubmit($submitCv, $bonus));
                }
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
            }

            //15 => 'Cancel onboard',
            if ($request['status_recruitment'] == 15) {
                //13 => 'Waiting onboard' chi o trang thai nay thi moi dc thay doi sang 15 => 'Cancel onboard',
                if ($submitCv->status != 13) return false;
                $submitCv->update([
                    'status' => $request['status_recruitment']
                ]);
                $onboard = $this->submitCvOnboardRepository->getCurrentBySubmitCvId($submitCv->id);
                if ($onboard) {
                    $onboard->update([
                        'status' => 2
                    ]);
                }
                $wareHouseCvSellingHistoryBuyData = $submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                //hoàn bao nhiêu point
                $bonus = 0;
                if ($wareHouseCvSellingHistoryBuyData) {
                    foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                        //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                        $value->status = 1;
                        $value->save();
                        //ghi log hoan tien
                        $submitCvHistoryPaymentRepository->create([
                            'user_id'      => $submitCv->employer->id,
                            'submit_cv_id' => $submitCv->id,
                            'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                            'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale' => $submitCv->job->bonus_type,
                            'amount'       => $value->amount,
                            'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                            'status'       => 0
                        ]);
                        $bonus += $value->amount;
                    }
                    $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                    $submitCv->save();
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn cọc', 'update_status_recruitment');
                    // $submitCv->employer->wallet->save();
                }

                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);

                $submitCv->employer->notify(new ChangeStatusCancelOnboardToEmployerSubmit($submitCv, $bonus));
                if ($submitCv->wareHouseCvSelling->authority > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdminSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusCancelOnboardToRecSubmit($submitCv));
                }
            }

            //14 => 'Trial work',
            if ($request['status_recruitment'] == 14) {

                // $point = $wareHouseCvSellingBuy->point - (0.1 * $wareHouseCvSellingBuy->point);
                $bonus = $submitCv->bonus_ntd;
                //13 => 'Waiting onboard' chi o trang thai nay thi moi dc thay doi sang 14 => 'Trial work',
                if ($submitCv->status != 13) return false;
                //check so point của NTD neu ko đủ thi dừng  + $wareHouseCvSellingBuy->point
                if ($bonus > $submitCv->employer->wallet->amount) {
                    //Luu vao 1 table Ghi nợ, user_id (ntd) status = 0 chưa trả nợ
                    //sau khi NTD nạp tiền -> đủ tiền thì sẽ thanh toán -> status đã thanh toán = 1
                    $this->submitCvPaymentDebitRepository->create([
                        'submit_cv_id' => $submitCv->id,
                        'user_id' => $submitCv->user_id,
                        'amount' => $bonus,
                    ]);
                    $submitCv->employer->notify((new RemindPaymentDebitSubmit($submitCv))
                        ->delay([
                            'mail' => now()->addMinutes(24 * 60),
                        ]));
                    $start = 10;
                    while ($start <= 15) {
                        $submitCv->employer->notify((new RemindPaymentDebitSubmit($submitCv))
                            ->delay([
                                'mail' => now()->addMinutes($start * 24 * 60),
                            ]));
                        $start++;
                    }
                } else {
                    $percent = 90; //    cọc lần 1 10%, trailwork thanh toan nốt 90%
                    //trừ point của NTD
                    $balance  = $submitCv->employer->wallet->amount - $bonus;
                    // $submitCv->employer->wallet->amount = $balance;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->subtractAmount($bonus, $submitCv, 'Thanh toán nốt 90%', 'change_status_recruitment');
                    // $submitCv->employer->wallet->save();
                    //ghi log mua
                    $submitCvHistoryPaymentRepository->create([
                        'user_id'      => $submitCv->employer->id,
                        'submit_cv_id' => $submitCv->id,
                        'type'         => 0,                                                             //0 trừ tiền, 1 hoàn tiền
                        'percent'      => $percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale' => $submitCv->job->bonus_type,
                        'amount'       => $bonus,
                        'balance'      => $balance,
                        'status'       => 0
                    ]);
                    $paymentStatus = 4;
                }

                $updateSubmitCv = [
                    'status' => $request['status_recruitment'],
                ];

                if (!empty($paymentStatus)) {
                    $updateSubmitCv['status_payment'] = $paymentStatus;
                }

                //update Trial work
                $submitCv->update($updateSubmitCv);
                //update status history: Trial work
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusTrailWorkToAdminSubmit($submitCv));
                    $submitCv->rec->notify(new ChangeStatusTrailWorkToAuthorityRecSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusTrailWorkToRecSubmit($submitCv));
                }
                //set queue sau 55 ngày  Trail work gửi email
                $start = 55;
                $onboard = $this->submitCvOnboardRepository->getFirstBySubmitCvId($submitCv->id);
                $carbonDate = $currentDateBook = Carbon::createFromFormat('Y-m-d H:i:s', $onboard->date_book);
                while ($start < 60) {
                    $addTime = clone $carbonDate;
                    $submitCv->employer->notify((new RemindExpireTrailWorkSubmit($submitCv, $start))
                        ->delay([
                            'mail' => $addTime->addMinutes($start * 24 * 60),
                        ]));
                    $start++;
                }

                //sau 7 ngay het han trailwork (2 thang) = 67 ngay nếu NTD ko đổi trạng thái thì tự động đổi sang 16 => 'Success Recruitment',
                $addRecuitmentTime = clone $carbonDate;
                SuccessRecuitmentSubmit::dispatch($submitCv->id)->delay($addRecuitmentTime->addMinutes(67 * 24 * 60));
                //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
                $add30Time = clone $carbonDate;
                PayOnboardSubmit::dispatch($submitCv->id, 15)->delay($add30Time->addMinutes(30 * 24 * 60));
                //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
                $add45Time = clone $carbonDate;
                PayOnboardSubmit::dispatch($submitCv->id, 10)->delay($add45Time->addMinutes(45 * 24 * 60));
                // Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
                $add67Time = clone $carbonDate;
                PayOnboardSubmit::dispatch($submitCv->id, 75)->delay($add67Time->addMinutes(67 * 24 * 60));
            }

            //16 => 'Success Recruitment',
            if ($request['status_recruitment'] == 16) {
                $submitCv->update([
                    'status' => $request['status_recruitment'],
                    'status_payment' => 4,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusSuccessRecruitmentToAdminSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusSuccessRecruitmentToRecSubmit($submitCv));
                }
            }

            //17 => 'Fail trail work',
            if ($request['status_recruitment'] == 17) {
                //14 => 'Trial work', chỉ 14 dc đổi sang 17
                if ($submitCv->status != 14) return false;
                $submitCv->update([
                    'status' => $request['status_recruitment'],
                    'status_payment' => 3,
                ]);
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailTrialWorkToAdminSubmit($submitCv, ['note' => $assessment]));
                } else {
                    $submitCv->rec->notify(new ChangeStatusFailTrialWorkToRecSubmit($submitCv, ['note' => $assessment]));
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusFailTrialWorkToAdminSubmit($submitCv, ['note' => $assessment]));
                }
                //Hoàn tiền NTD
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user(), $request['assessment']);
                $bookOnboard = $this->submitCvOnboardRepository->getBookBySubmitCvId($submitCv->id);
                $createDateTrailWork30 = strtotime('+30 day', strtotime($bookOnboard->date_book));
                $createDateTrailWork60 = strtotime('+60 day', strtotime($bookOnboard->date_book));
                $createDateTrailWork67 = strtotime('+67 day', strtotime($bookOnboard->date_book));
                $now = \Carbon\Carbon::now()->format('Y-m-d 23:59:59');
                $now =  strtotime($now);
                $percent = $bonus = 0;
                //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                if ($createDateTrailWork30 >= $now) {
                    $percent = 100;
                    $bonus = $submitCv->bonus_ntd;
                }
                //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                if ($createDateTrailWork30 < $now && $now <= $createDateTrailWork60) {
                    $percent = 70;
                    $bonus = (int) round(($submitCv->bonus_ntd * $percent) / 100);
                }
                //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                if ($createDateTrailWork60 < $now && $now <= $createDateTrailWork67) {
                    $percent = 50;
                    $bonus = (int) round(($submitCv->bonus_ntd * $percent) / 100);
                }
                $submitCv->employer->notify(new ChangeStatusFailTrialWorkToEmployerSubmit($submitCv, $bonus));
                //ghi log hoan tien
                $submitCvHistoryPaymentRepository->create([
                    'user_id'      => $submitCv->employer->id,
                    'submit_cv_id' => $submitCv->id,
                    'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                    'percent'      => $percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale' => $submitCv->job->bonus_type,
                    'amount'       => $bonus,
                    'balance'      => $balance,
                    'status'       => 0
                ]);

                //Cộng point của NTD
                // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
                // $submitCv->employer->wallet->save();
            }
        }


        return $submitCv;
    }

    /**
     * @param $id
     * @param
     * @return mixed
     * Thanh toan tien cho CTV : Onboard
     *
     */
    public function payOnboardCtv($submitCv, $percent)
    {
        //Cộng tiền trả CTV
        //1. payins
        $bonusRepository = resolve(BonusRepository::class);
        $payinMonthRepository = resolve(PayinMonthRepository::class);
        $countPayinMonth = $payinMonthRepository->countBySubmitCvId($submitCv->id);
        //nếu thanh toán rồi , thì ko thanh toán lại
        // Log::info('start pay onboard', [
        //     'xxxx: ' => $submitCv
        // ]);
        //nếu ủy quyền thì thanh toán 20% price
        $pricePriority = $submitCv->bonus_point;
        if ($submitCv->authorize > 0) $pricePriority = (int) round(($submitCv->bonus_point * 20) / 100);

        //THanh toan toi da 3 lan
        if ($countPayinMonth <= 3) {
            // Log::info('start 11111111111 pay onboard', [
            //     'yyyy: ' => $submitCv
            // ]);
            $now = \Carbon\Carbon::now();
            $year = $now->year;
            $month = $now->month;
            $bonusCheck = $bonusRepository->getBonusByUser($submitCv->rec->id, $month, $year);
            $price = (int) round(($pricePriority * $percent) / 100);
            if (!$bonusCheck) {
                //nếu tháng/năm chưa có thì insert mới
                $bonusRepository->create(
                    [
                        'user_id'   => $submitCv->rec->id,
                        'year'      => $year,
                        'month'     => $month,
                        'price'     => $price
                    ]
                );
            } else {
                //nếu có doanh thu từ trước thì cộng dồn
                $bonusCheck->price += $price;
                $bonusCheck->save();
            }

            //Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
            $status = 8;
            if ($percent == 15) {
                //TODO Thanh toán 15% cho CTV lần 1 sau 30 ngày sau "Trial work"
                //Gửi mail cho CTV : https://docs.google.com/document/d/1audIV5bLm_84XyAYFGhzL2ybA6gFM8aAObSLxLyXXBo/edit?usp=sharing
                $submitCv->rec->notify(new PaymentTrialWorkSubmit($submitCv, 1, $price, $percent));
                $status = 5;
            }
            //Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
            if ($percent == 10) {
                //TODO Thanh toán 10% cho CTV lần 2 sau 45 ngày sau "Trail work"
                //Gửi mail cho CTV: https://docs.google.com/document/d/1Yc9bDcm8yLE3UGjSzzle1miPw4mqOUaGlMkEBGM3qMA/edit?usp=sharing
                $submitCv->rec->notify(new PaymentTrialWorkSubmit($submitCv, 2, $price, $percent));
                $status = 6;
            }
            //Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
            if ($percent == 75) {
                //TODO Thanh toán 75% cho CTV lần 3 sau 60 + 7 ngày sau "Trail work" va ký hợp đồng chính thức
                //Gửi mail cho CTV: https://docs.google.com/document/d/1ujQjeIIbXOJukZHLu_Xsjw0AwS9siZOKq4G512Nvztk/edit?usp=sharing
                $submitCv->rec->notify(new PaymentTrialWorkSubmit($submitCv, 3, $price, $percent));
            }

            //từng giao dịch trong tháng
            $payinMonthRepository->create(
                [
                    'user_id'      => $submitCv->rec->id,
                    'submit_cv_id' => $submitCv->id,
                    'year'         => $year,
                    'month'        => $month,
                    'price'        => $price,
                    'percent'      => $percent,
                    'status'       => $status              //8 => 'Hoàn tất thanh toán',
                ]
            );
            //2. wallets
            // $submitCv->rec->wallet->price  = $submitCv->rec->wallet->price + $price;
            $submitCv->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
            $submitCv->rec->wallet->addPrice($price, $submitCv, 'Thanh toán định kỳ cho CTV', 'pay_ctv');
            // $submitCv->rec->wallet->save();
            //3. update status_complain =4 để không hoàn tiền lần nữa
            if ($submitCv->status_complain) {
                $submitCv->save();
            }
        }
    }

    public function updateScheduleOnboard($onboardId, $statusRecruitment)
    {
        $onboard = $this->submitCvOnboardRepository->find($onboardId);
        $submitCv = $onboard->submitCv;
        if ($submitCv->bonus_type == 'onboard') {

            $user = auth('client')->user();
            //13 => 'Waiting onboard'
            if ($statusRecruitment == 13) {
                $onboard->update([
                    'status' => 1
                ]);
                $employer = $onboard->employer;
                $employer->notify(new EmailConfirmOnboardSubmit($employer, $onboard, $submitCv));
                $submitCv->update([
                    'status' => $statusRecruitment,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
            }
            //12 => 'Reject Offer',
            if ($statusRecruitment == 12) {
                $onboard->update([
                    'status' => 2
                ]);
                $submitCv->update([
                    'status' => $statusRecruitment,
                ]);
                $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);

                $employer = $submitCv->employer;
                $employer->notify(new EmailRejectOnboardSubmit($employer, $onboard, $submitCv));
                if ($submitCv->authorize > 0) {
                    Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdminSubmit($submitCv));
                } else {
                    $submitCv->rec->notify(new ChangeStatusCancelOnboardToRecSubmit($submitCv));
                }
                //todo hoàn cọc
                DepositRefundRejectOfferSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(48 * 60));;
            }
        }
    }


    /**
     * @param $request
     * @return mixed
     * NTD gửi khiếu nại
     */
    public function complain($request)
    {

        $submitCv = $this->submitCvRepository->find($request['submit_cv_id']);
        if ($submitCv->can_complain === false) {
            throw new \Exception('Khiếu nại thất bại');
        }

        $imgComplain = '';
        if (!empty($request['image_complain'])) {
            $imgComplain = FileServiceS3::getInstance()->uploadToS3($request['image_complain'], config('constant.sub_path_s3.complain'));
        }

        //nếu gửi khiếu nại rồi hoặc chưa đủ dk thi return false
        if ($submitCv->status_complain != 0 && $submitCv->status_complain != 5) return false;
        //cv &&  18 => 'Buy CV data successfull',//danh cho type_of_sale = cv
        if ($submitCv->bonus_type == 'cv' && $submitCv->status != 18) return false;
        //onboard &&  7 => 'Waiting Interview', && 14 => 'Trial work',
        if ($submitCv->bonus_type == 'onboard' && $submitCv->status != 7 && $submitCv->status != 14) return false;
        //interview &&  7 => 'Waiting Interview',
        if ($submitCv->bonus_type == 'interview' && ($submitCv->status != 7 && $submitCv->status != 3)) return false;

        //neu uy quyen thi khi NTD khieu nai , coi nhu CTV tu choi, de chuyen cho admin phe duyet
        if ($submitCv->authorize > 0) {
            $statusComplain = 2;
            //sau 7 ngày khieu nai mà admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPointSubmit::dispatch($submitCv->id, 2)->delay(now()->addMinutes(7 * 24 * 60));
        } else if ($submitCv->authorize == 0) {
            $statusComplain = 1;
            //sau 7 ngày khieu nai mà CTV không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPointSubmit::dispatch($submitCv->id, 1)->delay(now()->addMinutes(7 * 24 * 60));
        }

        $data = [
            'txt_complain'    => !empty($request['content']) ? $request['content'] : '',
            'status_complain' => $statusComplain,
            'img_complain'    => $imgComplain,
            'count_complain'  => $submitCv->count_complain + 1,
            'date_complain'   => now(),
        ];

        $submitCv->update($data);
        $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth()->user());
        $rec = $submitCv->rec;
        $employer = $submitCv->employer;
        //uy quyen thi gui email cho admin
        if ($submitCv->authorize > 0) {
            Mail::to(config('settings.global.email_admin'))->send(new EmployerComplainAdminSubmit($rec, $employer, $submitCv));
        } else if ($submitCv->authorize == 0) {
            //khong uy quyen thi gui email cho CTV
            $rec->notify(new EmployerComplainSubmit($rec, $employer, $submitCv));
        }


        return $submitCv;
    }

    /**
     * @param $id
     * @param $status
     * @return mixed
     * CTV xác nhận hoặc từ chối khiếu nại
     * HT NTD
     */
    public function recChangeStatus($id, $status)
    {
        DB::beginTransaction();
        $submitCv = $this->submitCvRepository->find($id);
        if (!$submitCv) return false;
        //1 là trạng thái khi NTD vừa khiếu nại, nếu khác 1 thì CTV đã thay đổi rồi
        if ($submitCv->status_complain != 1) return false;
        $submitCvData = [
            'status_complain' => $status,
        ];
        //CTV xac nhan
        if ($submitCv->bonus_type == 'cv' && $status == 3) {
            $submitCvData['status'] = config('constant.status_recruitment_revert.CancelBuyCVdata');
        }

        if ($status == 3 && ($submitCv->bonus_type == 'cv' ||  $submitCv->bonus_type == 'interview')) {
            $submitCvData['status_payment'] = 3; //Hoan tien
        }

        if ($status == 3 && $submitCv->bonus_type == 'onboard') {
            $submitCvData['status_payment'] = 3; //Hoàn tien
        }

        $rec = $submitCv->rec;
        $employer = $submitCv->employer;
        //xác nhận thi gửi email cho cả CTV, NTD
        if ($status == 3) { // CTV xác nhận đồng ý khiếu nại 
            //Hoan tien tra NTD
            //CV data
            if ($submitCv->bonus_type == 'cv' || $submitCv->bonus_type == 'interview') {
                //ghi log mua
                $this->submitCvHistoryPaymentRepository->create([
                    'user_id'      => $submitCv->employer->id,
                    'submit_cv_id' => $submitCv->id,
                    'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                    'percent'      => 100,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale' => $submitCv->job->bonus_type,
                    'amount'       => $submitCv->bonus_ntd,
                    'balance'      => $submitCv->employer->wallet->amount + $submitCv->bonus_ntd,
                    'status'       => 0
                ]);

                if ($submitCv->bonus_type == 'cv') {
                    //Log thay đổi trang thái
                    $this->submitCvHistoryStatusRepository->create([
                        'user_id'            => $submitCv->employer->id,
                        'submit_cvs_id'      => $submitCv->id,
                        'status_recruitment' => config('constant.status_recruitment_revert.CancelBuyCVdata'),
                        'type'               => 'employer',
                        'authority'          => $submitCv->authorize
                    ]);
                }

                //Cộng point của NTD
                $point = $submitCv->bonus_ntd;
                // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $submitCv->bonus_ntd;
                $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                $submitCv->employer->wallet->addAmount($submitCv->bonus_ntd, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
                // $submitCv->employer->wallet->save();
            }
            //cv onboard
            if ($submitCv->bonus_type == 'onboard') {
                //14 => 'Trial work',
                if ($submitCv->status == 14) {
                    $onboard = $this->submitCvOnboardRepository->getFirstBySubmitCvId($submitCv->id);
                    $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                    $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                    $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                    $dateComplain = Carbon::createFromFormat('Y-m-d H:i:s', $submitCv->date_complain);
                    $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                    $dateComplain =  strtotime($dateComplain);
                    $percent = $point = 0;
                    //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                    if ($createDateTrailWork30 >= $dateComplain) {
                        $percent = 100;
                        $point = $submitCv->bonus_ntd;
                    }
                    //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                    if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                        $percent = 70;
                        $point = (int) round(($submitCv->bonus_ntd * $percent) / 100);
                    }
                    //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                    if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                        $percent = 50;
                        $point = (int) round(($submitCv->bonus_ntd * $percent) / 100);
                    }
                    //ghi log hoan tien
                    $this->submitCvHistoryPaymentRepository->create([
                        'user_id'      => $submitCv->employer->id,
                        'submit_cv_id' => $submitCv->id,
                        'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                        'percent'      => $percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale' => $submitCv->job->bonus_type,
                        'amount'       => $point,
                        'balance'      => $submitCv->employer->wallet->amount + $point,
                        'status'       => 0
                    ]);
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($point, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
                    // $submitCv->employer->wallet->save();
                }
                //Hoàn cả
                else {
                    $paymentHistoryData = $this->submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                    //hoàn bao nhiêu point
                    $point = 0;
                    if ($paymentHistoryData) {
                        foreach ($paymentHistoryData as $key => $value) {
                            //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                            $value->status = 1;
                            $value->save();
                            //ghi log hoan tien
                            $this->submitCvHistoryPaymentRepository->create([
                                'user_id'      => $submitCv->employer->id,
                                'submit_cv_id' => $submitCv->id,
                                'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                                'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                'type_of_sale' => $submitCv->job->bonus_type,
                                'amount'       => $value->amount,
                                'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                                'status'       => 0
                            ]);
                            $point += $value->amount;
                        }
                    }
                    //Cộng point của NTD
                    // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $point;
                    $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->employer->wallet->addAmount($point, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
                    // $submitCv->employer->wallet->save();
                    $submitCvData['status_payment'] = 2; //Hoàn cọc
                }
            }

            //gửi email cho CTV
            if ($rec) {
                $rec->notify(new RecAgreeComplainSubmit($rec, $employer, $submitCv));
            }
            //gửi email cho NTD
            if ($employer) {
                $employer->notify(new RecAgreeComplainEmployerSubmit($rec, $employer, $submitCv, $point));
            }
        }
        // từ chối thì gửi email cho admin phê duyệt
        if ($status == 2) {
            Mail::to(config('settings.global.email_admin'))->send(new RecRefuseComplainSubmit($rec, $employer, $submitCv));
            //sau 7 ngày CTV từ chối, mà Admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
            RecSumExpiredPointSubmit::dispatch($submitCv->id, 2)->delay(now()->addMinutes(7 * 24 * 60));
        }


        $submitCv->update($submitCvData);
        DB::commit();
        return $submitCv;
    }

    public function getMinSubmitPrice($params)
    {
        // $isIT            = isset($params['is_it']) && $params['is_it']           == 'true' ? true : false;
        $is_onboard      = isset($params['bonus_type']) && $params['bonus_type'] == 'onboard' ? true : false;
        $salary          = $params['salary_min'];
        $salary_max      = $params['salary_max'];
        $skill_id        = $params['skill_id'];
        $career          = $params['career'];
        $salary_currency = $params['salary_currency'];
        $isIT            = in_array($career, [31, 30]) ? true : false;

        if ($is_onboard) {
            if ($salary_currency == 'USD') {
                $salary_max = $salary_max * config('settings.global.exchange_rate');
            }
            $min = $salary_max * 0.7;
            // if ($salary < 15000000) {
            //     $min = $salary * 1.6;
            // } elseif ($salary < 35000000) {
            //     $min = $salary * 1.8;
            // } else {
            //     $min = $salary * 2;
            // }
            return $min;
        } else {
            if ($isIT) {
                $levelBySkillMainRepository = resolve(LevelBySkillMainRepository::class);
                $data =  $levelBySkillMainRepository->find($params['level']);
            } else {
                $levelByJobTopRepository = resolve(LevelByJobTopRepository::class);
                $data =  $levelByJobTopRepository->find($params['level']);
            }
            if ($data) {
                if ($params['bonus_type'] == 'cv') {
                    return $data->price_cv_min_submit;
                } else {
                    return $data->price_interview_min_submit;
                }
            }
        }
        return 0;
    }
    public function getBonusForCtv($params = [])
    {
        $job_bonus = $params['bonus'];
        // $salary_min = $params['salary_min'];
        $bonus_type = $params['bonus_type'];

        switch ($bonus_type) {
            case 'cv':
                return 0.40 * $job_bonus; // 40% giá bán
            case 'interview':
                return 0.30 * $job_bonus; // 30% giá bán
            case 'onboard':
                if ($job_bonus < 15000000) {
                    return 0.50 * $job_bonus; // 50% lương gross ứng viên
                } elseif ($job_bonus >= 15000000 && $job_bonus < 35000000) {
                    return 0.55 * $job_bonus; // 55% lương gross ứng viên
                } elseif ($job_bonus >= 35000000) {
                    return 0.60 * $job_bonus; // 60% lương gross ứng viên
                }
                break;
            default:
                return 0; // Trường hợp không xác định
        }
    }


    public function cancelInterview($id)
    {
        $submitCv = $this->submitCvRepository->find($id);
        // dd($submitCv);
        $user = auth('client')->user();
        $statusRecruitment = 9;
        $point = 0;
        if ($submitCv->bonus_type == 'interview') {
            //ghi log hoan tien
            $this->submitCvHistoryPaymentRepository->create([
                'user_id'      => $submitCv->employer->id,
                'submit_cv_id' => $submitCv->id,
                'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                'percent'      => 100,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                'type_of_sale' => $submitCv->job->bonus_type,
                'amount'       => $submitCv->bonus_ntd,
                'balance'      => $submitCv->employer->wallet->amount + $submitCv->bonus_ntd,
                'status'       => 3
            ]);
            $bonus = $submitCv->bonus_ntd;
            //update status_payment
            $submitCv->status_payment = 3; //Hoan tien
            $submitCv->save();
            //Cộng point của NTD
            // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
            $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
            $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
            // $submitCv->employer->wallet->save();
        }
        //cv onboard
        if ($submitCv->bonus_type == 'onboard') {

            $submitCvHistoryPaymentData = $this->submitCvHistoryPaymentRepository->finBySubmitTypeStatus($submitCv->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
            //hoàn bao nhiêu point
            $bonus = 0;
            if ($submitCvHistoryPaymentData) {
                foreach ($submitCvHistoryPaymentData as $key => $value) {
                    //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                    $value->status = 1;
                    $value->save();
                    //ghi log hoan tien
                    $this->submitCvHistoryPaymentRepository->create([
                        'user_id'      => $submitCv->employer->id,
                        'submit_cv_id' => $submitCv->id,
                        'type'         => 1,                                                             //0 trừ tiền, 1 hoàn tiền
                        'percent'      => $value->percent,                                               //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                        'type_of_sale' => $submitCv->job->bonus_type,
                        'amount'       => $value->amount,
                        'balance'      => $submitCv->employer->wallet->amount + $value->amount,
                        'status'       => 0
                    ]);
                    $bonus += $value->amount;
                }
                $submitCv->status_payment = 2; //2 => 'Hoàn cọc'
                $submitCv->save();
                //Cộng point của NTD
                // $submitCv->employer->wallet->amount = $submitCv->employer->wallet->amount + $bonus;
                $submitCv->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                $submitCv->employer->wallet->addAmount($bonus, $submitCv, 'Hoàn tiền', 'change_status_recruitment');
                // $submitCv->employer->wallet->save();
            }
        }

        // $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
        $submitCv->employer->notify(new ChangeStatusCancelInterviewToEmployerSubmit($submitCv, $bonus));
        if ($submitCv->authorize > 0) {
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdminSubmit($submitCv, $bonus));
        } else {
            $submitCv->rec->notify(new ChangeStatusCancelInterviewToRecSubmit($submitCv, $bonus));
        }
        $submitCv->update([
            'status' => $statusRecruitment
        ]);
        $this->submitCvHistoryStatusRepository->logStatus($submitCv, auth('client')->user());
        return $submitCv;
    }
}
