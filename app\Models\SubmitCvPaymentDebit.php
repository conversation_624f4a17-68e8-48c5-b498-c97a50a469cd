<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class SubmitCvPaymentDebit extends BaseModel
{
    protected $table = 'submit_cvs_payment_debits';
    protected $fillable = [
        'submit_cv_id',
        'user_id',
        'amount',
        'paid_amount',
        'status',
    ];

    public function getMissingAmountAttribute(){
        return $this->amount - $this->paid_amount > 0 ? $this->amount - $this->paid_amount : 0;
    }
    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }
    public function submitCv()
    {
        return $this->belongsTo(SubmitCv::class, 'submit_cv_id', 'id');
    }
}
