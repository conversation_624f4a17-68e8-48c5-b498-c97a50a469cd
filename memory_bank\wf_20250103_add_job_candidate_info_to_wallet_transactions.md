# Workflow: Thêm thông tin Job và Ứng viên v<PERSON><PERSON> sử Giao dịch Ví

**<PERSON><PERSON><PERSON> thực hiện**: 03/01/2025  
**M<PERSON><PERSON> tiêu**: Hi<PERSON><PERSON> thị thêm title job, tên <PERSON>ng viên, email ứng viên và thông tin cộng tác viên trong bảng "Lịch sử giao dịch ví" của trang tra cứu nhà tuyển dụng

## Các thay đổi đã thực hiện

### 1. Cập nhật Controller

**File**: `app/Http/Controllers/Admin/EmployerLookupController.php`
**Phương thức**: `getWalletTransactions()`

#### Thay đổi chính:

1. **Eager Loading**:

    - Thêm `->with(['object.job', 'object.submitCvMeta', 'object.warehouseCv', 'object.wareHouseCvSelling.wareHouseCv', 'object.user:id,name,referral_define,type'])`
    - Tr<PERSON>h N+1 query problem khi load relationships
    - Thêm eager loading cho user relationship để lấy thông tin cộng tác viên

2. **Logic xử lý Object Types**:

    - **SubmitCv**: Lấy job title từ `job.name`, candidate info từ `submitCvMeta` hoặc `warehouseCv`, thông tin CTV từ `user`
    - **WareHouseCvSellingBuy**: Lấy candidate info từ `wareHouseCvSelling.wareHouseCv`, job title = "Mua CV từ kho"

3. **Response format mới**:
    ```php
    return [
        'id' => $transaction->id,
        'amount' => number_format($transaction->amount, 0, ',', '.'),
        'balance_after' => number_format($transaction->balance_after, 0, ',', '.'),
        'description' => $transaction->note ?? '',
        'job_title' => $jobTitle,           // MỚI
        'candidate_name' => $candidateName, // MỚI
        'candidate_email' => $candidateEmail, // MỚI
        'ctv_name' => $ctvName,             // MỚI - Tên cộng tác viên
        'ctv_code' => $ctvCode,             // MỚI - Mã định danh CTV (referral_define)
        'created_at' => $transaction->created_at ? $transaction->created_at->format('d/m/Y H:i') : '',
        'type' => $transaction->amount >= 0 ? 'Nạp tiền' : 'Trừ tiền'
    ];
    ```

## Cấu trúc dữ liệu liên quan

### WalletTransaction Model

-   Có polymorphic relationship `object()` với các model khác
-   Các object types phổ biến: `SubmitCv`, `WareHouseCvSellingBuy`

### SubmitCv Model

-   `job()` relationship để lấy thông tin công việc
-   `submitCvMeta()` để lấy candidate info (ưu tiên)
-   `warehouseCv()` để lấy candidate info (backup)

### WareHouseCvSellingBuy Model

-   `wareHouseCvSelling.wareHouseCv` để lấy candidate info

## Lợi ích

1. **Thông tin đầy đủ hơn**: Admin có thể xem chi tiết giao dịch liên quan đến job và ứng viên nào
2. **Performance tối ưu**: Sử dụng eager loading tránh N+1 queries
3. **Tương thích ngược**: Không ảnh hưởng đến logic hiện tại, chỉ thêm thông tin mới
4. **Xử lý đa dạng**: Support các object types khác nhau của giao dịch ví

## Testing

Cần test các trường hợp:

-   Giao dịch từ SubmitCv (có submitCvMeta và có CTV)
-   Giao dịch từ SubmitCv (có warehouseCv và có CTV)
-   Giao dịch từ SubmitCv (không có thông tin CTV)
-   Giao dịch từ WareHouseCvSellingBuy
-   Giao dịch không có object liên quan
-   Performance với số lượng giao dịch lớn
-   Hiển thị đúng referral_define (mã CTV) và tên CTV

## Hoàn thành

-   [x] Cập nhật logic trong EmployerLookupController
-   [x] Thêm eager loading để tối ưu performance
-   [x] Xử lý logic cho các object types khác nhau
-   [x] Cập nhật response format với thông tin mới
-   [x] Thêm thông tin cộng tác viên (tên và referral_define)
-   [x] Eager loading cho user relationship
