<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Job;
use App\Models\Company;
use App\Models\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Http\Request;
use Carbon\Carbon;
use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;


/**
 * Class JobCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class JobCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\FetchOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(Job::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/jobs-management');
        CRUD::setEntityNameStrings('công việc', 'công việc');
    }
    protected function fetchCompany()
    {
        // return $this->fetch(\App\Models\Company::class);
        return $this->fetch([
            'model' => \App\Models\Company::class, // required
            // 'searchable_attributes' => ['name'],
            'paginate' => 10, // items to show per page
            'searchOperator' => 'LIKE',
            // 'query' => function ($model) {
            //     return $model->active();
            // } // to filter the results that are returned
        ]);
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Eager load relationships để tránh N+1 query problem
        $this->crud->query = $this->crud->query->with(['company', 'user']);

        // Giới hạn số bản ghi mỗi trang
        $this->crud->setDefaultPageLength(25);
        $this->crud->setPageLengthMenu([10, 25, 50, 100]);

        // Tắt các operations không cần thiết
        $this->crud->denyAccess(['create', 'update', 'delete', 'show']);

        // Tắt tất cả các nút mặc định
        $this->crud->removeAllButtons();        // Thêm nút edit tùy chỉnh link đến trang edit cũ
        $this->crud->addButtonFromView('line', 'job.custom_edit', 'admin.pages.job.list_button', 'beginning');
        // Thêm nút nhân bản
        // $this->crud->addButtonFromView('line', 'job.duplicate', 'admin.pages.job.duplicate_button', 'beginning');

        // Thêm nút "Thêm tin tuyển dụng" ở đầu trang
        $this->crud->addButton('top', 'job.add_job_button', 'view', 'admin.buttons.add_job_button', 'beginning');

        // Thêm widgets thống kê
        Widget::add([
            'type'         => 'div',
            'class'        => 'row',
            'content'      => [
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-primary col-md-12',
                    'value'        => $this->getTotalJobs(),
                    'description'  => 'Tổng số công việc',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Tổng cộng tất cả công việc trong hệ thống',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-success col-md-12',
                    'value'        => $this->getActiveJobs(),
                    'description'  => 'Công việc đang hoạt động',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số công việc đang hoạt động',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-info col-md-12',
                    'value'        => $this->getJobsThisMonth(),
                    'description'  => 'Công việc tháng này',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số công việc được tạo trong tháng này',
                ]
            ]
        ])->to('before_content');

        // Cột ID
        CRUD::column('id')
            ->label('ID')
            ->type('number');

        // Cột tên job
        CRUD::column('name')
            ->label('Tên công việc')
            ->type('custom_html')
            ->value(function ($entry) {
                $name = \Illuminate\Support\Str::limit($entry->name, 50);
                $frontendUrl = '';
                if ($entry->slug) {
                    $frontendUrl = config('constant.url', env('APP_URL')) . '/job/' . $entry->slug;
                }
                $html = '<a href="' . $frontendUrl . '" target="_blank" title="' . e($entry->name) . '">' . e($name) . '</a>';

                // Hiển thị label cho job cao cấp (level = 1)
                if ($entry->level == 1) {
                    $html .= '<br><small class="badge badge-warning mt-1">Cao cấp</small>';
                }

                $html .= $entry->note ? '<br><small class="text-muted mt-1">' . $entry->note . '</small>' : '';
                if ($entry->is_real) {
                    // $html .= '<br><small class="badge badge-success mt-2">Tin thật</small>';
                } else {
                    $html .= '<br><small class="badge badge-danger mt-2">Tin ảo</small>';
                }
                return $html;
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhere('name', 'like', '%' . $searchTerm . '%');
            });


        // Cột công ty
        CRUD::column('company_name')
            ->label('Công ty')
            ->type('custom_html')
            ->value(function ($entry) {
                $html = $entry->company ? $entry->company->name : ($entry->company_name ?? 'N/A');
                return $html;
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhere(function ($q) use ($searchTerm) {
                    $q->whereHas('company', function ($subQ) use ($searchTerm) {
                        $subQ->where('name', 'like', '%' . $searchTerm . '%');
                    });
                });
            });

        // // Cột email nhà tuyển dụng
        // CRUD::column('employer_email')
        //     ->label('Email NTD')
        //     ->type('closure')
        //     ->function(function ($entry) {
        //         return $entry->user ? $entry->user->email : 'N/A';
        //     })
        //     ->searchLogic(function ($query, $column, $searchTerm) {
        //         $query->whereHas('user', function ($q) use ($searchTerm) {
        //             $q->where('email', 'like', '%' . $searchTerm . '%');
        //         });
        //     });

        // // Cột số lượng tuyển
        CRUD::column('vacancies')
            ->label('Số lượng tuyển')
            ->type('number');

        // // Cột trạng thái hoạt động
        CRUD::column('is_active')
            ->label('Trạng thái')
            ->type('custom_html')
            ->value(function ($entry) {
                $status = $entry->is_active ? 'Hoạt động' : 'Không hoạt động';
                $class = $entry->is_active ? 'badge-success' : 'badge-warning';
                $html = '<span class="badge ' . $class . '">' . $status . '</span>';
                $html .= $entry->company ? '<br><small class="badge badge-info mt-1">' . $entry->job_type . '</small>' : '';
                return $html;
            });

        // // Cột trạng thái tuyển dụng
        CRUD::column('status')
            ->label('Trạng thái tuyển')
            ->type('custom_html')
            ->value(function ($entry) {
                $status = '';
                $class = '';
                switch ($entry->status) {
                    case 0:
                        $status = 'Dừng tuyển';
                        $class = 'badge-secondary';
                        break;
                    case 1:
                        $status = 'Đang tuyển';
                        $class = 'badge-success';
                        break;
                    case 2:
                        $status = 'Hết hạn tuyển';
                        $class = 'badge-warning';
                        break;
                    default:
                        $status = 'Không xác định';
                        $class = 'badge-dark';
                }
                return '<span class="badge ' . $class . '">' . $status . '</span>';
            });

        // // Cột hạn nộp
        CRUD::column('expire_at')
            ->label('Hạn nộp')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->expire_at) {
                    try {
                        $date = Carbon::createFromFormat('Y-m-d', $entry->expire_at);
                        return $date->format('d/m/Y');
                    } catch (\Exception $e) {
                        return $entry->expire_at;
                    }
                }
                return 'N/A';
            });

        // // Cột ngày tạo
        CRUD::column('created_at')
            ->label('Ngày tạo')
            ->type('closure')
            ->function(function ($entry) {
                return $entry->created_at ? $entry->created_at->format('d/m/Y H:i') : 'N/A';
            });

        // Tùy chỉnh ordering
        $this->crud->orderBy('id', 'desc');

        // Thêm filters
        $this->setupFilters();
    }

    /**
     * Setup các bộ lọc
     */
    private function setupFilters()
    {
        // Filter trạng thái hoạt động
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_active',
                'label' => 'Trạng thái hoạt động'
            ],
            [
                '1' => 'Hoạt động',
                '0' => 'Không hoạt động'
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_active', $value);
            }
        );

        // Filter trạng thái tuyển dụng
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'status',
                'label' => 'Trạng thái tuyển dụng'
            ],
            [
                '0' => 'Dừng tuyển',
                '1' => 'Đang tuyển',
                '2' => 'Hết hạn tuyển'
            ],
            function ($value) {
                $this->crud->addClause('where', 'status', $value);
            }
        );

        // Filter công ty
        CRUD::addFilter(
            [
                'name'        => 'company_id',
                'type'        => 'select2_ajax',
                'label'       => 'Công ty',
                'placeholder' => 'Chọn công ty',
                'method'      => 'GET',
                'select_attribute' => 'text',
                'select_key'  => 'id',
                'minimum_input_length' => 2,
            ],
            '/admin/ajax/companies',
            function ($value) { // if the filter is active
                $this->crud->addClause('where', 'company_id', $value);
            }
        );

        // Filter nhà tuyển dụng,
        $this->crud->addFilter(
            [
                'type'  => 'select2_ajax',
                'name'  => 'employer_id',
                'label' => 'Nhà tuyển dụng',
                'placeholder' => 'Chọn nhà tuyển dụng',
                'method' => 'GET',
            ],
            '/admin/ajax/employers',
            function ($value) {
                $this->crud->addClause('where', 'employer_id', $value);
            }
        );
        // Filter tin thật/ảo
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_real',
                'label' => 'Tin thật/ảo'
            ],
            [
                '1' => 'Tin thật',
                '0' => 'Tin ảo',
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_real', $value);
            }
        );

        // Filter theo level
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'level',
                'label' => 'Cấp độ'
            ],
            [
                '0' => 'Thường',
                '1' => 'Cao cấp',
            ],
            function ($value) {
                $this->crud->addClause('where', 'level', $value);
            }
        );


        // Filter thời gian tạo
        $this->crud->addFilter(
            [
                'type'  => 'date_range',
                'name'  => 'created_at',
                'label' => 'Thời gian đăng'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from && $dates->to) {
                    $this->crud->addClause('whereBetween', 'created_at', [
                        $dates->from . ' 00:00:00',
                        $dates->to . ' 23:59:59'
                    ]);
                }
            }
        );

        // Filter hạn nộp
        $this->crud->addFilter(
            [
                'type'  => 'date_range',
                'name'  => 'expire_at',
                'label' => 'Hạn nộp'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from && $dates->to) {
                    $this->crud->addClause('whereBetween', 'expire_at', [
                        $dates->from,
                        $dates->to
                    ]);
                }
            }
        );
    }

    /**
     * Helper methods cho thống kê
     */
    private function getTotalJobs()
    {
        return Job::count();
    }

    private function getActiveJobs()
    {
        return Job::where('is_active', 1)->count();
    }

    private function getJobsThisMonth()
    {
        return Job::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }

    /**
     * Tạo nút "Thêm tin tuyển dụng" custom
     */
    public function createJobButton()
    {
        return '<a class="btn btn-primary" href="/admin/job/create" style="margin-bottom: 15px;">
            <i class="la la-plus"></i> Thêm tin tuyển dụng
        </a>';
    }
}
