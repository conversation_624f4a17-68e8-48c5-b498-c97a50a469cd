graph TD
    subgraph "Giai đoạn 1: CTV Gửi CV & Ứng viên <PERSON>c nhận"
        A[Bắt đầu] --> B{CTV Gửi CV};
        B --> C["<div style='text-align:left'><b>Tạo `SubmitCv`</b><br>Controller: `WareHouseSubmitCvController@candidateIntroduction`<br>DB: Tạo record `SubmitCv` với `status` = 1 (Waitingcandidateconfirm).<br>DB: Tạo `confirm_token` duy nhất.</div>"];
        C --> D["<div style='text-align:left'><b>G<PERSON>i Email cho Ứng viên</b><br>Action: Gửi Notification `CandidateConfirmRecruitmentSubmitCv`.</div>"];
        D --> E{Ứng viên tương tác Email?};
        E -- "Bấm Đồng ý" --> F;
        E -- "Bấm Từ chối" --> G;
        E -- "Không tương tác" --> H["<div style='text-align:left'><b>Tự động <PERSON>ủy (Cron Job)</b><br>Logic: Job hàng ngày quét các `SubmitCv` có `status`=1 và `created_at` > 7 ngày.<br>Action: Cập nhật `status` = 2 (CandidateCancelApply).<br>Notify: Gửi email cho CTV, Admin.</div>"];

        F["<div style='text-align:left'><b>Ứng viên Đồng ý</b><br>Service: `SubmitCvService@verifyEmailCandidate`<br>DB: Cập nhật `SubmitCv.status` = 21 (WaitingPayment).<br>DB: Xóa `confirm_token`.<br>Notify: Gửi `CandidateConfirmRecruitmentSubmitCv` cho NTD.</div>"];
        G["<div style='text-align:left'><b>Ứng viên Từ chối</b><br>Service: `SubmitCvService@verifyEmailCandidate`<br>DB: Cập nhật `SubmitCv.status` = 2 (CandidateCancelApply).<br>Notify: Gửi `CandidateRejectRecruitmentRecSubmit` cho CTV.<br>Notify: Gửi `CandidateRejectRecruitmentAdminSubmit` cho Admin.</div>"];
    end

    subgraph "Giai đoạn 2: NTD Thanh toán & Review"
        F --> I["<div style='text-align:left'><b>Chờ NTD Thanh toán</b><br>Trạng thái: `SubmitCv.status` = 21 (WaitingPayment).<br>Action: NTD thực hiện thanh toán qua cổng thanh toán.</div>"];
        I -- "Thanh toán thành công" --> J["<div style='text-align:left'><b>Chờ Xếp lịch Phỏng vấn</b><br>DB: Cập nhật `SubmitCv.status` = 3 (Waiting setup interview).<br><b>Job:</b> Dispatch `CandidateCancelInterviewSubmitCv` với delay 7 ngày.</div>"];
        J --> K{NTD ra quyết định?};
        K -- "Từ chối CV" --> L["<div style='text-align:left'><b>NTD Từ chối</b><br>Controller: `EmployerController@submitCvChangeStatus`<br>DB: Cập nhật `SubmitCv.status` = 22 (RecruiterRejectCV).<br>Action: Hoàn tiền cho NTD.</div>"];
        K -- "Xếp lịch PV" --> M;
        K -- "Không làm gì" --> N["<div style='text-align:left'><b>Tự động Hủy (Job)</b><br>Job: `CandidateCancelInterviewSubmitCv` chạy sau 7 ngày.<br>DB: Cập nhật `SubmitCv.status` = 6 (RecruiterCancelInterview).<br>Action: Hoàn tiền cho NTD.<br>Notify: Gửi email cho các bên.</div>"];
    end

    subgraph "Giai đoạn 3: Phỏng vấn"
        M["<div style='text-align:left'><b>Đã xếp lịch</b><br>Controller: `EmployerController@scheduleInterviewSubmit`<br>DB: Cập nhật `SubmitCv.status` = 7 (Waiting Interview).<br>DB: Tạo record `SubmitCvBook` chứa thông tin lịch PV.<br>Notify: Gửi email thông báo lịch cho CTV & Ứng viên.</div>"];
        M --> O{Kết quả phỏng vấn?};
        O -- "Trượt" --> P["<div style='text-align:left'><b>Phỏng vấn Thất bại</b><br>DB: Cập nhật `SubmitCv.status` = 10 (FailInterview).<br>IF `bonus_type` == 'onboard' THEN Hoàn cọc 10% cho NTD.<br>Notify: Gửi `ChangeStatusFailIInterviewSubmit`.</div>"];
        O -- "Đậu" --> Q["<div style='text-align:left'><b>Phỏng vấn Thành công</b><br>DB: Cập nhật `SubmitCv.status` = 8 (PassInterview).<br>Notify: Gửi `ChangeStatusPassInterviewSubmit`.</div>"];
        Q --> R{Check `bonus_type`};
        R -- "interview" --> S["<div style='text-align:left'><b>Thanh toán cho CTV</b><br><b>Job:</b> Dispatch `PayInterviewSubmit` với delay 24 giờ.</div>"];
        R -- "onboard" --> T["<div style='text-align:left'><b>Chuyển sang Offering</b><br>DB: Cập nhật `SubmitCv.status` = 11 (Offering).</div>"];
    end

    subgraph "Giai đoạn 4: Onboard & Thử việc"
        T --> U["<div style='text-align:left'><b>Chờ Onboard</b><br>Controller: `EmployerController@scheduleOnboardSubmit`<br>DB: Cập nhật `SubmitCv.status` = 13 (Waitingonboard).<br>DB: Tạo record `SubmitCvOnboard`.<br>Notify: Gửi email cho Ứng viên xác nhận lịch onboard.</div>"];
        U --> V{Ứng viên xác nhận Onboard?};
        V -- "Đồng ý" --> W["<div style='text-align:left'><b>Bắt đầu Thử việc</b><br>Service: `SubmitCvService@updateScheduleOnboard`<br>DB: Cập nhật `SubmitCv.status` = 14 (Trialwork).<br><b>Jobs:</b><br>- Dispatch `PayOnboardSubmit`(15%) delay 30 ngày.<br>- Dispatch `PayOnboardSubmit`(10%) delay 45 ngày.<br>- Dispatch `PayOnboardSubmit`(75%) delay 67 ngày.<br>- Dispatch `SuccessRecuitmentSubmit` delay 67 ngày.</div>"];
        V -- "Từ chối" --> X["<div style='text-align:left'><b>Từ chối Offer</b><br>Service: `SubmitCvService@updateScheduleOnboard`<br>DB: Cập nhật `SubmitCv.status` = 12 (RejectOffer).<br><b>Job:</b> Dispatch `DepositRefundRejectOfferSubmit` delay 48 giờ để hoàn cọc cho NTD.</div>"];
        W --> Y{Kết quả thử việc?};
        Y -- "NTD cập nhật Thất bại" --> Z["<div style='text-align:left'><b>Thử việc Thất bại</b><br>DB: Cập nhật `SubmitCv.status` = 17 (Failtrailwork).<br>Action: Hủy các jobs `PayOnboardSubmit` đã tạo.<br>Action: Hoàn tiền cho NTD theo mốc:<br>- 0-30 ngày: hoàn 100%<br>- 31-60 ngày: hoàn 70%<br>- 61-67 ngày: hoàn 50%</div>"];
        Y -- "NTD cập nhật Thành công" --> AA;
        Y -- "NTD không cập nhật" --> BB["<div style='text-align:left'><b>Tự động Thành công (Job)</b><br>Job: `SuccessRecuitmentSubmit` chạy sau 67 ngày.<br>DB: Cập nhật `SubmitCv.status` = 16 (SuccessRecruitment).</div>"];
        AA["<div style='text-align:left'><b>Tuyển dụng Thành công</b><br>DB: Cập nhật `SubmitCv.status` = 16 (SuccessRecruitment).<br>Note: Các jobs `PayOnboardSubmit` tiếp tục chạy theo lịch đã định.</div>"];
    end

    subgraph "Kết thúc Luồng"
        G --> End[Kết thúc];
        H --> End;
        L --> End;
        N --> End;
        P --> End;
        S --> End;
        X --> End;
        Z --> End;
        AA --> End;
        BB --> End;
    end

    style End fill:#f9f,stroke:#333,stroke-width:2px