$(document).ready(function () {

    const renderAction = {
        actionEdit: function (data, type, row, meta) {
            const url = CURRENT_URL + data + '/edit';
            return '<a href="' + url + '" class="action-btns1">' +
                '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
        },
        actionEditSubmitcv: function (data, type, row, meta) {
            var url = URL_SUBMIT_CV;
            url = url.replace(':id', data)
            return '<a href="' + url + '" class="action-btns1">' +
                '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
        },

        actionEditBonus: function (data, type, row, meta) {

            const url = CURRENT_URL + data + '/edit';
            const urlBonus = CURRENT_URL + 'history-bonus/' + data;
            let str = '<a href="' + url + '" class="action-btns1">' +
                '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
            str += '<a href="' + urlBonus + '" class="action-btns1">' +
                '<i class="feather feather-file-text  text-success" title="Lịch sử doanh thu" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
            str += '<a href="javascript:void(0)" class="action-btns1 list-team-user" data-user="' + data + '">' +
                '<i class="feather feather-users  text-success" title="Danh sách team" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';

            return str;
        },
        actionEditPayment: function (data, type, row, meta) {
            let str = '<a href="javascript:void(0)" data-id="' + data + '" class="action-btns1 btn-edit">' +
                '<i class="feather feather-file-text  text-success" title="Lịch sử thanh toán" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
            if (row.status_payment < 1) {
                str += '<button type="button" class="btn btn-primary btn-sm" style="margin-right: 5px" onclick="changeStatusPaymentNtd(' + row.id + ',1);" >Đang thanh toán</button>';
                str += '<button type="button" class="btn btn-success btn-sm" onclick="changeStatusPaymentNtd(' + row.id + ',2);" >Đã thanh toán</button>';
            } else if (row.status_payment == 1) {
                str += '<button type="button" class="btn btn-success btn-sm" onclick="changeStatusPaymentNtd(' + row.id + ',2);" >Đã thanh toán</button>';
            }
            return str;
        },

        renderIsPaymentBonus: function (data, type, row, meta) {
            let html
            html = '';
            if (row.payment_status == 0) {
                html = '<div class="text-center"><button type="button" onclick="changePaymentStatus(' + row.id + ');" data-id="' + row.id + '" class="btn btn-success">Đã thanh toán</button></div>';
            }
            return html;
        },
        actionEditAjax: function (data, type, row, meta) {
            return '<a class="action-btns1 btn-edit" data-id="' + data + '">' +
                '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>'
        },
        actionDetail: function (data, type, row, meta) {
            const url = CURRENT_URL + data;
            return '<a href="' + url + '" class="action-btns1">' +
                '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                '</a>';
        },
        actionEditReportAjax: function (data, type, row, meta) {
            if (row.status == 0) {
                return '<a class="action-btns1 btn-edit" data-id="' + data + '">' +
                    '<i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i>' +
                    '</a>'
            } else {
                return '';
            }
        },
    }

    const renderColumn = {
        renderLogo: function (data, type, row, meta) {
            return '<img src="' + data + '" style="height: 100px; width: 100px" />';
        },
        renderIsActive: function (data, type, row, meta) {
            let html
            if (data == 1) {
                html = '<div class="text-center"><span class="badge badge-success-light">Active</span></div>';
            } else if (data == 0) {
                html = '<div class="text-center"><span class="badge badge-danger-light">Inactive</span></div>';
            } else {
                html = data;
            }
            return html;
        },
        renderIsPaymentStatus: function (data, type, row, meta) {
            let html
            if (data) {
                html = '<div class="text-center">';
                html += '<span class="badge badge-success-light" style="margin-right: 5px">Active</span>';
                if (row.check_bonus) {
                    html += '<span class="badge badge-success-light">Đã thanh toán</span>';
                } else {
                    html += '<span class="badge badge-danger-light">Chưa thanh toán</span>';
                }
                html += '</div>';
            } else {
                html = '<div class="text-center">';
                html += '<span class="badge badge-danger-light" style="margin-right: 5px">Inactive</span>';
                if (row.check_bonus) {
                    html += '<span class="badge badge-success-light">Đã thanh toán</span>';
                } else {
                    html += '<span class="badge badge-danger-light">Chưa thanh toán</span>';
                }
                html += '</div>';
            }
            return html;
        },
        renderPaymentStatusBonusValue: function (data, type, row, meta) {
            let html
            if (row.payment_status) {
                html = '<div class="text-center"><span class="badge badge-success">Đã thanh toán</span></div>';
            } else {
                html = '<div class="text-center"><span class="badge badge-dark">Chưa thanh toán</span></div>';
            }
            return html;
        },
        renderAction: function (data, type, row, meta) {
            let button = '';
            let actions = $('[data-action]').data('action').split(',');
            $.each(actions, function (i, v) {
                button += renderAction[v](data, type, row, meta);
            })

            return '<div class="d-flex justify-content-end float-left">' + button + '</div>';
        },
        renderContent: function (data, type, row, meta) {
            return '<div class="render-content-datatable">' + data + '</div>'
        },
        renderUrl: function (data, type, row, meta) {
            return '<a target="_blank" href="' + data + '">' + data + '</a>'
        },
        renderStt: function (data, type, row, meta) {
            let length = meta.settings._iDisplayLength;
            let start = meta.settings._iDisplayStart
            let page = (start / length) + 1;
            let index = meta.row + 1;
            let stt = (page - 1) * length + index;
            return stt;
        },
        renderAuthority: function (data, type, row, meta) {
            let html = '';
            if (data == 1) {
                html += '<button type="button" class="update-authority btn btn-success btn-sm sbm_form" data-toggle="modal" data-target="#normalmodal" data-id="' + row.id + '">Xác nhận</button>';
            }

            html += '<button type="button" class="btn-get-view-token btn btn-primary btn-sm sbm_form" data-toggle="modal" data-target="#getViewTokenModal" data-id="' + row.id + '">Tạo link xem</button>';
            return html;
        },
        renderwith: function (data, type, row, meta) {
            return '<div style="width: 150px">' + data + '</div>';
        },
        renderUserRole: function (data, type, row, meta) {
            let html = '';
            $.each(data, function (i, v) {
                if (i == 0) {
                    html += v.role.name;
                } else {
                    html += ', ' + v.role.name;
                }
            })
            return html;
        },
        renderCategory: function (data, type, row, meta) {
            let str = data.name_vi;

            return str;
        },

        renderStatusPost: function (data, type, row, meta) {
            let str = '';
            if (data == 1) {
                str = 'DRAFT'
            } else if (data == 2) {
                str = 'REVIEW'
            } else {
                str = 'PUBLISHED'
            }
            return str;
        },

        renderCvPublic: function (data, type, row, meta) {
            let html = '';
            if (data) {
                html += '<i class="fa fa-eye" role="button" data-value=' + data + ' data-toggle="tooltip" style="font-size: 20px" title="" data-original-title="fa fa-eye"></i>';
                html += '<i class="ti-import" role="button" style="font-size: 20px; margin-left: 10px;" data-value=' + data + ' data-name=' + row.name_cv_public + ' data-toggle="tooltip" title="" data-original-title="download"></i>';
            }
            return html;
        },

        renderCvPrivate: function (data, type, row, meta) {
            let html = '';
            if (data) {
                html += '<i class="fa fa-eye" role="button" data-value=' + data + ' data-toggle="tooltip" style="font-size: 20px" title="" data-original-title="fa fa-eye"></i>';
                html += '<i class="ti-import" role="button" style="font-size: 20px; margin-left: 10px;" data-value=' + data + ' data-name=' + row.name_cv_private + ' data-toggle="tooltip" title="" data-original-title="download"></i>';
            }
            return html;
        },

        renderJobFileJd: function (data, type, row, meta) {
            let html = '';
            if (data) {
                html += '<i class="fa fa-eye" role="button" data-value=' + data + ' data-toggle="tooltip" style="font-size: 20px" title="" data-original-title="fa fa-eye"></i>';
                html += '<i class="ti-import" role="button" style="font-size: 20px; margin-left: 10px;" data-value=' + data + ' data-name=' + row.job_file_jd + ' data-toggle="tooltip" title="" data-original-title="download"></i>';
            }
            return html;
        },

        renderStatusCv: function (data, type, row, meta) {
            return data;
            if (data != null) {
                data = data.toLowerCase();
            }

            let html = '';
            if (data == 'pending review') {
                html += '<div class="text-center"><span class="badge badge-primary">' + data + '</span></div>'
            } else if (data == 'accepted') {
                html += '<div class="text-center"><span class="badge badge-secondary">' + data + '</span></div>'
            } else if (data == 'rejected') {
                html += '<div class="text-center"><span class="badge badge-success">' + data + '</span></div>'
            } else if (data == 'pass-interview') {
                html += '<div class="text-center"><span class="badge badge-warning">' + data + '</span></div>'
            } else if (data == 'fail-interview') {
                html += '<div class="text-center"><span class="badge badge-info">' + data + '</span></div>'
            } else if (data == 'onboarded') {
                html += '<div class="text-center"><span class="badge badge-dark">' + data + '</span></div>'
            } else if (data == 'cancel') {
                html += '<div class="text-center"><span class="badge badge-danger">' + data + '</span></div>'
            } else if (data == 'offering') {
                html += '<div class="text-center"><span class="badge badge-light">' + data + '</span></div>'
            } else if (data == 'pending confirm') {
                html += '<div class="text-center"><span class="badge badge-primary">' + data + '</span></div>'
            } else if (data == 'admin review') {
                html += '<div class="text-center"><span class="badge badge-primary">' + data + '</span></div>'
            } else if (data == 'admin rejected') {
                html += '<div class="text-center"><span class="badge badge-success">' + data + '</span></div>'
            } else if (data == 'candidate rejected') {
                html += '<div class="text-center"><span class="badge badge-danger">' + data + '</span></div>'
            }
            return html;
        },

        renderStatusUqCv: function (data, type, row, meta) {
            let html = '<div style="display: flex">';
            html += '<button type="button" class="btn btn-sm btn-success" onclick="changeUqStatus(' + row.id + ', 1);" style="margin-right: 5px">Xác nhận</button>';
            html += '<button type="button" class="btn btn-sm btn-danger" onclick="changeUqStatus(' + row.id + ', 2);" style="">Từ chối</button></div>';

            return html;
        },

        renderStatusPaymentValue: function (data, type, row, meta) {
            let html = '';

            if (data == 0) {
                html += '<div class="text-center"><span class="badge badge-secondary">' + row.status_payment_value + '</span></div>'
            } else if (data == 1) {
                html += '<div class="text-center"><span class="badge badge-primary">' + row.status_payment_value + '</span></div>'
            } else if (data == 2) {
                html += '<div class="text-center"><span class="badge badge-success">' + row.status_payment_value + '</span></div>'
            }

            return html;
        },

        renderVerifyStatus: function (data, type, row, meta) {
            let html = '';
            html += '<div class="text-center"><label class="custom-switch">';
            if (data == 'No') {
                html += '<input type="checkbox" name="custom-switch-checkbox1" class="custom-switch-input" disabled>\n';
            } else {
                html += '<input type="checkbox" name="custom-switch-checkbox1" class="custom-switch-input" disabled checked>\n';
            }
            html += '<span class="custom-switch-indicator custom-switch-indicator-lg"></span>\n';
            html += '</label></div>';
            return html;
        },

        renderNameJob: function (data, type, row, meta) {
            let html = '';
            html += '<a href=' + row.full_path_slug + ' target="_blank">' + data + '</a>'
            return html;
        },

        renderPostPosition: function (data, type, row, meta) {
            return data ? data : '';
        },

        renderCompanyName: function (data, type, row, meta) {
            if (data) {
                return data;
            }
            return '';
        },

        renderEmployer: function (data, type, row, meta) {
            if (data) {
                return data;
            }
            return '';
        },

        renderStatusWareHouseCvSelling: function (data, type, row, meta) {
            let html = '';
            if (row.authority === 3) {
                return html;
            }
            if (row.status === 0) {
                html = '<span class="text-success">' + data + '</span>';
            }
            if (row.status === 1) {
                html = '<span class="text-danger">' + data + '</span>';
            }
            if (row.status === 2) {
                html = '<span class="text-primary">' + data + '</span>';
            }

            let type_of_sale_class = 'warning';
            if (row.type_of_sale === 'onboard') {
                type_of_sale_class = 'success';
            } else if (row.type_of_sale === 'interview') {
                type_of_sale_class = 'primary';
            }

            if (typeof row.type_of_sale !== 'undefined') {
                html += '<br><span class="badge badge-' + type_of_sale_class + '">' + row.type_of_sale + '</span>';
            }
            return html;
        },

        renderAuthorityWareHouseCvSelling: function (data, type, row, meta) {
            let html = '';
            if (row.authority === 0) {
                html = '<span class="text-secondary">' + data + '</span>';
            }
            if (row.authority === 1) {
                html = '<span class="text-primary">' + data + '</span>';
            }
            if (row.authority === 2) {
                html = '<span class="text-success">' + data + '</span>';
            }
            if (row.authority === 3) {
                html = '<span class="text-danger">' + data + '</span>';
            }
            return html;
        },

        renderStatusPaymentCtv: function (data, type, row, meta) {
            if (data == 5) {
                return '<span class="badge badge-secondary">Đã thanh toán 15% CTV</span>'
            } else if (data == 6) {
                return '<span class="badge badge-warning">Đã thanh toán 10% CTV</span>'
            } else if (data == 7) {
                return '<span class="badge badge-info">Đã thanh toán 75% CTV</span>'
            } else if (data == 8) {
                return '<span class="badge badge-success">Hoàn tất thanh toán</span>';
            } else {
                return '<span class="badge badge-danger">Chưa thanh toán</span>';
            }
        },

        renderStatusReport: function (data, type, row, meta) {
            if (data == 0) {
                return '<span class="badge badge-secondary">Chưa xử lý</span>'
            } else if (data == 1) {
                return '<span class="badge badge-success">Đã xử lý</span>'
            } else {
                return '<span class="badge badge-danger">Từ chối xử lý</span>'
            }
            // let html = `
            //     <select class="form-control change-status" data-id="${row.id}">
            //         <option value="0" ${data == 0 ? 'selected' : ''} ${data != 0 ? `disabled="disabled"` : '' }>Chưa xử lý</option>
            //         <option value="1" ${data == 1 ? 'selected' : ''} ${data != 0 ? `disabled="disabled"` : '' }>Đã xử lý</option>
            //         <option value="2" ${data == 2 ? 'selected' : ''} ${data != 0 ? `disabled="disabled"` : '' }>Từ chối xử lý</option>
            //     </select>
            // `
            // return html
        },
        renderJobStatus: function (data, type, row, meta) {
            if (data == 0) {
                return '<span class="badge badge-secondary">Dừng tuyển</span>'
            } else if (data == 1) {
                return '<span class="badge badge-success">Đang tuyển</span>'
            } else {
                return '<span class="badge badge-warning">Hết hạn tuyển</span>'
            }
        },

        renderFileReport: function (data, type, row, meta) {
            if (data) {
                return '<img class="blank_image" src="' + data + '" style="height: 100px; width: 100px" />';
            } else {
                return ''
            }
        },

    }


    if ($("#datatable").length > 0) {
        let table = $("#datatable");
        let url = table.data('url');
        let th = table.find('thead tr th');
        let columns = [];
        th.each(function (i, v) {
            let data = $(v);
            let column = {};
            if (data.data('mdata')) {
                column.data = data.data('mdata');
            }
            if (data.data('fn')) {
                let fn = data.data('fn');
                column.render = renderColumn[fn];
            }
            if (typeof data.data('orderable') !== 'undefined' && data.data('orderable') === true) {
                column.orderable = true;
            } else {
                column.orderable = false;
            }
            columns.push(column);
        })


        const dataTableObject = {
            processing: true,
            serverSide: true,
            searchDelay: 1000,
            ajax: url,
            columns: columns,
            aaSorting: [],
            language: {
                searchPlaceholder: "Search ..",
                sSearch: ""
            },
            search: {
                return: true
            },
            initComplete: function () {
                $(this.api().table().container()).find('input').parent().wrap('<form>').parent().attr('autocomplete', 'off');
            },
        }

        let datatable = table.DataTable(dataTableObject);


    }

    $("#filter-datatable").submit(function (e) {
        e.preventDefault();
        let url = $(this).attr('action') + '?' + $(this).serialize();
        let table = $('#datatable').DataTable();
        table.ajax.url(url).load();
    })


});

