<?php

namespace App\Services\Admin;


use App\Repositories\SubmitCvPaymentDebitRepository;

class SubmitCvPaymentDebitService
{
    protected $submitCvPaymentDebitRepository;

    public function __construct(SubmitCvPaymentDebitRepository $submitCvPaymentDebitRepository){
        $this->submitCvPaymentDebitRepository = $submitCvPaymentDebitRepository;
    }
    public function getDataComplain($params)
    {
        return $this->submitCvPaymentDebitRepository->getBySubmitId($params['id']);
    }
}
