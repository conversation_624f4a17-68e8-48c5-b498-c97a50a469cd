@extends('admin.layouts.app')

@section('content')
<!--Page header-->
<div class="page-header d-xl-flex d-block">
    <div class="page-leftheader">
        <h4 class="page-title">Danh sách giao dịch</h4>
    </div>
</div>
<!--End Page header-->

<!-- Summary widgets -->
<div class="row">
    <div class="col-lg-6 col-md-6 col-sm-12 col-xl-6">
        <div class="card overflow-hidden">
            <div class="card-body">
                <div class="d-flex">
                    <div class="mt-2">
                        <h6 class="">Tổng tiền đã nhận</h6>
                        <h2 class="mb-0 number-font text-success">{{ number_format($summary['total_income'], 0, ',',
                            '.') }} đ</h2>
                    </div>
                    <div class="ms-auto">
                        <div class="chart-wrapper mt-1">
                            <span class="badge bg-success-transparent text-success">
                                <i class="fa fa-arrow-up"></i> Thu nhập
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-md-6 col-sm-12 col-xl-6">
        <div class="card overflow-hidden">
            <div class="card-body">
                <div class="d-flex">
                    <div class="mt-2">
                        <h6 class="">Tổng tiền đã chi</h6>
                        <h2 class="mb-0 number-font text-danger">{{ number_format($summary['total_expense'], 0, ',',
                            '.') }} đ</h2>
                    </div>
                    <div class="ms-auto">
                        <div class="chart-wrapper mt-1">
                            <span class="badge bg-danger-transparent text-danger">
                                <i class="fa fa-arrow-down"></i> Chi tiêu
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter form -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Bộ lọc</h3>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ route('transaction-list.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Từ ngày</label>
                                <input type="date" name="date_from" class="form-control"
                                    value="{{ request('date_from') }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Đến ngày</label>
                                <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Admin quản lý</label>
                                <select name="admin_id" class="form-control">
                                    @foreach($admins as $id => $name)
                                    <option value="{{ $id }}" {{ request('admin_id')==$id ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Loại tài khoản</label>
                                <select name="user_type" class="form-control">
                                    <option value="">-- Tất cả --</option>
                                    <option value="employer" {{ request('user_type')=='employer' ? 'selected' : '' }}>
                                        Nhà tuyển dụng</option>
                                    <option value="rec" {{ request('user_type')=='rec' ? 'selected' : '' }}>Cộng tác
                                        viên</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Công ty</label>
                                <select name="company_id" class="form-control" id="company-select">
                                    <option value="">-- Tất cả --</option>
                                    @if(request('company_id'))
                                    @php
                                    $selectedCompany = \App\Models\Company::find(request('company_id'));
                                    @endphp
                                    @if($selectedCompany)
                                    <option value="{{ $selectedCompany->id }}" selected>
                                        {{ $selectedCompany->name }} - {{ $selectedCompany->mst }}
                                    </option>
                                    @endif
                                    @endif
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Loại giao dịch</label>
                                <select name="transaction_type" class="form-control">
                                    <option value="">-- Tất cả --</option>
                                    <option value="income" {{ request('transaction_type')=='income' ? 'selected' : ''
                                        }}>Tiền vào</option>
                                    <option value="expense" {{ request('transaction_type')=='expense' ? 'selected' : ''
                                        }}>Tiền ra</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-flex">
                                    <button type="submit" class="btn btn-primary me-2">
                                        <i class="fa fa-filter"></i> Lọc
                                    </button>
                                    <a href="{{ route('transaction-list.index') }}" class="btn btn-secondary ml-2">
                                        <i class="fa fa-refresh"></i> Làm mới
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transaction table -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Danh sách giao dịch ({{ $transactions->total() }} kết quả)</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered text-nowrap border-bottom">
                        <thead>
                            <tr>
                                <th>STT</th>
                                <th>Thời gian</th>
                                <th>Người dùng</th>
                                <th>Admin quản lý</th>
                                <th>Thông tin ứng tuyển</th>
                                <th>Số tiền</th>
                                <th>Số dư sau GD</th>
                                <th>Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($transactions as $index => $transaction)
                            <tr>
                                <td>{{ $transactions->firstItem() + $index }}</td>
                                <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $transaction->user_name }} </strong>
                                        @switch($transaction->user_type)
                                        @case('employer')
                                        <span class="badge bg-info">Nhà tuyển dụng</span>
                                        @break
                                        @case('rec')
                                        <span class="badge bg-success">Cộng tác viên</span>
                                        @break
                                        @default
                                        <span class="badge bg-secondary">{{ $transaction->user_type }}</span>
                                        @endswitch<br>
                                        @if($transaction->wallet->user->company)
                                        <div>
                                            <strong>{{ $transaction->wallet->user->company->name }}</strong>
                                        </div>
                                        @else
                                        <span class="text-muted">Chưa có công ty</span>
                                        @endif
                                        <small class="text-muted">{{ $transaction->user_email }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($transaction->wallet->user->company &&
                                    $transaction->wallet->user->company->admin)
                                    <span class="badge bg-primary">{{ $transaction->wallet->user->company->admin->name
                                        }}</span>
                                    @else
                                    <span class="text-muted">Chưa có admin</span>
                                    @endif
                                </td>
                                <td>
                                    @if($transaction->job_info['job_name'])
                                    <div>
                                        <strong>Job:</strong> {{ $transaction->job_info['job_name'] }}<br>
                                        @if($transaction->job_info['candidate_name'])
                                        <strong>Ứng viên:</strong> {{ $transaction->job_info['candidate_name'] }}<br>
                                        <small class="text-muted">{{ $transaction->job_info['candidate_email']
                                            }}</small><br>
                                        @endif
                                        @if($transaction->job_info['status'])
                                        <span class="badge bg-info">{{ $transaction->job_info['status'] }}</span>
                                        @endif
                                    </div>
                                    @else
                                    <span class="text-muted">Không có thông tin ứng tuyển</span>
                                    @endif
                                </td>
                                <td>
                                    @if($transaction->amount > 0)
                                    <span class="text-success">+{{ number_format($transaction->amount, 0, ',', '.') }}
                                        đ</span>
                                    @else
                                    <span class="text-danger">{{ number_format($transaction->amount, 0, ',', '.') }}
                                        đ</span>
                                    @endif
                                </td>
                                <td>{{ number_format($transaction->balance_after, 0, ',', '.') }} đ</td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;"
                                        title="{{ $transaction->note }}">
                                        {{ $transaction->note }}
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">Không có giao dịch nào</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-3">
                    {{ $transactions->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<style>
    .bg-primary {
        color: #fff;
    }

    .bg-info {
        color: #fff;
    }
</style>
<script>
    $(document).ready(function() {
    // Initialize Select2 for company select
    $('#company-select').select2({
        placeholder: '-- Tất cả --',
        allowClear: true,
        ajax: {
            url: '{{ route("transaction-list.search-companies") }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term
                };
            },
            processResults: function (data) {
                return {
                    results: data
                };
            },
            cache: true
        },
        minimumInputLength: 1
    });
});
</script>
@endsection