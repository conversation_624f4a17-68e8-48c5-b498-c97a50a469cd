<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_discuss', function (Blueprint $table) {
            $table->id()->comment('Chỉ có NTD đã mua thì mới dc thảo luận với CTV đã tạo CV');
            // $table->integer('company_id')->comment('Company')->nullable();
            $table->integer('ntd_id')->comment('NTD')->nullable();
            $table->integer('ctv_id')->comment('CTV')->nullable();
            $table->integer('submit_cvs_id')->comment('join sang submit_cvs->id');
            $table->string('comment');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_discuss');
    }
};
