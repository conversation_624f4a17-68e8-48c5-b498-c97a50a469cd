<?php

namespace App\Http\Resources\Frontend\Api;

use Illuminate\Http\Resources\Json\ResourceCollection;

class SubmitCvCollection extends ResourceCollection
{
    public function toArray($request)
    {
        return [
            'data' => $this->collection,
            'total' => $this->total(),
            'current_page' => $this->currentPage(),
            'last_page' => $this->lastPage(),
            'per_page' => $this->perPage(),
        ];
    }
} 