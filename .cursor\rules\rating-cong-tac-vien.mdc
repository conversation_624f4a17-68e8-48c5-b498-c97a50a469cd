---
description: 
globs: 
alwaysApply: false
---

# Hệ Thống Đánh Giá Cộng Tác Viên

RecLand cung cấp hệ thống đánh giá cộng tác viên (CTV) nhằm đánh giá chất lượng nhân sự tham gia vào hệ thống.

## Models

- [Collaborator](mdc:app/Models/Collaborator.php) - Thông tin cộng tác viên
- [Rating](mdc:app/Models/Rating.php) - <PERSON><PERSON>h giá của nhà tuyển dụng về CTV
- [Performance](mdc:app/Models/Performance.php) - Chỉ số hiệu suất của CTV

## Controllers

- [CollaboratorController](mdc:app/Http/Controllers/CollaboratorController.php) - Quản lý thông tin CTV
- [RatingController](mdc:app/Http/Controllers/RatingController.php) - <PERSON><PERSON> lý đánh giá CTV
- [Admin/CollaboratorController](mdc:app/Http/Controllers/Admin/CollaboratorController.php) - Quản lý CTV ở phía admin

## Tính năng

1. Nhà tuyển dụng đánh giá CTV sau khi hoàn thành công việc
2. Hệ thống tự động tính điểm hiệu suất dựa trên các tiêu chí
3. Xếp hạng CTV dựa trên điểm đánh giá và hiệu suất
4. Cung cấp báo cáo hiệu suất cho CTV
5. Quản lý hợp đồng và thanh toán cho CTV

## Quy trình đánh giá

1. Nhà tuyển dụng gửi yêu cầu tuyển dụng
2. CTV được phân công hoặc apply vào yêu cầu
3. CTV thực hiện nhiệm vụ tuyển dụng
4. Nhà tuyển dụng đánh giá kết quả
5. Hệ thống cập nhật rating và performance
6. CTV nhận thanh toán theo hiệu suất
