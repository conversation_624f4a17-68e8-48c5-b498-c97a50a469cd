<?php

namespace App\Services\Admin;

use App\Models\WareHouseCvSellingBuy;
use App\Notifications\CandidateRejectRecruitmentEmployer;
use App\Notifications\DepositToEmployer;
use App\Repositories\DepositRepository;
use App\Repositories\InformationContactRepository;
use App\Repositories\PaymentDebitRepository;
use App\Repositories\WalletRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Carbon\Carbon;

class DepositService{

    protected $depositRepository;
    protected $walletRepository;
    protected $paymentDebitRepository;
    protected $wareHouseCvSellingBuyRepository;
    protected $wareHouseCvSellingBuyHistoryStatusRepository;
    protected $wareHouseCvSellingHistoryBuyRepository;

    public function __construct(DepositRepository $depositRepository,
                                WalletRepository $walletRepository,
                                PaymentDebitRepository $paymentDebitRepository,
                                WareHouseCvSellingBuyRepository $wareHouseCvSellingBuyRepository,
                                WareHouseCvSellingHistoryBuyRepository $wareHouseCvSellingHistoryBuyRepository,
                                WareHouseCvSellingBuyHistoryStatusRepository $wareHouseCvSellingBuyHistoryStatusRepository)
    {
        $this->depositRepository = $depositRepository;
        $this->walletRepository = $walletRepository;
        $this->paymentDebitRepository = $paymentDebitRepository;
        $this->wareHouseCvSellingBuyRepository = $wareHouseCvSellingBuyRepository;
        $this->wareHouseCvSellingBuyHistoryStatusRepository = $wareHouseCvSellingBuyHistoryStatusRepository;
        $this->wareHouseCvSellingHistoryBuyRepository = $wareHouseCvSellingHistoryBuyRepository;
    }

    public function datatable($params,$userId)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->depositRepository->getListDepositByUser($params['request'], $userId);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function buildDatatable($employerId)
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'deposit_time',
                ),
                'value' => 'Thời gian'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'amount',
                ),
                'value' => 'Tiền đã nạp (VND)'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'point',
                ),
                'value' => 'Điểm tương ứng'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'balance',
                ),
                'value' => 'Số dư'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'status_value',
                ),
                'value' => 'Trạng thái'
            ),
        );

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('datatable-deposit',['employer' => $employerId]))
            ->setRenderValue($renderValue)
            ->setTitle('Danh sách nạp tiền');
    }

    public function deposit($request){
        $wallet = $this->walletRepository->getWalletUser($request['user_id']);
        $wallet->amount = (int)$request['point'] + $wallet->amount;
        $depositTime = Carbon::createFromFormat('Y-m-d\TH:i',$request['deposit_time']);
        $deposit = $this->depositRepository->create([
            'deposit_time' => $depositTime,
            'amount'       => $request['amount'],
            'point'        => $request['point'],
            'balance'      => $wallet->amount,
            'user_id'      => $request['user_id'],
            'status'       => 1,
        ]);
        $deposit->employer->notify(new DepositToEmployer($deposit,$deposit->employer));
        $debits = $this->paymentDebitRepository->getDebtBuyUserId($request['user_id']);
        if ($debits->count() > 0){
            foreach ($debits as $debit){
//                if ($wallet->amount >= $debit->missing_point){
//                    $lostPoint = $debit->missing_point;
//                }else{
//                    $lostPoint = $wallet->amount;
//                }
//                $debit->paid_point = $debit->paid_point + $lostPoint;
//                if ($debit->paid_point >= $debit->point){
//                    $debit->status = 1;
//                }
//                $debit->save();
//
//                $wallet->amount = $wallet->amount - $lostPoint;
//                if ($wallet->amount <= 0){
//                    break;
//                }
                if ($wallet->amount >= $debit->point){
                    $debit->status = 1;
                    $debit->save();
                    $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($debit->warehouse_cv_selling_buy_id);
                    // update cv selling buy sang đã thanh toán
                    $wareHouseCvSellingBuy->update([
                        'status_payment' => 4
                    ]);

                    $wallet->amount = $wallet->amount - $debit->point;
                    $percent = 90;
                    $this->wareHouseCvSellingHistoryBuyRepository->create([
                        'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                        'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                        'type'                          =>  0,
                        'percent'                       =>  $percent,
                        'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                        'point'                         =>  $debit->point,
                        'balance'                       =>  $wallet->amount,
                        'status'                        =>  0
                    ]);
                }
            }
        }
        $wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wallet->id]);
        $wallet->save();
        return $deposit;
    }


}
