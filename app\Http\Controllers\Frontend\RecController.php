<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\ProfileRequest;
use App\Http\Requests\Frontend\RecChangePasswordRequest;
use App\Http\Requests\Frontend\UpdateBankInfo;
use App\Http\Requests\Frontend\WareHouseCvRequest;
use App\Http\Requests\Frontend\WithdrawRequest;
use App\Http\Resources\WarehouseCvFullResource;
use App\Http\Resources\WarehouseCvResource;
use App\Models\WareHouseCvSellingBuy;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Frontend\JobService;
use App\Services\Frontend\PayinMonthService;
use App\Services\Frontend\PayoutLogService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\SubmitCvDiscussService;
use App\Services\Frontend\WareHouseCvSellingBuyBookService;
use App\Services\Frontend\WareHouseCvSellingBuyDiscussService;
use App\Services\Frontend\WareHouseCvSellingBuyOnboardService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use App\Services\Frontend\WarehouseCvSellingHistoryBuyService;
use App\Services\Frontend\WareHouseCvSellingService;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Services\Admin\BonusService;
use App\Services\Frontend\SubmitCvBookService;
use App\Services\Frontend\SubmitCvOnboardService;
use App\Services\Frontend\SubmitCvService;
use App\Models\SurveyField;
use App\Models\UserSurveyResult;
use App\Services\Frontend\UserJobCareService;

class RecController extends Controller
{

    protected $wareHouseSubmitCvService;
    protected $seoService;
    protected $routeName;
    protected $userService;
    protected $settingService;
    protected $bonusService;
    protected $jobService;
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingBuyDiscussService;
    protected $submitCvDiscussService;
    protected $wareHouseCvSellingBuyBookService;
    protected $wareHouseCvSellingBuyService;
    protected $submitCvBookService;
    protected $submitCvService;
    protected $payinMonthService;
    protected $payoutLogService;
    protected $warehouseCvSellingBuyOnboardService;
    protected $submitCvOnboardService;
    protected $userJobCareService;


    public function __construct(
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        SeoService $seoService,
        SettingService $settingService,
        UserService $userService,
        BonusService $bonusService,
        JobService $jobService,
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvSellingBuyDiscussService $wareHouseCvSellingBuyDiscussService,
        SubmitCvDiscussService $submitCvDiscussService,
        SubmitCvBookService $submitCvBookService,
        WareHouseCvSellingBuyBookService $wareHouseCvSellingBuyBookService,
        WareHouseCvSellingBuyService $wareHouseCvSellingBuyService,
        PayinMonthService $payinMonthService,
        PayoutLogService $payoutLogService,
        SubmitCvService $submitCvService,
        WareHouseCvSellingBuyOnboardService $warehouseCvSellingBuyOnboardService,
        SubmitCvOnboardService $submitCvOnboardService,
        \App\Services\Frontend\UserJobCareService $userJobCareService,
    ) {
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->seoService = $seoService;
        $this->settingService = $settingService;
        $this->userService = $userService;
        $this->bonusService = $bonusService;
        $this->routeName = \Illuminate\Support\Facades\Route::currentRouteName();
        $this->jobService = $jobService;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingBuyDiscussService = $wareHouseCvSellingBuyDiscussService;
        $this->wareHouseCvSellingBuyBookService = $wareHouseCvSellingBuyBookService;
        $this->submitCvDiscussService = $submitCvDiscussService;
        $this->wareHouseCvSellingBuyService = $wareHouseCvSellingBuyService;
        $this->payinMonthService = $payinMonthService;
        $this->submitCvBookService = $submitCvBookService;
        $this->submitCvService = $submitCvService;
        $this->payoutLogService = $payoutLogService;
        $this->warehouseCvSellingBuyOnboardService = $warehouseCvSellingBuyOnboardService;
        $this->submitCvOnboardService = $submitCvOnboardService;
        $this->userJobCareService = $userJobCareService;
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
    }

    public function index()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $totalWareHouseCv = $this->wareHouseSubmitCvService->getTotalWareHouseCvByUserId([auth('client')->id()]);
        $totalSubmitCvService = $this->wareHouseSubmitCvService->countSubmitcvByCondition(['user_id' => auth('client')->id()]);
        //onboarded
        $status = config('constant.submit_cvs_status_value.onboarded');
        $countJobOnboard = $this->wareHouseSubmitCvService->countSubmitcvByCondition(['user_id' => auth('client')->id(), 'status' => $status]);
        $getNewCvs = $this->wareHouseSubmitCvService->getNewCv(auth('client')->id());
        $now = Carbon::now();
        $end = $now->lastOfMonth()->format('d');
        $daysOfMonth = [];
        for ($i = 1; $i <= (int)$end; $i++) {
            $daysOfMonth[] = $i;
        }

        $statistical = $this->wareHouseSubmitCvService->statisticalFrontend(auth('client')->id());

        $str = 'rec_dashboard';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        return view('frontend.pages.collaborator.dashboard', compact(
            'totalWareHouseCv',
            'totalSubmitCvService',
            'countJobOnboard',
            'getNewCvs',
            'end',
            'daysOfMonth',
            'statistical',
            'arrLang',
            'now',
        ));
    }

    /**
     * profile
     */
    public function profile()
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
        $client = auth('client')->user();
        $userInfo = $client->userInfo;

        $str = 'rec_profile';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $referralDefineCode = route('rec-register-from-affi', $client->referral_define);

        return view('frontend.pages.collaborator.profile', [
            'client' => $client,
            'userInfo' => $userInfo,
            'arrLang' => $arrLang,
            'referralDefineCode' => $referralDefineCode,
        ]);
    }

    public function profilePost(ProfileRequest $request)
    {
        if (auth('client')->check()) {
            //ko phai CTV
            if (auth('client')->user()->type != config('constant.role.rec')) {
                Toast::warning(__('frontend/collaborator/message.permission_deny'));
            } else {
                $this->userService->updateProfile($request->all(), auth('client')->user()->id);
                Toast::success(__('message.edit_success'));
            }
        }

        return back();
    }

    public function updateBankInfo(UpdateBankInfo $request)
    {
        if (auth('client')->check()) {
            //ko phai CTV
            if (auth('client')->user()->type != config('constant.role.rec')) {
                Toast::warning(__('frontend/collaborator/message.permission_deny'));
            } else {
                $this->userService->updateBankInfo($request->all(), auth('client')->user()->id);
                Toast::success(__('message.edit_success'));
            }
        }
        return back();
    }

    public function changePassword(RecChangePasswordRequest $request)
    {
        $this->userService->changePasswordById(auth('client')->user()->id, $request->password);
        Toast::success(__('message.change_password_success'));
        return back();
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Giới thiệu ứng viên của CTV sau khi login
     */
    public function submitCv(Request $request)
    {
        $lang = app()->getLocale();
        //get config seo
        $this->seoService->getConfig($this->routeName, $lang);
        $client = auth('client')->user();
        //get list submit_cvs
        $params = $request->all();
        $params['user_id'] = $client->id;
        //                $params['search'] = 'test';
        //                $params['status'] = 1;
        $arrSubmitCv = $this->wareHouseSubmitCvService->getListSubmitCvSearch($params);
        //submit_cvs.status == 0 thì mới hiển thị : HỦy ứng tuyển, Khi hủy ứng tuyển thì status = 6 (cancel)
        $this->generateParamSubmitcvs();


        return view('frontend.pages.collaborator.submitcv', compact('arrSubmitCv'));
    }

    private function generateParamSubmitcvs()
    {
        $lang = app()->getLocale();
        $submitCvsStatus = config('constant.submit_cvs_status');
        unset($submitCvsStatus[10]);
        unset($submitCvsStatus[11]);
        unset($submitCvsStatus[12]);
        unset($submitCvsStatus[7]);
        $submit_cvs_status_value = config('constant.submit_cvs_status_value');
        $currency = config('constant.currency');
        $rank = config('job.rank.' . $lang);
        $career = config('job.career.' . $lang);
        $yearExperience = config('constant.sonamkinhnghiem');
        $str = 'rec_submitcv';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        view()->share([
            'submitCvsStatus' => $submitCvsStatus,
            'submit_cvs_status_value' => $submit_cvs_status_value,
            'currency' => $currency,
            'arrLang' => $arrLang,
            'rank' => $rank,
            'yearExperience' => $yearExperience,
            'career' => $career,
        ]);
    }

    /**
     * @param $id
     *
     * @return \Illuminate\Http\RedirectResponse
     * Hủy ứng tuyển
     */
    public function cancelStatusSubmitCv($id)
    {

        $client = auth('client')->user();
        $checkSubmitCv = $this->wareHouseSubmitCvService->cancelSubmitCvByIdUserStatus($id, $client->id);
        if ($checkSubmitCv) {
            //warehouseCv
            Toast::success(__('frontend/collaborator/message.submit_cv_cancel', ['ungvien' => $checkSubmitCv->warehouseCv->candidate_name]));
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_not_exit'));
        }

        return true;
    }

    /**
     * @param $id
     *
     * @return \Illuminate\Http\RedirectResponse
     * Chi tiết giới thiệu ứng viên
     */
    public function detailSubmitCv($id)
    {
        $client = auth('client')->user();
        $submitCv = $this->wareHouseSubmitCvService->getSubmitCvByIdUserStatus($id, $client->id);
        $this->generateParamSubmitcvs();
        if ($submitCv) {
            $view = view('frontend.pages.collaborator.modal.detailCandidate', compact('submitCv'));
            $view = $view->render();
            return $view;
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_not_exit'));

            return redirect()->route('rec-submitcv');
        }
    }

    /**
     * @param Request $request
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     * Kho CV của CTV sau khi login
     */
    public function warehouseCv(Request $request)
    {

        $lang = app()->getLocale();
        //get config seo
        $this->seoService->getConfig($this->routeName, $lang);
        $str = 'rec-warehousecv';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $client = auth('client')->user();
        //get list warehouse_cvs
        $params = $request->all();
        $params['user_id'] = $client->id;
        $arrWarehouseCv = $this->wareHouseSubmitCvService->getListWarehouseCvSearch($params);
        $skills = $this->jobService->getSkill($request->skill);
        //        $yearExperience = config('constant.sonamkinhnghiem');
        $this->generateWarehouseCvs();
        $this->generateParamSubmitcvs();

        $bonusTypes = config('job.bonus_type.' . $lang);
        return view(
            'frontend.pages.collaborator.warehousecv',
            compact('arrWarehouseCv', 'arrLang', 'skills', 'bonusTypes')
        );
    }

    private function generateWarehouseCvs()
    {
        $lang = app()->getLocale();
        $submit_cvs_status_value = config('constant.submit_cvs_status_value');
        $cities = Common::getCities();
        $yearOfExperience = config('constant.sonamkinhnghiem');
        $candidateStatus = config('constant.thoigiandilamdukien');
        $career = config('job.career.' . $lang);

        view()->share([
            'submit_cvs_status_value' => $submit_cvs_status_value,
            'cities' => $cities,
            'career' => $career,
            'yearOfExperience' => $yearOfExperience,
            'candidateStatus' => $candidateStatus,
            //TODO
        ]);
    }

    //Man hinh Them moi CV
    public function registWarehousecv(Request $request)
    {
        $currency = config('constant.currency');
        $lang = app()->getLocale();
        $cities = Common::getCities();
        $career = config('job.career.' . $lang);
        unset($career[0]);
        $candidateFormwork = config('constant.hinhthuclamviec');
        $candidateEstTimeToWork = config('constant.thoigiandilamdukien');
        $yearExperience = config('constant.sonamkinhnghiem');
        return view(
            'frontend.pages.collaborator.warehousecv_regist',
            compact('currency', 'cities', 'career', 'candidateFormwork', 'candidateEstTimeToWork', 'yearExperience')
        );
    }

    //    public function registWarehousecvPost(WareHouseCvRequest $request)
    public function registWarehousecvPost(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = auth('client')->user()->id;
        $id = $request->id;
        if ($id > 0) {
            $checkWarehousecv = $this->wareHouseSubmitCvService->getWarehousecvByIdUser($id, auth('client')->user()->id);
            if ($checkWarehousecv) {
                $id = $this->wareHouseSubmitCvService->saveWarehouseCvModal($data, $id);
            }
        } else {
            $id = $this->wareHouseSubmitCvService->saveWarehouseCvModal($data);
        }

        if ($id) {
            Toast::success(__('message.edit_success'));
            return back();
        }
    }


    //Man hinh edit CV
    public function editWarehouseCv($id)
    {
        $client = auth('client')->user();
        $currency = config('constant.currency');
        $checkWarehousecv = $this->wareHouseSubmitCvService->getWarehousecvByIdUser($id, $client->id);
        if ($checkWarehousecv) {
            $lang = app()->getLocale();

            $cities = Common::getCities();
            $career = config('job.career.' . $lang);
            unset($career[0]);
            $candidateFormwork = config('constant.hinhthuclamviec');
            $candidateEstTimeToWork = config('constant.thoigiandilamdukien');
            $yearExperience = config('constant.sonamkinhnghiem');

            return view(
                'frontend.pages.collaborator.warehousecv_edit',
                compact(
                    'checkWarehousecv',
                    'currency',
                    'cities',
                    'career',
                    'candidateFormwork',
                    'candidateEstTimeToWork',
                    'yearExperience'
                )
            );
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_not_exit'));

            return redirect()->route('rec-warehousecv');
        }
    }

    public function editWarehouseCvPost($id, WareHouseCvRequest $request)
    {
        $data = $request->all();
        $data['user_id'] = auth('client')->user()->id;
        $checkWarehousecv = $this->wareHouseSubmitCvService->getWarehousecvByIdUser($id, auth('client')->user()->id);
        if ($checkWarehousecv) {
            $id = $this->wareHouseSubmitCvService->saveWarehouseCv($data, $id);
            if ($id) {
                Toast::success(__('message.edit_success'));
                return redirect()->route('rec-warehousecv-edit', ['id' => $id]);
            }
        } else {
            Toast::warning(__('frontend/collaborator/message.submit_cv_not_exit'));

            return redirect()->route('rec-warehousecv');
        }
    }

    public function turnovers(Request $request)
    {
        $request = $request->all();
        $userId = auth('client')->user()->id;
        $request['user_id'] = $userId;
        $bonus = $this->bonusService->indexService($request, [], true);
        $totalTurnoverMonth = $this->bonusService->getTotalTurnoverCurrentMonthByUserId($userId);
        $totalTurnoverYear = $this->bonusService->getTotalTurnoverCurrentYearByUserId($userId);
        $walletUser = $this->userService->getWalletUser($userId);
        $bankUser = auth('client')->user()->userInfo;
        if (!$bankUser) {
            $bankUser = new \StdClass();
            $bankUser->bank_account = "";
            $bankUser->bank_account_number = "";
            $bankUser->bank_name = "";
            $bankUser->bank_branch = "";
        }
        return view('frontend.pages.collaborator.turnovers', compact('bonus', 'totalTurnoverMonth', 'totalTurnoverYear', 'walletUser', 'bankUser'));
    }

    public function teamManager()
    {
        return view('frontend.pages.collaborator.team_manager');
    }


    public function teams(Request $request)
    {
        $data = $this->userService->teams($request->all());
        $countData = $this->userService->countTeams($request->all());

        $currentPage = isset($request->page) ? $request->page : 1;
        $lastPage = (int)ceil($countData / config('constant.limitPaginate'));
        $paginate = [
            'currentPage' => $currentPage,
            'lastPage'    => $lastPage,
        ];
        return view('frontend.pages.collaborator.teams', compact('data', 'countData', 'paginate'));
    }

    public function detailUserTeam($id)
    {
        $user = $this->userService->statisticalUser($id);

        $result = $this->userService->detailUserTeam($id);

        $view = view('frontend.pages.collaborator.detail-teams', compact('result', 'user'));
        $view = $view->render();
        return $view;
    }

    public function searchSkill(Request $request)
    {
        $arr = $this->jobService->getSkill($request->search);
        $response = [];
        if ($arr) {
            foreach ($arr as $key => $value) {
                $response[$key]['id'] = $value;
                $response[$key]['text'] = $value;
            }
        }
        return response()->json($response);
    }

    public function getWarehouseCv($id)
    {
        $client = auth('client')->user();
        $checkWarehousecv = $this->wareHouseSubmitCvService->getWarehousecvByIdUser($id, $client->id);
        return response()->json(new WarehouseCvFullResource($checkWarehousecv));
    }

    public function getWarehouseCvSellingQa($id)
    {
        $client = auth('client')->user();
        $checkWarehousecvQa = $this->wareHouseSubmitCvService->getWarehousecvQa($id, $client->id);

        return response()->json($checkWarehousecvQa);
    }

    public function getWarehouseCvSellingDeleteQa($id)
    {
        $client = auth('client')->user();
        $checkWarehousecvQa = $this->wareHouseSubmitCvService->deleteWarehousecvQa($id, $client->id);

        return response()->json($checkWarehousecvQa);
    }

    public function getWarehouseCvSellingSendQa(Request $request)
    {
        $client = auth('client')->user();
        $send = $this->wareHouseSubmitCvService->sendQa($request->all(), $client);
        $rp['name_ctv'] = $client->name;
        $rp['child_id'] = $send->id;
        $rp['created_at_value'] = $send->created_at->format('d/m/Y H:i');
        $rp['comment'] = $send->comment;

        return response()->json($rp);
    }

    public function getWarehouseCvSellingSendQaComment(Request $request)
    {
        $client = auth('client')->user();
        $send = $this->wareHouseSubmitCvService->comment($request->all(), $client);
        $rp['name_ctv'] = $client->name;
        $rp['created_at_value'] = $send->created_at->format('d/m/Y H:i');
        $rp['comment'] = $send->comment;

        return response()->json($rp);
    }

    public function getCvSelling(Request $request)
    {
        $lang = app()->getLocale();
        $levels = config('constant.level.' . $lang);
        $bonusTypes = config('job.bonus_type.' . $lang);

        $str = 'rec_cv_selling';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $data = $this->wareHouseCvSellingService->getCvSelling($request->all());

        return view('frontend.pages.collaborator.cv-selling', compact('data', 'levels', 'bonusTypes', 'arrLang'));
    }

    public function postCvSelling(Request $request)
    {
        try {
            $this->wareHouseCvSellingService->createCvSelling($request->all());

            $response = [
                'success'   =>  true
            ];

            return response()->json($response);
        } catch (\Exception $e) {
            $response = [
                'success'   =>  false
            ];

            return response()->json($response);
        }
    }

    public function cancelCvSelling($id)
    {
        $client = auth('client')->user();
        $checkCvSelling = $this->wareHouseCvSellingService->cancelCvSelling($id, $client->id);
        if ($checkCvSelling) {
            Toast::success(__('message.cancel_post_success'));
        } else {
            Toast::warning(__('message.cancel_post_fail'));
        }
        return true;
    }

    public function getDetailCvSelling($id)
    {
        try {
            $data = $this->wareHouseCvSellingService->findDetailCvSelling($id);
            $view = view('frontend.pages.collaborator.modal.detail-cv-selling', compact('data'));
            $view = $view->render();
            return $view;
        } catch (\Exception $exception) {
            return response()->json(['error' => $exception->getMessage()], 403);
        }
    }

    public function getCvSold(Request $request)
    {
        $data = $this->wareHouseCvSellingBuyService->getCvSold($request->all());
        return view('frontend.pages.collaborator.cv-sold', ['data' => $data]);
    }

    public function getDetailCvSold($id)
    {
        $data = $this->wareHouseCvSellingBuyService->findDetailCvSold($id);
        if (!$data) return false;
        $view = view('frontend.pages.collaborator.modal.detail-cv-sold', compact('data'));
        $view = $view->render();
        return $view;
    }

    public function getDetailCvSubmit($id)
    {
        $data = $this->wareHouseSubmitCvService->getDetailSubmitCvByRec($id);
        // dd($data);
        if (!$data) return false;
        $view = view('frontend.pages.collaborator.modal.detail-submit-cv', compact('data'));
        $view = $view->render();
        return $view;
    }

    public function discusses($id)
    {
        $data = [
            'discuss' => $this->wareHouseCvSellingBuyDiscussService->getByWarehouseCvBuyId($id),
            'book' => $this->wareHouseCvSellingBuyBookService->getByWarehouseCvBuyId($id),
            'onboard' => $this->warehouseCvSellingBuyOnboardService->getByWarehouseCvBuyId($id),
        ];
        return response()->json($data);
    }

    public function discussesSubmit($id)
    {
        $data = [
            'discuss' => $this->submitCvDiscussService->getBySubmitId($id),
            'book' => $this->submitCvBookService->getBySubmitCvId($id),
            'onboard' => $this->submitCvOnboardService->getBySubmitCvId($id),
            // 'book'    => [],
            // 'onboard' => [],
        ];
        return response()->json($data);
    }

    public function sendDiscusses(Request $request)
    {

        try {
            $data = $this->wareHouseCvSellingBuyDiscussService->recSendDiscuss($request->warehouse_cv_selling_buy_id, $request->comment);
            $response = [
                'success'   =>  true,
                'data' => $data
            ];
            return response()->json($response);
        } catch (\Exception $e) {
            $response = [
                'success'   =>  false
            ];

            return response()->json($response);
        }
    }

    public function sendDiscussesSubmit(Request $request)
    {

        try {
            $data = $this->submitCvDiscussService->recSendDiscuss($request->submit_id, $request->comment);
            $response = [
                'success'   =>  true,
                'data' => $data
            ];
            return response()->json($response);
        } catch (\Exception $e) {
            $response = [
                'success'   =>  false
            ];

            return response()->json($response);
        }
    }

    public function complainStatus(Request $request)
    {
        $message = '';
        //CTV chi co the xác nhận 3, hoặc từ chối 2
        if ($request->status != 2 && $request->status != 3) redirect()->back();
        if ($request->status == 2) {
            $message = 'Từ chối';
        }
        if ($request->status == 3) {
            $message = 'Chấp nhận';
        }

        try {
            $this->wareHouseCvSellingBuyService->recChangeStatus($request->warehouse_cv_selling_buy_id, $request->status);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }


    public function complainStatusSubmit(Request $request)
    {
        $message = '';
        //CTV chi co the xác nhận 3, hoặc từ chối 2
        if ($request->status != 2 && $request->status != 3) redirect()->back();
        if ($request->status == 2) {
            $message = 'Từ chối';
        }
        if ($request->status == 3) {
            $message = 'Chấp nhận';
        }

        try {
            $this->submitCvService->recChangeStatus($request->submit_id, $request->status);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * CTV xác nhận, tù chối lịch phỏng vấn
     */
    public function bookStatus(Request $request)
    {
        $message = '';
        if ($request->status_recruitment == 5) {
            $message = 'Từ chối';
        }
        if ($request->status_recruitment == 7) {
            $message = 'Xác nhận';
        }
        try {
            $this->wareHouseCvSellingBuyService->updateScheduleInterview($request->warehouse_cv_selling_buy_book_id, $request->status_recruitment);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }
    public function bookStatusSubmit(Request $request)
    {
        $message = '';
        if (!in_array($request->status_recruitment, [
            config('constant.status_recruitment_revert.CandidateCancelApply'),
            config('constant.status_recruitment_revert.RejectInterviewschedule'),
            config('constant.status_recruitment_revert.WaitingInterview')
        ])) {
            Toast::error('Không thể thực hiện');
            redirect()->back();
        }
        if ($request->status_recruitment == config('constant.status_recruitment_revert.CandidateCancelApply')) {
            $message = 'Từ chối ứng tuyển';
        }
        if ($request->status_recruitment == config('constant.status_recruitment_revert.RejectInterviewschedule')) {
            $message = 'Yêu cầu đổi lịch phỏng vấn';
        }
        if ($request->status_recruitment == config('constant.status_recruitment_revert.WaitingInterview')) {
            $message = 'Xác nhận lịch phỏng vấn';
        }
        try {
            $this->submitCvBookService->updateScheduleInterview($request->submit_book_id, $request->status_recruitment);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }

    public function onboardStatus(Request $request)
    {
        $message = '';
        if (!in_array($request->status_recruitment, [
            config('constant.status_recruitment_revert.RejectOffer'),
            config('constant.status_recruitment_revert.Waitingonboard'),
        ])) {
            Toast::error('Không thể thực hiện');
            redirect()->back();
        }
        if ($request->status_recruitment == config('constant.status_recruitment_revert.RejectOffer')) {
            $message = 'Từ chối Offer';
        }
        if ($request->status_recruitment == config('constant.status_recruitment_revert.Waitingonboard')) {
            $message = 'Xác nhận lịch Onboard';
        }
        try {
            $this->wareHouseCvSellingBuyService->updateScheduleOnboard($request->warehouse_cv_selling_buy_onboard_id, $request->status_recruitment);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }

    public function onboardStatusSubmit(Request $request)
    {
        $message = '';
        if ($request->status_recruitment == 12) {
            $message = 'Từ chối';
        }
        if ($request->status_recruitment == 13) {
            $message = 'Xác nhận';
        }
        try {
            $this->submitCvService->updateScheduleOnboard($request->submit_onboard_id, $request->status_recruitment);
            Toast::success($message . ' thành công');
        } catch (\Exception $e) {
            Toast::error($message . ' thất bại');
        }
        return redirect()->back();
    }


    public function revenue($month, $year, Request $request)
    {
        $params = $request->all();
        $params['month'] = $month;
        $params['year'] = $year;
        $userId = auth('client')->user()->id;
        $walletUser = $this->userService->getWalletUser($userId);
        $data = $this->payinMonthService->getPayinMonths($params);
        $amountPending = $this->payoutLogService->sumAmountPending();
        $bankUser = auth('client')->user()->userInfo;
        if (!$bankUser) {
            $bankUser = new \StdClass();
            $bankUser->bank_account = "";
            $bankUser->bank_account_number = "";
            $bankUser->bank_name = "";
            $bankUser->bank_branch = "";
        }
        return view('frontend.pages.collaborator.revenue', compact('data', 'month', 'year', 'walletUser', 'amountPending', 'bankUser'));
    }


    public function requestWithdraw(WithdrawRequest $request)
    {
        $data = $this->payoutLogService->requestWithdraw($request->all());
        if ($data) {
            $response = [
                'success' => true,
                'data'  => $data
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }
        return response()->json($response);
    }

    public function historyWithdraw(Request $request)
    {
        $data = $this->payoutLogService->getHistoryWithdraw($request->all());
        $statistical = $this->payoutLogService->statistical();
        $userId = auth('client')->user()->id;
        $walletUser = $this->userService->getWalletUser($userId);
        $bankUser = auth('client')->user()->userInfo;
        if (!$bankUser) {
            $bankUser = new \StdClass();
            $bankUser->bank_account = "";
            $bankUser->bank_account_number = "";
            $bankUser->bank_name = "";
            $bankUser->bank_branch = "";
        }
        return view('frontend.pages.collaborator.history_withdraw', compact('data', 'statistical', 'walletUser', 'bankUser'));
    }

    public function payoutDetail($id)
    {
        $data = $this->payoutLogService->getDetail($id);
        $view = view('frontend.pages.collaborator.detail_payout', compact('data'));
        $view = $view->render();
        return $view;
    }

    public function ajaxUpdateBankInfo(UpdateBankInfo $request)
    {
        $userService = resolve(UserService::class);
        $userService->updateBankInfo($request->all(), auth('client')->user()->id);
        return response()->json(['status' => 'success']);
    }

    /**
     * Upload multiple CV files
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadMultipleFiles(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|mimes:pdf,doc,docx|max:10240', // 10MB max
            ]);

            $file = $request->file('file');
            $user = auth('client')->user();

            // Generate file name
            $fileName = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();

            // Store file in local storage
            $filePath = $file->storeAs('cvs', $fileName, 'public');

            // Create warehouse_cv record with temporary data
            $warehouseCv = new \App\Models\WareHouseCv();
            $warehouseCv->user_id = $user->id;
            $warehouseCv->candidate_name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $warehouseCv->cv_public = 'pending_process::' . $filePath;
            $warehouseCv->candidate_email = '';
            $warehouseCv->candidate_mobile = '';
            $warehouseCv->candidate_job_title = '';
            $warehouseCv->year_experience = 0;
            $warehouseCv->candidate_est_timetowork = 1;
            $warehouseCv->career = '';
            $warehouseCv->main_skill = '';
            // $warehouseCv->status = 'pending_process'; // Trạng thái chờ xử lý
            $warehouseCv->save();

            // Dispatch job to process the CV (sẽ tạo ở bước sau)
            \App\Jobs\ProcessWarehouseCvJob::dispatch($warehouseCv->id);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'warehouse_cv_id' => $warehouseCv->id,
                'file_name' => $file->getClientOriginalName()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function saveSurvey(Request $request)
    {
        try {
            $user = auth('client')->user();
            $fields = $request->input('fields', []);

            // Kiểm tra số lượng lĩnh vực được chọn
            if (count($fields) < 3) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng chọn ít nhất 3 lĩnh vực'
                ]);
            }

            // Lấy các survey field từ slug
            $surveyFields = SurveyField::whereIn('id', $fields)->get();
            // SurveyField::where('user_id', $user->id)->delete();
            // Lưu kết quả khảo sát
            foreach ($surveyFields as $field) {
                UserSurveyResult::create([
                    'user_id' => $user->id,
                    'survey_field_id' => $field->id,
                    'surveyed_at' => now()
                ]);
            }

            // Đánh dấu user đã khảo sát
            $user->meta('da_khao_sat', true);

            return response()->json([
                'success' => true,
                'message' => 'Cảm ơn bạn đã hoàn thành khảo sát!'
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lỗi khi lưu khảo sát: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra, vui lòng thử lại!'
            ]);
        }
    }

    /**
     * Hiển thị danh sách job đã lưu của CTV
     * 
     * @param Request $request
     * @return \Illuminate\Contracts\View\View
     */
    public function savedJobs(Request $request)
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        $client = auth('client')->user();
        $params = $request->all();
        $params['user_id'] = $client->id;

        // Lấy danh sách job đã lưu
        $savedJobs = $this->userJobCareService->getSavedJobs($client->id, $params);

        // Lấy danh sách company để filter
        $companies = \App\Models\Company::select('companies.id', 'companies.name', 'companies.logo')
            ->join('job', 'companies.id', '=', 'job.company_id')
            ->join('user_job_cares', 'job.id', '=', 'user_job_cares.job_id')
            ->where('user_job_cares.user_id', $client->id)
            ->where('user_job_cares.type', 'save')
            ->orderBy('companies.name', 'asc')
            ->groupBy('companies.id', 'companies.name', 'companies.logo')
            ->get();

        // Lấy danh sách career để filter
        $careers = config('job.career.' . $lang);

        // Lấy danh sách trạng thái tuyển tin
        $jobStatuses = [
            1 => 'Đang tuyển',
            0 => 'Dừng tuyển',
            2 => 'Hết hạn tuyển'
        ];

        $str = 'rec_saved_jobs';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        return view('frontend.pages.collaborator.saved-jobs', compact(
            'savedJobs',
            'companies',
            'careers',
            'jobStatuses',
            'arrLang'
        ));
    }
}
