<?php

namespace App\Jobs;

use App\Notifications\ComplaintsAcceptedToEmployer;
use App\Notifications\ComplaintsAcceptedToRec;
use App\Notifications\RecAgreeComplain;
use App\Notifications\RecAgreeComplainEmployer;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
/*
 * sau 7 ngày khieu nai mà CTV hoac Admin không xác nhận, thì tự động update thành đồng ý khiếu nại và Hoàn tiền trả NTD
 */

class RecSumExpiredPoint implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $wareHouseCvSellingBuyId;
    public $statusComplain;

    public function __construct($wareHouseCvSellingBuyId, $statusComplain)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->statusComplain = $statusComplain;
    }

    /**
     * @return void
     * HT NTD
     */
    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingHistoryBuyRepository = app(WareHouseCvSellingHistoryBuyRepository::class);
        $wareHouseCvSellingBuyOnboardRepository = app(WareHouseCvSellingBuyOnboardRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $cvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        if (!empty($cvSellingBuy)) {
            //Trạng thái khiếu nại: NTD 1 khiếu nại, CTV 2 từ chối, CTV 3 xác nhận, Admin 4 xác nhân, Admin 5 từ chối
            //1. sau 7 ngày CTV ko cập nhật thì tự động chuyển thành xác nhận khiếu nại => hoan tien NTD
            //2. sau 7 ngày admin ko cập nhật thì tự động chuyển thành xác nhận khiếu nại => hoan tien NTD
            if (
                ($cvSellingBuy->status_complain == 1 && $this->statusComplain == 1) ||
                ($cvSellingBuy->status_complain == 2 && $this->statusComplain == 2)
            ) {
                try {
                    $cvSellingBuyData = [
                        //1 Neu CTV ko xác nhận, thì tự động udpate CTV xác nhận
                        //2 CTV từ chối, admin chưa xác nhận thì tự động update thành admin đã xác nhận
                        'status_complain' => $cvSellingBuy->status_complain == 1 ? 3 : 4,
                    ];
                    //CTV xac nhan
                    if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv') {
                        $cvSellingBuyData['status_recruitment'] = config('constant.status_recruitment_revert.CancelBuyCVdata');
                    }

                    if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' ||  $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                        $cvSellingBuyData['status_payment'] = 3; //Hoan tien
                    }

                    //                    if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard'){
                    //                        $cvSellingBuyData['status_payment'] = 2;//Hoàn cọc
                    //                    }

                    $cvSellingBuy->update($cvSellingBuyData);
                    $rec = $cvSellingBuy->rec;
                    $employer = $cvSellingBuy->employer;
                    $point = 0;
                    //Hoan tien tra NTD
                    //CV data , interview
                    if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv' || $cvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                        //ghi log mua
                        $wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $cvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $cvSellingBuy->point,
                            'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point,
                            'status'                        =>  0
                        ]);

                        if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'cv') {
                            //interview ko thay đổi trạng thái
                            //Log thay đổi trang thái
                            $wareHouseCvSellingBuyHistoryStatusRepository->create([
                                'user_id'                       =>  $cvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                                'status_recruitment'            =>  config('constant.status_recruitment_revert.CancelBuyCVdata'),
                                'type'                          =>  'employer',
                                'authority'                     =>  $cvSellingBuy->wareHouseCvSelling->authority
                            ]);
                        }

                        //Cộng point của NTD
                        $point = $cvSellingBuy->point;
                        // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $cvSellingBuy->point;
                        $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                        $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Cộng tiền vào ví', 'rec_sum_expired_point');
                        // $cvSellingBuy->employer->wallet->save();
                    }
                    //cv onboard
                    if ($cvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {

                        if ($cvSellingBuy->status_recruitment == 14) {
                            $onboard = $wareHouseCvSellingBuyOnboardRepository->getFirstByWarehouseCvBuyId($cvSellingBuy->id);
                            $createDateTrailWork30 = strtotime('+30 day', strtotime($onboard->date_book));
                            $createDateTrailWork60 = strtotime('+60 day', strtotime($onboard->date_book));
                            $createDateTrailWork67 = strtotime('+67 day', strtotime($onboard->date_book));
                            $dateComplain = \Illuminate\Support\Carbon::createFromFormat('Y-m-d H:i:s', $cvSellingBuy->date_complain);
                            $dateComplain = $dateComplain->format('Y-m-d 23:59:59');
                            $dateComplain =  strtotime($dateComplain);
                            $percent = $point = 0;
                            //* 0-30 ngày trial work mà NTD đổi sang "Fail Trial work" => hoàn 100% số point
                            if ($createDateTrailWork30 >= $dateComplain) {
                                $percent = 100;
                                $point = $cvSellingBuy->point;
                            }
                            //* 31-60 ngày Trial work mà NTD đổi sang  "Fail Trial work"=> hoàn 70% sô point
                            if ($createDateTrailWork30 < $dateComplain && $dateComplain <= $createDateTrailWork60) {
                                $percent = 70;
                                $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                            }
                            //* 61-67 ngày Trial work mà NTD đổi sang "Fail Trial work"=> hoàn 50% số point
                            if ($createDateTrailWork60 < $dateComplain && $dateComplain <= $createDateTrailWork67) {
                                $percent = 50;
                                $point = (int) round(($cvSellingBuy->point * $percent) / 100);
                            }
                            //ghi log hoan tien
                            $wareHouseCvSellingHistoryBuyRepository->create([
                                'user_id'                       =>  $cvSellingBuy->employer->id,
                                'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                                'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                'percent'                       =>  $percent,
                                'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                                'point'                         =>  $point,
                                'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $point,
                                'status'                        =>  0
                            ]);

                            //Cộng point của NTD
                            // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                            $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                            $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Cộng tiền vào ví', 'rec_sum_expired_point');
                            // $cvSellingBuy->employer->wallet->save();
                            $cvSellingBuyData['status_payment'] = 3; //Hoàn cọc
                            $cvSellingBuy->update($cvSellingBuyData);
                        } else {
                            $wareHouseCvSellingHistoryBuyData = $wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($cvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                            //hoàn bao nhiêu point
                            $point = 0;
                            if ($wareHouseCvSellingHistoryBuyData) {
                                foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                                    //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                    $value->status = 1;
                                    $value->save();
                                    //ghi log hoan tien
                                    $wareHouseCvSellingHistoryBuyRepository->create([
                                        'user_id'                       =>  $cvSellingBuy->employer->id,
                                        'warehouse_cv_selling_buy_id'   =>  $cvSellingBuy->id,
                                        'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                        'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                        'type_of_sale'                  =>  $cvSellingBuy->wareHouseCvSelling->type_of_sale,
                                        'point'                         =>  $value->point,
                                        'balance'                       =>  $cvSellingBuy->employer->wallet->amount + $value->point,
                                        'status'                        =>  0
                                    ]);
                                    $point += $value->point;
                                }
                            }
                            //Cộng point của NTD
                            // $cvSellingBuy->employer->wallet->amount = $cvSellingBuy->employer->wallet->amount + $point;
                            $cvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $cvSellingBuy->id]);
                            $cvSellingBuy->employer->wallet->addAmount($point, $cvSellingBuy, 'Cộng tiền vào ví', 'rec_sum_expired_point');
                            // $cvSellingBuy->employer->wallet->save();
                            $cvSellingBuyData['status_payment'] = 2; //Hoàn cọc
                            $cvSellingBuy->update($cvSellingBuyData);
                        }
                    }

                    //gửi email cho CTV
                    if ($cvSellingBuy->status_complain == 4) {
                        $cvSellingBuy->rec->notify(new ComplaintsAcceptedToRec($cvSellingBuy));
                        $cvSellingBuy->employer->notify(new ComplaintsAcceptedToEmployer($cvSellingBuy, $point));
                    }
                    if ($cvSellingBuy->status_complain == 3) {
                        $cvSellingBuy->rec->notify(new RecAgreeComplain($rec, $employer, $cvSellingBuy));
                        $cvSellingBuy->employer->notify(new RecAgreeComplainEmployer($rec, $employer, $cvSellingBuy, $point));
                    }
                } catch (\Exception $e) {
                    Log::info('RecSumPoint error log: ', [
                        'RecSumPoint: ' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}
