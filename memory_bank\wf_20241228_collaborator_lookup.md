# Workflow: <PERSON><PERSON><PERSON> chứ<PERSON> năng Collaborator Lookup

## Ngày: 28/12/2024

## <PERSON><PERSON><PERSON> tiêu

Tạ<PERSON> chức năng tra cứu thông tin cộng tác viên (CTV) tương tự như EmployerLookup hiện có.

## C<PERSON><PERSON> tác vụ cần thực hiện

### 1. Tạo CollaboratorLookupController

-   [x] Tạo controller với các method:
    -   `index()`: Hi<PERSON><PERSON> thị trang tra cứu
    -   `search()`: Tì<PERSON> kiếm CTV theo email
    -   `getCollaboratorInfo()`: <PERSON><PERSON><PERSON> thông tin cơ bản của CTV
    -   `getCollaboratorSubmits()`: <PERSON><PERSON><PERSON> danh sách ứng tuyển của CTV
    -   `getWalletTransactions()`: <PERSON><PERSON><PERSON> lịch sử giao dịch ví
    -   `getReferralInfo()`: <PERSON><PERSON><PERSON> thô<PERSON> tin tài khoản giới thiệu

### 2. Tạo Routes

-   [x] Thêm routes vào `routes/admin.php`
-   [x] Bao gồm: index, search, get-submits

### 3. Tạo Views

-   [x] Tạo view `admin/pages/collaborator-lookup/index.blade.php`
-   [x] Tạo JavaScript cho tính năng tìm kiếm và hiển thị kết quả

### 4. Test Functionality

-   [x] Kiểm tra routes đã được đăng ký
-   [x] Kiểm tra syntax PHP controller
-   [x] Tất cả các routes và controller hoạt động bình thường

### 5. Cập nhật Navigation (nếu cần)

-   [ ] Thêm menu item cho Collaborator Lookup

## Thông tin cần hiển thị

### Thông tin CTV:

-   ID, Email, Tên
-   Ngày tạo tài khoản
-   Lần đăng nhập cuối
-   Tổng số ứng tuyển
-   Số dư ví

### Thông tin tài khoản giới thiệu:

-   Tên người giới thiệu
-   Email người giới thiệu
-   Mã giới thiệu (referral_code)

### Danh sách ứng tuyển:

-   Tên job
-   Tên ứng viên
-   Email ứng viên
-   Trạng thái ứng tuyển
-   Số tiền bonus
-   Ngày ứng tuyển

### Lịch sử giao dịch ví:

-   Số tiền
-   Số dư sau giao dịch
-   Mô tả
-   Ngày giao dịch
-   Loại giao dịch

## Ghi chú

-   Sử dụng cấu trúc tương tự EmployerLookup
-   CTV có type = 'rec' trong bảng users
-   Referral info lấy từ referral_code field

## Kết quả

### Các file đã tạo:

1. **Controller**: `app/Http/Controllers/Admin/CollaboratorLookupController.php`
2. **Routes**: Đã thêm vào `routes/admin.php`
3. **View**: `resources/views/admin/pages/collaborator-lookup/index.blade.php`

### Cách sử dụng:

1. Truy cập: `/admin/collaborator-lookup`
2. Nhập email cộng tác viên
3. Kết quả sẽ hiển thị:
    - Thông tin cơ bản của CTV
    - Thông tin tài khoản giới thiệu
    - Danh sách ứng tuyển
    - Lịch sử giao dịch ví

### Routes đã tạo:

-   `GET /admin/collaborator-lookup` - Hiển thị trang tra cứu
-   `POST /admin/collaborator-lookup/search` - Tìm kiếm CTV theo email

### Trạng thái: ✅ HOÀN THÀNH

## Cập nhật 28/12/2024 - Thêm phân trang và tìm kiếm

### Các thay đổi đã thực hiện:

#### 1. **Controller**: `CollaboratorLookupController.php`

-   [x] Cập nhật `getCollaboratorSubmits()` để giới hạn 10 records cho hiển thị tổng quan
-   [x] Thêm method `getSubmitsPaginated()` để hỗ trợ phân trang và tìm kiếm
-   [x] Tìm kiếm theo `candidate_name` và `candidate_email` trong `submitCvMeta` và `warehouseCv`

#### 2. **Routes**: `routes/admin.php`

-   [x] Thêm route `GET /admin/collaborator-lookup/submits-paginated` cho phân trang

#### 3. **View**: `collaborator-lookup/index.blade.php`

-   [x] Thêm form tìm kiếm riêng cho danh sách ứng tuyển
-   [x] Thêm select box cho số lượng records mỗi trang (10, 20, 50, 100)
-   [x] Thêm nút "Xem tất cả với phân trang"
-   [x] Thêm phần hiển thị thông tin phân trang
-   [x] Thêm thông báo khi hiển thị giới hạn 10 records

#### 4. **JavaScript**:

-   [x] Thêm biến global để theo dõi trạng thái phân trang
-   [x] Thêm function `loadSubmitsPaginated()` để load data có phân trang
-   [x] Thêm function `displaySubmitsTablePaginated()` để hiển thị table với phân trang
-   [x] Thêm function `displayPagination()` để hiển thị pagination UI
-   [x] Thêm event handlers cho tìm kiếm, phân trang, và thay đổi số lượng/trang
-   [x] Thêm tính năng tìm kiếm theo Enter key

### Tính năng mới:

1. **Hiển thị mặc định**: Chỉ hiển thị 10 ứng tuyển gần nhất
2. **Nút "Xem tất cả"**: Chuyển sang chế độ phân trang đầy đủ
3. **Tìm kiếm**: Theo tên hoặc email ứng viên
4. **Phân trang**: Hỗ trợ 10, 20, 50, 100 records/trang
5. **Thống kê**: Hiển thị thông tin "Hiển thị X đến Y của Z kết quả"
6. **Navigation**: Phân trang với nút Previous/Next và số trang

### Trạng thái: ✅ HOÀN THÀNH CẬP NHẬT
