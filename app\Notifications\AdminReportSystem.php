<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class AdminReportSystem extends Mailable
{
    use Queueable;

    protected $report;

    public function __construct($report)
    {
        $this->report = $report;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $name = $this->report->user->name;
        $email = $this->report->user->email;
        $mobile = $this->report->user->mobile;
        $code = $this->report->id;
        $type = $this->report->type_issue_value;
        $description = $this->report->description;
        $image = $this->report->file_url;

        return new Content(
            view: 'email.admin_baocao_hethong',
            with: [
                'name' => $name,
                'email' => $email,
                'mobile' => $mobile,
                'code' => $code,
                'type' => $type,
                'description' => $description,
                'image' => $image,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $message->subject(' [Recland] Báo cáo hệ thống');
        return $this;
    }

}
