<?php

namespace App\Services\Frontend;

use App\Repositories\SeoRepository;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\SEOMeta;

class SeoService
{

    protected $seoRepository;

    public function __construct(SeoRepository $seoRepository)
    {
        $this->seoRepository = $seoRepository;
    }

    public function getConfig($key, $lang, $isDetail = false)
    {
        if (!$isDetail) {
            $dataSeo = $this->seoRepository->findByKeyword($key);
//            dd($dataSeo);
            if (!$dataSeo) {
                //get config seos.php
                $settingConfigs = config('seos.' . $key . '.' . $lang);
                $dataSeo = new \stdClass();
                if ($settingConfigs && count($settingConfigs)) {
                    foreach ($settingConfigs as $k => $item) {
                        if ($k == 'key') {
                            $dataSeo->$k = $item;
                        } else {
                            $kTemp = $k . '_' . $lang;
                            $dataSeo->$kTemp = $item;
                        }
                    }
                } else {
                    $dataSeo = false;
                }
            }
        } else {
            $dataSeo = $key;
        }

        $response = [];
        if (!$dataSeo) {
            return $response;
        }

        if ($lang == config('constant.language.vi')) {
//            $response['id'] = $dataSeo->id;
            $response['key'] = $dataSeo->key;
            $response['title'] = $dataSeo->title_vi;
            $response['description'] = $dataSeo->description_vi;
            $response['keyword'] = $dataSeo->keyword_vi;
            $response['img_thumb'] = isset($dataSeo->img_thumb_vi) ? gen_url_file_s3($dataSeo->img_thumb_vi, '', false) : gen_url_file_s3($dataSeo->image,'', false);
        } else {
//            $response['id'] = $dataSeo->id;
            $response['key'] = $dataSeo->key;
            $response['title'] = $dataSeo->title_en;
            $response['description'] = $dataSeo->description_en;
            $response['keyword'] = $dataSeo->keyword_en;
            $response['img_thumb'] = isset($dataSeo->img_thumb_en) ? gen_url_file_s3($dataSeo->img_thumb_en, '', false) : gen_url_file_s3($dataSeo->image,'', false);
        }

        $this->setSeoConfig($response);

        return $response;
    }

    public function setSeoConfig($dataSeo)
    {

        SEOMeta::setTitle($dataSeo['title']);
        SEOMeta::setDescription($dataSeo['description']);
        SEOMeta::setKeywords($dataSeo['keyword']);
        SEOMeta::addMeta('url',  url()->full());
        SEOMeta::addMeta('language', app()->getLocale());

        OpenGraph::setTitle($dataSeo['title']);
        OpenGraph::setDescription($dataSeo['description']);
        OpenGraph::setUrl(url()->full());
        OpenGraph::addProperty('locale', app()->getLocale());
        OpenGraph::addProperty('type', 'article');
        OpenGraph::addProperty('image', $dataSeo['img_thumb']);
        OpenGraph::addProperty('content', 'text');
    }
}
