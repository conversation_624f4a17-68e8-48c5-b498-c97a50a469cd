<?php

namespace App\Console\Commands;

use App\Services\Admin\JobService;
use Illuminate\Console\Command;

class ExpiredStatusJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:expired-status-job';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check job expired';
    protected $jobService;

    public function __construct(JobService $jobService)
    {
        parent::__construct();
        $this->jobService = $jobService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('Batch Update Expired Job Start');
        try {
            $this->jobService->updateJobExpired();

            \Log::info('Batch Update Expired Job End');
        } catch (\Exception $e) {
            \Log::error('Batch Update Expired Job Error: ' . $e);
        }
    }
}
