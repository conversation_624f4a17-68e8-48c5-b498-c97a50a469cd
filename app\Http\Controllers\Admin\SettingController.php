<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\Admin\SettingService;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SettingController extends Controller
{

    protected $settingService;

    public function __construct(SettingService $settingService)
    {
        $this->settingService = $settingService;
    }

    public function edit(Request $request){
        $data = $this->settingService->getListSetting($request->all());
        return view('admin.pages.setting.edit',compact('data'));
    }

    public function update(Request $request)
    {
        $this->settingService->updateAllService($request->settings);
        Cache::forget('Setting.getOveriteConfig');
        Toast::success(__('message.edit_success'));
        return back();
    }

}
