<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wallet_id')->constrained('wallets')->onDelete('cascade');
            $table->decimal('amount', 15, 2)->comment('Số tiền thay đổi (dương: cộng, âm: trừ)');
            $table->decimal('balance_after', 15, 2)->comment('Số dư sau khi thay đổi');
            $table->nullableMorphs('object');
            $table->string('note')->nullable()->comment('<PERSON>hi chú về giao dịch');
            $table->string('type')->nullable()->comment('Loại giao dịch');
            $table->timestamps();

            $table->index('wallet_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
