<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ComplaintsRejectToEmployer extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $point = $this->wareHouseCvSellingBuy->point;
        $marketCv = route('market-cv');
        $link = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);
        return (new MailMessage)
            ->view('email.complaintsRejectToEmployer', [
                'employerName' => $employerName,
                'companyName' => $employerName,
                'position' => $position,
                'point' => $point,
                'marketCv' => $marketCv,
                'link' => $link,
                'wareHouseCvSellingBuyId' => $this->wareHouseCvSellingBuy->id
            ])
            ->subject('[Recland][Case #'.$this->wareHouseCvSellingBuy->id.'] Kết quả khiếu nại');
    }

}
