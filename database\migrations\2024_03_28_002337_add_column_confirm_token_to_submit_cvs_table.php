<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->string('confirm_token')->comment('Token xac nhan tuyen dung danh cho Ung vien')->nullable();
            $table->text('txt_complain')->nullable()->comment('Nội dung khiếu nại');
            $table->string('img_complain')->nullable()->comment('Hình ảnh khiếu nại');
            $table->integer('count_complain')->default(0);
            $table->dateTime('date_complain')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->dropColumn('confirm_token');
            $table->dropColumn('txt_complain');
            $table->dropColumn('img_complain');
            $table->dropColumn('date_complain');
            $table->dropColumn('count_complain');
        });
    }
};
