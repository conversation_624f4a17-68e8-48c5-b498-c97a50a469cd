<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindPaymentDebit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $warehouseCvSellingBuyDiscuss;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $point = $this->wareHouseCvSellingBuy->point;
        $point = $point - (0.1 * $point);
        $employerWallet = route('employer-wallet',['deposit'=>1]);
        return (new MailMessage)
            ->view('email.remindPaymentDebit', [
                'candidateName' => $candidateName,
                'position' => $position,
                'employerName' => $employerName,
                'point' => $point,
                'employerWallet' => $employerWallet,
            ])
            ->subject('[Recland] Thông báo thanh toán chi phí tuyển dụng ứng viên '.$candidateName);
    }

}
