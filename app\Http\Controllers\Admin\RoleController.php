<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RoleRequest;
use App\Services\Admin\RoleService;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Http\Request;

class RoleController extends Controller
{

    protected $roleService;

    public function __construct(RoleService $roleService)
    {
        $this->roleService = $roleService;
    }

    public function index(){
        $datatable = $this->roleService->buildDatatable();
        return view('admin.pages.role.index',
            compact('datatable'));
    }

    public function datatable(Request $request)
    {
        $data = $this->roleService->datatable($request->all());
        return response($data);
    }

    public function create(){
        return view('admin.pages.role.create');
    }

    public function store(RoleRequest $request){
        $data = $this->roleService->createService($request->except('_token'));
        Toast::success(__('message.edit_success'));
        return redirect()->route('role.edit',['role'=>$data->id]);
    }

    public function edit($id){
        $data = $this->roleService->detailService($id);
        return view('admin.pages.role.edit',compact('data'));
    }

    public function update(RoleRequest $request,$id){
        $data = $this->roleService->updateService($request->except('_token'),$id);
        Toast::success(__('message.edit_success'));
        return back();
    }

}
