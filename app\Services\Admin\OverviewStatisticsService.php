<?php

namespace App\Services\Admin;

use App\Models\Company;
use App\Models\User;
use App\Models\WareHouseCv;
use App\Models\Job;
use Carbon\Carbon;

class OverviewStatisticsService
{
    /**
     * L<PERSON>y thống kê tổng quan theo các bộ lọc
     */
    public function getOverviewStatistics($filters = [])
    {
        $fromDate = $filters['from_date'] ?? null;
        $toDate = $filters['to_date'] ?? null;
        $isReal = $filters['is_real'] ?? null;
        $cvSource = $filters['cv_source'] ?? null;

        // Tạo các khoảng thời gian theo quý
        $quarters = $this->generateQuarters($fromDate, $toDate);
        
        $statistics = [];
        
        foreach ($quarters as $quarter) {
            $quarterData = [
                'period' => $quarter['label'],
                'companies' => $this->getCompaniesCount($quarter['start'], $quarter['end'], $isReal),
                'collaborators' => $this->getCollaboratorsCount($quarter['start'], $quarter['end'], $isReal),
                'users' => $this->getUsersCount($quarter['start'], $quarter['end'], $isReal),
                'total_cv' => $this->getTotalCvCount($quarter['start'], $quarter['end'], $isReal, $cvSource),
                'job_is_running' => $this->getRunningJobsCount($quarter['start'], $quarter['end'], $isReal),
            ];
            
            $statistics[] = $quarterData;
        }

        return $statistics;
    }

    /**
     * Tạo danh sách các quý dựa trên khoảng thời gian
     */
    private function generateQuarters($fromDate = null, $toDate = null)
    {
        $quarters = [];

        if (!$fromDate || !$toDate) {
            // Mặc định lấy 4 quý gần nhất
            $currentYear = Carbon::now()->year;
            $currentQuarter = Carbon::now()->quarter;

            for ($i = 3; $i >= 0; $i--) {
                $quarterNum = $currentQuarter - $i;
                $year = $currentYear;

                if ($quarterNum <= 0) {
                    $quarterNum += 4;
                    $year--;
                }

                $quarters[] = [
                    'label' => "Q{$quarterNum} {$year}",
                    'start' => Carbon::createFromDate($year, ($quarterNum - 1) * 3 + 1, 1)->startOfDay(),
                    'end' => Carbon::createFromDate($year, $quarterNum * 3, 1)->endOfMonth()->endOfDay(),
                ];
            }
        } else {
            // Tạo quý dựa trên khoảng thời gian được chọn
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);

            $quarters[] = [
                'label' => $start->format('d/m/Y') . ' - ' . $end->format('d/m/Y'),
                'start' => $start->startOfDay(),
                'end' => $end->endOfDay(),
            ];
        }

        return $quarters;
    }

    /**
     * Đếm số lượng công ty
     */
    private function getCompaniesCount($startDate, $endDate, $isReal = null)
    {
        $query = Company::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($isReal !== null) {
            $query->where('is_real', $isReal);
        }
        
        return $query->count();
    }

    /**
     * Đếm số lượng cộng tác viên
     */
    private function getCollaboratorsCount($startDate, $endDate, $isReal = null)
    {
        $query = User::where('type', config('constant.role.rec'))
                    ->whereBetween('created_at', [$startDate, $endDate]);

        if ($isReal !== null) {
            $query->where('is_real', $isReal);
        }

        return $query->count();
    }

    /**
     * Đếm số lượng users (tổng cộng)
     */
    private function getUsersCount($startDate, $endDate, $isReal = null)
    {
        $query = User::where('type', config('constant.role.employer'))
            ->whereBetween('created_at', [$startDate, $endDate]);
        
        if ($isReal !== null) {
            $query->where('is_real', $isReal);
        }
        
        return $query->count();
    }

    /**
     * Đếm tổng số CV
     */
    private function getTotalCvCount($startDate, $endDate, $isReal = null, $cvSource = null)
    {
        $query = WareHouseCv::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($isReal !== null) {
            $query->where('is_real', $isReal);
        }
        
        if ($cvSource !== null) {
            if ($cvSource === 'internal') {
                $query->whereNotNull('source');
            } elseif ($cvSource === 'collaborator') {
                $query->whereNull('source');
            }
        }
        // dd($query->toSql(), $query->getBindings());
        return $query->count();
    }

    /**
     * Đếm số lượng job đang chạy
     */
    private function getRunningJobsCount($startDate, $endDate, $isReal = null)
    {
        $query = Job::whereBetween('created_at', [$startDate, $endDate]);
        
        if ($isReal !== null) {
            $query->where('is_real', $isReal);
        }
        
        return $query->count();
    }
}
