<?php

namespace App\Jobs;

use App\Notifications\PaymentInterviewToRec;
use App\Notifications\PaymentOpenTurnCv;
use App\Repositories\BonusRepository;
use App\Repositories\PayinMonthRepository;
use App\Repositories\PayinRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
/*
 * sau 7 ngày ko co khieu nai thi thanh toan cho CTV
 */

class RecSumPointSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    /**
     * @return void
     */
    public function handle()
    {
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        if (!empty($submitCv)) {
            $is_authorize = ($submitCv->authorize == 1 && $submitCv->authorize_status == 1) ? true : false;
            //18 => 'Buy CV data successfull',
            // danh cho type_of_sale = cv && status_complain = 0 khong co khieu nại
            if ($submitCv->status == config('constant.status_recruitment_revert.BuyCVdatasuccessfull') && ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)) {
                try {
                    //Cộng tiền trả CTV
                    //1. payins
                    $bonusRepository = resolve(BonusRepository::class);
                    $payinMonthRepository = resolve(PayinMonthRepository::class);
                    $now = Carbon::now();
                    $year = $now->year;
                    $month = $now->month;
                    $bonusCheck = $bonusRepository->getBonusByUser($submitCv->rec->id, $month, $year);
                    //nếu ủy quyền thì thanh toán 20% price
                    $pricePriority = $submitCv->bonus;
                    if ($is_authorize) $pricePriority = (int) round(($pricePriority * 20) / 100);

                    if (!$bonusCheck) {
                        //nếu tháng/năm chưa có thì insert mới
                        $bonusRepository->create(
                            [
                                'user_id'   => $submitCv->rec->id,
                                'year'      => $year,
                                'month'     => $month,
                                'price'     => $pricePriority,
                            ]
                        );
                    } else {
                        //nếu có doanh thu từ trước thì cộng dồn
                        $bonusCheck->price += $pricePriority;
                        $bonusCheck->save();
                    }
                    //từng giao dịch trong tháng
                    $payinMonthRepository->create(
                        [
                            'user_id'                       => $submitCv->rec->id,
                            'warehouse_cv_selling_buy_id'   => $submitCv->id,
                            'year'                          => $year,
                            'month'                         => $month,
                            'price'                         => $pricePriority,
                            'status'                        => 8 //8 => 'Hoàn tất thanh toán',
                        ]
                    );
                    //2. wallets
                    // $submitCv->rec->wallet->price  = $submitCv->rec->wallet->price + $pricePriority;
                    $submitCv->rec->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $submitCv->id]);
                    $submitCv->rec->wallet->addPrice($pricePriority, $submitCv, 'Cộng tiền vào ví', 'rec_sum_point_submit');
                    // $submitCv->rec->wallet->save();
                    //3. update status_complain =4 để không hoàn tiền lần nữa
                    if ($submitCv->status_complain) {
                        $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                        $submitCv->save();
                    }
                    // $submitCv->rec->notify(new PaymentOpenTurnCv($wareHouseCvSellingBuy,$pricePriority));
                } catch (\Exception $e) {
                    Log::info('RecSumPoint error log: ', [
                        'RecSumPoint: ' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}