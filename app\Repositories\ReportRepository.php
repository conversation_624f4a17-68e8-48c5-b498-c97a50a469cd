<?php

namespace App\Repositories;

use App\Models\Report;

class ReportRepository extends BaseRepository
{
    const MODEL = Report::class;

    public function getListReport($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['search'])) {
            $query->where('id', $params['search']);
        }

        $query->orderBy($order_by, $sort);

        $query->with('user');

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }
}
