<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class PostSeoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
            'seo_title_vi'       => 'required',
            'seo_title_en'       => 'required',
            'seo_description_vi' => 'required',
            'seo_description_en' => 'required',
            'seo_keyword_vi'     => 'required',
            'seo_keyword_en'     => 'required',
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
        ];
    }
}
