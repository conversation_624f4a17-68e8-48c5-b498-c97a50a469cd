<?php

namespace App\Services\Admin;


use App\Repositories\AboutUsAlbumRepository;

class AboutUsAlbumService
{

    protected $aboutUsAlbumRepository;

    public function __construct(AboutUsAlbumRepository $aboutUsAlbumRepository)
    {
        $this->aboutUsAlbumRepository = $aboutUsAlbumRepository;
    }

    public function indexService($params)
    {
        return $this->aboutUsAlbumRepository->getQuery($params);
    }

    public function create($attributes = [])
    {
        return $this->aboutUsAlbumRepository->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function update($id, $options = [], $attributes = [])
    {
        return $this->aboutUsAlbumRepository->update($id, $options, $attributes);

    }

    public function delete($id, $options)
    {
        return $this->aboutUsAlbumRepository->delete($id, $options);
    }
}
