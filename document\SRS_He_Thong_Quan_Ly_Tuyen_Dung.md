
# Tài liệu Đặc tả Yêu cầu <PERSON> mềm (SRS) - Website Quản lý Tuyển dụng Recland

**Phiên bản:** 1.0  
**Ngày:** 2025-07-07

---

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1. <PERSON><PERSON><PERSON> đích

Tài liệu này đặc tả các yêu cầu chức năng và phi chức năng của hệ thống website quản lý tuyển dụng Recland. Mục tiêu của hệ thống là tạo ra một nền tảng kết nối Nhà tuyển dụng (NTD) với các <PERSON>ng viên (UV) tiềm năng thông qua hai luồng nghiệp vụ chính: Mua bán CV và Ứng tuyển CV, đồng thời quản lý các giao dịch tài chính liên quan thông qua hệ thống ví.

### 1.2. <PERSON><PERSON><PERSON> vi

<PERSON>ệ thống là một ứng dụng web, cung cấp các tính năng cho các đối tượng người dùng sau:
*   **Quản trị viên (Admin):** Quản lý toàn bộ hệ thống, người dùng, giao dịch, và cấu hình.
*   **Nhà tuyển dụng (Employer - NTD):** Đăng tin tuyển dụng, tìm kiếm, mua và quản lý CV ứng viên.
*   **Ứng viên (Candidate - UV):** Tạo và quản lý hồ sơ, ứng tuyển vào các công việc.
*   **Cộng tác viên (Collaborator - CTV):** Tìm kiếm và giới thiệu ứng viên cho các tin tuyển dụng để nhận hoa hồng.

### 1.3. Định nghĩa, Thuật ngữ

| Thuật ngữ | Mô tả |
| --- | --- |
| **NTD** | Nhà Tuyển Dụng |
| **UV** | Ứng Viên |
| **CTV** | Cộng Tác Viên |
| **CV Data** | Hình thức dịch vụ mà NTD trả phí để nhận dữ liệu/thông tin liên hệ cơ bản của UV. |
| **Interview** | Hình thức dịch vụ mà NTD trả phí khi UV đồng ý tham gia phỏng vấn. |
| **Onboard** | Hình thức dịch vụ mà NTD trả phí khi UV được tuyển dụng thành công và qua thời gian thử việc. |
| **Ví (Wallet)** | Hệ thống tài khoản tiền tệ của NTD trên website để thực hiện các giao dịch. |
| **Kho CV** | Cơ sở dữ liệu CV của hệ thống, được thu thập từ nhiều nguồn khác nhau. |

---

## 2. Mô tả tổng quan

### 2.1. Bối cảnh sản phẩm

Recland là một nền tảng SaaS (Software as a Service) hoạt động trong lĩnh vực nhân sự. Hệ thống giải quyết bài toán tìm kiếm và tuyển dụng nhân sự chất lượng cho các doanh nghiệp, đồng thời tạo ra một mô hình kinh doanh dựa trên chi phí cho mỗi hành động thành công (cost-per-action) thay vì chi phí đăng tin truyền thống.

### 2.2. Các chức năng chính

*   Quản lý người dùng (Admin, NTD, UV, CTV).
*   Quản lý tin tuyển dụng.
*   Quản lý ví điện tử và giao dịch.
*   Luồng nghiệp vụ Mua bán CV từ Kho CV của hệ thống.
*   Luồng nghiệp vụ Ứng tuyển CV vào các tin tuyển dụng cụ thể.
*   Hệ thống báo cáo, thống kê.

### 2.3. Đối tượng người dùng

| Đối tượng | Mô tả | Quyền hạn chính |
| --- | --- | --- |
| **Admin** | Quản trị viên hệ thống | CRUD tất cả dữ liệu, quản lý cấu hình, duyệt giao dịch, xem báo cáo tổng thể. |
| **NTD** | Doanh nghiệp có nhu cầu tuyển dụng | Đăng tin, quản lý tin tuyển dụng, nạp tiền vào ví, mua CV, quản lý quy trình tuyển dụng. |
| **UV** | Người tìm việc | Tạo/sửa hồ sơ CV, tìm kiếm và ứng tuyển công việc, theo dõi trạng thái ứng tuyển. |
| **CTV** | Người giới thiệu nhân sự | Tìm kiếm ứng viên phù hợp và submit vào các tin tuyển dụng để hưởng hoa hồng theo các mốc (Data, Interview, Onboard). |

---

## 3. Yêu cầu cụ thể

### 3.1. Yêu cầu chức năng

#### 3.1.1. Luồng 1: Mua bán CV (Sourcing từ Kho CV)

Luồng này cho phép NTD chủ động tìm kiếm và mua thông tin ứng viên từ Kho CV có sẵn của Recland.

*   **Mô tả chung:** NTD tìm kiếm trong Kho CV của hệ thống với các bộ lọc (ngành nghề, kỹ năng, kinh nghiệm, cấp bậc). Kết quả ban đầu sẽ ẩn thông tin liên hệ. NTD phải trả phí để xem thông tin chi tiết.

#### 3.1.1.0. Xử lý CV tự động (Backend)

Khi một CV mới được thêm vào hệ thống (ví dụ: thông qua upload hoặc các nguồn khác), một job `ProcessWarehouseCvJob` sẽ được kích hoạt để tự động xử lý CV đó.

*   **Các bước xử lý tự động:**
    1.  **Tìm kiếm CV:** Job sẽ tìm CV dựa trên `warehouseCvId`. Nếu không tìm thấy hoặc đường dẫn file CV không hợp lệ (không có tiền tố `pending_process::`), job sẽ ghi log lỗi và dừng lại.
    2.  **Đọc file CV:** Job đọc nội dung file CV từ đường dẫn cục bộ (storage_path).
    3.  **Gọi API phân tích CV:**
        *   Job gửi file CV đến một API bên ngoài (n8n webhook `cv-parser-with-markdown`) để phân tích nội dung.
        *   API key (`match-cv-api-key`) được lấy từ cấu hình ứng dụng.
    4.  **Lưu trữ dữ liệu CV đã phân tích:**
        *   Nếu API phân tích thành công, dữ liệu JSON trả về sẽ được lưu vào trường `meta` của `WareHouseCv` dưới dạng `cv_parsed_data`.
        *   Các trường thông tin chính của ứng viên (tên, email, số điện thoại, địa chỉ, chức danh, kinh nghiệm, kỹ năng, đánh giá bản thân) sẽ được cập nhật vào các trường tương ứng trong bảng `WareHouseCv` từ dữ liệu đã phân tích.
        *   **Lưu ý:** Trường `career` trong `WareHouseCv` không được cập nhật trong code (đang bị comment).
        *   Nếu có `pdf_url` từ API phân tích, file PDF này sẽ được tải lên S3 làm `cv_public`.
        *   Một phiên bản ẩn danh của CV (`cv_private`) cũng sẽ được tạo ra bằng `HideService` (che giấu thông tin liên hệ) và tải lên S3.
        *   Nếu không có `pdf_url` nhưng có file CV cục bộ đang chờ xử lý (`pending_process::`), file này sẽ được tải lên S3 làm `cv_public`, sau đó một phiên bản ẩn danh (`cv_private`) cũng được tạo và tải lên S3. File cục bộ sẽ bị xóa sau khi upload thành công.
        *   **Lưu ý:** Các trường `status` của `WareHouseCv` (processing, completed) hiện đang bị comment trong code và không được cập nhật.
    5.  **Xử lý lỗi:** Nếu có lỗi trong quá trình xử lý (ví dụ: không tìm thấy CV, lỗi API, lỗi upload S3), job sẽ ghi log lỗi.
        *   **Lưu ý:** Trường `status` của `WareHouseCv` (failed) hiện đang bị comment trong code và không được cập nhật khi job thất bại.
        *   **Lưu ý:** Phương thức `saveAdditionalMetaData` (lưu thông tin chi tiết như học vấn, kinh nghiệm làm việc, dự án, ngôn ngữ, và thông tin bổ sung khác) hiện đang bị comment trong code và không được gọi.

*   **Các dịch vụ hỗ trợ:**
    *   **`FileServiceS3`:** Cung cấp các chức năng để tương tác với Amazon S3 để lưu trữ tệp (tải lên, di chuyển, xóa).
        *   **`getInstance()`:** Phương thức static để lấy thể hiện duy nhất của service (Singleton pattern).
        *   **`uploadToS3($file, $pathFolder)`:** Tải file lên S3. Hỗ trợ upload từ đường dẫn URL (http/https/data:image) hoặc từ đối tượng file (ví dụ: từ request upload). Trả về đường dẫn S3.
        *   **`uploadFromLocalToS3(string $localFilePath, string $pathFolder)`:** Tải file từ đường dẫn cục bộ lên S3. Kiểm tra sự tồn tại của file local trước khi upload.
        *   **`uploadFromStorageToS3(string $storagePath, string $disk, string $pathFolder)`:** Tải file từ Laravel storage disk lên S3. Kiểm tra sự tồn tại của file trong storage disk.
        *   **`uploadToS3FromLink($link, $pathFolder)`:** Tải file từ một URL (link) lên S3.
        *   **`moveFileS3($pathFrom, $pathFolder)`:** Di chuyển file giữa các thư mục trên S3.
        *   **`deleteFileOnS3($paths)`:** Xóa một hoặc nhiều file trên S3.
    *   **`HideService`:** Gửi CV đến một dịch vụ AI bên ngoài để ẩn thông tin nhạy cảm, tạo phiên bản `cv_private`.
        *   **`sendRequest($params, $uri, $method, $headers, $chanel_log)`:** Gửi request đến API bên ngoài để xử lý CV. Nhận các tham số `file` trong `$params`.
        *   **`json2Array($contents)`:** Chuyển đổi chuỗi JSON thành mảng PHP.
        *   **`hideCv($public_cv)`:** Phương thức chính để ẩn thông tin CV. Nhận URL của CV công khai, gửi đến API bên ngoài thông qua `sendRequest`, và trả về URL của CV đã ẩn thông tin.

*   **3.1.1.1. Hình thức "CV Data"**
    1.  NTD thực hiện tìm kiếm và chọn được một hoặc nhiều CV ưng ý.
    2.  NTD chọn mua "CV Data".
    3.  Hệ thống kiểm tra số dư trong ví của NTD.
    4.  Nếu đủ, hệ thống trừ tiền trong ví theo đơn giá đã cấu hình cho dịch vụ "CV Data".
    5.  Hệ thống ghi nhận giao dịch.
    6.  Hệ thống mở khóa thông tin liên hệ (email, SĐT) của UV cho NTD xem.
    7.  **Thanh toán hoa hồng cho CTV (Job `RecSumPoint`):**
        *   Sau khi NTD mua "CV Data" thành công (trạng thái `BuyCVdatasuccessfull`) và không có khiếu nại (`status_complain` là 0 hoặc 5), hệ thống sẽ kích hoạt một job `RecSumPoint` để xử lý việc thanh toán hoa hồng cho CTV.
        *   **Thời điểm thanh toán:** Việc thanh toán này sẽ được thực hiện tự động sau 7 ngày kể từ khi giao dịch mua "CV Data" thành công, với điều kiện không có khiếu nại hoặc khiếu nại đã được giải quyết.
        *   **Tính toán hoa hồng:**
            *   Nếu giao dịch là "ủy quyền" (`authority == 2`), CTV sẽ nhận được 20% giá trị giao dịch.
            *   Trong các trường hợp khác, CTV sẽ nhận được 100% giá trị giao dịch.
        *   **Cập nhật ví CTV:** Số tiền hoa hồng sẽ được cộng trực tiếp vào ví điện tử của CTV.
        *   **Ghi nhận giao dịch:** Hệ thống sẽ ghi nhận giao dịch hoa hồng này vào bảng `payins` (tổng doanh thu theo tháng/năm cho CTV) và `payin_month` (chi tiết từng giao dịch hoa hồng trong tháng).
        *   **Thông báo:** CTV sẽ nhận được thông báo khi hoa hồng được cộng vào ví.
        *   **Ghi audit tag:** Job ghi audit tag trước khi cộng tiền vào ví CTV.

*   **3.1.1.2. Hình thức "Interview"**
    1.  NTD chọn một CV và yêu cầu sắp xếp một cuộc "Phỏng vấn".
    2.  Hệ thống/Admin/CTV liên hệ với UV để xác nhận sự đồng ý phỏng vấn.
    3.  **Nếu UV đồng ý:**
        *   Hệ thống thông báo cho NTD và yêu cầu thanh toán phí "Interview".
        *   Hệ thống trừ tiền trong ví của NTD.
        *   Hệ thống ghi nhận giao dịch và cập nhật trạng thái ứng viên thành "Chờ phỏng vấn".
        *   **Thanh toán hoa hồng cho CTV (Job `PayInterview`):** Hệ thống sẽ kích hoạt một job `PayInterview` để xử lý việc thanh toán hoa hồng cho CTV sau khi một cuộc phỏng vấn được xác nhận. Việc thanh toán này chỉ diễn ra khi trạng thái tuyển dụng của CV (`status_recruitment`) là 8 (có thể là "Phỏng vấn thành công" hoặc tương tự) hoặc 10 (có thể là "Đã phỏng vấn") VÀ trạng thái khiếu nại (`status_complain`) là 0 (không có khiếu nại) hoặc 5 (khiếu nại đã được giải quyết). Job này có thể được lên lịch chạy sau 24 giờ kể từ khi xác nhận phỏng vấn, để đảm bảo không có khiếu nại ngay lập tức.
        *   **Xử lý lịch phỏng vấn quá hạn (Job `OutOfDateBookInterview`):** Nếu một lịch phỏng vấn đã được đặt và không có hành động nào được thực hiện trong 7 ngày, job `OutOfDateBookInterview` sẽ được kích hoạt. Job này sẽ cập nhật trạng thái của `WareHouseCvSellingBuy` từ `5` (Từ chối lịch phỏng vấn) sang `6` (Hủy phỏng vấn). NTD và CTV (hoặc Admin) sẽ nhận được thông báo.
        *   **Xử lý khi ứng viên "Đạt phỏng vấn" (Job `PassInterview`):** Khi NTD cập nhật trạng thái ứng viên thành "Đạt phỏng vấn" (status `8`), job `PassInterview` sẽ được kích hoạt. Job này nhận các tham số `wareHouseCvSellingBuyId` và `user`. Điều kiện để job chạy là trạng thái hiện tại của CV là `7` ("Chờ phỏng vấn") và không có khiếu nại hoặc khiếu nại đã được giải quyết. Nếu loại hình dịch vụ là "Interview", job `PayInterview` sẽ được kích hoạt và trì hoãn 24 giờ để thanh toán hoa hồng cho CTV.
    4.  **Nếu UV từ chối:** Quy trình kết thúc, NTD không bị trừ tiền.

*   **3.1.1.3. Hình thức "Onboard"**
    1.  Sau quá trình phỏng vấn, NTD cập nhật trạng thái UV thành "Trúng tuyển".
    2.  Hệ thống bắt đầu tính thời gian thử việc (ví dụ: 60 ngày, có thể cấu hình).
    3.  Sau khi kết thúc thời gian thử việc, NTD xác nhận UV "Onboard" thành công.
    4.  Hệ thống yêu cầu NTD thanh toán phí "Onboard".
    5.  Hệ thống trừ tiền trong ví của NTD và ghi nhận giao dịch.
    6.  **Logic thanh toán hoa hồng cho CTV (Job `PayOnboard`):** Tương tự như `PayInterview`, hệ thống sẽ kích hoạt một job `PayOnboard` để xử lý việc thanh toán hoa hồng cho CTV khi ứng viên "Onboard" thành công. Việc thanh toán này chỉ diễn ra khi trạng thái tuyển dụng của CV (`status_recruitment`) là 14 (có thể là "Thử việc") hoặc 16 (có thể là "Tuyển dụng thành công") VÀ trạng thái khiếu nại (`status_complain`) là 0 (không có khiếu nại) hoặc 5 (khiếu nại đã được giải quyết). Job này nhận thêm tham số `percent` để tính toán hoa hồng, cho thấy có thể có các mức hoa hồng khác nhau tùy thuộc vào cấu hình.
    7.  **Chuyển trạng thái sang "Thử việc" (Job `ChangeToTrailWork`):** Khi NTD cập nhật trạng thái ứng viên thành "Thử việc" (status `14`), job `ChangeToTrailWork` sẽ được kích hoạt.
        *   **Kiểm tra số dư ví NTD:** Hệ thống sẽ kiểm tra số dư ví của NTD để đảm bảo đủ tiền thanh toán phần còn lại của phí "Onboard" (90% giá trị, vì 10% có thể đã được đặt cọc trước đó).
        *   **Xử lý ghi nợ:** Nếu số dư không đủ, hệ thống sẽ ghi nhận khoản nợ vào bảng `PaymentDebit` cho NTD. NTD sẽ nhận được chuỗi thông báo nhắc nhở thanh toán nợ (email nhắc nhở sau 24 giờ, sau đó lặp lại từ ngày thứ 10 đến ngày thứ 15). Khi NTD nạp đủ tiền, khoản nợ sẽ được tự động thanh toán.
        *   **Trừ tiền từ ví NTD (nếu đủ):** Nếu số dư đủ, hệ thống sẽ trừ 90% giá trị "Onboard" từ ví của NTD. Giao dịch trừ tiền sẽ được ghi lại chi tiết.
        *   **Cập nhật trạng thái và lịch sử:** Trạng thái của `WareHouseCvSellingBuy` sẽ được cập nhật thành `14` ("Thử việc"). Lịch sử thay đổi trạng thái sẽ được ghi lại.
        *   **Thông báo:** Admin sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc (nếu CV thuộc "authority"). CTV sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc.
        *   **Nhắc nhở hết hạn thử việc:** Hệ thống sẽ gửi email nhắc nhở NTD về việc sắp hết hạn thử việc của ứng viên, bắt đầu từ ngày thứ 55 đến ngày thứ 59 của giai đoạn thử việc.
        *   **Tự động chuyển trạng thái "Tuyển dụng thành công":** Nếu NTD không thay đổi trạng thái của ứng viên sau 67 ngày kể từ ngày bắt đầu thử việc, hệ thống sẽ tự động cập nhật trạng thái thành `16` ("Tuyển dụng thành công"). Job `SuccessRecuitment` sẽ được kích hoạt sau 67 ngày.
        *   **Thanh toán hoa hồng cho CTV theo giai đoạn (Onboard):**
            *   **Lần 1 (15%):** Job `PayOnboard` sẽ được kích hoạt sau 30 ngày kể từ ngày bắt đầu thử việc để thanh toán 15% hoa hồng cho CTV.
            *   **Lần 2 (10%):** Job `PayOnboard` sẽ được kích hoạt sau 45 ngày kể từ ngày bắt đầu thử việc để thanh toán 10% hoa hồng cho CTV.
            *   **Lần 3 (75%):** Job `PayOnboard` sẽ được kích hoạt sau 67 ngày kể từ ngày bắt đầu thử việc để thanh toán 75% hoa hồng còn lại cho CTV (khi ứng viên đã ký hợp đồng chính thức).
    8.  **Từ chối Offer/Hủy Onboard (Job `ChangeToRejectOffer`):** Khi một ứng viên bị từ chối sau khi đã nhận offer (hoặc quá trình onboard bị hủy), hệ thống sẽ kích hoạt job `ChangeToRejectOffer`.
        *   Job này sẽ cập nhật trạng thái của `WareHouseCvSellingBuy` thành `12` (có thể là "Từ chối Offer" hoặc "Hủy Onboard").
        *   Trạng thái của bản ghi `onboard` liên quan cũng được cập nhật thành `2` (có thể là "Đã hủy").
        *   Hệ thống ghi lại sự thay đổi trạng thái này.
        *   **Thông báo:** Nhà tuyển dụng (NTD) sẽ nhận được email thông báo về việc từ chối/hủy onboard. Nếu CV được cung cấp thông qua "authority", admin sẽ nhận được thông báo. Nếu không, CTV đã nộp CV sẽ nhận được thông báo.
        *   **Hoàn cọc (Job `DepositRefundRejectOffer`):** Một job `DepositRefundRejectOffer` sẽ được dispatch (kích hoạt) để xử lý việc hoàn cọc cho NTD. Job hoàn cọc này sẽ được trì hoãn (delay) trong 48 giờ, có thể để cho phép thời gian xử lý khiếu nại hoặc xác nhận.

#### 3.1.2. Luồng 2: Ứng tuyển CV (Submit vào Job)

Luồng này xảy ra khi có một tin tuyển dụng cụ thể do NTD đăng tải. UV hoặc CTV sẽ nộp hồ sơ vào tin tuyển dụng này.

*   **Mô tả chung:** NTD đăng tin tuyển dụng. UV/CTV tìm thấy tin và nộp hồ sơ. NTD sẽ trả phí dựa trên các giai đoạn của quy trình tuyển dụng.

*   **3.1.2.1. Hình thức "CV Data"**
    1.  UV/CTV nộp hồ sơ (submit CV) vào một tin tuyển dụng.
    2.  Hệ thống thông báo cho NTD có CV mới.
    3.  NTD vào xem danh sách CV đã ứng tuyển.
    4.  Để xem chi tiết và thông tin liên hệ của CV, NTD phải trả phí "CV Data".
    5.  Hệ thống trừ tiền trong ví NTD, ghi nhận giao dịch.
    6.  Nếu người submit là CTV, hệ thống ghi nhận một khoản hoa hồng nhỏ cho CTV.
    7.  **Thanh toán hoa hồng cho CTV (Job `RecSumPointSubmit`):** Tương tự như luồng "Mua bán CV", sau khi NTD mua "CV Data" thành công và không có khiếu nại, hệ thống sẽ kích hoạt một job `RecSumPointSubmit` để xử lý việc thanh toán hoa hồng cho CTV sau 7 ngày.
        *   **Tính toán hoa hồng:**
            *   Nếu `submitCv->authorize` là 1 và `submitCv->authorize_status` là 1, CTV sẽ nhận được 20% giá trị `submitCv->bonus`.
            *   Trong các trường hợp khác, CTV sẽ nhận được 100% giá trị `submitCv->bonus`.
        *   **Cập nhật ví CTV:** Số tiền hoa hồng sẽ được cộng trực tiếp vào ví điện tử của CTV.
        *   **Ghi nhận giao dịch:** Hệ thống sẽ ghi nhận giao dịch hoa hồng này vào bảng `payins` (tổng doanh thu theo tháng/năm cho CTV) và `payin_month` (chi tiết từng giao dịch hoa hồng trong tháng).
        *   **Thông báo:** Notification `PaymentOpenTurnCv` (gửi cho CTV) hiện đang bị comment trong code và không được gửi.
        *   **Ghi audit tag:** Job ghi audit tag trước khi cộng tiền vào ví CTV.

*   **3.1.2.2. Hình thức "Interview"**
    1.  After reviewing CV, NTD decides to invite UV for an interview and updates status on the system.
    2.  System notifies UV/CTV.
    3.  When interview is confirmed (UV agrees), system deducts "Interview" fee from NTD's wallet.
    4.  System records transaction and commission for CTV.
    5.  **CTV/UV cancels interview (Job `CandidateCancelInterviewSubmitCv`):** If CTV or UV cancels an interview for a submitted CV, system automatically updates `SubmitCv` status from `Waitingsetupinterview` to `RecruiterCancelInterview`. Status change history is recorded. NTD, Admin (if authorized) and CTV will be notified.
    6.  **Expired interview schedule (Job `OutOfDateBookInterviewSubmitCv`):** If an interview schedule for a submitted CV expires (`status = RejectInterviewschedule`), `OutOfDateBookInterviewSubmitCv` job will be activated. This job will update `SubmitCv` status to `RecruiterCancelInterview`. System will automatically refund NTD for the interview fee. NTD, Admin (nếu ủy quyền) and CTV will be notified.
    7.  **Candidate "Passes Interview" (Job `PassInterviewSubmit`):** When NTD updates candidate status to "Pass Interview" (status `8`) for a submitted CV, `PassInterviewSubmit` job will be activated. Condition for job to run is current CV status is `7` ("Waiting Interview") and no complaint or complaint resolved. If service type is "Interview", `PayInterviewSubmit` job will be activated and delayed 24 hours to pay commission to CTV. If "Onboard", status will change to `11` ("Offering"). Notification will be sent to Admin (if authorized) or CTV.

*   **3.1.2.3. Hình thức "Onboard"**
    1.  After interview, NTD updates UV status to "Hired".
    2.  After trial period, NTD confirms UV "Onboard" successfully.
    3.  System deducts "Onboard" fee (largest) from NTD's wallet.
    4.  System records transaction and final, largest commission for CTV.
    5.  **Change status to "Trial work" (Job `ChangeToTrailWorkSubmit`):** When NTD updates candidate status to "Trial work" (status `14`) for a submitted CV, `ChangeToTrailWorkSubmit` job will be activated.
        *   **Check NTD wallet balance:** System will check NTD's wallet balance to ensure enough funds to pay the remaining "Onboard" fee (90% of value, as 10% may have been deposited previously).
        *   **Handle debt:** If balance is insufficient, system will record debt in `SubmitCvPaymentDebit` table for NTD. NTD will receive a series of debt reminder notifications (email reminder after 24 hours, then repeated from day 10 to day 15). When NTD deposits enough money, debt will be automatically paid.
        *   **Deduct from NTD wallet (if sufficient):** If balance is sufficient, system will deduct 90% of "Onboard" value from NTD's wallet. Deduction transaction will be recorded in `SubmitCvHistoryPayment`.
        *   **Update status and history:** `SubmitCv` status will be updated to `14` ("Trial work"). Status change history will be recorded.
        *   **Notification:** Admin will receive notification about candidate changing to trial work status (if CV is "authority"). CTV will receive notification about candidate changing to trial work status.
        *   **Trial period expiration reminder:** System will send email reminders to NTD about candidate's upcoming trial period expiration, starting from day 55 to day 59 of trial period.
        *   **Automatic change to "Successful Recruitment":** If NTD does not change candidate status after 67 days from trial work start date, system will automatically update status to `16` ("Successful Recruitment"). Job `SuccessRecuitmentSubmit` will be activated after 67 days.
        *   **Pay commission to CTV in stages (Onboard):**
            *   **1st payment (15%):** Job `PayOnboardSubmit` will be activated 30 days after trial work start date to pay 15% commission to CTV.
            *   **2nd payment (10%):** Job `PayOnboardSubmit` will be activated 45 days after trial work start date to pay 10% commission to CTV.
            *   **3rd payment (75%):** Job `PayOnboardSubmit` will be activated 67 days after trial work start date to pay remaining 75% commission to CTV (when candidate has signed official contract).
    6.  **Reject Offer/Cancel Onboard (Job `ChangeToRejectOfferSubmit`):** When a candidate is rejected after receiving an offer (or onboard process is cancelled) in Submit CV flow, system will activate `ChangeToRejectOfferSubmit` job.
        *   This job will update `SubmitCv` status to `12` (could be "Reject Offer" or "Cancel Onboard").
        *   Related `onboard` record status will also be updated to `2` (could be "Cancelled").
        *   System records this status change.
        *   **Notification:** NTD will receive email notification about reject/cancel onboard. If CV is provided through "authority", admin will receive notification. Otherwise, CTV who submitted CV will receive notification.
        *   **Deposit refund (Job `DepositRefundRejectOfferSubmit`):** A `DepositRefundRejectOfferSubmit` job will be dispatched to handle deposit refund for NTD. This refund job will be delayed for 48 hours, possibly to allow time for complaint processing or confirmation.

#### 3.1.3. Quản lý Ví điện tử (Wallet)

*   Each NTD account has an e-wallet.
*   NTD can top up wallet via payment gateways or bank transfer (Admin manually confirms).
*   All purchase/service usage transactions are deducted from wallet.
*   Transaction history must be recorded in detail: time, amount, service type, related Job/CV information.
*   Admin has rights to add/deduct money from NTD's wallet (with reason note).

##### 3.1.3.1. Manual Top-up/Deduction to Wallet (Admin)

*   **Purpose:** Allows Admin to manually adjust Employer and Recruiter wallet balances.
*   **`topup` function:**
    *   Admin specifies user, amount, and transaction type (`add` - top-up, `subtract` - deduct).
    *   Admin can add notes for transaction.
    *   **Transaction processing:**
        *   If `employer`: Money is added/deducted to `amount` field of wallet.
        *   If `rec`: Money is added/deducted to `price` field of wallet.
        *   System checks balance before deducting to ensure it's not negative.
        *   Transaction is performed within a database transaction to ensure integrity.
    *   **Limitation:** Admin accounts do not support wallet function.

##### 3.1.3.2. Automatic Complaint Handling and Refund

*   **Process:**
    *   Job `RecSumExpiredPoint` is dispatched with `statusComplain` parameter (1 or 2) 7 days after NTD submits complaint (`status_complain` = 1) or CTV rejects complaint (`status_complain` = 2).
    *   **Execution condition:** Job only runs if `cvSellingBuy->status_complain` currently matches `statusComplain` passed (ensuring complaint has not been manually processed).
    *   **Update complaint status:**
        *   If `status_complain` was 1 (NTD complaint), update to 3 (CTV confirmed).
        *   If `status_complain` was 2 (CTV rejected), update to 4 (Admin confirmed).
    *   **Refund logic for "CV Data" and "Interview":**
        *   If `type_of_sale` is 'cv', `status_recruitment` is updated to "Cancel Buy CV Data" (`CancelBuyCVdata`).
        *   `status_payment` is updated to 3 (Refund) for both "CV Data" and "Interview".
        *   System refunds 100% of deducted amount from NTD's wallet for that transaction.
        *   Records detailed refund transaction in `WareHouseCvSellingHistoryBuy` (type = 1, percent = 100).
        *   Adds Point to NTD's wallet (`employer->wallet->addAmount`) and records audit tag.
    *   **Refund logic for "Onboard":**
        *   If `status_recruitment` is 14 ("Trial work"): Refund depends on complaint time (`date_complain`) compared to trial work start date (`date_book`):
            *   0-30 days trial work: 100% refund.
            *   31-60 days trial work: 70% refund.
            *   61-67 days trial work: 50% refund.
            *   `status_payment` is updated to 3 (Refund).
        *   If `status_recruitment` is not 14 (e.g., already in another status after complaint): System will refund 100% of previous payments (if any) by finding `WareHouseCvSellingHistoryBuy` records not yet refunded and processing them similarly to CV Data/Interview.
        *   Records detailed refund transaction in `WareHouseCvSellingHistoryBuy` (type = 1, with corresponding percent).
        *   Adds Point to NTD's wallet (`employer->wallet->addAmount`) and records audit tag.
    *   **Notification:**
        *   If final `status_complain` is 4 (Admin confirmed): Sends `ComplaintsAcceptedToRec` (to CTV) and `ComplaintsAcceptedToEmployer` (to NTD).
        *   If final `status_complain` is 3 (CTV confirmed): Sends `RecAgreeComplain` (to CTV) and `RecAgreeComplainEmployer` (to NTD).

#### 3.1.4. Quản lý Tin tuyển dụng (Jobs)

*   NTD can create, edit, delete, hide/show job postings.
*   Each job posting has information: title, description, requirements, salary, location, level, industry...
*   Admin can manage all job postings.

##### *******. API to create job postings

*   **Purpose:** Provides an API allowing external systems to create new job postings in Recland.
*   **Process:**
    *   API receives job posting parameters such as job name, company ID, employer ID, expiration date, number of positions, job type, industry, level, skills, bonus, bonus type, city slug, address, job description, job requirements, benefits, min/max salary, and salary type.
    *   Address is converted to an array of address objects.
    *   Uses `JobService` (Frontend) to create job posting.
    *   Database transaction is used to ensure integrity.
    *   Returns ID and slug of created job posting.
    *   Handles errors and returns error messages if any.

##### *******. Search API

*   **Purpose:** Provides API endpoints to search and retrieve data related to companies, employers, and job categories.
*   **`searchCompany` function:**
    *   Allows searching for companies based on a query string (`q`).
    *   Only returns active companies (`is_active = 1`) and real companies (`is_real = 1`).
    *   Limits returned results (max 20).
    *   Returns company ID, name, and slug.
*   **`searchEmployer` function:**
    *   Allows searching for employers based on `company_id`.
    *   Checks if `company_id` is valid and company exists.
    *   Returns employer ID and name.
*   **`getCareer` function:**
    *   Returns a list of available careers in the system, localized by current language.
*   **`getJobType` function:**
    *   Returns a list of available job types in the system, localized by current language.

### 3.2. Non-functional requirements

*   **Performance:** Page load time no more than 3 seconds. System must handle at least 100 concurrent requests.
*   **Security:**
    *   All access must be via HTTPS.
    *   Mật khẩu người dùng phải được mã hóa (hashing).
    *   Ngăn chặn các lỗ hổng bảo mật phổ biến (XSS, SQL Injection).
    *   Phân quyền truy cập chặt chẽ giữa các vai trò.
*   **Usability:**
    *   Friendly interface, easy to use on both desktop and mobile devices (Responsive Design).
    *   Business flows must be clear, with user guides.
*   **Scalability:**
    *   Source code written according to Laravel standards, easy to maintain and upgrade.
    *   System designed modularly to allow adding new features in the future.

#### 3.2.1. Email logging system

*   **Purpose:** To track and check sent emails, support debugging and ensure notification integrity.
*   **Process:**
    *   Each time an email is sent from the system, a `CreateEmailLog` job will be activated to record detailed email information.
    *   Recorded information includes: sender (from), recipient (to), CC recipient (cc), subject, HTML content, and a unique hash for the email.
    *   Initial status of email log is `0` (unprocessed or pending).

---
## 4. Phụ lục

### 4.1. Related Jobs (Background Processes)

*   **`RejectRecruitment`**
    *   **Description:** Handles cases where candidates do not confirm interview/onboard offers within 48 hours, leading to automatic rejection and refund for NTD.
    *   **When dispatched:** 48 hours after the offer is sent and `wareHouseCvSellingBuy->status_recruitment` still is `Waitingcandidateconfirm` (1).
    *   **Happy Path:**
        *   Job receives `wareHouseCvSellingBuyId` parameter.
        *   If `wareHouseCvSellingBuy->status_recruitment` is `Waitingcandidateconfirm` (1):
            *   **If `type_of_sale` is 'interview':**
                *   Records 100% refund in `WareHouseCvSellingHistoryBuy`.
                *   Updates `wareHouseCvSellingBuy->status_recruitment` to `CandidateCancelApply` (2).
                *   Updates `wareHouseCvSellingBuy->status_payment` to 3 (Refund).
                *   Logs status history.
                *   Adds Point to NTD's wallet (`employer->wallet->addAmount`) and records audit tag.
            *   **If `type_of_sale` is 'onboard':**
                *   Refunds previous payments (if any) by finding `WareHouseCvSellingHistoryBuy` records not yet refunded and updating their status.
                *   Updates `wareHouseCvSellingBuy->status_recruitment` to `CandidateCancelApply` (2).
                *   Updates `wareHouseCvSellingBuy->status_payment` to 2 (Deposit Refund).
                *   Logs status history.
                *   Adds Point to NTD's wallet (`employer->wallet->addAmount`) and records audit tag.
            *   Sends notifications to CTV (`CandidateRejectRecruitmentRec`), NTD (`CandidateRejectRecruitmentEmployer`), and Admin (`CandidateRejectRecruitmentAdmin`).
    *   **Sad Path / Error Handling:** If `wareHouseCvSellingBuy` does not exist or `status_recruitment` is not `Waitingcandidateconfirm`, job will not perform any action.

*   **`RejectRecruitmentSubmit`**
    *   **Description:** Handles cases where candidates do not confirm interview/onboard offers within 48 hours in the Submit CV flow, leading to automatic rejection and **intended** refund for NTD. (Note: This job is currently disabled (`return false;`) in the code).
    *   **When dispatched:** 48 hours after the offer is sent and `submitCv->status` is still `Waitingcandidateconfirm` (1).
    *   **Happy Path (Based on commented code):**
        *   Job receives `submitCvId` parameter.
        *   If `submitCv->status` is `Waitingcandidateconfirm` (1):
            *   **If `job->bonus_type` is 'interview':**
                *   **Intended:** Records 100% refund in `SubmitCvHistoryPayment` (currently commented out in code).
                *   Updates `submitCv->status` to `CandidateCancelApply` (2).
                *   **Intended:** Updates `submitCv->status_payment` to 3 (Refund) (currently commented out in code).
                *   Logs status history.
                *   **Intended:** Adds Point to NTD's wallet (currently commented out in code).
            *   **If `job->bonus_type` is 'onboard':**
                *   **Intended:** Refunds previous payments (if any) (currently commented out in code).
                *   Updates `submitCv->status` to `CandidateCancelApply` (2).
                *   **Intended:** Updates `submitCv->status_payment` to 2 (Deposit Refund) (currently commented out in code).
                *   Logs status history.
                *   **Intended:** Adds Point to NTD's wallet (currently commented out in code).
            *   **Intended:** Sends notifications to CTV (`CandidateRejectRecruitmentRec`), NTD (`CandidateRejectRecruitmentEmployer`), and Admin (`CandidateRejectRecruitmentAdmin`) (currently commented out in code).
    *   **Sad Path / Error Handling:** If `submitCv` does not exist or `status` is not `Waitingcandidateconfirm`, job will not perform any action.

*   **`UpdateEmailLogStatus`**
    *   **Description:** Cập nhật trạng thái của bản ghi email log thành `1` (đã xử lý/gửi thành công) sau khi email đã được gửi.
    *   **Khi nào được dispatch:** Sau khi một email được gửi đi từ hệ thống.
    *   **Happy Path:**
        *   Job nhận một đối tượng `Email`.
        *   Tính toán một mã hash dựa trên người nhận, tiêu đề và nội dung HTML của email.
        *   Tìm kiếm các bản ghi `EmailLog` có cùng mã hash và được tạo trong vòng 5 phút gần nhất.
        *   Nếu tìm thấy, cập nhật `status` của các bản ghi đó thành `1`.
    *   **Sad Path / Xử lý lỗi:** Nếu không tìm thấy bản ghi `EmailLog` phù hợp, không có hành động nào được thực hiện.

*   **`PushToITNaviController`**
    *   **Mô tả:** Controller này xử lý việc đẩy dữ liệu CV của ứng viên sang hệ thống ITNavi bên ngoài.
    *   **Endpoint:** `POST /api/candidates` (trên hệ thống ITNavi).
    *   **Quy trình:**
        *   Nhận `cvId` từ request.
        *   Tìm `WareHouseCv` và `User` liên quan.
        *   Trích xuất các kỹ năng từ trường `main_skill` (dạng JSON) của CV.
        *   Chuẩn bị dữ liệu CV để gửi đi, bao gồm: `name`, `email`, `mobile`, `job_title`, `yoe` (years of experience), `skills`, `salary_expect`, `salary_current`, `assessment`, `public_cv_url`, `private_cv_url`.
        *   Gửi request POST đến API của ITNavi với `Authorization` header chứa API key.
        *   Nếu request thành công và trả về `candidate` ID, lưu `itnavi_candidate_id` vào `meta` của `WareHouseCv`.
        *   Trả về phản hồi JSON từ ITNavi.
    *   **Xử lý lỗi:** Ghi log lỗi nếu có vấn đề trong quá trình gửi request hoặc nhận phản hồi từ ITNavi.

### 4.2. Base Repository

*   **Mô tả:** `BaseRepository` là một lớp trừu tượng cung cấp các phương thức CRUD (Create, Read, Update, Delete) và các chức năng truy vấn cơ bản cho các repository khác trong ứng dụng. Nó giúp tái sử dụng code và đảm bảo tính nhất quán trong việc tương tác với cơ sở dữ liệu.
*   **Các phương thức chính:**
    *   **`optionQuery($options, $query = null)`:** Xây dựng query builder dựa trên các điều kiện `where` được truyền vào. Hỗ trợ các điều kiện đơn giản và `whereIn`.
    *   **`getListRelationShip(array $relations = [], array $where = [], $orderBy = null, $paginate = false, $limit = null, $sort = 'asc', $orderBys= [])`:** Lấy danh sách các bản ghi với các mối quan hệ (relationships) được eager load, điều kiện `where`, sắp xếp và phân trang tùy chọn.
    *   **`getAll($options = [], $order_by = null, $sort = 'asc')`:** Lấy tất cả các bản ghi dựa trên các điều kiện và sắp xếp.
    *   **`getCount($options = [])`:** Đếm số lượng bản ghi dựa trên các điều kiện.
    *   **`paginated($options = [], $page = 1, $limit = 15, $order_by = 'created_at', $sort = 'desc')`:** Lấy danh sách bản ghi có phân trang dựa trên số trang và giới hạn.
    *   **`paginationOffset($options = [], $limit = 15, $offset_position = 0, $condition = "<", $order_by = 'created_at', $sort = 'desc')`:** Lấy danh sách bản ghi có phân trang dựa trên offset và giới hạn.
    *   **`getFirst($options = [])`:** Lấy bản ghi đầu tiên khớp với các điều kiện.
    *   **`getFirstRelation($relation = [], $options = [])`:** Lấy bản ghi đầu tiên với các mối quan hệ được eager load.
    *   **`find($id, $options = [])`:** Tìm một bản ghi theo ID. (Lưu ý: Code sử dụng `find($id)` thay vì `findOrFail($id)`).
    *   **`create($attributes = [])`:** Tạo một bản ghi mới.
    *   **`insert($attributes = [])`:** Chèn nhiều bản ghi mới.
    *   **`update($id, $options = [], $attributes = [])`:** Cập nhật một bản ghi theo ID. Ghi audit tag trước khi cập nhật.
    *   **`delete($id, $options = [])`:** Xóa một bản ghi theo ID.
    *   **`increment($id, $attribute, $valueIncrement = 1)`:** Tăng giá trị của một thuộc tính số.
    *   **`decrement($id, $attribute, $valueDecrement = 1)`:** Giảm giá trị của một thuộc tính số.
    *   **`query()`:** Trả về một query builder cho model được liên kết.
