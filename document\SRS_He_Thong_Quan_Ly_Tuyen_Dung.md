
# Tài liệu Đặc tả Yêu cầu <PERSON> mềm (SRS) - Website Quản lý Tuyển dụng Recland

**Phiên bản:** 1.0  
**Ngày:** 2025-07-07

---

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1. <PERSON><PERSON><PERSON> đích

Tài liệu này đặc tả các yêu cầu chức năng và phi chức năng của hệ thống website quản lý tuyển dụng Recland. Mục tiêu của hệ thống là tạo ra một nền tảng kết nối Nhà tuyển dụng (NTD) với các <PERSON>ng viên (UV) tiềm năng thông qua hai luồng nghiệp vụ chính: Mua bán CV và Ứng tuyển CV, đồng thời quản lý các giao dịch tài chính liên quan thông qua hệ thống ví.

### 1.2. <PERSON><PERSON><PERSON> vi

<PERSON>ệ thống là một ứng dụng web, cung cấp các tính năng cho các đối tượng người dùng sau:
*   **Quản trị viên (Admin):** Quản lý toàn bộ hệ thống, người dùng, giao dịch, và cấu hình.
*   **Nhà tuyển dụng (Employer - NTD):** Đăng tin tuyển dụng, tìm kiếm, mua và quản lý CV ứng viên.
*   **Ứng viên (Candidate - UV):** Tạo và quản lý hồ sơ, ứng tuyển vào các công việc.
*   **Cộng tác viên (Collaborator - CTV):** Tìm kiếm và giới thiệu ứng viên cho các tin tuyển dụng để nhận hoa hồng.

### 1.3. Định nghĩa, Thuật ngữ

| Thuật ngữ | Mô tả |
| --- | --- |
| **NTD** | Nhà Tuyển Dụng |
| **UV** | Ứng Viên |
| **CTV** | Cộng Tác Viên |
| **CV Data** | Hình thức dịch vụ mà NTD trả phí để nhận dữ liệu/thông tin liên hệ cơ bản của UV. |
| **Interview** | Hình thức dịch vụ mà NTD trả phí khi UV đồng ý tham gia phỏng vấn. |
| **Onboard** | Hình thức dịch vụ mà NTD trả phí khi UV được tuyển dụng thành công và qua thời gian thử việc. |
| **Ví (Wallet)** | Hệ thống tài khoản tiền tệ của NTD trên website để thực hiện các giao dịch. |
| **Kho CV** | Cơ sở dữ liệu CV của hệ thống, được thu thập từ nhiều nguồn khác nhau. |

---

## 2. Mô tả tổng quan

### 2.1. Bối cảnh sản phẩm

Recland là một nền tảng SaaS (Software as a Service) hoạt động trong lĩnh vực nhân sự. Hệ thống giải quyết bài toán tìm kiếm và tuyển dụng nhân sự chất lượng cho các doanh nghiệp, đồng thời tạo ra một mô hình kinh doanh dựa trên chi phí cho mỗi hành động thành công (cost-per-action) thay vì chi phí đăng tin truyền thống.

### 2.2. Các chức năng chính

*   Quản lý người dùng (Admin, NTD, UV, CTV).
*   Quản lý tin tuyển dụng.
*   Quản lý ví điện tử và giao dịch.
*   Luồng nghiệp vụ Mua bán CV từ Kho CV của hệ thống.
*   Luồng nghiệp vụ Ứng tuyển CV vào các tin tuyển dụng cụ thể.
*   Hệ thống báo cáo, thống kê.

### 2.3. Đối tượng người dùng

| Đối tượng | Mô tả | Quyền hạn chính |
| --- | --- | --- |
| **Admin** | Quản trị viên hệ thống | CRUD tất cả dữ liệu, quản lý cấu hình, duyệt giao dịch, xem báo cáo tổng thể. |
| **NTD** | Doanh nghiệp có nhu cầu tuyển dụng | Đăng tin, quản lý tin tuyển dụng, nạp tiền vào ví, mua CV, quản lý quy trình tuyển dụng. |
| **UV** | Người tìm việc | Tạo/sửa hồ sơ CV, tìm kiếm và ứng tuyển công việc, theo dõi trạng thái ứng tuyển. |
| **CTV** | Người giới thiệu nhân sự | Tìm kiếm ứng viên phù hợp và submit vào các tin tuyển dụng để hưởng hoa hồng theo các mốc (Data, Interview, Onboard). |

---

## 3. Yêu cầu cụ thể

### 3.1. Yêu cầu chức năng

#### 3.1.1. Luồng 1: Mua bán CV (Sourcing từ Kho CV)

Luồng này cho phép NTD chủ động tìm kiếm và mua thông tin ứng viên từ Kho CV có sẵn của Recland.

*   **Mô tả chung:** NTD tìm kiếm trong Kho CV của hệ thống với các bộ lọc (ngành nghề, kỹ năng, kinh nghiệm, cấp bậc). Kết quả ban đầu sẽ ẩn thông tin liên hệ. NTD phải trả phí để xem thông tin chi tiết.

#### 3.1.1.0. Xử lý CV tự động (Backend)

Khi một CV mới được thêm vào hệ thống (ví dụ: thông qua upload hoặc các nguồn khác), một job `ProcessWarehouseCvJob` sẽ được kích hoạt để tự động xử lý CV đó.

*   **Các bước xử lý tự động:**
    1.  **Tìm kiếm CV:** Job sẽ tìm CV dựa trên `warehouseCvId`. Nếu không tìm thấy hoặc đường dẫn file CV không hợp lệ (không có tiền tố `pending_process::`), job sẽ ghi log lỗi và dừng lại.
    2.  **Đọc file CV:** Job đọc nội dung file CV từ đường dẫn cục bộ (storage_path).
    3.  **Gọi API phân tích CV:**
        *   Job gửi file CV đến một API bên ngoài (n8n webhook `cv-parser-with-markdown`) để phân tích nội dung.
        *   API key (`match-cv-api-key`) được lấy từ cấu hình ứng dụng.
    4.  **Lưu trữ dữ liệu CV đã phân tích:**
        *   Nếu API phân tích thành công, dữ liệu JSON trả về sẽ được lưu vào trường `meta` của `WareHouseCv` dưới dạng `cv_parsed_data`.
        *   Các trường thông tin chính của ứng viên (tên, email, số điện thoại, địa chỉ, chức danh, kinh nghiệm, kỹ năng, đánh giá bản thân) sẽ được cập nhật vào các trường tương ứng trong bảng `WareHouseCv` từ dữ liệu đã phân tích.
        *   Nếu có `pdf_url` từ API phân tích, file PDF này sẽ được tải lên S3 làm `cv_public`.
        *   Một phiên bản ẩn danh của CV (`cv_private`) cũng sẽ được tạo ra bằng `HideService` (che giấu thông tin liên hệ) và tải lên S3.
        *   Nếu không có `pdf_url` nhưng có file CV cục bộ đang chờ xử lý (`pending_process::`), file này sẽ được tải lên S3 làm `cv_public`, sau đó một phiên bản ẩn danh (`cv_private`) cũng được tạo và tải lên S3. File cục bộ sẽ bị xóa sau khi upload thành công.
    5.  **Xử lý lỗi:** Nếu có lỗi trong quá trình xử lý (ví dụ: không tìm thấy CV, lỗi API, lỗi upload S3), job sẽ ghi log lỗi.

*   **Các dịch vụ hỗ trợ:**
    *   **`FileServiceS3`:** Cung cấp các chức năng để tương tác với Amazon S3 để lưu trữ tệp (tải lên, di chuyển, xóa).
    *   **`HideService`:** Gửi CV đến một dịch vụ AI bên ngoài để ẩn thông tin nhạy cảm, tạo phiên bản `cv_private`.

*   **3.1.1.1. Hình thức "CV Data"**
    1.  NTD thực hiện tìm kiếm và chọn được một hoặc nhiều CV ưng ý.
    2.  NTD chọn mua "CV Data".
    3.  Hệ thống kiểm tra số dư trong ví của NTD.
    4.  Nếu đủ, hệ thống trừ tiền trong ví theo đơn giá đã cấu hình cho dịch vụ "CV Data".
    5.  Hệ thống ghi nhận giao dịch.
    6.  Hệ thống mở khóa thông tin liên hệ (email, SĐT) của UV cho NTD xem.
    7.  **Thanh toán hoa hồng cho CTV (Job `RecSumPoint`):**
        *   Sau khi NTD mua "CV Data" thành công (trạng thái `BuyCVdatasuccessfull`) và không có khiếu nại (`status_complain` là 0 hoặc 5), hệ thống sẽ kích hoạt một job `RecSumPoint` để xử lý việc thanh toán hoa hồng cho CTV.
        *   **Thời điểm thanh toán:** Việc thanh toán này sẽ được thực hiện tự động sau 7 ngày kể từ khi giao dịch mua "CV Data" thành công, với điều kiện không có khiếu nại hoặc khiếu nại đã được giải quyết.
        *   **Tính toán hoa hồng:**
            *   Nếu giao dịch là "ủy quyền" (`authority == 2`), CTV sẽ nhận được 20% giá trị giao dịch.
            *   Trong các trường hợp khác, CTV sẽ nhận được 100% giá trị giao dịch.
        *   **Cập nhật ví CTV:** Số tiền hoa hồng sẽ được cộng trực tiếp vào ví điện tử của CTV.
        *   **Ghi nhận giao dịch:** Hệ thống sẽ ghi nhận giao dịch hoa hồng này vào bảng `payins` (tổng doanh thu theo tháng/năm cho CTV) và `payin_month` (chi tiết từng giao dịch hoa hồng trong tháng).
        *   **Thông báo:** CTV sẽ nhận được thông báo khi hoa hồng được cộng vào ví.

*   **3.1.1.2. Hình thức "Interview"**
    1.  NTD chọn một CV và yêu cầu sắp xếp một cuộc "Phỏng vấn".
    2.  Hệ thống/Admin/CTV liên hệ với UV để xác nhận sự đồng ý phỏng vấn.
    3.  **Nếu UV đồng ý:**
        *   Hệ thống thông báo cho NTD và yêu cầu thanh toán phí "Interview".
        *   Hệ thống trừ tiền trong ví của NTD.
        *   Hệ thống ghi nhận giao dịch và cập nhật trạng thái ứng viên thành "Chờ phỏng vấn".
        *   **Thanh toán hoa hồng cho CTV (Job `PayInterview`):** Hệ thống sẽ kích hoạt một job `PayInterview` để xử lý việc thanh toán hoa hồng cho CTV sau khi một cuộc phỏng vấn được xác nhận. Việc thanh toán này chỉ diễn ra khi trạng thái tuyển dụng của CV (`status_recruitment`) là 8 (có thể là "Phỏng vấn thành công" hoặc tương tự) hoặc 10 (có thể là "Đã phỏng vấn") VÀ trạng thái khiếu nại (`status_complain`) là 0 (không có khiếu nại) hoặc 5 (khiếu nại đã được giải quyết). Job này có thể được lên lịch chạy sau 24 giờ kể từ khi xác nhận phỏng vấn, để đảm bảo không có khiếu nại ngay lập tức.
        *   **Xử lý lịch phỏng vấn quá hạn (Job `OutOfDateBookInterview`):** Nếu một lịch phỏng vấn đã được đặt và không có hành động nào được thực hiện trong 7 ngày, job `OutOfDateBookInterview` sẽ được kích hoạt. Job này sẽ cập nhật trạng thái của `WareHouseCvSellingBuy` từ `5` (Từ chối lịch phỏng vấn) sang `6` (Hủy phỏng vấn). NTD và CTV (hoặc Admin) sẽ nhận được thông báo.
        *   **Xử lý khi ứng viên "Đạt phỏng vấn" (Job `PassInterview`):** Khi NTD cập nhật trạng thái ứng viên thành "Đạt phỏng vấn" (status `8`), job `PassInterview` sẽ được kích hoạt. Điều kiện để job chạy là trạng thái hiện tại của CV là `7` ("Chờ phỏng vấn") và không có khiếu nại hoặc khiếu nại đã được giải quyết. Nếu loại hình dịch vụ là "Interview", job `PayInterview` sẽ được kích hoạt và trì hoãn 24 giờ để thanh toán hoa hồng cho CTV.
    4.  **Nếu UV từ chối:** Quy trình kết thúc, NTD không bị trừ tiền.

*   **3.1.1.3. Hình thức "Onboard"**
    1.  Sau quá trình phỏng vấn, NTD cập nhật trạng thái UV thành "Trúng tuyển".
    2.  Hệ thống bắt đầu tính thời gian thử việc (ví dụ: 60 ngày, có thể cấu hình).
    3.  Sau khi kết thúc thời gian thử việc, NTD xác nhận UV "Onboard" thành công.
    4.  Hệ thống yêu cầu NTD thanh toán phí "Onboard".
    5.  Hệ thống trừ tiền trong ví của NTD và ghi nhận giao dịch.
    6.  **Logic thanh toán hoa hồng cho CTV (Job `PayOnboard`):** Tương tự như `PayInterview`, hệ thống sẽ kích hoạt một job `PayOnboard` để xử lý việc thanh toán hoa hồng cho CTV khi ứng viên "Onboard" thành công. Việc thanh toán này chỉ diễn ra khi trạng thái tuyển dụng của CV (`status_recruitment`) là 14 (có thể là "Thử việc") hoặc 16 (có thể là "Tuyển dụng thành công") VÀ trạng thái khiếu nại (`status_complain`) là 0 (không có khiếu nại) hoặc 5 (khiếu nại đã được giải quyết). Job này nhận thêm tham số `percent` để tính toán hoa hồng, cho thấy có thể có các mức hoa hồng khác nhau tùy thuộc vào cấu hình.
    7.  **Chuyển trạng thái sang "Thử việc" (Job `ChangeToTrailWork`):** Khi NTD cập nhật trạng thái ứng viên thành "Thử việc" (status `14`), job `ChangeToTrailWork` sẽ được kích hoạt.
        *   **Kiểm tra số dư ví NTD:** Hệ thống sẽ kiểm tra số dư ví của NTD để đảm bảo đủ tiền thanh toán phần còn lại của phí "Onboard" (90% giá trị, vì 10% có thể đã được đặt cọc trước đó).
        *   **Xử lý ghi nợ:** Nếu số dư không đủ, hệ thống sẽ ghi nhận khoản nợ vào bảng `PaymentDebit` cho NTD. NTD sẽ nhận được chuỗi thông báo nhắc nhở thanh toán nợ (email nhắc nhở sau 24 giờ, sau đó lặp lại từ ngày thứ 10 đến ngày thứ 15). Khi NTD nạp đủ tiền, khoản nợ sẽ được tự động thanh toán.
        *   **Trừ tiền từ ví NTD (nếu đủ):** Nếu số dư đủ, hệ thống sẽ trừ 90% giá trị "Onboard" từ ví của NTD. Giao dịch trừ tiền sẽ được ghi lại chi tiết.
        *   **Cập nhật trạng thái và lịch sử:** Trạng thái của `WareHouseCvSellingBuy` sẽ được cập nhật thành `14` ("Thử việc"). Lịch sử thay đổi trạng thái sẽ được ghi lại.
        *   **Thông báo:** Admin sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc (nếu CV thuộc "authority"). CTV sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc.
        *   **Nhắc nhở hết hạn thử việc:** Hệ thống sẽ gửi email nhắc nhở NTD về việc sắp hết hạn thử việc của ứng viên, bắt đầu từ ngày thứ 55 đến ngày thứ 59 của giai đoạn thử việc.
        *   **Tự động chuyển trạng thái "Tuyển dụng thành công":** Nếu NTD không thay đổi trạng thái của ứng viên sau 67 ngày kể từ ngày bắt đầu thử việc, hệ thống sẽ tự động cập nhật trạng thái thành `16` ("Tuyển dụng thành công"). Job `SuccessRecuitment` sẽ được kích hoạt sau 67 ngày.
        *   **Thanh toán hoa hồng cho CTV theo giai đoạn (Onboard):**
            *   **Lần 1 (15%):** Job `PayOnboard` sẽ được kích hoạt sau 30 ngày kể từ ngày bắt đầu thử việc để thanh toán 15% hoa hồng cho CTV.
            *   **Lần 2 (10%):** Job `PayOnboard` sẽ được kích hoạt sau 45 ngày kể từ ngày bắt đầu thử việc để thanh toán 10% hoa hồng cho CTV.
            *   **Lần 3 (75%):** Job `PayOnboard` sẽ được kích hoạt sau 67 ngày kể từ ngày bắt đầu thử việc để thanh toán 75% hoa hồng còn lại cho CTV (khi ứng viên đã ký hợp đồng chính thức).
    8.  **Từ chối Offer/Hủy Onboard (Job `ChangeToRejectOffer`):** Khi một ứng viên bị từ chối sau khi đã nhận offer (hoặc quá trình onboard bị hủy), hệ thống sẽ kích hoạt job `ChangeToRejectOffer`.
        *   Job này sẽ cập nhật trạng thái của `WareHouseCvSellingBuy` thành `12` (có thể là "Từ chối Offer" hoặc "Hủy Onboard").
        *   Trạng thái của bản ghi `onboard` liên quan cũng được cập nhật thành `2` (có thể là "Đã hủy").
        *   Hệ thống ghi lại sự thay đổi trạng thái này.
        *   **Thông báo:** Nhà tuyển dụng (NTD) sẽ nhận được email thông báo về việc từ chối/hủy onboard. Nếu CV được cung cấp thông qua "authority", admin sẽ nhận được thông báo. Nếu không, CTV đã nộp CV sẽ nhận được thông báo.
        *   **Hoàn cọc (Job `DepositRefundRejectOffer`):** Một job `DepositRefundRejectOffer` sẽ được dispatch (kích hoạt) để xử lý việc hoàn cọc cho NTD. Job hoàn cọc này sẽ được trì hoãn (delay) trong 48 giờ, có thể để cho phép thời gian xử lý khiếu nại hoặc xác nhận.

#### 3.1.2. Luồng 2: Ứng tuyển CV (Submit vào Job)

Luồng này xảy ra khi có một tin tuyển dụng cụ thể do NTD đăng tải. UV hoặc CTV sẽ nộp hồ sơ vào tin tuyển dụng này.

*   **Mô tả chung:** NTD đăng tin tuyển dụng. UV/CTV tìm thấy tin và nộp hồ sơ. NTD sẽ trả phí dựa trên các giai đoạn của quy trình tuyển dụng.

*   **3.1.2.1. Hình thức "CV Data"**
    1.  UV/CTV nộp hồ sơ (submit CV) vào một tin tuyển dụng.
    2.  Hệ thống thông báo cho NTD có CV mới.
    3.  NTD vào xem danh sách CV đã ứng tuyển.
    4.  Để xem chi tiết và thông tin liên hệ của CV, NTD phải trả phí "CV Data".
    5.  Hệ thống trừ tiền trong ví NTD, ghi nhận giao dịch.
    6.  Nếu người submit là CTV, hệ thống ghi nhận một khoản hoa hồng nhỏ cho CTV.
    7.  **Thanh toán hoa hồng cho CTV (Job `RecSumPointSubmit`):** Tương tự như luồng "Mua bán CV", sau khi NTD mua "CV Data" thành công và không có khiếu nại, hệ thống sẽ kích hoạt một job `RecSumPointSubmit` để xử lý việc thanh toán hoa hồng cho CTV sau 7 ngày.

*   **3.1.2.2. Hình thức "Interview"**
    1.  Sau khi xem CV, NTD quyết định mời UV phỏng vấn và cập nhật trạng thái trên hệ thống.
    2.  Hệ thống thông báo đến UV/CTV.
    3.  Khi cuộc phỏng vấn được xác nhận (UV đồng ý), hệ thống sẽ trừ phí "Interview" từ ví của NTD.
    4.  Hệ thống ghi nhận giao dịch và hoa hồng cho CTV.
    5.  **CTV/UV hủy phỏng vấn (Job `CandidateCancelInterviewSubmitCv`):** Nếu CTV hoặc UV hủy một cuộc phỏng vấn cho CV đã nộp vào một tin tuyển dụng, hệ thống sẽ tự động cập nhật trạng thái của `SubmitCv` từ `Waitingsetupinterview` sang `RecruiterCancelInterview`. Lịch sử thay đổi trạng thái sẽ được ghi lại. NTD, Admin (nếu ủy quyền) và CTV sẽ nhận được thông báo.
    6.  **Xử lý lịch phỏng vấn quá hạn (Job `OutOfDateBookInterviewSubmitCv`):** Nếu một lịch phỏng vấn cho CV đã nộp bị quá hạn (`status = RejectInterviewschedule`), job `OutOfDateBookInterviewSubmitCv` sẽ được kích hoạt. Job này sẽ cập nhật trạng thái của `SubmitCv` thành `RecruiterCancelInterview`. Hệ thống sẽ tự động hoàn tiền cho NTD đối với phí phỏng vấn. NTD, Admin (nếu ủy quyền) và CTV sẽ nhận được thông báo.
    7.  **Xử lý khi ứng viên "Đạt phỏng vấn" (Job `PassInterviewSubmit`):** Khi NTD cập nhật trạng thái ứng viên thành "Đạt phỏng vấn" (status `8`) cho một CV đã nộp, job `PassInterviewSubmit` sẽ được kích hoạt. Điều kiện để job chạy là trạng thái hiện tại của CV là `7` ("Chờ phỏng vấn") và không có khiếu nại hoặc khiếu nại đã được giải quyết. Nếu loại hình dịch vụ là "Interview", job `PayInterviewSubmit` sẽ được kích hoạt và trì hoãn 24 giờ để thanh toán hoa hồng cho CTV. Nếu là "Onboard", trạng thái sẽ chuyển sang `11` ("Offering"). Thông báo sẽ được gửi đến Admin (nếu ủy quyền) hoặc CTV.

*   **3.1.2.3. Hình thức "Onboard"**
    1.  Sau phỏng vấn, NTD cập nhật trạng thái UV thành "Trúng tuyển".
    2.  Sau thời gian thử việc, NTD xác nhận UV "Onboard" thành công.
    3.  Hệ thống trừ khoản phí "Onboard" (lớn nhất) từ ví của NTD.
    4.  Hệ thống ghi nhận giao dịch và khoản hoa hồng cuối cùng, lớn nhất cho CTV.
    5.  **Chuyển trạng thái sang "Thử việc" (Job `ChangeToTrailWorkSubmit`):** Khi NTD cập nhật trạng thái ứng viên thành "Thử việc" (status `14`) cho một CV đã nộp, job `ChangeToTrailWorkSubmit` sẽ được kích hoạt.
        *   **Kiểm tra số dư ví NTD:** Hệ thống sẽ kiểm tra số dư ví của NTD để đảm bảo đủ tiền thanh toán phần còn lại của phí "Onboard" (90% giá trị, vì 10% có thể đã được đặt cọc trước đó).
        *   **Xử lý ghi nợ:** Nếu số dư không đủ, hệ thống sẽ ghi nhận khoản nợ vào bảng `SubmitCvPaymentDebit` cho NTD. NTD sẽ nhận được chuỗi thông báo nhắc nhở thanh toán nợ (email nhắc nhở sau 24 giờ, sau đó lặp lại từ ngày thứ 10 đến ngày thứ 15). Khi NTD nạp đủ tiền, khoản nợ sẽ được tự động thanh toán.
        *   **Trừ tiền từ ví NTD (nếu đủ):** Nếu số dư đủ, hệ thống sẽ trừ 90% giá trị "Onboard" từ ví của NTD. Giao dịch trừ tiền sẽ được ghi lại chi tiết trong `SubmitCvHistoryPayment`.
        *   **Cập nhật trạng thái và lịch sử:** Trạng thái của `SubmitCv` sẽ được cập nhật thành `14` ("Thử việc"). Lịch sử thay đổi trạng thái sẽ được ghi lại.
        *   **Thông báo:** Admin sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc (nếu CV thuộc "authority"). CTV sẽ nhận được thông báo về việc ứng viên chuyển sang trạng thái thử việc.
        *   **Nhắc nhở hết hạn thử việc:** Hệ thống sẽ gửi email nhắc nhở NTD về việc sắp hết hạn thử việc của ứng viên, bắt đầu từ ngày thứ 55 đến ngày thứ 59 của giai đoạn thử việc.
        *   **Tự động chuyển trạng thái "Tuyển dụng thành công":** Nếu NTD không thay đổi trạng thái của ứng viên sau 67 ngày kể từ ngày bắt đầu thử việc, hệ thống sẽ tự động cập nhật trạng thái thành `16` ("Tuyển dụng thành công"). Job `SuccessRecuitmentSubmit` sẽ được kích hoạt sau 67 ngày.
        *   **Thanh toán hoa hồng cho CTV theo giai đoạn (Onboard):**
            *   **Lần 1 (15%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 30 ngày kể từ ngày bắt đầu thử việc để thanh toán 15% hoa hồng cho CTV.
            *   **Lần 2 (10%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 45 ngày kể từ ngày bắt đầu thử việc để thanh toán 10% hoa hồng cho CTV.
            *   **Lần 3 (75%):** Job `PayOnboardSubmit` sẽ được kích hoạt sau 67 ngày kể từ ngày bắt đầu thử việc để thanh toán 75% hoa hồng còn lại cho CTV (khi ứng viên đã ký hợp đồng chính thức).
    6.  **Từ chối Offer/Hủy Onboard (Job `ChangeToRejectOfferSubmit`):** Khi một ứng viên bị từ chối sau khi đã nhận offer (hoặc quá trình onboard bị hủy) trong luồng Submit CV, hệ thống sẽ kích hoạt job `ChangeToRejectOfferSubmit`.
        *   Job này sẽ cập nhật trạng thái của `SubmitCv` thành `12` (có thể là "Từ chối Offer" hoặc "Hủy Onboard").
        *   Trạng thái của bản ghi `onboard` liên quan cũng được cập nhật thành `2` (có thể là "Đã hủy").
        *   Hệ thống ghi lại sự thay đổi trạng thái này.
        *   **Thông báo:** Nhà tuyển dụng (NTD) sẽ nhận được email thông báo về việc từ chối/hủy onboard. Nếu CV được cung cấp thông qua "authority", admin sẽ nhận được thông báo. Nếu không, CTV đã nộp CV sẽ nhận được thông báo.
        *   **Hoàn cọc (Job `DepositRefundRejectOfferSubmit`):** Một job `DepositRefundRejectOfferSubmit` sẽ được dispatch (kích hoạt) để xử lý việc hoàn cọc cho NTD. Job hoàn cọc này sẽ được trì hoãn (delay) trong 48 giờ, có thể để cho phép thời gian xử lý khiếu nại hoặc xác nhận.

#### 3.1.3. Quản lý Ví điện tử (Wallet)

*   Mỗi tài khoản NTD có một ví điện tử.
*   NTD có thể nạp tiền vào ví thông qua các cổng thanh toán hoặc chuyển khoản (Admin xác nhận thủ công).
*   Tất cả các giao dịch mua/sử dụng dịch vụ đều được trừ tiền từ ví.
*   Lịch sử giao dịch phải được ghi lại chi tiết: thời gian, số tiền, loại dịch vụ, thông tin Job/CV liên quan.
*   Admin có quyền cộng/trừ tiền trong ví của NTD (kèm theo ghi chú lý do).

##### 3.1.3.1. Nạp/Trừ tiền thủ công vào ví (Admin)

*   **Mục đích:** Cho phép Admin điều chỉnh số dư ví của Nhà tuyển dụng (Employer) và Cộng tác viên (Recruiter) một cách thủ công.
*   **Chức năng `topup`:**
    *   Admin chỉ định người dùng, số tiền và loại giao dịch (`add` - nạp, `subtract` - trừ).
    *   Admin có thể thêm ghi chú cho giao dịch.
    *   **Xử lý giao dịch:**
        *   Nếu là `employer`: Tiền được cộng/trừ vào trường `amount` của ví.
        *   Nếu là `rec`: Tiền được cộng/trừ vào trường `price` của ví.
        *   Hệ thống kiểm tra số dư trước khi trừ tiền để đảm bảo không âm.
        *   Giao dịch được thực hiện trong một transaction cơ sở dữ liệu để đảm bảo tính toàn vẹn.
    *   **Hạn chế:** Tài khoản Admin không hỗ trợ chức năng ví.

##### 3.1.3.2. Xử lý khiếu nại và hoàn tiền tự động

*   **Mục đích:** Tự động xử lý các khiếu nại của NTD và hoàn tiền nếu khiếu nại không được xử lý kịp thời bởi CTV hoặc Admin.
*   **Quy trình:**
    *   Nếu NTD gửi khiếu nại về một giao dịch (mua CV Data, Interview, Onboard) và khiếu nại đó không được CTV hoặc Admin xử lý trong vòng 7 ngày, hệ thống sẽ tự động chấp nhận khiếu nại và hoàn tiền cho NTD (Job `RecSumExpiredPoint` hoặc `RecSumExpiredPointSubmit`).
    *   **Logic hoàn tiền cho "CV Data" và "Interview":**
        *   Hệ thống sẽ hoàn lại 100% số tiền đã trừ từ ví của NTD cho giao dịch đó.
        *   Trạng thái giao dịch sẽ được cập nhật thành "Hủy mua CV Data" (đối với CV Data) hoặc "Hoàn tiền".
    *   **Logic hoàn tiền cho "Onboard":**
        *   Việc hoàn tiền cho dịch vụ "Onboard" sẽ phụ thuộc vào thời gian khiếu nại so với thời gian thử việc của ứng viên:
            *   Nếu khiếu nại trong vòng 0-30 ngày thử việc: Hoàn 100% số tiền.
            *   Nếu khiếu nại trong vòng 31-60 ngày thử việc: Hoàn 70% số tiền.
            *   Nếu khiếu nại trong vòng 61-67 ngày thử việc: Hoàn 50% số tiền.
        *   Số tiền hoàn lại sẽ được cộng vào ví của NTD.
    *   **Ghi nhận giao dịch hoàn tiền:** Tất cả các giao dịch hoàn tiền đều được ghi lại chi tiết trong lịch sử giao dịch của NTD.
    *   **Thông báo:** Hệ thống sẽ gửi thông báo cho cả NTD và CTV về việc khiếu nại đã được xử lý tự động và số tiền hoàn lại (nếu có).

#### 3.1.4. Quản lý Tin tuyển dụng (Jobs)

*   NTD có thể tạo, sửa, xóa, ẩn/hiện tin tuyển dụng.
*   Mỗi tin tuyển dụng có các thông tin: chức danh, mô tả, yêu cầu, mức lương, địa điểm, cấp bậc, ngành nghề...
*   Admin có thể quản lý tất cả các tin tuyển dụng.

##### 3.1.4.1. API tạo tin tuyển dụng

*   **Mục đích:** Cung cấp một API cho phép các hệ thống bên ngoài tạo tin tuyển dụng mới trong Recland.
*   **Quy trình:**
    *   API nhận các tham số tin tuyển dụng như tên công việc, ID công ty, ID nhà tuyển dụng, ngày hết hạn, số lượng vị trí, loại hình công việc, ngành nghề, cấp bậc, kỹ năng, tiền thưởng, loại tiền thưởng, slug thành phố, địa chỉ, mô tả công việc, yêu cầu công việc, phúc lợi, mức lương tối thiểu/tối đa và loại tiền lương.
    *   Địa chỉ được chuyển đổi thành một mảng các đối tượng địa chỉ.
    *   Sử dụng `JobService` (Frontend) để tạo tin tuyển dụng.
    *   Giao dịch cơ sở dữ liệu được sử dụng để đảm bảo tính toàn vẹn.
    *   Trả về ID và slug của tin tuyển dụng đã tạo.
    *   Xử lý lỗi và trả về thông báo lỗi nếu có.

##### 3.1.4.2. API tìm kiếm

*   **Mục đích:** Cung cấp các điểm cuối API để tìm kiếm và truy xuất dữ liệu liên quan đến công ty, nhà tuyển dụng và các danh mục công việc.
*   **Chức năng `searchCompany`:**
    *   Cho phép tìm kiếm công ty dựa trên một chuỗi truy vấn (`q`).
    *   Chỉ trả về các công ty đang hoạt động (`is_active = 1`) và là công ty thực (`is_real = 1`).
    *   Giới hạn kết quả trả về (tối đa 20).
    *   Trả về ID, tên và slug của công ty.
*   **Chức năng `searchEmployer`:**
    *   Cho phép tìm kiếm nhà tuyển dụng (employer) dựa trên `company_id`.
    *   Kiểm tra xem `company_id` có hợp lệ và công ty có tồn tại không.
    *   Trả về ID và tên của nhà tuyển dụng.
*   **Chức năng `getCareer`:**
    *   Trả về danh sách các ngành nghề có sẵn trong hệ thống, được bản địa hóa theo ngôn ngữ hiện tại.
*   **Chức năng `getJobType`:**
    *   Trả về danh sách các loại hình công việc có sẵn trong hệ thống, được bản địa hóa theo ngôn ngữ hiện tại.

### 3.2. Yêu cầu phi chức năng

*   **Hiệu năng:** Thời gian tải trang không quá 3 giây. Hệ thống phải xử lý được ít nhất 100 yêu cầu đồng thời.
*   **Bảo mật:**
    *   Tất cả các truy cập phải qua HTTPS.
    *   Mật khẩu người dùng phải được mã hóa (hashing).
    *   Ngăn chặn các lỗ hổng bảo mật phổ biến (XSS, SQL Injection).
    *   Phân quyền truy cập chặt chẽ giữa các vai trò.
*   **Tính khả dụng:**
    *   Giao diện thân thiện, dễ sử dụng trên cả máy tính và thiết bị di động (Responsive Design).
    *   Các luồng nghiệp vụ phải rõ ràng, có hướng dẫn cho người dùng.
*   **Khả năng mở rộng:**
    *   Mã nguồn được viết theo chuẩn của Laravel, dễ dàng bảo trì và nâng cấp.
    *   Hệ thống được thiết kế theo module để có thể thêm các tính năng mới trong tương lai.

#### 3.2.1. Hệ thống ghi log email

*   **Mục đích:** Để theo dõi và kiểm tra các email đã gửi, hỗ trợ việc debug và đảm bảo tính toàn vẹn của thông báo.
*   **Quy trình:**
    *   Mỗi khi một email được gửi đi từ hệ thống, một job `CreateEmailLog` sẽ được kích hoạt để ghi lại thông tin chi tiết của email.
    *   Thông tin được ghi lại bao gồm: người gửi (from), người nhận (to), người nhận CC (cc), tiêu đề (subject), nội dung HTML (html_content), và một mã hash duy nhất cho email.
    *   Trạng thái ban đầu của email log là `0` (chưa xử lý hoặc đang chờ xử lý).

---
## 4. Phụ lục

*(Phần này có thể bổ sung các sơ đồ luồng dữ liệu, sơ đồ CSDL, hoặc các tài liệu thiết kế khác trong tương lai.)*
