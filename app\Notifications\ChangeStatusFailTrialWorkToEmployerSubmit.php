<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailTrialWorkToEmployerSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $point;

    public function __construct($submitCv,$point)
    {
        $this->submitCv = $submitCv;
        $this->point = $point;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $employerName   = $this->submitCv->employer->name;
        $candidateName  = $this->submitCv->warehouseCv->candidate_name;
        $position       = $this->submitCv->job->name;
        $companyName    = $this->submitCv->employer->name;
        // $url            = route('employer-cv-bought',['view-detail' => $this->submitCv->wareHouseCvSelling->id]);
        $url            = route('employer-submitcv', ['view-detail' => $this->submitCv->id]);
        $employerWallet = route('employer-wallet');
        $linkMarket     = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusFailTrialWorkToEmployerSubmit', [
                'employerName'   => $employerName,
                'candidateName'  => $candidateName,
                'companyName'    => $companyName,
                'position'       => $position,
                'url'            => $url,
                'employerWallet' => $employerWallet,
                'linkMarket'     => $linkMarket,
                'point'          => $this->point,
            ])
            ->subject('Nhà tuyển dụng đã cập nhật trạng thái “Fail trial work” ứng viên '.$candidateName.' vị trí '.$position);

    }




}
