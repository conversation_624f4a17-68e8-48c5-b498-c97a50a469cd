<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeJob extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'jobs';

    protected $fillable = [
        'status_id',
        'company_id',
        'employer_id',
        'name',
        'slug',
        'description',
        'requirement',
        'benefit',
        'content',
        'vacancy',
        'published_at',
        'published_at_show',
        'expired_at',
        'salary_min',
        'salary_max',
        'salary_text',
        'salary_is_negotiate',
        'order',
        'is_real',
        'source',
        'status_active',
    ];

    protected $casts = [
        'published_at' => 'date',
        'published_at_show' => 'date',
        'expired_at' => 'date',
    ];

    public function company()
    {
        return $this->belongsTo(FakeCompany::class, 'company_id', 'id');
    }

    public function employer()
    {
        return $this->belongsTo(FakeEmployer::class, 'employer_id', 'id');
    }

    public function addresses()
    {
        return $this->hasMany(FakeJobAddress::class, 'job_id', 'id');
    }
}
