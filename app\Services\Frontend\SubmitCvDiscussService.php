<?php

namespace App\Services\Frontend;

use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\EmployerSendDiscusToRec;
use App\Notifications\EmployerSendSubmitDiscusToRec;
use App\Notifications\RecSendDiscusToEmployer;
use App\Notifications\RecSendSubmitDiscusToEmployer;
use App\Repositories\SubmitCvDiscussRepository;
use App\Repositories\WareHouseCvSellingBuyDiscussRepository;

class SubmitCvDiscussService
{

    protected $submitCvDiscussRepository;

    public function __construct(SubmitCvDiscussRepository $submitCvDiscussRepository)
    {
        $this->submitCvDiscussRepository = $submitCvDiscussRepository;
    }

    public function getBySubmitId($submitId)
    {
        return $this->submitCvDiscussRepository->getBySubmitId($submitId);
    }
    public function setReadByType ($submitId,$type){
        return $this->submitCvDiscussRepository->setReadByType($submitId,$type);
    }
    public function employerSendDiscuss($submitId,$comment){
        $user = auth('client')->user();
        $data = [
            'ntd_id'        => $user->id,
            'comment'       => $comment,
            'submit_cvs_id' => $submitId,
        ];
        // dd($data);
        $discuss = $this->submitCvDiscussRepository->create($data);
        $discuss->load('employer');
        $submitCv = $discuss->submitCv;
        $submitCv->rec->notify(new EmployerSendSubmitDiscusToRec($submitCv,$discuss));
        return $discuss;
    }

    public function recSendDiscuss($submitId,$comment){
        $user = auth('client')->user();
        $data = [
            'ctv_id' => $user->id,
            'comment' => $comment,
            'submit_cvs_id' => $submitId,
        ];
        $discuss = $this->submitCvDiscussRepository->create($data);
        $discuss->load('rec');
        $submitCv = $discuss->submitCv;
        $submitCv->employer->notify(new RecSendSubmitDiscusToEmployer($discuss));
        return $discuss;
    }




}
