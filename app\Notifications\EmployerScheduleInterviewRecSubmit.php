<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerScheduleInterviewRecSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $rec;
    protected $submitCvBook;

    public function __construct($submitCv,$rec,$submitCvBook)
    {
        $this->submitCv = $submitCv;
        $this->rec = $rec;
        $this->submitCvBook = $submitCvBook;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName       = $this->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $timeInterview    = $this->submitCvBook->date_time_book_format;
        $address          = $this->submitCvBook->address;
        $phone            = $this->submitCvBook->phone;
        $type_of_sale     = $this->submitCv->bonus_type;
        $link = route('rec-submitcv') . '?submit_id=' . $this->submitCv->id . '&open_comment=1';

        return (new MailMessage)
            ->view('email.employerScheduleInterviewRec', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'timeInterview' => $timeInterview,
                'address'       => $address,
                'phone'         => $phone,
                'type_of_sale'  => $type_of_sale,
                'link'          => $link,
            ])
            ->subject('[RECLAND] Thông báo lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);

    }

}

