<?php

namespace App\Repositories;

use App\Models\Wallet;

class WalletRepository extends BaseRepository
{
    const MODEL = Wallet::class;

    public function getWalletUser($id)
    {
        return $this->query()->where('user_id', $id)->first();
    }

    public function updateByUser($userId, $data)
    {
        return $this->query()->where('user_id', $userId)->update($data);
    }

    public function findByUser($userId)
    {
        return $this->query()->where('user_id', $userId)->first();
    }

    public function getPointWallet($userId)
    {
        $userWallet = $this->query()->where('user_id', $userId)->first();
        if (!$userWallet) {
            // create wallet
            $type = auth('client')->user()->type;
            $userWallet = $this->create([
                'user_id' => $userId,
                'type'    => $type,
                'point'   => 0,
                'amount'  => 0,
                'price'   => 0,
            ]);
        }

        return $userWallet ? $userWallet->amount : '';
    }
}
