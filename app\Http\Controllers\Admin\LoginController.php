<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LoginRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    public function getLogin()
    {
        return view('admin.pages.login.index');
    }

    public function postLogin(LoginRequest $request)
    {
        $credentials = [
            'email'     => $request->email,
            'password'  => $request->password,
            'type'      => config('constant.role.admin'),
            'is_active' => config('constant.active')
        ];
        $remember = isset($request->remember) ? true : false;

        if (Auth::guard('admin')->attempt($credentials, $remember)) {
            if (!empty($request->redirect) && $request->redirect != '') {
                return redirect(base64_decode($request->redirect));
            }
            return redirect()->route('dashboard');
        }

        $status = __('message.login_fail');
        return redirect()->route('login')->withErrors(['status' => $status]);
//        return view('admin.pages.login.index', compact('status'));
    }

    public function logout()
    {
        Auth::guard('admin')->logout();
        return redirect()->route('login');
    }
}
