<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecSendSubmitDiscusToEmployer extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCvDiscuss;

    public function __construct($submitCvDiscuss)
    {
        $this->submitCvDiscuss = $submitCvDiscuss;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->submitCvDiscuss->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCvDiscuss->submitCv->employer->name;
        $position      = $this->submitCvDiscuss->submitCv->job->name;
        $type          = $this->submitCvDiscuss->submitCv->bonus_type;
        // $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        // $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCvDiscuss->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCvDiscuss->submitCv->job)){
            $position = $this->submitCvDiscuss->submitCv->job->name;
        }
        $link = route('employer-submitcv',['discuss' => $this->submitCvDiscuss->submitCv->id]);;
        return (new MailMessage)
            ->view('email.recSendDiscusToEmployer', [
                'link'          => $link,
                'companyName'   => $companyName,
                'position'      => $position,
                'candidateName' => $candidateName,
                'comment'       => $this->submitCvDiscuss->comment,
            ])
            ->subject('[Recland] Bạn nhận được 1 thảo luận mới ứng viên '.$candidateName);

    }


}
