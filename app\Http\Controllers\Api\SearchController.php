<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    /**
     * Search công ty theo tham số "q"
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchCompany(Request $request): JsonResponse
    {
        try {
            $query = $request->input('q');

            if (empty($query)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tham số tìm kiếm "q" không được để trống',
                    'data' => []
                ], 400);
            }

            $companies = Company::where('is_active', 1)
                ->where(function ($q) use ($query) {
                    $q->where('name', 'LIKE', "%{$query}%");
                    //   ->orWhere('about', 'LIKE', "%{$query}%")
                    //   ->orWhere('address', 'LIKE', "%{$query}%");
                })
                ->where('is_real', 1)
                ->select([
                    'id',
                    'name',
                    'slug',
                    // 'address',
                    // 'logo',
                    // 'scale',
                    // 'website',
                    // 'about',
                    'created_at',
                    'updated_at'
                ])
                // ->orderBy('priority', 'desc')
                // ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Tìm kiếm công ty thành công',
                'data' => $companies
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm công ty',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search employer theo company_id
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function searchEmployer(Request $request): JsonResponse
    {
        try {
            $companyId = $request->input('company_id');

            if (empty($companyId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tham số "company_id" không được để trống',
                    'data' => []
                ], 400);
            }

            // Kiểm tra company có tồn tại không
            $company = Company::where('id', $companyId)
                // ->where('is_active', 1)
                ->first();

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy công ty với ID được cung cấp',
                    'data' => []
                ], 404);
            }

            $employers = User::where('company_id', $companyId)
                // ->whereHas('roles', function ($query) {
                //     $query->where('name', 'employer');
                // })
                // ->with(['company:id,name,slug'])
                ->select([
                    'id',
                    'name',
                    // 'email',
                    // 'mobile',
                    // 'company_id',
                    // 'avatar',
                    'created_at',
                    'updated_at'
                ])
                // ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Tìm kiếm employer thành công',
                'data' => $employers,
                // 'company_info' => $company
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm employer',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getCareer(Request $request): JsonResponse
    {
        try {
            $lang = app()->getLocale();
            $career = config('job.career.' . $lang);
            unset($career[0]);
            $career =   array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($career), array_values($career));
            return response()->json([
                'success' => true,
                'message' => 'Tìm kiếm career thành công',
                'data' => $career
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm career',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function getJobType(Request $request): JsonResponse
    {
        try {
            $lang = app()->getLocale();
            $jobType = config('job.type.' . $lang);
            unset($jobType[0]);
            $jobType =   array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($jobType), array_values($jobType));
            return response()->json([
                'success' => true,
                'message' => 'Tìm kiếm job type thành công',
                'data' => $jobType
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tìm kiếm career',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
