<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\CategoryRepository;

class CategoryService
{
    protected $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->categoryRepository->getListCategory($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false'
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-orderable' => 'false'
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Tên danh mục VN'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Tên danh mục EN'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'home_value',
                    'data-fn' => 'renderHome',
                ),
                'value' => 'Hiển thị trang chủ Blog'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                    'data-orderable' => 'false'
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('category-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function createService($params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $isHome = isset($params['home']) ? config('constant.active') : config('constant.inActive');
        $data = [
            'name_vi' => $params['name_vi'],
            'name_en' => $params['name_en'],
            'slug_vi' => Common::buildSlug($params['name_vi']),
            'slug_en' => Common::buildSlug($params['name_en']),
            'is_active' => $isActive,
            'home' => $isHome,
        ];

        return $this->categoryRepository->create($data);;
    }

    public function detailService($id)
    {
        return $this->categoryRepository->find($id);
    }

    public function updateService($params, $id)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $isHome = isset($params['home']) ? config('constant.active') : config('constant.inActive');
        $data = [
            'name_vi' => $params['name_vi'],
            'name_en' => $params['name_en'],
            'slug_vi' => Common::buildSlug($params['name_vi']),
            'slug_en' => Common::buildSlug($params['name_en']),
            'is_active' => $isActive,
            'home' => $isHome,
        ];

        $this->categoryRepository->update($id, [], $data);
        $this->_updateHome();
        return true;
    }

    //luc nao cung chi co 2 record Trang chu
    public function _updateHome()
    {
        $data = $this->categoryRepository->getListHome();
        if (count($data) > 3) {
            foreach ($data as $key => $value) {
                if ($key > 2) {
                    $this->categoryRepository->update($value['id'], [], ['home' => 0]);
                }
            }
        }
    }
}
