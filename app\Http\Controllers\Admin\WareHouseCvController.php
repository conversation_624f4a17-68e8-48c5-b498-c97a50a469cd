<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\WareHouseCvRequest;
use App\Services\Admin\SkillService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\WareHouseCvService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class WareHouseCvController extends Controller
{

    protected $wareHouseCvService;
    protected $skillService;

    public function __construct(
        WareHouseCvService $wareHouseCvService,
        SkillService       $skillService,
    )
    {
        $this->wareHouseCvService = $wareHouseCvService;
        $this->skillService = $skillService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->wareHouseCvService->buildDatatable();
        $total = $this->wareHouseCvService->total();
        $totalCurrentMonth = $this->wareHouseCvService->totalCurrentMonth();
        return view('admin.pages.cv.index', compact('datatable', 'total', 'totalCurrentMonth'));
    }

    /**
     * @param $id
     * @return Application|Factory|View
     */
    public function edit($id): View|Factory|Application
    {

        $data = $this->wareHouseCvService->detailService($id);
        $currency = config('constant.currency');
        $skill = $this->skillService->getName();
        $lang = app()->getLocale();
        $yearExperiences = config('constant.sonamkinhnghiem');
        $rank = config('job.rank.' . $lang);
        $career = config('job.career.' . $lang);
        return view('admin.pages.cv.edit', compact('data', 'currency', 'skill', 'yearExperiences', 'rank', 'career'));
    }

    /**
     * @param WareHouseCvRequest $request
     * @param $id
     * @return RedirectResponse
     */
    public function update(WareHouseCvRequest $request, $id): RedirectResponse
    {

        $data = $request->all();
        $data['career'] = implode(',', $data['career']);
        $this->wareHouseCvService->updateService($data, $id);
        Toast::success(__('message.edit_success'));
        return back();
    }

    public function datatable(Request $request)
    {
        $data = $this->wareHouseCvService->datatable($request->all(), true);
        return response($data);
    }
}
