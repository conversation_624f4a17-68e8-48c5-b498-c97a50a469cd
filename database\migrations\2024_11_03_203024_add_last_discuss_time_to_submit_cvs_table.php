<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->timestamp('last_discuss_time')->nullable();
        });
    }

    public function down()
    {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->dropColumn('last_discuss_time');
        });
    }
}; 