<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\WareHouseCv;
use Illuminate\Support\Str;

class PushToITNaviController extends Controller
{
    public function push(Request $request)
    {
        $cvId = $request->input('id');
        $cv = WareHouseCv::findOrFail($cvId);
        $user = $cv->user;
        $main_skill = $cv->main_skill;
        $skills = [];
        if ($main_skill) {
            $skills = json_decode($main_skill, true);
            if (count($skills)) {
                $skills = array_keys($skills);
            }
        }
        $data = [
            'name'           => $cv->candidate_name,
            'email'          => $cv->candidate_email,
            'mobile'         => $cv->candidate_mobile,
            'job_title'      => $cv->candidate_job_title,
            // 'dob'            => $user->birthday,
            'yoe'            => $cv->year_experience,
            // 'location'       => Str::slug($user->work_location),
            // 'languages'      => $cv->candidate,
            // 'level'          => $cv->candidate,
            'skills'         => $skills,
            'salary_expect'  => $cv->candidate_salary_expect,
            'salary_current' => $cv->candidate_currency,
            'assessment'     => $cv->assessment,
            'public_cv_url'  => gen_url_file_s3($cv->cv_public, '', false),
            'private_cv_url' => gen_url_file_s3($cv->cv_private, '', false),
            // Add more fields as needed
        ];
        $response = Http::withHeaders([
            'Authorization' => config('services.itnavi.api_key'),
            ])->post(config('services.itnavi.api_base_url') . 'api/candidates', $data);
        $json = $response->json();
        if ($json && isset($json['candidate']) && isset($json['candidate']['id'])) {
            $cv->setMeta('itnavi_candidate_id', $json['candidate']['id']);
        }
        return $json;
    }
}
