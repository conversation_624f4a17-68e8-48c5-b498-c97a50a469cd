---
description:
globs:
alwaysApply: false
---
# Models Guide

RecLand sử dụng Eloquent ORM của <PERSON>vel để tương tác với database. Dưới đây là các models chính:

## Core Models

- [User](mdc:app/Models/User.php) - Người dùng hệ thống (ứng viên, nhà tuyển dụng, admin)
- [Company](mdc:app/Models/Company.php) - Công ty và thông tin nhà tuyển dụng
- [Job](mdc:app/Models/Job.php) - Công việc đăng tuyển
- [Resume/CV](mdc:app/Models/Resume.php) - C<PERSON> của ứng viên
- [Application](mdc:app/Models/Application.php) - Đơn ứng tuyển vào công việc

## Supporting Models

- [Category](mdc:app/Models/Category.php) - <PERSON><PERSON> <PERSON><PERSON><PERSON> lĩnh vực công việc
- [Skill](mdc:app/Models/Skill.php) - <PERSON><PERSON> năng của ứng viên
- [JobType](mdc:app/Models/JobType.php) - Loại công việc (full-time, part-time, etc.)
- [Location](mdc:app/Models/Location.php) - Địa điểm công việc

## Transaction Models

- [Payment](mdc:app/Models/Payment.php) - Thanh toán dịch vụ
- [Transaction](mdc:app/Models/Transaction.php) - Lịch sử giao dịch
- [Subscription](mdc:app/Models/Subscription.php) - Đăng ký dịch vụ

## Relationships

- User có thể có nhiều CV (1:n)
- User có thể là người quản lý của Company (1:1)
- Company có thể đăng nhiều Jobs (1:n)
- User có thể ứng tuyển nhiều Jobs thông qua Applications (m:n)
- Job thuộc về nhiều Categories (m:n)

## Traits & Scopes

Các models sử dụng các traits và scopes để mở rộng chức năng:
- Traits: HasUuid, Searchable, HasStatus
- Scopes: active(), featured(), recent()
