<?php

namespace App\Services\Admin;

use App\Notifications\SendEmailStatusCancel;
use App\Notifications\SendEmailStatusSelling;
use App\Repositories\WareHouseCvSellingRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class WareHouseCvSellingService
{
    protected $wareHouseCvSellingRepository;

    public function __construct(WareHouseCvSellingRepository $wareHouseCvSellingRepository)
    {
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        try {
            $data = $this->wareHouseCvSellingRepository->getListCvSellingAll($params, $order, $paginate);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'authority',
                    'data-fn' => 'renderAuthority',
                    'data-orderable' => 'false',
                ],
                'value' => 'Hành động',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv.candidate_name',
                    'data-orderable' => 'false',
                ],
                'value' => 'Tên ứng viên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv.candidate_job_title',
                    'data-orderable' => 'false',
                ],
                'value' => 'Vị trí',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'candidate_mobile',
                    'data-orderable' => 'false',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'created_at_value',
                    'data-orderable' => 'false',
                ],
                'value' => 'Thời gian bán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ma_ctv_value',
                    'data-fn' => 'renderwith',
                    'data-orderable' => 'false',
                ],
                'value' => 'Mã CTV',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'status_value',
                    'data-fn' => 'renderStatusWareHouseCvSelling',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái bán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'price_format',
                    'data-orderable' => 'false',
                ],
                'value' => 'Giá bán',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'point',
                    'data-orderable' => 'false',
                ],
                'value' => 'Giá mua(point)',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'authority_value',
                    'data-fn' => 'renderAuthorityWareHouseCvSelling',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái ủy quyền',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv.url_cv_public',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPublic',
                ],
                'value' => 'CV Public',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'ware_house_cv.url_cv_private',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderCvPrivate',
                ],
                'value' => 'CV Private',
            ],
        ];

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('cv-selling-datatable'))
            ->setRenderValue($renderValue)
            ->setTitle('');
    }

    public function total()
    {
        return $this->wareHouseCvSellingRepository->total();
    }

    public function updateAuthority($request)
    {
        $id = $request['selling_id'];
        $warehouseCvSelling = $this->wareHouseCvSellingRepository->find($id);

        $save['reason'] = $request['reason'];
        $save['authority'] = $request['authority'];
        //Ấn vào xác nhận
        if ($request['authority'] == 2 && !empty($warehouseCvSelling->user)) {
            $save['status'] = 0;
            //gửi email cho CTV $warehouseCvSelling->wareHouseCv->candidate_email
            //tạo template email theo link dưới
            //https://docs.google.com/document/d/1KXJIZmB2lcfxNqLHYO9pY97odDRKYCUrDVEeMA1-bi4/edit
            Mail::to($warehouseCvSelling->user->email)->send(new SendEmailStatusSelling($warehouseCvSelling));
        }
        //ấn vào từ chối
        if ($request['authority'] == 3 && !empty($warehouseCvSelling->user)) {
            $save['status'] = 2;
            //gửi email cho CTV $warehouseCvSelling->wareHouseCv->candidate_email
            //tạo template email theo link dưới
            //https://docs.google.com/document/d/1zcmLUvhWIyb9ylcmWox1plZ6U09uYB0p35NkMcT9JKk/edit?usp=drive_link
            Mail::to($warehouseCvSelling->user->email)->send(new SendEmailStatusCancel($warehouseCvSelling));
        }

        return $this->wareHouseCvSellingRepository->update($id, [], $save);
    }

    public function createViewTokenUrl($request)
    {
        $id = $request['selling_id'];
        $warehouseCvSelling = $this->wareHouseCvSellingRepository->find($id);
        if ($warehouseCvSelling) {
            if (empty($warehouseCvSelling->view_token)) {
                $viewToken = Str::random(20);
                $warehouseCvSelling->view_token = $viewToken;
                $warehouseCvSelling->save();
            } else {
                $viewToken = $warehouseCvSelling->view_token;
            }
            // $url = env('APP_URL') . '/cv-selling/' . $viewToken;
            $url = route('market-cv', ['view_id' => $warehouseCvSelling->id, 'view_token' => $viewToken]);
            return $url;
        } else {
            return false;
        }
    }
}