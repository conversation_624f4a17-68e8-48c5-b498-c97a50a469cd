<?php

namespace App\Repositories;

use App\Models\Role;
use App\Models\User;

class RoleRepository extends BaseRepository
{
    const MODEL = Role::class;

    public function getListRole($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['search'])) {
            $query->where('id', $params['search'] )
                ->orWhere('name', 'like', '%' . $params['search'] . '%');
        }

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findByName($name){
        return $this->query()->where('name',$name)->first();
    }

}
