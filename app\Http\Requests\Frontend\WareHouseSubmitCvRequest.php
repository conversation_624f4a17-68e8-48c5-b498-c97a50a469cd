<?php

namespace App\Http\Requests\Frontend;

use App\Repositories\JobRepository;
use App\Rules\Frontend\CheckDuplicateSubmitByInfo;
use App\Rules\Frontend\CheckDuplicateSubmitByCv;
use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class WareHouseSubmitCvRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $jobRepository = app(JobRepository::class);
        if (isset($this->slug)) {
            $jobDetail = $jobRepository->findBySlug($this->slug);
        } elseif (isset($this->job)) {
            $jobDetail = $jobRepository->find($this->job);
        }
        $arr = [];
        if ($this->get('tab') == 'chontukho') {
            $arr = [
                /*'ware_house_cv'           => 'required',
                'ware_house_cv'           => ['required',new CheckDuplicateSubmitByCv($this->ware_house_cv,$jobDetail->id)],
                'expected_date'           => 'required',*/
                'status'                  => 'required',
            ];
        } else {
            if ($this->get('tab') == 'themmoi_cv') {
                $arr = [
//                    'slug'                    => 'required',
                    'status'                  => 'required',
                    'candidate_name'          => 'required',
//                    'expected_date'           => 'required',
                    'candidate_mobile'        => 'required',
                    'candidate_email'         => ['required','email',new CheckDuplicateSubmitByInfo($this->candidate_email,$this->candidate_mobile,$jobDetail->id)],
                    'candidate_job_title'     => 'required',
//                    'candidate_salary_expect' => 'required',
                    'candidate_currency'      => 'required',
                    /*'cv_public'               => 'required|mimes:pdf,docx|max:5120',
                    'cv_private'              => 'required|mimes:pdf,docx|max:5120',
                    'candidate_portfolio'     => 'required',*/
                ];
            }
        }

        return $arr;
    }

    protected function failedValidation(Validator $validator)
    {
        Toast::error(__('frontend/validation.error_validate'));
        $this->session()->flash('open_tab', $this->get('tab'));
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }

}
