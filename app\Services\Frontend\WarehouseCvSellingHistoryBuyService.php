<?php

namespace App\Services\Frontend;

use App\Repositories\WareHouseCvSellingHistoryBuyRepository;

class WarehouseCvSellingHistoryBuyService
{

    protected $wareHouseCvSellingHistoryBuyRepository;

    public function __construct(WareHouseCvSellingHistoryBuyRepository $wareHouseCvSellingHistoryBuyRepository)
    {
        $this->wareHouseCvSellingHistoryBuyRepository = $wareHouseCvSellingHistoryBuyRepository;
    }

    public function getCvHistory($params)
    {
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingHistoryBuyRepository->getCvHistoryByUser($userId, $params, true);
    }

    public function employerGetHistory($params){
        $userId = auth('client')->user()->id;
        $params['user_id'] = $userId;
        return $this->wareHouseCvSellingHistoryBuyRepository->employerGetHistory($params);
    }

    public function spentThisMonth(){
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingHistoryBuyRepository->spentThisMonth($userId);
    }

    public function spentThisYear(){
        $userId = auth('client')->user()->id;
        return $this->wareHouseCvSellingHistoryBuyRepository->spentThisYear($userId);
    }




}
