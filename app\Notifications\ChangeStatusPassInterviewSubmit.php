<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusPassInterviewSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->type_of_sale;
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->employer->name;
        // $url = route('rec-cv-sold',['cv_sold' =>  $this->submitCv->id,'open_comment' => 1]);
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id, 'open_comment' => 1]);
        return (new MailMessage)
            ->view('email.changeStatusPassInterviewSubmit', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'url'           => $url,
            ])
            ->subject('[Recland] Thông báo Ứng viên ' . $candidateName . ' đã Pass phỏng vấn của công ty ' . $companyName);
    }
}
