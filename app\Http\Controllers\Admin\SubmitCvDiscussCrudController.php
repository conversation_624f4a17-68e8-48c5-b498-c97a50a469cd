<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\SubmitCvDiscussRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use App\Http\Resources\Admin\DiscussResource;

class SubmitCvDiscussCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    
    public function setup()
    {
        CRUD::setModel(\App\Models\SubmitCv::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/submit-cv-discuss');
        CRUD::setEntityNameStrings('Thảo luận ứng tuyển', 'Thảo luận ứng tuyển');
        
        // Chỉ hiển thị các bản ghi có last_discuss_time khác null
        $this->crud->addClause('whereNotNull', 'last_discuss_time');
    }

    protected function setupListOperation()
    {
        CRUD::column('id')
            ->label('ID')
            ->type('custom_html')
            ->value(function($value) {
                return '<a href="' . route('submit-cv.edit', $value->id) . '" >' . $value->id . '</a>';
            });
        // Hiển thị thông tin từ bảng warehouse_cvs
        CRUD::column('warehouseCv.candidate_name')
            ->label('Tên ứng viên');
        
        // Hiển thị thông tin từ bảng companies
        CRUD::column('company.name')
            ->label('Tên công ty');
        
        // Hiển thị thông tin từ bảng jobs
        CRUD::column('job.name')
            ->label('Tên công việc');
        
        // Hiển thị thảo luận mới nhất từ bảng discusses
        CRUD::column('last_discuss_content')
            ->label('Thảo luận mới nhất')
            ->type('custom_html')
            ->value(function($value) {
                $lastDiscuss = $value->discuss()->latest()->first();
                if ($lastDiscuss) {
                    return '<div class="text-wrap">' . nl2br($lastDiscuss->message) . '</div>';
                }
                return '<div class="text-wrap">Không có thảo luận</div>';
            })
            // ->function(function($value) {
            //     return '<div class="text-wrap">fdfd</div>';
            // })
            ->limit(100); // Giới hạn số ký tự hiển thị
        
        CRUD::column('last_discuss_time')
            ->label('Thời gian thảo luận mới nhất')
            ->type('datetime');

        // Hiển thị thảo luận mới nhất từ bảng discusses
        CRUD::column('unread_discuss')
            ->label('Số tin nhắn chưa đọc')
        ->type('custom_html')
        ->value(function ($value) {
            $rec_unread = $value->getMeta('rec_unread_discuss');
            $employer_unread = $value->getMeta('employer_unread_discuss');
            if ($rec_unread + $employer_unread) {
                return '<span class="badge badge-danger">' . ($rec_unread + $employer_unread) . '</span>';
            }
            return '<span class="badge badge-success">0</span>';
        });
        // Thêm các bộ lọc
        CRUD::filter('candidate_name')
            ->type('text')
            ->label('Tên ứng viên')
            ->whenActive(function($value) {
                $this->crud->query->whereHas('warehouseCv', function($query) use ($value) {
                    $query->where('candidate_name', 'LIKE', "%$value%");
                });
            });

        CRUD::filter('company_name')
            ->type('text')
            ->label('Tên công ty')
            ->whenActive(function($value) {
                $this->crud->query->whereHas('company', function($query) use ($value) {
                    $query->where('name', 'LIKE', "%$value%");
                });
            });

        CRUD::column('discussions')
            ->label('Xem thảo luận')
            ->type('custom_html')
            ->value(function($entry) {
                return '<button type="button" 
                        class="btn btn-sm btn-primary show-discussions" 
                        data-submit-cv-id="'.$entry->id.'">
                        Xem thảo luận
                        </button>';
            });
    }
    public function getDiscussions($id)
    {
        $submitCv = \App\Models\SubmitCv::findOrFail($id);
        $discussions = $submitCv->discuss()->orderBy('created_at')->get();
        return DiscussResource::collection($discussions);
    }
} 