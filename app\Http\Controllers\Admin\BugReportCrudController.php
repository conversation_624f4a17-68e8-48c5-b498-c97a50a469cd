<?php

namespace App\Http\Controllers\Admin;


use App\Models\BugReport;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Http\Request;

/**
 * Class BugReportCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class BugReportCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(BugReport::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/bug-reports');
        CRUD::setEntityNameStrings('báo cáo lỗi', 'báo cáo lỗi');

        // Set custom views
        // CRUD::setListView('vendor.backpack.crud.list_bug_reports');
        CRUD::setShowView('vendor.backpack.crud.show_bug_report');

        // Disable create and delete operations
        $this->crud->denyAccess(['create', 'delete', 'update']);
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Giới hạn số bản ghi mỗi trang
        $this->crud->setDefaultPageLength(25);
        $this->crud->setPageLengthMenu([10, 25, 50, 100]);

        // Thêm widgets thống kê
        $this->crud->enableDetailsRow();
        // $this->addWidgets();

        // Cấu hình columns
        CRUD::column('id')
            ->label('ID')
            ->type('number');

        CRUD::column('user_name')
            ->label('Người báo cáo')
            ->type('custom_html')
            ->value(function ($entry) {
                return $entry->user ? $entry->user->name : '<span class="text-muted">N/A</span>';
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhereHas('user', function ($q) use ($searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            });

        CRUD::column('user_email')
            ->label('Email')
            ->type('custom_html')
            ->value(function ($entry) {
                return $entry->user ? $entry->user->email : '<span class="text-muted">N/A</span>';
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhereHas('user', function ($q) use ($searchTerm) {
                    $q->where('email', 'like', '%' . $searchTerm . '%');
                });
            });

        CRUD::column('url')
            ->label('URL')
            ->type('custom_html')
            ->value(function ($entry) {
                $shortUrl = \Str::limit($entry->url, 50);
                return '<a href="' . e($entry->url) . '" target="_blank" title="' . e($entry->url) . '">'
                    . e($shortUrl) . ' <i class="fa fa-external-link-alt"></i></a>';
            })
            ->orderable(false);

        CRUD::column('short_description')
            ->label('Mô tả')
            ->type('custom_html')
            ->value(function ($entry) {
                return '<span title="' . e($entry->description) . '">' . e($entry->short_description) . '</span>';
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhere('description', 'like', '%' . $searchTerm . '%');
            });

        CRUD::column('image_preview')
            ->label('Ảnh')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->image_path) {
                    return '<img src="' . gen_url_file_s3($entry->image_path) . '" alt="Bug Image"
                        style="max-width: 80px; max-height: 50px; cursor: pointer;"
                        class="img-thumbnail" onclick="showImageModal(\'' . gen_url_file_s3($entry->image_path) . '\')">';
                }
                return '<span class="text-muted">Không có ảnh</span>';
            })
            ->orderable(false)
            ->searchLogic(false);

        CRUD::column('status')
            ->label('Trạng thái')
            ->type('custom_html')
            ->value(function ($entry) {
                $class = $entry->status === 'pending' ? 'badge-warning' : 'badge-success';
                $text = $entry->status === 'pending' ? 'Chờ xử lý' : 'Đã xử lý';
                return '<span class="badge ' . $class . '">' . $text . '</span>';
            });

        CRUD::column('created_at')
            ->label('Thời gian')
            ->type('closure')
            ->function(function ($entry) {
                return $entry->created_at ? $entry->created_at->format('d/m/Y H:i') : 'N/A';
            });

        // Tùy chỉnh ordering
        $this->crud->orderBy('id', 'desc');

        // Thêm filters
        $this->setupFilters();
    }

    /**
     * Define what happens when the Show operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-show
     * @return void
     */
    protected function setupShowOperation()
    {
        $this->setupListOperation();

        // Thêm thông tin chi tiết
        CRUD::column('description')
            ->label('Mô tả đầy đủ')
            ->type('custom_html')
            ->value(function ($entry) {
                return '<div class="border p-3 bg-light rounded">' . nl2br(e($entry->description)) . '</div>';
            });

        CRUD::column('image_full')
            ->label('Ảnh minh họa')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->image_path) {
                    return '<img src="' . $entry->image_url . '" alt="Bug Report Image"
                        class="img-thumbnail" style="max-width: 100%; max-height: 400px; cursor: pointer;"
                        onclick="showImageModal(\'' . $entry->image_url . '\')">';
                }
                return '<span class="text-muted">Không có ảnh</span>';
            });

        CRUD::column('updated_at')
            ->label('Cập nhật cuối')
            ->type('closure')
            ->function(function ($entry) {
                return $entry->updated_at ? $entry->updated_at->format('d/m/Y H:i') : 'N/A';
            });
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        // Chỉ cho phép cập nhật trạng thái
        CRUD::field('status')
            ->label('Trạng thái')
            ->type('select_from_array')
            ->options([
                'pending' => 'Chờ xử lý',
                'resolved' => 'Đã xử lý'
            ])
            ->allows_null(false);

        // Hiển thị thông tin chỉ đọc
        CRUD::field('user_info')
            ->label('Người báo cáo')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->user) {
                    return $entry->user->name . ' (' . $entry->user->email . ')';
                }
                return 'N/A';
            })
            ->attributes(['readonly' => true]);

        CRUD::field('url')
            ->label('URL')
            ->type('url')
            ->attributes(['readonly' => true]);

        CRUD::field('description')
            ->label('Mô tả')
            ->type('textarea')
            ->attributes(['readonly' => true, 'rows' => 5]);

        if ($this->crud->getCurrentEntry() && $this->crud->getCurrentEntry()->image_path) {
            CRUD::field('image_display')
                ->label('Ảnh minh họa')
                ->type('custom_html')
                ->value('<img src="' . $this->crud->getCurrentEntry()->image_url . '"
                    class="img-thumbnail" style="max-width: 300px;">');
        }
    }

    /**
     * Add widgets to the list view
     */
    private function addWidgets()
    {
        // Widget tổng số báo cáo
        $this->crud->addWidget([
            'type' => 'card',
            'class' => 'card bg-primary text-white',
            'wrapper' => ['class' => 'col-sm-6 col-md-3'],
            'content' => [
                'header' => $this->getTotalReports(),
                'body' => 'Tổng báo cáo',
                'footer' => '<i class="fa fa-bug"></i>',
            ]
        ]);

        // Widget báo cáo chờ xử lý
        $this->crud->addWidget([
            'type' => 'card',
            'class' => 'card bg-warning text-white',
            'wrapper' => ['class' => 'col-sm-6 col-md-3'],
            'content' => [
                'header' => $this->getPendingReports(),
                'body' => 'Chờ xử lý',
                'footer' => '<i class="fa fa-clock"></i>',
            ]
        ]);

        // Widget báo cáo đã xử lý
        $this->crud->addWidget([
            'type' => 'card',
            'class' => 'card bg-success text-white',
            'wrapper' => ['class' => 'col-sm-6 col-md-3'],
            'content' => [
                'header' => $this->getResolvedReports(),
                'body' => 'Đã xử lý',
                'footer' => '<i class="fa fa-check"></i>',
            ]
        ]);

        // Widget báo cáo tháng này
        $this->crud->addWidget([
            'type' => 'card',
            'class' => 'card bg-info text-white',
            'wrapper' => ['class' => 'col-sm-6 col-md-3'],
            'content' => [
                'header' => $this->getReportsThisMonth(),
                'body' => 'Báo cáo tháng này',
                'footer' => '<i class="fa fa-calendar"></i>',
            ]
        ]);
    }

    /**
     * Setup filters for the list view
     */
    private function setupFilters()
    {
        // Filter theo trạng thái
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'status',
                'label' => 'Trạng thái'
            ],
            [
                'pending' => 'Chờ xử lý',
                'resolved' => 'Đã xử lý'
            ],
            function ($value) {
                $this->crud->addClause('where', 'status', $value);
            }
        );

        // Filter theo thời gian tạo
        $this->crud->addFilter(
            [
                'type' => 'date_range',
                'name' => 'created_at',
                'label' => 'Thời gian tạo'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                $this->crud->addClause('where', 'created_at', '>=', $dates->from);
                $this->crud->addClause('where', 'created_at', '<=', $dates->to . ' 23:59:59');
            }
        );
    }

    /**
     * Get total number of bug reports
     */
    private function getTotalReports()
    {
        return BugReport::count();
    }

    /**
     * Get number of pending bug reports
     */
    private function getPendingReports()
    {
        return BugReport::where('status', 'pending')->count();
    }

    /**
     * Get number of resolved bug reports
     */
    private function getResolvedReports()
    {
        return BugReport::where('status', 'resolved')->count();
    }

    /**
     * Get number of bug reports created this month
     */
    private function getReportsThisMonth()
    {
        return BugReport::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
    }

    /**
     * Custom method to update status via AJAX
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,resolved'
        ]);

        $bugReport = BugReport::findOrFail($id);
        $bugReport->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'Trạng thái báo cáo đã được cập nhật thành công.'
        ]);
    }
}
