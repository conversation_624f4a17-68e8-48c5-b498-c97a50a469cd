# RecLand - Suggested Commands

## Development Commands

### Laravel Artisan Commands
```powershell
# Khởi động development server
php artisan serve

# Chạy migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Seed database
php artisan db:seed

# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Optimize for production
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate application key
php artisan key:generate

# Run queue workers
php artisan queue:work

# List all artisan commands
php artisan list
```

### Composer Commands
```powershell
# Install dependencies
composer install

# Update dependencies
composer update

# Install production dependencies only
composer install --no-dev --optimize-autoloader

# Dump autoload
composer dump-autoload
```

### NPM Commands
```powershell
# Install frontend dependencies
npm install

# Development build
npm run dev

# Watch for changes
npm run watch

# Production build
npm run prod

# Hot reload (if configured)
npm run hot
```

### Testing Commands
```powershell
# Run all tests
php artisan test

# Run PHPUnit tests
./vendor/bin/phpunit

# Run specific test file
php artisan test tests/Feature/ExampleTest.php

# Run tests with coverage
php artisan test --coverage
```

### Database Commands
```powershell
# Create migration
php artisan make:migration create_table_name

# Create model with migration
php artisan make:model ModelName -m

# Create seeder
php artisan make:seeder TableSeeder

# Fresh migrate with seed
php artisan migrate:fresh --seed
```

### Custom Artisan Commands (dự án specific)
```powershell
# Generate version
php artisan version:generate

# Clean old data
php artisan clean:data

# Process CV
php artisan cv:process

# Update CTV level
php artisan update:ctv-level

# Sync bonus
php artisan sync:bonus

# Check expired CVs
php artisan check:cv-expired
```

## Windows PowerShell Utility Commands

### File Operations
```powershell
# List directory contents
Get-ChildItem
# or
ls

# Change directory
Set-Location path\to\directory
# or
cd path\to\directory

# Create directory
New-Item -ItemType Directory -Name "folder_name"
# or
mkdir folder_name

# Copy files
Copy-Item source destination

# Remove files/folders
Remove-Item path\to\file
Remove-Item path\to\folder -Recurse
```

### Git Commands
```powershell
# Clone repository
git clone <repository-url>

# Check status
git status

# Add files
git add .
git add filename

# Commit changes
git commit -m "commit message"

# Push changes
git push origin branch-name

# Pull changes
git pull origin branch-name

# Create new branch
git checkout -b new-branch-name

# Switch branch
git checkout branch-name

# Merge branch
git merge branch-name
```

### Process Management
```powershell
# Find process by name
Get-Process -Name "process_name"

# Kill process by ID
Stop-Process -Id <process_id>

# Kill process by name
Stop-Process -Name "process_name"
```

## Deployment Commands

### Development Deployment
```powershell
# Run development deploy script
.\dev-deploy.sh
```

### Production Deployment
```powershell
# Run production deploy script
.\prod-deploy.sh
```

### Manual Deployment Steps
```powershell
# 1. Pull latest code
git pull origin main

# 2. Install/update dependencies
composer install --no-dev --optimize-autoloader

# 3. Run migrations
php artisan migrate --force

# 4. Clear and cache
php artisan optimize:clear
php artisan optimize

# 5. Generate version
php artisan version:generate

# 6. Set permissions (if needed)
# chmod -R 755 storage bootstrap/cache
```