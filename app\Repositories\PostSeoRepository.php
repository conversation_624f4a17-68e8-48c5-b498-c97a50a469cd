<?php

namespace App\Repositories;

use App\Models\PostSeo;

class PostSeoRepository extends BaseRepository
{
    const MODEL = PostSeo::class;

    public function findWithPostId($postId)
    {
        return $this->query()->where('post_id', $postId)->first();
    }

    public function updateWithPostId($postId, $data)
    {
        return $this->query()->where('post_id', $postId)->update($data);
    }

}
