<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerComplain extends Notification implements ShouldQueue
{

    use Queueable;

    protected $rec;
    protected $employer;
    protected $cvSellingBuy;

    public function __construct($rec,$employer,$cvSellingBuy)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->cvSellingBuy = $cvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {

        $candidateName = '';
        $position = '';
        $type = '';
        $content = $this->cvSellingBuy->txt_complain;
        $image = $this->cvSellingBuy->img_complain;
        $companyName = $this->employer->name;

        if (!empty($this->cvSellingBuy->wareHouseCvSelling)){
            $type = $this->cvSellingBuy->wareHouseCvSelling->type_of_sale;
        }

        if (!empty($this->cvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->cvSellingBuy->job)){
                $position = $this->cvSellingBuy->job->name;
            }
        }
        $link = route('rec-cv-sold',['cv_sold' =>  $this->cvSellingBuy->id,'open_complain' => 1]);
        return (new MailMessage)
            ->view('email.employer_complain_rec', [
                'name' => $this->rec->name,
                'companyName' => $companyName,
                'candidateName' => $candidateName,
                'position' => $position,
                'type' => $type,
                'content' => $content,
                'image' => gen_url_file_s3($image),
                'link' => $link,
            ])
            ->subject('[Recland][Case #'.$this->cvSellingBuy->id.']Thông báo khiếu nại của công ty '.$companyName.' đối với Ứng viên '.$candidateName);

    }


}
