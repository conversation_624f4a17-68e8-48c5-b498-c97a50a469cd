<?php

namespace App\Console\Commands;

use App\Models\WareHouseCv;
use App\Models\ItnaviSearchCv;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateWarehouseCvCreatedAt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'warehouse-cv:update-created-at 
                            {--dry-run : Chạy thử không thực sự cập nhật}
                            {--limit=100 : Số lượng CV xử lý mỗi batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật created_at cho warehouse_cvs từ bảng itnavi_search_cvs dựa trên email';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $limit = (int) $this->option('limit');

        $this->info('Bắt đầu cập nhật created_at cho warehouse_cvs...');
        $this->info('Dry run: ' . ($isDryRun ? 'Có' : 'Không'));
        $this->info('Limit: ' . $limit);
        $this->newLine();

        // Lấy danh sách CV cần cập nhật
        $query = WareHouseCv::whereNull('created_at')
                           ->where('source', 'itnavi_import_tructiep');

        $totalCount = $query->count();
        $this->info("Tổng số CV cần cập nhật: {$totalCount}");

        if ($totalCount === 0) {
            $this->info('Không có CV nào cần cập nhật.');
            return Command::SUCCESS;
        }

        $processedCount = 0;
        $updatedCount = 0;
        $notFoundCount = 0;

        // Xử lý theo batch
        $query->chunk($limit, function ($warehouseCvs) use ($isDryRun, &$processedCount, &$updatedCount, &$notFoundCount) {
            foreach ($warehouseCvs as $warehouseCv) {
                $processedCount++;
                
                $this->info("Xử lý CV #{$warehouseCv->id} - Email: {$warehouseCv->candidate_email}");

                // Tìm trong bảng itnavi_search_cvs
                $itnaviSearchCv = ItnaviSearchCv::where('email', $warehouseCv->candidate_email)
                                               ->orderBy('created_at', 'desc')
                                               ->first();

                if ($itnaviSearchCv && $itnaviSearchCv->created_at) {
                    $this->line("  → Tìm thấy trong itnavi_search_cvs: {$itnaviSearchCv->created_at}");
                    
                    if (!$isDryRun) {
                        // Cập nhật created_at
                        $warehouseCv->update([
                            'created_at' => $itnaviSearchCv->created_at,
                            'updated_at' => $itnaviSearchCv->updated_at
                        ]);
                        
                        $this->line("  → Đã cập nhật thành công!");
                    } else {
                        $this->line("  → [DRY RUN] Sẽ cập nhật created_at = {$itnaviSearchCv->created_at}");
                    }
                    
                    $updatedCount++;
                } else {
                    $this->warn("  → Không tìm thấy email trong itnavi_search_cvs");
                    $notFoundCount++;
                }

                // Hiển thị progress mỗi 10 records
                if ($processedCount % 10 === 0) {
                    $this->info("Đã xử lý: {$processedCount} CV");
                }
            }
        });

        $this->newLine();
        $this->info('=== KẾT QUẢ ===');
        $this->info("Tổng số CV đã xử lý: {$processedCount}");
        $this->info("Số CV được cập nhật: {$updatedCount}");
        $this->info("Số CV không tìm thấy email: {$notFoundCount}");

        if ($isDryRun) {
            $this->warn('Đây là chế độ dry-run, không có dữ liệu nào được thay đổi thực sự.');
            $this->info('Để thực hiện cập nhật thật, chạy lại command mà không có --dry-run');
        } else {
            $this->info('Hoàn thành cập nhật!');
        }

        return Command::SUCCESS;
    }
}
