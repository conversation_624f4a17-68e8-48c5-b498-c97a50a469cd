<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\DepositRequest;
use App\Models\ZalopayTransaction;
use App\Services\Frontend\DepositService;
use App\Services\Frontend\WalletService;
use App\Services\Frontend\ZalopayTransactionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;


class ZalopayTransactionController extends Controller
{
    protected $zalopayTransactionService;

    public function __construct(ZalopayTransactionService $zalopayTransactionService)
    {
        $this->zalopayTransactionService = $zalopayTransactionService;
    }

    public function store(DepositRequest $request)
    {
        try {
            $employer_id = auth('client')->user()->id;
            $params['amount'] = $request->amount;
            $params['app_trans_id'] = date("ymd") . "_U" . $employer_id . "_" . rand(0, 1000000);
            $params['user_id'] = $employer_id;
            $params['bank_code'] = $request->payment_method;

            //            dd($params);
            // Lấy bankcode từ phương thức thanh toán và request
            $params['bank_code'] = $this->getBankCode($params['bank_code']);
            $params['created_at'] = date('Y-m-d H:i:s');
            $params['updated_at'] = date('Y-m-d H:i:s');
            // dd($params);

            // Lưu đơn hàng tạm thời vào cơ sở dữ liệu
            $result = $this->zalopayTransactionService->insert($params);

            if ($result) {
                // Tạo yêu cầu thanh toán tới ZaloPay với bankcode đã lấy
                $zalopayResponse = $this->createZaloPayOrder($params);


                // Kiểm tra phản hồi từ ZaloPay và xử lý tương ứng
                if (isset($zalopayResponse['return_code']) && $zalopayResponse['return_code'] == 1) {
                    return response()->json(['success' => 1, 'order_url' => $zalopayResponse['order_url'], 'app_trans_id' => $params['app_trans_id']]);
                } else {
                    return response()->json(['error' => 'Không thể tạo đơn hàng trên ZaloPay']);
                }
            } else {
                return response()->json(['error' => 'Lỗi khi lưu đơn hàng']);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()]);
        }
    }



    protected function createZaloPayOrder($params)
    {
        $recName = auth('client')->user()->name;

        $config = [
            "appid"    => config('payment.zalo_appid'),
            "key1"     => config('payment.zalo_key1'),
            "key2"     => config('payment.zalo_key2'),
            "endpoint" => config('payment.zalo_endpoint'),
        ];

        $embeddata = [
            "merchantinfo" => "",
            //            "callback_url" => route('zalopay-callback'),
            "redirect_url" => route('zalopay-process-after-payment')
        ];

        if ($params['bank_code'] == 'zalopay_qr') {
            $params['bank_code'] = '';
            // $embeddata['preferred_payment_method'] = ['zalopay_wallet'];
            $embeddata['preferred_payment_method'] = [];
        } elseif ($params['bank_code'] == 'vietqr') {
            $params['bank_code'] = '';
            $embeddata['preferred_payment_method'] = ['vietqr'];
        }

        $items = [
            [
                "itemid"       => (int)$params['user_id'],
                "itemname"     => 'Nạp tiền vào tài khoản',
                "itemprice"    => (int)$params['amount'],
                "itemquantity" => 1
            ]
        ];

        $order = [
            "app_id"       => $config["appid"],
            "app_time"     => round(microtime(true) * 1000),
            "app_trans_id" => $params['app_trans_id'],
            "app_user"     => $recName,
            "item"         => json_encode($items, JSON_UNESCAPED_UNICODE),
            "embed_data"   => json_encode($embeddata, JSON_UNESCAPED_UNICODE),
            "amount"       => (int)$params['amount'],
            "description"  => "RECLAND - Thanh toán đơn hàng #" . $params['app_trans_id'],
            "bank_code"    => $params['bank_code']
        ];

        // dd($order);
        $data = $order["app_id"] . "|" . $order["app_trans_id"] . "|" . $order["app_user"] . "|" . $order["amount"]
            . "|" . $order["app_time"] . "|" . $order["embed_data"] . "|" . $order["item"];
        $order["mac"] = hash_hmac("sha256", $data, $config["key1"]);

        $context = stream_context_create([
            "http" => [
                "header" => "Content-type: application/x-www-form-urlencoded\r\n",
                "method" => "POST",
                "content" => http_build_query($order)
            ]
        ]);

        $resp = file_get_contents($config["endpoint"], false, $context);
        $result = json_decode($resp, true);

        foreach ($result as $key => $value) {
            //            echo "$key: $value<br>";
        }

        return $result;
    }

    public function zalopayProcessAfterPayment(Request $request)
    {
        try {
            return redirect()->route('employer-wallet', ['apptransid' => $request->get('apptransid')]); //->with('success', 'Giao dịch thành công');
            $transId = $_GET['apptransid'];
            $status = $_GET['status'];

            if ($status == 1) {
                $result = $this->zalopayTransactionService->getTransactionPayment($transId);
                if ($result) {
                    $result->status = '1';
                    $result->save();
                    return redirect()->route('employer-wallet')->with('success', 'Giao dịch thành công');
                } else {
                    return redirect()->route('zalopay-callback')->with('error', 'Không tìm thấy đơn hàng');
                }
            }
        } catch (\Exception $e) {
            //            return redirect()->route('zalopay-callback')->with('error', 'Giao dịch không thành công');
            return response()->json(['error' => $e->getMessage()]);
        }
    }

    public function zalopayCallback()
    {
        $result = [];

        try {
            //            die(404);
            $key2 = config('payment.zalo_key2');
            $postdata = file_get_contents('php://input');
            $postdatajson = json_decode($postdata, true);
            // Log::info('ZaloPay Callback: ' . json_encode($postdatajson));
            $mac = hash_hmac("sha256", $postdatajson["data"], $key2);

            $requestmac = $postdatajson["mac"];

            // kiểm tra callback hợp lệ (đến từ ZaloPay server)
            if (strcmp($mac, $requestmac) != 0) {
                // callback không hợp lệ
                $result["return_code"] = -1;
                $result["return_message"] = "mac not equal";
            } else {
                DB::beginTransaction();
                // thanh toán thành công
                // merchant cập nhật trạng thái cho đơn hàng
                $datajson = json_decode($postdatajson["data"], true);
                $trans = ZalopayTransaction::where('app_trans_id', $datajson['app_trans_id'])->first();
                if ($trans && $trans->status == 0) {
                    # cong tien vao vi
                    $trans->status = 1;
                    $trans->raw_data = $datajson;
                    $trans->zp_trans_id = $datajson['zp_trans_id'];
                    $trans->save();
                    $walletService = resolve(WalletService::class);
                    $walletService->addAmount($trans->user_id, $datajson['amount'], 'Nạp tiền Zalo', 'zalo');
                    $currentBalance = $walletService->getCurrentBalance($trans->user_id);
                    # Tao Log deposit
                    $depositService = resolve(DepositService::class);
                    $depositService->insert([
                        'user_id'               => $trans->user_id,
                        'amount'                => $datajson['amount'],
                        'balance'               => $currentBalance,
                        'point'                 => 0,
                        'balance_point'         => 0,
                        'transaction_source_id' => $trans->app_trans_id,
                        'note'                  => 'RECLAND - Thanh toán đơn hàng #' . $trans->app_trans_id,
                        'type'                  => 'zalo',
                        'deposit_time'          => $trans->created_at,
                        'status'                => 1
                    ]);

                    $result["return_code"] = 1;
                    $result["return_message"] = "Success";
                } else {
                    $result["return_code"] = -1;
                    $result["return_message"] = "Transaction not found or already processed";
                }
                DB::commit();
            }
        } catch (\Exception $e) {
            DB::rollBack();
            $result["return_code"] = 0; // ZaloPay server sẽ callback lại (tối đa 3 lần)
            $result["return_message"] = $e->getMessage();
            Log::error($e->getMessage());
        }

        // thông báo kết quả cho ZaloPay server
        return response()->json($result, 200, [], JSON_UNESCAPED_UNICODE);
    }

    protected function getBankCode($method)
    {
        $bankCodes = [
            'credit'     => 'CC',
            'atm'        => '',
            'zalo'       => 'zalopayapp',
            'zalopay_qr' => 'zalopay_qr',
            'vietqr'     => 'vietqr',
            'qr'         => 'QRCODE',
        ];

        return $bankCodes[$method] ?? '';
    }
}