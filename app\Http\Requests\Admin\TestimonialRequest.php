<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TestimonialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
            {
                return [
                    'name_vi' => 'required',
                    'name_en' => 'required',
                    'type' => 'required',
                    'position_vi' => 'required',
                    'position_en' => 'required',
                    'content_vi' => 'required',
                    'content_en' => 'required',
                    'avatar' => 'required|mimes:jpeg,jpg,png|max:5120'
                ];
            }
            case 'PUT':
            {
                return [
                    'name_vi' => 'required',
                    'name_en' => 'required',
                    'type' => 'required',
                    'position_vi' => 'required',
                    'position_en' => 'required',
                    'content_vi' => 'required',
                    'content_en' => 'required',
                    'avatar' => 'mimes:jpeg,jpg,png|max:5120'
                ];
            }
            default:
                break;
        }
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'mimes' => __('message.mimes'),
            'max' => __('message.file_max'),
        ];
    }
}
