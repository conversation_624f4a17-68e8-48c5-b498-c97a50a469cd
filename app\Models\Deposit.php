<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Deposit extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'deposits';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['created_at_value', 'updated_at_value','status_value'];

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }

    public function getUpdatedAtValueAttribute()
    {
        return $this->updated_at ? $this->updated_at->format('d/m/Y H:i') : null;
    }

    public function getStatusValueAttribute()
    {
        return config('constant.deposit_status.'.$this->status);
    }

    public function employer(){
        return $this->hasOne(User::class,'id','user_id');
    }

    public function getDepositTimeValueAttribute()
    {
        return $this->deposit_time ? Carbon::createFromFormat('Y-m-d H:i:s',$this->deposit_time) ->format('d/m/Y H:i') : null;
    }


}
