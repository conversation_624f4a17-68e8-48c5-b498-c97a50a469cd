<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusRecruiterCancelInterviewToRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }


    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $employerName  = $this->submitCv->employer->name;
        $recName       = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $url           = route('rec-submitcv',['submit_id' =>  $this->submitCv->id,'open_complain' => 1]);
        $link          = route('job-index');
        return (new MailMessage)
        ->view('email.changeStatusRecruiterCancelInterviewToRec', [
                'recName'       => $recName,
                'employerName'  => $employerName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'url'           => $url,
                'link'          => $link,
            ])
            ->subject('[Recland] Thông báo đóng trường hợp giới thiệu ứng viên '.$candidateName.' vị trí '.$position. '  của công ty  ' .$companyName);

    }


}

