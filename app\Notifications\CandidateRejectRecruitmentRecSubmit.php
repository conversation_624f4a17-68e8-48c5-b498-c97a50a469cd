<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CandidateRejectRecruitmentRecSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        //ten ctv
        $name = $this->submitCv->rec->name;  //name ctv
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $type = $this->submitCv->bonus_type;
        $candidateJobTitle = '';
        if ($type == 'cv'){
            $candidateJobTitle = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $candidateJobTitle = $this->submitCv->job->name;
        }
        $link = route('rec-submitcv') . '?submit_id=' . $this->submitCv->id;
        $linkOther = route('job-index');

       return (new MailMessage)
            ->view('email.ungvien_tuchoi_ungtuyen_ctv', [
                'name' => $name,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'candidateJobTitle' => $candidateJobTitle,
                'type'  => $type,
                'link' => $link,
                'linkOther' => $linkOther,
            ])
            ->subject('[Recland] Thông báo Ứng viên '. $candidateName .' đã từ chối lời mời ứng tuyển công ty '. $companyName);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [

        ];
    }
}
