<?php

namespace App\Listeners;

use Illuminate\Mail\Events\MessageSending;
use Request;
use App\Models\AccessLog;
use App\Jobs\CreateEmailLog;

class LogSendingEmail
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  MessageSending  $event
     * @return void
     */
    public function handle(MessageSending $event)
    {
        $message = $event->message;
        dispatch(new CreateEmailLog($message));
    }
}