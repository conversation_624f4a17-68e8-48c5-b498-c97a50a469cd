<?php

namespace App\Repositories;

use App\Models\UserInfo;

class UserInfoRepository extends BaseRepository
{
    const MODEL = UserInfo::class;

    public function updateByUserId($userid,$options = [],$data = []){
        $userInfo = $this->findByUserId($userid);
        if (!$userInfo){
            $userInfo = $this->create(['user_id'=>$userid]);
        }
        $this->update($userInfo->id,[],$data);
    }

    public function findByUserId($userId){
        return $this->query()->where('user_id',$userId)->first();
    }


}
