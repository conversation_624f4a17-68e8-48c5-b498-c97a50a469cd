<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class CandidateRejectRecruitmentAdminSubmit extends Mailable
{
    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $type = $this->submitCv->type_of_sale;
        $candidateJobTitle = '';
        if ($type == 'cv'){
            $candidateJobTitle = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $candidateJobTitle = $this->submitCv->job->name;
        }
        // $link = route('luot-ban.show',['luot_ban' => $this->submitCv->id]);
        $link = '';

        return new Content(
            view: 'email.ungvien_tuchoi_ungtuyen_admin',
            with: [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'candidateJobTitle' => $candidateJobTitle,
                'type'  => $type,
                'link' => $link,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $message->subject('[Ủy quyền] Thông báo Ứng viên ' . $candidateName . ' đã từ chối lời mời ứng tuyển công ty ' . $companyName);
        return $this;
    }

}
