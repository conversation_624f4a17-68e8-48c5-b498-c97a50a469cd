<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class WalletTopupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:1000|max:999999999',
            'transaction_type' => 'required|in:add,subtract',
            'note' => 'nullable|string|max:255'
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'user_id.required' => 'Vui lòng chọn người dùng',
            'user_id.exists' => 'Người dùng không tồn tại',
            'amount.required' => 'Vui lòng nhập số tiền',
            'amount.numeric' => 'Số tiền phải là số',
            'amount.min' => 'Số tiền tối thiểu là 1.000 VNĐ',
            'amount.max' => 'Số tiền tối đa là 999.999.999 VNĐ',
            'transaction_type.required' => 'Vui lòng chọn loại giao dịch',
            'transaction_type.in' => 'Loại giao dịch không hợp lệ',
            'note.max' => 'Ghi chú không được vượt quá 255 ký tự'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'user_id' => 'người dùng',
            'amount' => 'số tiền',
            'transaction_type' => 'loại giao dịch',
            'note' => 'ghi chú'
        ];
    }
}
