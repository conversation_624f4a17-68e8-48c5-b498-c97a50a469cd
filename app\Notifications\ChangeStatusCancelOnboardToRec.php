<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToRec extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $url = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id]);
        return (new MailMessage)
            ->view('email.changeStatusCancelOnboardToRec', [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'recName' => $recName,
                'url' => $url,
            ])
            ->subject('[Recland] Thông báo ứng viên '.$candidateName.' đã “Cancel Onboard” vị trí '.$position);
    }

}
