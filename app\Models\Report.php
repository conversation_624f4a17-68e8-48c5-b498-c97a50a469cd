<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Report extends BaseModel
{
    use HasFactory;

    protected $table = 'reports';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['created_at_value', 'updated_at_value', 'type_issue_value', 'file_url'];

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }

    public function getUpdatedAtValueAttribute()
    {
        return $this->updated_at ? $this->updated_at->format('d/m/Y H:i') : null;
    }

    public function getTypeIssueValueAttribute()
    {
        if ($this->role == 'rec') {
            $type_issue = config('constant.report_rec_type_issue.' . app()->getLocale());
        } else {
            $type_issue = config('constant.report_employer_type_issue.' . app()->getLocale());
        }
        return $type_issue[$this->type_issue];
    }

    public function getFileUrlAttribute()
    {
        return gen_url_file_s3($this->file);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
