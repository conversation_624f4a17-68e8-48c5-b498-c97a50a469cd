<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'title_vi' => $this->faker->name(),
            'title_en' => $this->faker->name(),
            'slug_vi' => $this->faker->slug(),
            'slug_en' => $this->faker->slug(),
            'category_id' => 1,
            'date' => $this->faker->dateTime(),
            'description_vi' => $this->faker->name(),
            'description_en' => $this->faker->name(),
            'tags'  =>  $this->faker->name()
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return static
     */
    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }
}
