<?php

namespace App\Rules\Admin;

use App\Repositories\UserRepository;
use Illuminate\Contracts\Validation\Rule;

class CheckEmailForgotPasswordRule implements Rule
{
    protected $type;

    public function __construct($type = null)
    {
        //
        $this->type = $type;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {

        $userRepository = resolve(UserRepository::class);
        $user = $userRepository->findEmail($this->type, $value);
        if (isset($user) && $user->provider){
            return false;
        }
        return true;

    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('frontend/login/message.forgot_password_permission');
    }
}
