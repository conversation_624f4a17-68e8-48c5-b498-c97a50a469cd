<?php

namespace App\Console\Commands;

use App\Services\Admin\RoleService;
use App\Services\Admin\UserService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MigrateDB extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:db';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate dữ liệu từ hệ thống cũ sang';

    /**
     * @var
     */
    private $now;

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (env('APP_ENV') == 'local') {
            $this->now = Carbon::now();

            DB::table('submit_cvs')->truncate();
            DB::table('warehouse_cvs')->truncate();
            DB::table('users')->truncate();
            DB::table('job')->truncate();
            DB::table('job_meta')->truncate();
            DB::table('job_seo')->truncate();
            DB::table('companies')->truncate();

            try {
                DB::beginTransaction();

                $this->createdSupperAdmin();

                $this->migrateUser();

                $this->migrateWarehouseCV();

                $this->migrateCompany();

                $this->migrateJob();

                $this->migrateSubmitCV();

                DB::commit();
            } catch (\Exception $exception) {
                DB::commit();
                Log::debug($exception);
            }
        }

        return 0;
    }

    private function migrateSubmitCV()
    {
        $submitCvs = DB::connection('mysql_old')->table('submit_cvs')->get();
        $jobs = DB::connection('mysql_old')->table('jobs')->get();
        $jobsOld = [];
        foreach ($jobs as $job) {
            $jobsOld[$job->id] = $job;
        }

        $dataSubmitCv = [];
        foreach ($submitCvs as $cv) {
            $dataSubmitCv[] = [
                'id' => $cv->id,
                'code' => $cv->code,
                'user_id' => $cv->user_id,
                'job_id' => $cv->job_id,
                'company_id' => $cv->company_id,
                'warehouse_cv_id' => $cv->inventory_cv_id,
                'candidate_salary_expect' => $cv->candidate_salary_expect,
                'candidate_currency' => !empty($cv->candidate_salary_expect) ? ($cv->candidate_salary_expect < 100000 ? 'USD' : 'VNĐ') : null,
                'status' => $cv->status,
                'is_self_apply' => $cv->is_self_apply,
                'expected_date' => $cv->expected_date,
                'date_change_status' => $cv->updated_at,

                'bonus_type' => $jobsOld[$cv->job_id]->bonus_type,
                'bonus' => $jobsOld[$cv->job_id]->bonus_type == 'onboard' ? $jobsOld[$cv->job_id]->bonus_onboard : $jobsOld[$cv->job_id]->bonus_cv,
                'incentive' => $jobsOld[$cv->job_id]->bonus_onboard_2,
                'bonus_currency' => $jobsOld[$cv->job_id]->bonus_onboard > 10000 ? 'VND' : 'USD',
                'bonus_self_apply' => $jobsOld[$cv->job_id]->bonus_self_apply,
                'bonus_self_apply_incentive' => 0,
                'bonus_self_apply_currency' => $jobsOld[$cv->job_id]->bonus_self_apply > 10000 ? 'VND' : 'USD',

                'created_at' => $cv->created_at,
                'updated_at' => $cv->updated_at,
                'deleted_at' => $cv->deleted_at,
            ];
        }

        $this->insertData($dataSubmitCv, 'submit_cvs');
    }

    private function migrateWarehouseCV()
    {
        $inventoryCvs = DB::connection('mysql_old')->table('inventory_cvs')->get();

        $dataInventoryCv = [];
        foreach ($inventoryCvs as $cv) {
            $dataInventoryCv[] = [
                'id' => $cv->id,
                'user_id' => $cv->user_id,
                'candidate_name' => $cv->candidate_name,
                'candidate_mobile' => $cv->candidate_mobile,
                'candidate_email' => $cv->candidate_email,
                'candidate_job_title' => $cv->candidate_job_title,
                'candidate_salary_expect' => $cv->candidate_salary_expect,
                'candidate_currency' => !empty($cv->candidate_salary_expect) ? ($cv->candidate_salary_expect < 100000 ? 'USD' : 'VND') : null,
                'cv_public' => $cv->cv_public,
                'cv_private' => $cv->cv_private,
                'created_at' => $cv->created_at,
                'updated_at' => $cv->updated_at,
                'deleted_at' => $cv->deleted_at,
            ];
        }

        $this->insertData($dataInventoryCv, 'warehouse_cvs');
    }

    private function migrateUser()
    {
        $users = DB::connection('mysql_old')->table('users')->get();

        $dataUser = [];
        foreach ($users as $user) {
            $dataUser[] = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'avatar' => $user->avatar,
                'mobile' => $user->mobile,
                'password' => $user->password,
                'email_verified_at' => $this->now,
                'type' => $user->type,
                'referral_code' => $user->referral_code,
                'remember_token' => $user->remember_token,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'is_active' => $user->deleted_at ? 0 : 1,
            ];
        }

        $this->insertData($dataUser, 'users');
    }

    private function migrateJob()
    {
        $jobs = DB::connection('mysql_old')->table('jobs')->get();
        $pluckCompanyEmployer = DB::table('users')
            ->where('type', 'employer')
            ->pluck('id', 'company_id')->all();

        $seos = DB::connection('mysql_old')->table('seos')->get();
        $dataSeoOld = [];
        foreach ($seos as $seo) {
            $dataSeoOld[$seo->key] = $seo;
        }

        $dataJob = [];
        $dataMeta = [];
        $dataSeo = [];
        foreach ($jobs as $job) {
            $dataJob[] = [
                'id' => $job->id,
                'company_id' => $job->company_id,
                'employer_id' => $pluckCompanyEmployer[$job->company_id],
                'name' => $job->name,
                'slug' => $job->slug,
                'file_jd' => $job->file_jd,
                'jd' => $job->jd,
                'address' => json_encode([["area" => $job->location, "address" => $job->address,]]),
                'vacancies' => $job->vacancies,
                'career' => '30,31',
                'skills' => $job->skills,
                'rank' => '1,3',
                'type' => 'all',
                'bonus_type' => $job->bonus_type,
                'bonus' => $job->bonus_type == 'onboard' ? $job->bonus_onboard : $job->bonus_cv,
                'incentive' => $job->bonus_onboard_2,
                'bonus_currency' => $job->bonus_onboard > 10000 ? 'VND' : 'USD',
                'bonus_self_apply' => $job->bonus_self_apply,
                'bonus_self_apply_incentive' => 0,
                'bonus_self_apply_currency' => $job->bonus_self_apply > 10000 ? 'VND' : 'USD',
                'publish_at' => $job->publish_at,
                'expire_at' => $job->expire_at,
                'is_active' => !$job->deleted_at ? 1 : 0,
                'created_at' => $job->created_at,
                'updated_at' => $job->updated_at,
                'deleted_at' => $job->deleted_at,
            ];

            $dataMeta[] = [
                'job_id' => $job->id,
                'created_at' => $this->now,
                'updated_at' => $this->now,
            ];

            $dataSeo[] = [
                'job_id' => $job->id,
                'title_vi' => $dataSeoOld[$job->id]->title,
                'title_en' => $dataSeoOld[$job->id]->title,
                'description_vi' => $dataSeoOld[$job->id]->description,
                'description_en' => $dataSeoOld[$job->id]->description,
                'keyword_vi' => $dataSeoOld[$job->id]->keyword,
                'keyword_en' => $dataSeoOld[$job->id]->keyword,
                'image' => $dataSeoOld[$job->id]->img_thumb,
                'created_at' => $dataSeoOld[$job->id]->created_at,
                'updated_at' => $dataSeoOld[$job->id]->updated_at,
            ];
        }

        $this->insertData($dataJob, 'job');
        $this->insertData($dataMeta, 'job_meta');
        $this->insertData($dataSeo, 'job_seo');
    }

    private function migrateCompany()
    {
        $companies = DB::connection('mysql_old')->table('companies')->get();

        $dataCompany = [];
        $dataEmployer = [];
        foreach ($companies as $company) {
            $dataCompany[] = [
                'id' => $company->id,
                'name' => $company->name,
                'slug' => $company->slug,
                'address' => json_encode([["area" => $company->location, "address" => $company->address,]]),
                'logo' => $company->logo,
                'scale' => 'N/A',
                'about' => $company->about,
                'is_active' => !$company->deleted_at ? 1 : 0,
            ];

            $dataEmployer[] = [
                'company_id' => $company->id,
                'company_name' => $company->name,
                'name' => 'Employer ' . $company->id,
                'email' => 'employer_' . $company->id . '@fake.dev',
                'password' => bcrypt('employer_' . $company->id),
                'type' => 'employer',
                'is_active' => !$company->deleted_at ? 1 : 0,
                'email_verified_at' => Carbon::now(),
            ];
        }

        $this->insertData($dataCompany, 'companies');
        $this->insertData($dataEmployer, 'users');
    }

    public function createdSupperAdmin()
    {
        $roleService = app(RoleService::class);
        $userService = app(UserService::class);

        $email = '<EMAIL>';
        $role = 'Super Admin';

        $permissions = [];

        $configPermission = config('constant.role_permission');
        foreach ($configPermission as $items){
            foreach ($items as $key => $item){
                if ($key == 'title'){
                    continue;
                }
                $permissions[] = $key;
            }
        }

        $supperAdmin = $userService->createService([
            'is_active' => true,
            'name' => 'Super Admin',
            'email' => $email,
            'role' => [],
        ]);

        $roleSupperAdmin = $roleService->findByName($role);
        if (!$roleSupperAdmin){
            $roleSupperAdmin = $roleService->createService([
                'name' => $role,
                'permission' => $permissions,
                'is_active' => true,
            ]);
        }else{
            $roleService->updateService([
                'permission' => $permissions,
                'is_active' => true,
            ],$roleSupperAdmin->id);
        }

        $userService->updateService([
            'is_active' => true,
            'email_verified_at' => Carbon::now(),
            'name' => $email,
            'email' => $email,
            'role' => [$roleSupperAdmin->id],
        ],$supperAdmin->id);
    }

    private function insertData($data, $tableName)
    {
        foreach (array_chunk($data, 500) as $key => $item) {
            $this->comment('INSERT ' . $tableName . ' NO.' . $key);
            DB::table($tableName)->insert($item);
        }
    }
}
