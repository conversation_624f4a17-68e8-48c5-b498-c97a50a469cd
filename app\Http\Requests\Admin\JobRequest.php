<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class JobRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST': {
                    $arr = [
                        'name'                       => 'required',
                        'company_id'                 => 'required',
                        'employer_id'                => 'required',
                        // 'file_jd'                    => 'required|max:5120|mimes:pdf,docx',
                        'jd_description'             => 'required',
                        'jd_request'                 => 'required',
                        'jd_welfare'                 => 'required',
                        'vacancies'                  => 'required|numeric',
                        'career'                     => 'required',
                        'skill'                      => 'required',
                        'rank'                       => 'required',
                        'type'                       => 'required',
                        'bonus_type'                 => 'required',
                        'salary_min'                 => 'required|numeric',
                        'salary_max'                 => 'required|numeric',
                        'salary_currency'            => 'required',
                        'bonus'                      => 'required|numeric',
                        // 'incentive'                  => 'required|numeric',
                        // 'bonus_currency'             => 'required',
                        // 'bonus_self_apply'           => 'required|numeric',
                        // 'bonus_self_apply_incentive' => 'required|numeric',
                        // 'bonus_self_apply_currency'  => 'required',
                        'expire_at'                  => 'required',
                        // 'payment_fee'                => 'required',
                        'job_type'                   => 'required',
                        'level'                      => '',
                    ];

                    if (!$this->get('remote')) {
                        $arr['address.0.area'] = 'required';
                        $arr['address.0.address'] = 'required';
                    }

                    return $arr;
                }
            case 'PUT': {
                    $arr = [
                        'name'                       => 'required',
                        'company_id'                 => 'required',
                        'employer_id'                => 'required',
                        // 'file_jd'                    => 'max:5120|mimes:pdf,docx',
                        'jd_description'             => 'required',
                        'jd_request'                 => 'required',
                        'jd_welfare'                 => 'required',
                        'vacancies'                  => 'required|numeric',
                        'career'                     => 'required',
                        'skill'                      => 'required',
                        'rank'                       => 'required',
                        'type'                       => 'required',
                        'bonus_type'                 => 'required',
                        'salary_min'                 => 'required|numeric',
                        'salary_max'                 => 'required|numeric',
                        'salary_currency'            => 'required',
                        'bonus'                      => 'required|numeric',
                        // 'incentive'                  => 'required|numeric',
                        // 'bonus_currency'             => 'required',
                        // 'bonus_self_apply'           => 'required|numeric',
                        // 'bonus_self_apply_incentive' => 'required|numeric',
                        // 'bonus_self_apply_currency'  => 'required',
                        'expire_at'                  => 'required',
                        // 'payment_fee'                => 'required',
                        'job_type'                   => 'required',
                        'level'                      => '',
                    ];

                    if (!$this->get('remote')) {
                        $arr['address.0.area'] = 'required';
                        $arr['address.0.address'] = 'required';
                    }

                    return $arr;
                }
            default:
                break;
        }
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'numeric'  => __('message.number'),
            'max'      => __('message.file_max'),
            'mimes'    => __('message.format_cv'),
        ];
    }
}
