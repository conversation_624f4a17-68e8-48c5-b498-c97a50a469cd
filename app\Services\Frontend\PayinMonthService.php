<?php

namespace App\Services\Frontend;

use App\Repositories\PayinMonthRepository;

class PayinMonthService
{

    protected $payinMonthRepository;

    public function __construct(PayinMonthRepository $payinMonthRepository){
        $this->payinMonthRepository = $payinMonthRepository;
    }

    public function getPayinMonths($params){
        $user = auth('client')->user();
        $params['user_id'] = $user->id;
        return $this->payinMonthRepository->getPayins($params);
    }






}
