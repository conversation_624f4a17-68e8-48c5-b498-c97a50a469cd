<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{

    public function changeLanguage(Request $request)
    {
        $lang = $request->language;
        $language = config('app.locale');
        if ($lang == config('constant.language.en')) {
            $language = config('constant.language.en');
        }
        if ($lang == config('constant.language.en')) {
            $language = config('constant.language.en');
        }

        Session::put('language', $language);
        return back();
    }
}
