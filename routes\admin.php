<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminDiscussController;
use App\Http\Controllers\Admin\AuthorizeController;
use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\CollaboratorController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DownloadFileController;
use App\Http\Controllers\Admin\EmployerController;
use App\Http\Controllers\Admin\InformationContactController;
use App\Http\Controllers\Admin\JobController;
use App\Http\Controllers\Admin\JobMetaController;
use App\Http\Controllers\Admin\JobSeoController;
use App\Http\Controllers\Admin\LoginController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\PostController;
use App\Http\Controllers\Admin\PostMetaController;
use App\Http\Controllers\Admin\PostSeoController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\SeoController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\SkillController;
use App\Http\Controllers\Admin\SubmitCvController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\WareHouseCvController;
use App\Http\Controllers\Admin\WareHouseCvCrudController;
use App\Http\Controllers\Admin\WareHouseCvSellingBuyController;
use App\Http\Controllers\Admin\WareHouseCvSellingController;
use App\Http\Controllers\Admin\JobCommentController;
use App\Http\Controllers\Admin\SurveyStatisticalController;
use App\Http\Controllers\Admin\TransactionListController;
use App\Http\Controllers\Admin\OverviewStatisticsController;
use App\Http\Controllers\Admin\JobStatisticsController;
use App\Http\Controllers\Admin\BugReportController;
use Illuminate\Support\Facades\Route;

Route::get('/login', [LoginController::class, 'getLogin'])->name('login');
// Route::get('/login', [LoginController::class, 'getLogin'])->name('backpack.auth.login');
// Route::get('/register', [LoginController::class, 'getLogin'])->name('backpack.auth.register');
Route::post('/login', [LoginController::class, 'postLogin'])->name('postLogin');
Route::get('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/', [AdminController::class, 'index']);

Route::middleware(['check-admin', 'check-role'])->group(function () {

    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    Route::prefix('company')->group(function () {
        Route::get('datatable', [CompanyController::class, 'datatable'])->name('company-datatable');
    });
    Route::resource('company', 'CompanyController');

    //category
    Route::prefix('category')->group(function () {
        Route::get('datatable', [CategoryController::class, 'datatable'])->name('category-datatable');
    });
    Route::resource('category', 'CategoryController');
    //end category
    //skill
    Route::prefix('skill')->group(function () {
        Route::get('datatable', [SkillController::class, 'datatable'])->name('skill-datatable');
    });
    Route::resource('skill', 'SkillController');
    //banner
    Route::prefix('banner')->group(function () {
        Route::get('datatable', [BannerController::class, 'datatable'])->name('banner-datatable');
    });
    Route::resource('banner', 'BannerController');

    //user
    Route::prefix('user')->group(function () {
        Route::get('datatable', [UserController::class, 'datatable'])->name('user-datatable');
    });
    Route::resource('user', 'UserController');
    //Testimonial
    Route::prefix('testimonial')->group(function () {
        Route::get('datatable', [TestimonialController::class, 'datatable'])->name('testimonial-datatable');
    });
    Route::resource('testimonial', 'TestimonialController');

    Route::prefix('information-contacts')->group(function () {
        Route::get('datatable', [InformationContactController::class, 'datatable'])->name('information-contacts-datatable');
    });
    Route::resource('information-contacts', 'InformationContactController');

    Route::prefix('role')->group(function () {
        Route::get('datatable', [RoleController::class, 'datatable'])->name('role-datatable');
    });
    Route::resource('role', 'RoleController');

    Route::get('setting', [SettingController::class, 'edit'])->name('setting.edit');
    Route::put('setting', [SettingController::class, 'update'])->name('setting.update');


    //post
    Route::prefix('post')->group(function () {
        Route::get('datatable', [PostController::class, 'datatable'])->name('post-datatable');
        Route::post('uploadImage', 'PostController@uploadImage')->name('post.uploadImage');
    });
    Route::resource('post', 'PostController');
    //end post

    //post_meta
    Route::post('post-meta/{post}', [PostMetaController::class, 'updatePostMeta'])->name('post-meta-update');
    //end post_meta

    //post_seo
    Route::post('post-seo/{post}', [PostSeoController::class, 'updatePostSeo'])->name('post-seo-update');
    //end post_seo

    //seo
    Route::prefix('seo')->group(function () {
        Route::get('', [SeoController::class, 'index'])->name('seo.index');
        Route::post('', [SeoController::class, 'create'])->name('seo.create');
        Route::get('getKey', [SeoController::class, 'getKey'])->name('seo.show');
    });
    //end seo

    //cv
    Route::prefix('cv')->group(function () {
        Route::get('datatable', [WareHouseCvController::class, 'datatable'])->name('cv-datatable');
    });
    Route::resource('cv', 'WareHouseCvController');
    //end cv

    // WareHouse CV Management (Backpack CRUD)
    // Route::prefix('warehouse-cv')->group(function () {
    //     Route::resource('/', WareHouseCvCrudController::class, [
    //         'names' => [
    //             'index' => 'warehouse-cv.index',
    //             'show' => 'warehouse-cv.show',
    //             'edit' => 'warehouse-cv.edit',
    //             'update' => 'warehouse-cv.update',
    //         ]
    //     ]);
    // });
    // end warehouse cv management
    Route::prefix('employer')->group(function () {
        Route::get('datatable', [EmployerController::class, 'datatable'])->name('employer-datatable');
        Route::post('change-password/{employer}', [EmployerController::class, 'changePassword'])->name('employer.change-password');
        Route::post('change-company', [EmployerController::class, 'changeCompany'])->name('employer.change-company');
        Route::get('datatable-deposit/{employer}', [EmployerController::class, 'datatableDeposit'])->name('datatable-deposit');
        Route::post('deposit', [EmployerController::class, 'deposit'])->name('admin-deposit-employer');
    });
    Route::resource('employer', 'EmployerController');

    //job
    Route::prefix('job')->group(function () {
        Route::get('datatable', [JobController::class, 'datatable'])->name('job-datatable');
        Route::post('list-skill', [JobController::class, 'listSkill'])->name('job.list-skill');
        Route::get('{job}/duplicate', [JobController::class, 'duplicate'])->name('job.duplicate');
    });
    Route::resource('job', 'JobController');
    //end job

    //job_meta
    Route::post('job-meta/{job}', [JobMetaController::class, 'updateJobMeta'])->name('job-meta-update');
    //end post_meta

    //job_seo
    Route::post('job-seo/{job}', [JobSeoController::class, 'updateJobSeo'])->name('job-seo-update');
    //end post_seo

    Route::prefix('ref')->group(function () {
        Route::get('datatable/{job}', [JobController::class, 'refDatatable'])->name('ref-datatable');
    });


    Route::prefix('collaborator')->group(function () {
        Route::get('datatable', [CollaboratorController::class, 'datatable'])->name('collaborator-datatable');
        Route::get('submit-cv-datatable/{user_id}', [CollaboratorController::class, 'submitCsvDatatable'])->name('submit.cv-datatable');
        Route::post('change-password/{collaborator}', [CollaboratorController::class, 'changePassword'])->name('collaborator.change-password');
        Route::get('{user_id}/edit', [CollaboratorController::class, 'edit'])->name('collaborator.edit');
        Route::post('{user_id}/update', [CollaboratorController::class, 'update'])->name('collaborator.update');

        //lich su doanh thu

        Route::get('{user_id}/history-bonus', [CollaboratorController::class, 'historyBonus'])->name('collaborator.collaborator-history-bonus');
        Route::post('history-bonus/change-status/{id}', [CollaboratorController::class, 'changeStatus'])->name('collaborator.collaborator-history-bonus-status');
        Route::get('datatable-bonus/{user_id}', [CollaboratorController::class, 'datatableBonus'])->name('collaborator-history-bonus-datatable');
    });
    Route::resource('collaborator', 'CollaboratorController');

    Route::get('change-password', [UserController::class, 'changePassword'])->name('change-password');
    Route::post('post-change-password', [UserController::class, 'postChangePassword'])->name('post-change-password');

    //payment
    Route::prefix('payment')->group(function () {
        Route::get('datatable', [PaymentController::class, 'datatable'])->name('payment-datatable');
        Route::post('change-status/{id}', [PaymentController::class, 'changeStatus'])->name('payment.payment-change-status-payment');
    });
    Route::resource('payment', 'PaymentController');


    Route::prefix('authorize')->group(function () {
        Route::get('authorize-datatable', [AuthorizeController::class, 'datatableAuthorize'])->name('authorize-datatable');
        Route::post('change-authorize/{id}/{authorize}', [AuthorizeController::class, 'changeAuthorize'])->name('authorize.authorize-change-status');
    });

    Route::resource('authorize', 'AuthorizeController');
    Route::prefix('submit-cv')->group(function () {
        Route::match(['get', 'post'], '/', [SubmitCvController::class, 'index'])->name('submit-cv.index');
        Route::get('/{id}/edit', [SubmitCvController::class, 'edit'])->name('submit-cv.edit');
        Route::post('/{id}/update', [SubmitCvController::class, 'update'])->name('submit-cv.update');
        Route::get('/{id}/delete', [SubmitCvController::class, 'destroy'])->name('submit-cv.delete');
    });

    Route::prefix('statistical')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\StatisticalController::class, 'index'])->name('statistical.index');
        Route::post('/filter-data', [\App\Http\Controllers\Admin\StatisticalController::class, 'filterData'])->name('statistical.filter-data');
        //        Route::post('/search', [\App\Http\Controllers\Admin\StatisticalController::class, 'search'])->name('statistical.submit-cv-search');
    });

    // Overview Statistics
    Route::prefix('overview-statistics')->group(function () {
        Route::get('/', [OverviewStatisticsController::class, 'index'])->name('overview-statistics.index');
        Route::post('/filter-data', [OverviewStatisticsController::class, 'filterData'])->name('overview-statistics.filter-data');
    });

    // Job Statistics
    Route::prefix('job-statistics')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\JobStatisticsController::class, 'index'])->name('job-statistics.index');
        Route::post('/filter-data', [\App\Http\Controllers\Admin\JobStatisticsController::class, 'filterData'])->name('job-statistics.filter-data');
        Route::get('/export-csv', [\App\Http\Controllers\Admin\JobStatisticsController::class, 'exportCsv'])->name('job-statistics.export-csv');
    });

    // Survey Statistical
    Route::prefix('survey-statistical')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\SurveyStatisticalController::class, 'index'])->name('survey-statistical.index');
        Route::post('/filter-data', [\App\Http\Controllers\Admin\SurveyStatisticalController::class, 'filterData'])->name('survey-statistical.filter-data');
        Route::get('/field-detail/{fieldId}', [\App\Http\Controllers\Admin\SurveyStatisticalController::class, 'fieldDetail'])->name('survey-statistical.field-detail');
        Route::get('/export', [\App\Http\Controllers\Admin\SurveyStatisticalController::class, 'export'])->name('survey-statistical.export');
    });

    // top ctv
    Route::prefix('top-collaborators')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\TopCollaboratorsController::class, 'index'])->name('top-ctv.index');
        Route::post('/create', [\App\Http\Controllers\Admin\TopCollaboratorsController::class, 'store'])->name('top-ctv.store');
        Route::post('/update/{id}', [\App\Http\Controllers\Admin\TopCollaboratorsController::class, 'update'])->name('top-ctv.update');
        Route::get('/delete/{id}', [\App\Http\Controllers\Admin\TopCollaboratorsController::class, 'destroy'])->name('top-ctv.delete');
    });

    // album about-us
    // Route::prefix('album-about-us')->group(function () {
    //     Route::get('/', [\App\Http\Controllers\Admin\AlbumAboutUsController::class, 'index'])->name('album-aboutUs.index');
    //     Route::get('/create', [\App\Http\Controllers\Admin\AlbumAboutUsController::class, 'create'])->name('album-aboutUs.create');
    //     Route::post('/store', [\App\Http\Controllers\Admin\AlbumAboutUsController::class, 'store'])->name('album-aboutUs.store');
    //     Route::post('/update/{id}', [\App\Http\Controllers\Admin\AlbumAboutUsController::class, 'update'])->name('album-aboutUs.update');
    //     Route::get('/delete/{id}', [\App\Http\Controllers\Admin\AlbumAboutUsController::class, 'destroy'])->name('album-aboutUs.delete');
    // });

    //cv selling
    Route::prefix('cv-selling')->group(function () {
        Route::get('datatable', [WareHouseCvSellingController::class, 'datatable'])->name('cv-selling-datatable');
        Route::post('create-view-token-url', [WareHouseCvSellingController::class, 'createViewTokenUrl'])->name('cv-selling-create-view-token-url');
    });
    Route::resource('cv-selling', 'WareHouseCvSellingController');
    //end cv selling

    //luot ban
    Route::prefix('luot-ban')->group(function () {
        Route::get('datatable', [WareHouseCvSellingBuyController::class, 'datatable'])->name('luot-ban-datatable');
        Route::post('change-status-recruitment/{warehouse_cv_selling_buy_id}', [WareHouseCvSellingBuyController::class, 'changeStatusRecruitment'])->name('luot-ban-change-status-recruitment');
        Route::post('change-status-onboard/{warehouse_cv_selling_buy_id}', [WareHouseCvSellingBuyController::class, 'changeStatusOnboard'])->name('luot-ban-change-status-onboard');
    });
    Route::resource('luot-ban', 'WareHouseCvSellingBuyController');
    //end luot-ban

    //report
    Route::prefix('report')->group(function () {
        Route::get('datatable', [ReportController::class, 'datatable'])->name('report-datatable');
    });
    Route::resource('report', 'ReportController');
    //end luot-ban

    // Thêm routes cho job comments
    Route::prefix('job-comments')->group(function () {
        Route::get('/{jobId}', [JobCommentController::class, 'index'])->name('admin.job-comments.index');
        Route::post('/', [JobCommentController::class, 'store'])->name('admin.job-comments.store');
        Route::delete('/{id}', [JobCommentController::class, 'destroy'])->name('admin.job-comments.destroy');
        // Route::patch('/{id}/toggle-status', [JobCommentController::class, 'toggleStatus'])->name('admin.job-comments.toggle-status');
    });

    // Wallet Topup routes - Nạp tiền ví
    Route::prefix('wallet-topup')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\WalletTopupController::class, 'index'])->name('admin.wallet-topup.index');
        Route::post('/search-user', [\App\Http\Controllers\Admin\WalletTopupController::class, 'searchUser'])->name('admin.wallet-topup.search-user');
        Route::post('/topup', [\App\Http\Controllers\Admin\WalletTopupController::class, 'topup'])->name('admin.wallet-topup.topup');
    });

    // Employer Lookup routes - Tra cứu thông tin nhà tuyển dụng
    Route::prefix('employer-lookup')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\EmployerLookupController::class, 'index'])->name('admin.employer-lookup.index');
        Route::post('/search', [\App\Http\Controllers\Admin\EmployerLookupController::class, 'search'])->name('admin.employer-lookup.search');
        Route::get('/job-submits/{jobId}', [\App\Http\Controllers\Admin\EmployerLookupController::class, 'getJobSubmits'])->name('admin.employer-lookup.job-submits');
    });

    // Collaborator Lookup routes - Tra cứu thông tin cộng tác viên
    Route::prefix('collaborator-lookup')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\CollaboratorLookupController::class, 'index'])->name('admin.collaborator-lookup.index');
        Route::post('/search', [\App\Http\Controllers\Admin\CollaboratorLookupController::class, 'search'])->name('admin.collaborator-lookup.search');
        Route::get('/submits-paginated', [\App\Http\Controllers\Admin\CollaboratorLookupController::class, 'getSubmitsPaginated'])->name('admin.collaborator-lookup.submits-paginated');
    });
    // Transaction List routes - Danh sách giao dịch
    Route::prefix('transaction-list')->group(function () {
        Route::get('/', [TransactionListController::class, 'index'])->name('transaction-list.index');
        Route::get('/search-companies', [TransactionListController::class, 'searchCompanies'])->name('transaction-list.search-companies');
    });
});

Route::prefix('ajax')->group(function () {
    Route::post('change-company', [EmployerController::class, 'changeCompany'])->name('ajax-employer.change-company');
    Route::post('list-skill', [JobController::class, 'listSkill'])->name('ajax-job.list-skill');
    Route::get('company/{id}', [CompanyController::class, 'show'])->name('ajax-company.show');
    Route::get('seo/getKey', [SeoController::class, 'getKey'])->name('ajax-seo.show');
    Route::get('transaction/{id}', [PaymentController::class, 'getTransaction'])->name('ajax-transaction');
    Route::post('transaction', [PaymentController::class, 'addTransaction'])->name('ajax-add-transaction');
    Route::get('teams/{id}', [UserController::class, 'teamByUser'])->name('ajax-teams');
    Route::get('detail-teams-user/{id}', [UserController::class, 'detailTeamByUser'])->name('ajax-detail-teams-user');
    Route::get('role-company/{id}', [EmployerController::class, 'roleCompany'])->name('ajax-role-company');
    Route::post('update-authority', [WareHouseCvSellingController::class, 'updateAuthority'])->name('cv-selling.authority');

    Route::get('luot-ban/preview', [WareHouseCvSellingBuyController::class, 'preview'])->name('ajax-luot-ban-preview');
    Route::post('luot-ban/preview/update-complain', [WareHouseCvSellingBuyController::class, 'updateComplain'])->name('ajax-luot-ban-update-complain');
    Route::post('luot-ban/preview/delete-evaluate', [WareHouseCvSellingBuyController::class, 'deleteEvaluate'])->name('ajax-luot-ban-delete-evaluate');
    Route::post('report-change-status', [ReportController::class, 'changeStatus'])->name('ajax-report-change-status');

    Route::get('/dashboard/filter-date', [DashboardController::class, 'filterDate'])->name('ajax-dashboard-filter-date');

    // submit cv preview
    Route::get('submit-cv/preview', [SubmitCvController::class, 'preview'])->name('ajax-submit-cv-preview');
    Route::post('submit-cv/preview/update-complain', [SubmitCvController::class, 'updateComplain'])->name('ajax-submit-cv-update-complain');

    Route::post('ajax-calculate-bonus-ctv', [JobController::class, 'calculateBonusCtv'])->name('ajax-calculate-bonus-ctv');
});

Route::middleware(['check-admin'])->group(function () {
    Route::post('/push-to-itnavi', [App\Http\Controllers\PushToITNaviController::class, 'push'])->name('push.to.itnavi');
    Route::get('download', [DownloadFileController::class, 'download'])->name('download-file');
});


Route::prefix('discusses')->group(function () {
    Route::get('/', [AdminDiscussController::class, 'index'])->name('admin.discusses.index');
    Route::get('/list', [AdminDiscussController::class, 'getDiscusses'])->name('admin.discusses.list');
    Route::get('/{discuss}', [AdminDiscussController::class, 'show'])->name('admin.discusses.show');
});
Route::get('submit-cv-discuss/{id}/discussions', 'SubmitCvDiscussCrudController@getDiscussions');
// Route::get('backpack/auth/login', 'SubmitCvDiscussCrudController@getDiscussions')->name('backpack.auth.login');

// // Bug Reports Admin Routes
// Route::prefix('bug-reports')->name('bug-reports.')->group(function () {
//     Route::get('/', [BugReportController::class, 'index'])->name('index');
//     Route::get('/datatable', [BugReportController::class, 'datatable'])->name('datatable');
//     Route::get('/{id}', [BugReportController::class, 'show'])->name('show');
//     Route::patch('/{id}/status', [BugReportController::class, 'updateStatus'])->name('update-status');
// });