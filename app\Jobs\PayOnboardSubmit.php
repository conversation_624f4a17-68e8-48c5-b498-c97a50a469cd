<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\SubmitCvService;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PayOnboardSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;
    public $percent;

    public function __construct($submitCvId, $percent)
    {
        $this->submitCvId = $submitCvId;
        $this->percent = $percent;
    }

    public function handle()
    {
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
//        14 => 'Trial work',  16 => 'Success Recruitment',
        if (($submitCv->status == 14 || $submitCv->status == 16) &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5) ) {

            $submitCvService = resolve(SubmitCvService::class);
            $submitCvService->payOnboardCtv($submitCv, $this->percent);
        }
    }
}
