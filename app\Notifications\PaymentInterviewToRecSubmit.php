<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentInterviewToRecSubmit extends Notification implements ShouldQueue
{
    use Queueable;


    protected $submitCv;
    protected $bonus;

    public function __construct($submitCv,$bonus)
    {
        $this->submitCv = $submitCv;
        $this->bonus = $bonus;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $recName = $this->submitCv->rec->name;
        $companyName = $this->submitCv->employer->name;
        $statusRecruitment = $this->submitCv->status;
        $url = route('rec-submitcv',['submit_id' =>  $this->submitCv->id]);
        // $link = route('rec-submitcv') . '?submit_id=' . $this->submitCv->id . '&open_comment=1';
        $recTurnovers = route('rec-turnovers');
        return (new MailMessage)
            ->view('email.paymentInterviewToRecSubmit', [
                'candidateName'     => $candidateName,
                'recName'           => $recName,
                'companyName'       => $companyName,
                'url'               => $url,
                'point'             => $this->bonus,
                'recTurnovers'      => $recTurnovers,
                'statusRecruitment' => $statusRecruitment
            ])
            ->subject('[RECLAND] Thanh toán thành công Ứng viên Interview - Marketplace');

    }

}

