<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('survey_fields', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên lĩnh vực
            $table->string('slug')->unique(); // Slug để dễ dàng truy vấn
            $table->text('description')->nullable(); // <PERSON><PERSON> tả thêm về lĩnh vực
            $table->boolean('is_active')->default(true); // Trạng thái kích hoạt
            $table->integer('order')->default(0); // Thứ tự hiển thị
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('survey_fields');
    }
}; 