<?php

namespace App\Jobs;

use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DepositRefundRejectOffer implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    public function handle()
    {

        $WareHouseCvSellingHistoryBuyRepository = app(WareHouseCvSellingHistoryBuyRepository::class);
        $wareHouseCvSellingBuyRepository = app(WareHouseCvSellingBuyRepository::class);

        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);

        $wareHouseCvSellingHistoryBuyData = $WareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
        //hoàn bao nhiêu point
        $point = 0;
        if ($wareHouseCvSellingHistoryBuyData) {
            foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                $value->status = 1;
                $value->save();
                //ghi log hoan tien
                $WareHouseCvSellingHistoryBuyRepository->create([
                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                    'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                    'point'                         =>  $value->point,
                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                    'status'                        =>  0
                ]);
                $point += $value->point;
            }
        }
        $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
        $wareHouseCvSellingBuy->save();
        //Cộng point của NTD
        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
        $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'deposit_refund_reject_offer');
        // $wareHouseCvSellingBuy->employer->wallet->save();
    }
}
