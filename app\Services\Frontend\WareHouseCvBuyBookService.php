<?php

namespace App\Services\Frontend;

use App\Repositories\WareHouseCvBuyBookRepository;
use Carbon\Carbon;

class WareHouseCvBuyBookService
{

    protected $wareHouseCvBuyBookRepository;

    public function __construct(WareHouseCvBuyBookRepository $wareHouseCvBuyBookRepository)
    {
        $this->wareHouseCvBuyBookRepository = $wareHouseCvBuyBookRepository;
    }

    public function scheduleInterview($data){
        $user = auth('client')->user();
        $data = [
            'cvt_id' => $user->id,
            'address' => $data['address'],
            'name' => $data['name'],
            'warehouse_cv_buy_id' => $data['warehouse_cv_buy_id'],
            'date_book' => Carbon::createFromFormat('d/m/Y',$data['date']),
            'time_book' => $data['hour'].':'.$data['minute'],
        ];
        $book = $this->wareHouseCvBuyBookRepository->create($data);
        $book->load('rec');
        return $book;
    }

    public function getByWarehouseCvBuyId($warehouseCvBuyId)
    {
        return $this->wareHouseCvBuyBookRepository->getByWarehouseCvBuyId($warehouseCvBuyId);
    }




}
