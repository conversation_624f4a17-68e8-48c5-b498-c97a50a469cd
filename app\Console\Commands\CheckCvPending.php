<?php

namespace App\Console\Commands;

use App\Services\Admin\SubmitCvService;
use Illuminate\Console\Command;

class CheckCvPending extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cv:pending';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Cv Pedding and send notif, email';

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        parent::__construct();
        $this->submitCvService = $submitCvService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('Batch CheckCvPending Start');
        try {
            //get data pending
            $this->submitCvService->getListCvNotif(config('constant.status_recruitment_revert.WaitingPayment'));
            $this->submitCvService->getListCvNotif(config('constant.status_recruitment_revert.Waitingsetupinterview'));
            
            //get data accepted
            $this->submitCvService->getListCvNotif(config('constant.status_recruitment_revert.WaitingInterview'));
            $this->submitCvService->getListCvNotif(config('constant.status_recruitment_revert.Offering'));
            $this->submitCvService->getListCvNotif(config('constant.status_recruitment_revert.Waitingonboard'));

            \Log::info('Batch CheckCvPending End');
        } catch (\Exception $e) {
            \Log::error('Batch CheckCvPending Error: ' . $e);
        }
    }
}
