<?php

namespace App\Models;

use App\Eloquent\Deactivate;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends BaseModel
{
    use HasFactory;

    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = [
        'home_value',
    ];

    public function delete()
    {
        return parent::delete(); // TODO: Change the autogenerated stub
    }

    public function getHomeValueAttribute()
    {
        return $this->home ? 'Có' : 'Không';
    }
}
