<?php

namespace App\Repositories;

use App\Models\SubmitCv;
use App\Models\SubmitCvHistoryStatus;
use Carbon\Carbon;

class SubmitCvHistoryStatusRepository extends BaseRepository
{
    const MODEL = SubmitCvHistoryStatus::class;


    public function employerGetHistory($params){
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_10');
        }

        if ($limit > 100) {
            $limit = 100;
        }
        $query = $this->query();

        if (isset($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        if (isset($params['search'])) {
            $query->whereHas('wareHouseCvSellingBuy.wareHouseCvSelling.wareHouseCv', function ($q) use ($params) {
                $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
            });
        }

        $query->orderBy('id','desc');

        $query->with('wareHouseCvSelling.wareHouseCvSelling.wareHouseCv');

        return $query->paginate($limit, ['*'], 'page', $page);
    }

    public function spentThisMonth($userId){
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('user_id',$userId)
            ->where('type',0)
            ->whereYear('created_at', '=', $year)
            ->whereMonth('created_at', '=', $month)
            ->sum('point');

//        $moneyIn = $this->query()
//            ->where('user_id',$userId)
//            ->where('type',1)
//            ->whereYear('created_at', '=', $year)
//            ->whereMonth('created_at', '=', $month)
//            ->sum('point');

        return $moneyOut;
    }

    public function spentThisYear($userId){
        $now = Carbon::now();
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('user_id',$userId)
            ->where('type',0)
            ->whereYear('created_at', '=', $year)
            ->sum('point');

//        $moneyIn = $this->query()
//            ->where('user_id',$userId)
//            ->where('type',1)
//            ->whereYear('created_at', '=', $year)
//            ->sum('point');

        return $moneyOut;
    }

    public function finByTypeStatus($submitCvId, $type, $status)
    {
        return $this->query()
            ->where('submit_cvs_id', $submitCvId)
            ->where('type', $type)
            ->where('status', $status)->get();
    }

    public function findAllBySelling($warehousecvsellingbuyId)
    {
        return $this->query()
            ->where('warehouse_cv_selling_buy_id', $warehousecvsellingbuyId)->get();
    }

    public function logStatus(SubmitCv $submitCv, $user = null, $comment = null)
    {
        //$user null trong truong hop Ung vien Click vao xac nhan, tu choi, TUPA sẽ dùng
        $candidate_name = '';
        if (!empty($submitCv->warehouseCv)) {
            $candidate_name = $submitCv->warehouseCv->candidate_name;
        }
        //status_recruitment = 2 (từ chối), = 3 xác nhận => là trường hợp Ứng viên click link trong email
        return $this->create([
            'user_id'            => $submitCv->employer->id,
            'submit_cvs_id'      => $submitCv->id,
            'status_recruitment' => !empty($submitCv->status) ? $submitCv->status : 0,
            'candidate_name'     => $submitCv->warehouseCv->candidate_name,
            'type'               => 'rec',
            'authority'          => $submitCv->authorize
        ]);
    }

    public function getLogStatusWithSubmitCv($idSubmitCv)
    {
        return $this->query()->where('submit_cvs_id', $idSubmitCv)->orderBy('id','asc')->get();
    }
}
