<?php

namespace App\Http\Middleware;

use App\Models\Seo;
use App\Models\Setting;
use Closure;
use Illuminate\Http\Request;

class OveriteConfig
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        config(Setting::getOveriteConfig());

        return $next($request);
    }
}
