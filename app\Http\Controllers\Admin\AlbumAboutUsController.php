<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\AlbumAboutUsDatatable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AlbumAboutUsRequest;
use App\Services\Admin\AboutUsAlbumPhotoService;
use App\Services\Admin\AboutUsAlbumService;
use App\Services\FileServiceS3;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class AlbumAboutUsController extends Controller
{
    protected $aboutUsAlbumService;
    protected $aboutUsAlbumPhotoService;

    public function __construct(AboutUsAlbumService $aboutUsAlbumService, AboutUsAlbumPhotoService $aboutUsAlbumPhotoService)
    {
        $this->aboutUsAlbumService = $aboutUsAlbumService;
        $this->aboutUsAlbumPhotoService = $aboutUsAlbumPhotoService;
    }

    public function index(AlbumAboutUsDatatable $dataTable)
    {
        return $dataTable->render('admin.pages.album-aboutUs.list');
    }

    public function create()
    {
        return view('admin.pages.album-aboutUs.create');
    }

    public function store(AlbumAboutUsRequest $request)
    {
        try {
            DB::beginTransaction();
            $data = $request->all();
            $aboutUs = $this->aboutUsAlbumService->create(['name' => $request->name]);
            if ($aboutUs){
                $url = [];
                if(count($data['image_big'])){
                    foreach ($data['image_big'] as $imageBig){
                        $url[] = [
                            'type' => 1,// ảnh to
                            'album_id' => $aboutUs->id,
                            'photo_url' => FileServiceS3::getInstance()->uploadToS3($imageBig, config('constant.sub_path_s3.album-about')),
                        ];
                    }
                }
                if(count($data['image_small'])){
                    foreach ($data['image_small'] as $imageBig){
                        $url[] = [
                            'type' => 0,// ảnh bé
                            'album_id' => $aboutUs->id,
                            'photo_url' => FileServiceS3::getInstance()->uploadToS3($imageBig, config('constant.sub_path_s3.album-about')),
                        ];
                    }
                }
                if (count($url)){
                    $this->aboutUsAlbumPhotoService->insert($url);
                }
            }
            DB::commit();
            return redirect()->route('album-aboutUs.index')->with('success', 'Thêm mới thành công');
            
                // return response()->json(['success' => 'Thành công'], 200);
        } catch (\Exception $exception) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Thêm mới thất bại');
            // return response()->json(['error' => $exception->getMessage()], 500);
        }


    }
//    public function update($id)
//    {
//        try {
//            $this->albumAboutUsService->update($id,[],\request()->all());
//            return response()->json(['success' => 'Thành công'], 200);
//        }catch (\Exception $exception) {
//            return response()->json(['error' => $exception->getMessage()], 500);
//        }
//    }
}
