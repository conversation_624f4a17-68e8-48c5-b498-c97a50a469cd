<?php

namespace App\Console\Commands;

use App\Models\SubmitCv;
use App\Models\Job;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateBonusNtdCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'submit-cv:update-bonus-ntd {--force : Force update all records} {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update bonus_ntd field for existing submit_cvs from their related job bonus';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info('Bắt đầu cập nhật bonus_ntd cho submit_cvs...');

        // Query để lấy submit_cvs cần cập nhật
        $query = SubmitCv::with('job')
            ->whereHas('job', function($q) {
                $q->whereNotNull('bonus');
            });

        if (!$force) {
            // Chỉ cập nhật những record chưa có bonus_ntd
            $query->whereNull('bonus_ntd');
        }

        $submitCvs = $query->get();

        if ($submitCvs->isEmpty()) {
            $this->info('Không có submit_cv nào cần cập nhật.');
            return Command::SUCCESS;
        }

        $this->info("Tìm thấy {$submitCvs->count()} submit_cv cần cập nhật.");

        if ($dryRun) {
            $this->info('--- DRY RUN MODE ---');
            $this->table(
                ['ID', 'Job ID', 'Current bonus_ntd', 'Job bonus', 'Action'],
                $submitCvs->map(function($submitCv) {
                    return [
                        $submitCv->id,
                        $submitCv->job_id,
                        $submitCv->bonus_ntd ?? 'NULL',
                        $submitCv->job->bonus ?? 'NULL',
                        $submitCv->job->bonus ? 'Will update to ' . $submitCv->job->bonus : 'No change'
                    ];
                })->toArray()
            );
            return Command::SUCCESS;
        }

        // Xác nhận trước khi thực hiện
        if (!$force && !$this->confirm('Bạn có chắc chắn muốn cập nhật?')) {
            $this->info('Hủy bỏ cập nhật.');
            return Command::SUCCESS;
        }

        $updated = 0;
        $errors = 0;

        DB::beginTransaction();

        try {
            foreach ($submitCvs as $submitCv) {
                if ($submitCv->job && $submitCv->job->bonus) {
                    $oldValue = $submitCv->bonus_ntd;
                    $submitCv->bonus_ntd = $submitCv->job->bonus;
                    $submitCv->timestamps = false;

                    if ($submitCv->save()) {
                        $updated++;
                        $this->line("✓ Updated SubmitCv ID {$submitCv->id}: {$oldValue} → {$submitCv->job->bonus}");
                    } else {
                        $errors++;
                        $this->error("✗ Failed to update SubmitCv ID {$submitCv->id}");
                    }
                }
            }

            DB::commit();

            $this->info("Hoàn thành! Đã cập nhật {$updated} bản ghi.");
            if ($errors > 0) {
                $this->error("Có {$errors} lỗi xảy ra.");
            }

        } catch (\Exception $e) {
            DB::rollback();
            $this->error("Lỗi: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
