<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailConfirmOnboardSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $submitCvBook;
    protected $submitCv;

    public function __construct($employer,$submitCvBook,$submitCv)
    {
        $this->employer = $employer;
        $this->submitCvBook = $submitCvBook;
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position = '';
        $employerName = $this->employer->name;
        $timeInterview = $this->submitCvBook->time_book_format .' '. $this->submitCvBook->date_book_format;
        if (!empty($this->submitCv->warehouseCv)){
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            $position = $this->submitCv->job->name;
        }

        $link = route('employer-submitcv', ['view-detail' => $this->submitCv->id]);

        return (new MailMessage)
            ->view('email.emailConfirmOnboardSubmit', [
                'employerName'  => $employerName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'timeInterview' => $timeInterview,
                'link'          => $link,
            ])
            ->subject('[RECLAND] Thông báo đồng ý lời mời làm việc của ứng viên '.$candidateName.' đối với vị trí '.$position);
    }


}
