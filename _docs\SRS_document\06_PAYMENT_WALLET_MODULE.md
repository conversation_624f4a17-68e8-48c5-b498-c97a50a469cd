# Module <PERSON> toán và Ví điện tử (Payment & Wallet Module)

## 1. Tổng quan

Module <PERSON> toán và Ví điện tử quản lý toàn bộ các giao dịch tài chính trong hệ thống <PERSON>, bao gồm: n<PERSON><PERSON> ti<PERSON>, thanh to<PERSON> dịch vụ, tr<PERSON> hoa hồ<PERSON>, và rút tiền. <PERSON><PERSON> thống sử dụng ví điện tử nội bộ để tối ưu hóa quy trình thanh toán.

## 2. <PERSON><PERSON> thống Ví điện tử

### 2.1. Loại Ví

#### 2.1.1. V<PERSON> Cộng tác viên
- **Chứ<PERSON> năng**:
  - Nh<PERSON>n hoa hồng từ giới thiệu ứng viên
  - Nhận tiền từ bán CV
  - Rút tiền về tài khoản ngân hàng
  - <PERSON>yển tiền cho CT<PERSON> (future)

- **Thông tin ví**:
  - <PERSON><PERSON> dư kh<PERSON> dụng
  - <PERSON><PERSON> dư đang chờ
  - <PERSON><PERSON> dư đã rút
  - <PERSON><PERSON><PERSON> sử giao dịch

#### 2.1.2. Ví Nhà tuyển dụng
- **Chức năng**:
  - Nạp tiền vào ví
  - Thanh toán phí đăng tin
  - Thanh toán mua CV
  - Thanh toán hoa hồng CTV
  - Hoàn tiền khi cần

- **Thông tin ví**:
  - Số dư hiện tại
  - Số dư đã nạp
  - Số dư đã sử dụng
  - Credit limit (nếu có)

### 2.2. Quản lý Số dư

#### 2.2.1. Cấu trúc Số dư
- **Available Balance**: Số tiền có thể sử dụng ngay
- **Pending Balance**: Số tiền đang chờ xử lý
  - Hoa hồng chờ confirm onboard
  - Tiền bán CV chờ qua thời gian hoàn
  - Rút tiền đang xử lý

- **Locked Balance**: Số tiền bị khóa
  - Do tranh chấp
  - Do vi phạm chính sách

#### 2.2.2. Cập nhật Số dư
- Real-time update
- Transaction isolation
- Double-entry bookkeeping
- Audit trail đầy đủ

## 3. Nạp tiền (Deposit)

### 3.1. Phương thức Nạp tiền

#### 3.1.1. Chuyển khoản Ngân hàng
- **Quy trình**:
  1. Tạo mã giao dịch unique
  2. Hiển thị thông tin chuyển khoản
  3. User chuyển khoản với nội dung
  4. Hệ thống check và cộng tiền tự động
  5. Gửi email/SMS xác nhận

- **Thông tin CK**:
  - Ngân hàng: [Danh sách NH hỗ trợ]
  - Số tài khoản: [STK công ty]
  - Nội dung: RECLAND [MÃ GD] [SĐT]
  - Số tiền: Tối thiểu 100,000 VNĐ

#### 3.1.2. Ví điện tử (ZaloPay)
- **Integration**: ZaloPay Gateway API
- **Quy trình**:
  1. Chọn số tiền nạp
  2. Redirect to ZaloPay
  3. Xác thực và thanh toán
  4. Callback và update balance
  5. Notification

- **Limits**:
  - Min: 50,000 VNĐ
  - Max: 50,000,000 VNĐ/lần
  - Daily limit: 200,000,000 VNĐ

#### 3.1.3. Thẻ cào (Future)
- Viettel, Vinaphone, Mobifone
- Mệnh giá: 50k, 100k, 200k, 500k
- Phí: 20-25%

### 3.2. Xử lý Nạp tiền

#### 3.2.1. Auto Processing
- Bank API integration
- Check transaction every 5 minutes
- Match với nội dung CK
- Auto credit vào ví
- Send notification

#### 3.2.2. Manual Processing
- Cho các case không match
- Upload bukti transfer
- Admin verify
- Process trong 24h

### 3.3. Khuyến mãi Nạp tiền
- **First-time bonus**: +10% cho lần nạp đầu
- **Volume bonus**: 
  - Nạp 5tr: +5%
  - Nạp 10tr: +8%
  - Nạp 20tr: +10%
- **Special events**: Double bonus days

## 4. Thanh toán Dịch vụ

### 4.1. Thanh toán Đăng tin

#### 4.1.1. Gói Đăng tin
- **Basic**: 500,000 VNĐ/tin/30 ngày
- **Premium**: 1,000,000 VNĐ/tin/30 ngày
  - Hiển thị top
  - Badge "Hot"
  - Push notification to CTV
- **VIP**: 2,000,000 VNĐ/tin/30 ngày
  - Tất cả Premium features
  - Dedicated support
  - Analytics dashboard

#### 4.1.2. Quy trình Thanh toán
1. Chọn gói đăng tin
2. Review chi phí
3. Confirm thanh toán
4. Check số dư ví
5. Trừ tiền và active tin
6. Invoice tự động

### 4.2. Thanh toán Mua CV

#### 4.2.1. Single Purchase
- Click mua CV
- Xem chi tiết giá
- Confirm thanh toán
- Trừ tiền từ ví
- Unlock full CV info
- Commission cho CTV

#### 4.2.2. Bulk Purchase
- Add multiple CVs to cart
- Apply bulk discount
- Single checkout
- Batch processing
- Download all CVs

### 4.3. Thanh toán Hoa hồng

#### 4.3.1. Trigger Thanh toán
- **Conditions met**:
  - Ứng viên onboard
  - Qua thời gian thử việc
  - Employer confirm
  - No disputes

- **Auto process**:
  - Calculate commission
  - Deduct từ employer wallet
  - Credit to CTV wallet
  - Update các records
  - Send notifications

#### 4.3.2. Commission Structure
- **Direct referral**: 100% commission
- **Upline bonus**: 
  - Level 1: 10% của commission
  - Level 2: 5% của commission
- **Platform fee**: 0% (promotional)

## 5. Rút tiền (Withdrawal)

### 5.1. Điều kiện Rút tiền

#### 5.1.1. Cho CTV
- **Requirements**:
  - Verified account
  - Bank info completed
  - Minimum: 500,000 VNĐ
  - No pending disputes

- **Limits**:
  - Max/lần: 50,000,000 VNĐ
  - Max/ngày: 100,000,000 VNĐ
  - Frequency: 1 lần/ngày

#### 5.1.2. Cho Employer
- Chỉ hoàn tiền khi:
  - Dịch vụ bị lỗi
  - Double charge
  - Approved by admin

### 5.2. Quy trình Rút tiền

#### 5.2.1. Request Flow
1. **Submit request**:
   - Nhập số tiền
   - Chọn tài khoản NH
   - Verify OTP
   - Confirm

2. **Processing**:
   - Freeze amount
   - Admin review (if > 10tr)
   - Process payment
   - Update status

3. **Completion**:
   - Transfer to bank
   - Update wallet
   - Send confirmation
   - Save invoice

#### 5.2.2. Processing Time
- **Working days**: 
  - < 5tr: 2-4 giờ
  - 5-20tr: 4-8 giờ  
  - > 20tr: 24 giờ
- **Weekends**: +1 ngày
- **Holidays**: Tạm dừng

### 5.3. Phí Rút tiền
- **Standard fee**: 
  - Nội địa: 11,000 VNĐ/giao dịch
  - Liên ngân hàng: 22,000 VNĐ
- **Free withdrawal**:
  - VIP members
  - Rút > 10tr
  - Monthly first withdrawal

## 6. Lịch sử Giao dịch

### 6.1. Transaction Types
- **Credit (+)**:
  - Nạp tiền
  - Nhận hoa hồng
  - Bán CV
  - Hoàn tiền
  - Bonus/Promotion

- **Debit (-)**:
  - Thanh toán dịch vụ
  - Mua CV
  - Rút tiền
  - Phí giao dịch
  - Trả hoa hồng

### 6.2. Transaction Details
- Transaction ID
- Type & Description
- Amount (+/-)
- Balance before/after
- Related order/service
- Status
- Created date
- IP address

### 6.3. Filter & Export
- **Filters**:
  - Date range
  - Transaction type
  - Status
  - Amount range

- **Export**:
  - Excel format
  - PDF statement
  - API access

## 7. Báo cáo Tài chính

### 7.1. Cho CTV
- **Monthly Summary**:
  - Tổng thu nhập
  - Chi tiết từng nguồn
  - Thuế (nếu có)
  - Net income

- **Annual Report**:
  - Tổng thu nhập năm
  - Biểu đồ trend
  - Tax documents

### 7.2. Cho Employer
- **Expense Report**:
  - Chi phí tuyển dụng
  - ROI analysis
  - Budget tracking
  - Department allocation

- **Invoice Management**:
  - Auto-generate invoices
  - VAT invoices
  - Bulk download
  - Email invoices

### 7.3. Cho Admin
- **Platform Revenue**:
  - Total transactions
  - Revenue by service
  - Commission earned
  - Outstanding balance

- **Financial Health**:
  - Cash flow
  - Pending withdrawals
  - Risk assessment
  - Fraud detection

## 8. Database Schema

### 8.1. Bảng wallets
```sql
- id
- user_id
- wallet_type (ctv/employer)
- balance
- pending_balance
- locked_balance
- total_earned
- total_spent
- total_withdrawn
- status (active/locked/closed)
- created_at
- updated_at
```

### 8.2. Bảng transactions
```sql
- id
- wallet_id
- transaction_code
- type (credit/debit)
- category (deposit/withdraw/payment/commission)
- amount
- balance_before
- balance_after
- description
- reference_type (job/cv/withdraw)
- reference_id
- status (pending/completed/failed/reversed)
- processed_at
- created_at
```

### 8.3. Bảng deposits
```sql
- id
- user_id
- wallet_id
- deposit_code
- method (bank/zalopay/card)
- amount
- bonus_amount
- bank_code
- bank_transaction_id
- evidence_url
- status
- confirmed_by
- confirmed_at
- created_at
```

### 8.4. Bảng withdrawals
```sql
- id
- user_id
- wallet_id
- withdrawal_code
- amount
- fee
- net_amount
- bank_name
- bank_account
- account_name
- status
- reason (if rejected)
- processed_by
- processed_at
- created_at
```

### 8.5. Bảng payment_debits
```sql
- id
- payer_wallet_id
- receiver_wallet_id
- type (commission/refund)
- amount
- reference_type
- reference_id
- due_date
- status
- paid_at
- created_at
```

## 9. API Endpoints

### 9.1. Wallet APIs
- `GET /api/wallet` - Thông tin ví
- `GET /api/wallet/transactions` - Lịch sử giao dịch
- `GET /api/wallet/statement` - Sao kê

### 9.2. Deposit APIs
- `POST /api/wallet/deposit` - Tạo lệnh nạp
- `GET /api/wallet/deposit/{code}` - Check trạng thái
- `POST /api/wallet/deposit/confirm` - Upload chứng từ

### 9.3. Withdrawal APIs
- `POST /api/wallet/withdraw` - Yêu cầu rút
- `GET /api/wallet/withdrawals` - Lịch sử rút
- `PUT /api/wallet/withdraw/{id}/cancel` - Hủy lệnh rút

### 9.4. Payment APIs
- `POST /api/payment/preview` - Preview thanh toán
- `POST /api/payment/process` - Xử lý thanh toán
- `GET /api/payment/invoice/{id}` - Lấy hóa đơn

## 10. Security & Compliance

### 10.1. Security Measures
- **Encryption**: All financial data encrypted
- **2FA**: For withdrawals > 5M
- **IP Whitelist**: For API access
- **Rate Limiting**: Prevent abuse
- **Fraud Detection**: ML-based

### 10.2. Compliance
- **KYC**: Verify identity for large transactions
- **AML**: Anti-money laundering checks
- **Tax Reporting**: Annual reports
- **Data Protection**: PCI DSS compliance

### 10.3. Audit Trail
- Log all transactions
- Immutable records
- Regular audits
- Reconciliation reports

## 11. Integration

### 11.1. Payment Gateway
- **ZaloPay**: Primary gateway
- **Bank API**: For auto-check
- **SMS Gateway**: For OTP
- **Email Service**: For notifications

### 11.2. Accounting System
- Export to accounting software
- Auto-generate reports
- Tax calculation
- Budget integration

## 12. Future Enhancements

### 12.1. New Payment Methods
- Credit/Debit cards
- E-wallets (Momo, VNPay)
- Cryptocurrency
- BNPL (Buy now pay later)

### 12.2. Advanced Features
- Auto-invest surplus
- Loyalty points system
- Referral rewards
- Cashback programs
- Multi-currency support
