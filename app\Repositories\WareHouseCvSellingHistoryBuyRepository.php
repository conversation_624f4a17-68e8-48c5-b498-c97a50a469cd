<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingHistoryBuy;
use Carbon\Carbon;

class WareHouseCvSellingHistoryBuyRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingHistoryBuy::class;


    public function employerGetHistory($params){
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_10');
        }

        if ($limit > 100) {
            $limit = 100;
        }
        $query = $this->query();

        if (isset($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        if (isset($params['search'])) {
            $query->whereHas('wareHouseCvSellingBuy.wareHouseCvSelling.wareHouseCv', function ($q) use ($params) {
                $q->where('candidate_name', 'like', '%' . $params['search'] . '%');
            });
        }

        $query->orderBy('id','desc');

        $query->with('wareHouseCvSelling.wareHouseCvSelling.wareHouseCv');

        return $query->paginate($limit, ['*'], 'page', $page);
    }

    public function spentThisMonth($userId){
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('user_id',$userId)
            ->where('type',0)
            ->whereYear('created_at', '=', $year)
            ->whereMonth('created_at', '=', $month)
            ->sum('point');

//        $moneyIn = $this->query()
//            ->where('user_id',$userId)
//            ->where('type',1)
//            ->whereYear('created_at', '=', $year)
//            ->whereMonth('created_at', '=', $month)
//            ->sum('point');

        return $moneyOut;
    }

    public function spentThisYear($userId){
        $now = Carbon::now();
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('user_id',$userId)
            ->where('type',0)
            ->whereYear('created_at', '=', $year)
            ->sum('point');

//        $moneyIn = $this->query()
//            ->where('user_id',$userId)
//            ->where('type',1)
//            ->whereYear('created_at', '=', $year)
//            ->sum('point');

        return $moneyOut;
    }

    public function finBySellingTypeStatus($warehousecvsellingbuyId, $type, $status)
    {
        return $this->query()
            ->where('warehouse_cv_selling_buy_id', $warehousecvsellingbuyId)
            ->where('type', $type)
            ->where('status', $status)->get();
    }

    public function findAllBySelling($warehousecvsellingbuyId)
    {
        return $this->query()
            ->where('warehouse_cv_selling_buy_id', $warehousecvsellingbuyId)->get();
    }


}
