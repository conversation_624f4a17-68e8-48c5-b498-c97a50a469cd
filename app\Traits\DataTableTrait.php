<?php

namespace App\Traits;

trait DataTableTrait
{
    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\Datatables\Html\Builder
     */
    protected $ajaxData = [];
    protected $dataParameter = [];

    public function html()
    { //comment
        return $this->builder()
            ->columns($this->getColumns())
            ->ajax($this->ajaxData)
            ->parameters($this->getBuilderParameters());
    }

    public function getBuilderParameters()
    {
        $parameters = [

            'language' => [
                'paginate' => [
                    'next' => '<i class="fas fa-angle-right"></i>',
                    'previous' => '<i class="fas fa-angle-left"></i>',
                ],
                'url' => asset2('backend/assets/js/vietnamese.json'),
            ],

            'stateSave' => true,
            'scrollX' => true,
            'lengthChange' => true,
        ];

        return array_merge($parameters, $this->dataParameter);
    }
}
