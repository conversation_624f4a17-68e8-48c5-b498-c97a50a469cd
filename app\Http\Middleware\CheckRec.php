<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CheckRec
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::guard('client')->check()
            && Auth::guard('client')->user()->email_verified_at
            && Auth::guard('client')->user()->type == config('constant.role.rec')
        ) {
            return $next($request);
        }

        $params = [];
        if (url()->previous()){
            $params['redirect'] = base64_encode(url()->full());
        }

        $params = http_build_query($params);

        Session::flash('error', __('frontend/login/message.please_check_email'));
        return redirect()->route('rec-login', $params);
    }
}
