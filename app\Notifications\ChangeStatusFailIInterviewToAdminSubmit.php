<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailIInterviewToAdminSubmit extends Mailable
{

    use Queueable;

    protected $submitCv;
    protected $options;

    public function __construct($submitCv, $options = [])
    {
        $this->submitCv = $submitCv;
        $this->options = $options;
    }

    public function content(): Content
    {
        $recName = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }

        $note = '';
        if (isset($this->options['note'])) {
            $note = $this->options['note'];
        }
        $companyName = $this->submitCv->company->name;
        // $url = route('luot-ban.show',['luot_ban' => $this->submitCv->id]);
        $url           = route('submit-cv.edit', ['id' => $this->submitCv->id]);
        return new Content(
            view: 'email.changeStatusFailIInterviewToAdminSubmit',
            with: [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'note'          => $note,
                'url'           => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->company->name;
        $message->subject('[Recland] Thông báo Ứng viên ' . $candidateName . ' đã Fail phỏng vấn của công ty ' . $companyName);
        return $this;
    }
}
