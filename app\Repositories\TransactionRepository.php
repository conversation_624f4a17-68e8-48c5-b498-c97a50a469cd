<?php

namespace App\Repositories;


use App\Models\Transaction;

class TransactionRepository extends BaseRepository
{
    const MODEL = Transaction::class;

    public function findByKeyword($keyword)
    {
        return $this->query()->where('key', $keyword)->first();
    }

    public function getTransactionWithSubmitCvId($submitCvId)
    {
        return $this->query()->where('submit_cv_id', $submitCvId)->orderBy('id', 'desc')->get();
    }

    public function countTotalMoneyWithSubmitCv($submitCvId)
    {
        return $this->query()->where('submit_cv_id', $submitCvId)->sum('money');
    }
}
