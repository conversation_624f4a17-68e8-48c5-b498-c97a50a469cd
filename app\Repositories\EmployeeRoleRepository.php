<?php

namespace App\Repositories;

use App\Models\EmployeeRole;
use Illuminate\Support\Facades\DB;

class EmployeeRoleRepository extends BaseRepository
{
    const MODEL = EmployeeRole::class;

    public function getListRole($companyId)
    {
        return $this->query()->where('company_id', $companyId)->get()->toArray();
    }

    public function getRoleWithCompany($companyId)
    {
        $query = $this->query()->select(
            'employee_roles.*',
            DB::raw("(select count(*) from employer_types where employer_types.employee_role_id = employee_roles.id) as count"),
        );
        $query->where('company_id', $companyId);

        $query = $query->orderBy('id', 'desc')->paginate(config('constant.paginate_10'));
        return $query;
    }

}
