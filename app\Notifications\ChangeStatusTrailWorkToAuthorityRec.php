<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusTrailWorkToAuthorityRec extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        return (new MailMessage)
            ->view('email.changeStatusTrailWorkToAuthorityRec', [
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'recName' => $recName,
            ])
            ->subject('[Recland] Ứng viên '.$candidateName.' bắt đầu ngày làm việc đầu tiên tại công ty '.$companyName);
    }

}
