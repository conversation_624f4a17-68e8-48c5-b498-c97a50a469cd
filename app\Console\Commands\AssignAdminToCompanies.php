<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\Job;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AssignAdminToCompanies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assign:admin-to-companies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign admin_id to companies based on audit logs from their jobs';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Bắt đầu assign admin_id cho companies...');

        // Lấy danh sách companies có is_real = 1 và admin_id IS NULL
        $companies = Company::where('is_real', 1)
            ->whereNull('admin_id')
            ->get();

        $this->info("Tìm thấy {$companies->count()} companies cần assign admin_id");

        $processed = 0;
        $assigned = 0;

        foreach ($companies as $company) {
            $this->info("Đang xử lý company ID: {$company->id} - {$company->name}");

            // Lấy danh sách jobs của company
            $jobs = Job::where('company_id', $company->id)->get();

            $adminId = null;

            foreach ($jobs as $job) {
                $this->info("  - Kiểm tra job ID: {$job->id}");

                // Lấy audit logs của job
                $audits = DB::table('audits')
                    ->where('auditable_type', Job::class)
                    ->where('auditable_id', $job->id)
                    ->where('user_type', User::class)
                    ->whereNotNull('user_id')
                    ->get();

                foreach ($audits as $audit) {
                    // Kiểm tra user có type = admin và có quyền company management
                    $user = User::find($audit->user_id);

                    if ($user && $user->type === 'admin' && $this->hasCompanyRotationPermission($user)) {
                        $adminId = $user->id;
                        $this->info("    - Tìm thấy admin phù hợp: {$user->name} (ID: {$user->id})");
                        break 2; // Thoát cả hai vòng lặp
                    }
                }
            }

            if ($adminId) {
                $company->admin_id = $adminId;
                $company->save();
                $assigned++;
                $this->info("  ✓ Đã assign admin_id = {$adminId} cho company {$company->name}");
            } else {
                $this->warn("  ✗ Không tìm thấy admin phù hợp cho company {$company->name}");
            }

            $processed++;
        }

        $this->info("Hoàn thành! Đã xử lý {$processed} companies, assign thành công {$assigned} companies");

        return 0;
    }

    /**
     * Kiểm tra user có quyền company.rotation không
     *
     * @param User $user
     * @return bool
     */
    private function hasCompanyRotationPermission(User $user): bool
    {
        // Lấy các roles của user
        $roles = $user->roles;

        foreach ($roles as $role) {
            // Kiểm tra permissions của role
            $permissions = json_decode($role->permission, true);

            // Kiểm tra các permissions liên quan đến company management
            $companyPermissions = ['company.rotation'];

            if (is_array($permissions)) {
                foreach ($companyPermissions as $permission) {
                    if (in_array($permission, $permissions)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
