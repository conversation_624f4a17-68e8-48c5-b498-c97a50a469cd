<?php

namespace App\Services\Frontend;

use App\Repositories\CompanyRepository;
use App\Repositories\ContactUsesRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Facades\Cache;

class ContactUsesService
{

    protected $contactUsesService;

    public function __construct(ContactUsesRepository $contactUsesService)
    {
        $this->contactUsesService = $contactUsesService;
    }

}
