<?php

namespace App\Services\Admin;

use App\Jobs\RejectBookExpire;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\RecRefuseComplain;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingBuyOnboardService
{

    protected $wareHouseCvSellingBuyOnboardRepository;

    public function __construct(WareHouseCvSellingBuyOnboardRepository $wareHouseCvSellingBuyOnboardRepository)
    {
        $this->wareHouseCvSellingBuyOnboardRepository = $wareHouseCvSellingBuyOnboardRepository;
    }

    public function getCurrentByWarehouseCvBuyId($sellingBuyId){
        return $this->wareHouseCvSellingBuyOnboardRepository->getCurrentByWarehouseCvBuyId($sellingBuyId);
    }


    public function getByWarehouseCvBuyId($warehouseCvBuyId)
    {
        return $this->wareHouseCvSellingBuyOnboardRepository->getByWarehouseCvBuyId($warehouseCvBuyId);
    }


}
