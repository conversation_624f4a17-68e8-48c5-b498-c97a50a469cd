<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class CheckLoginAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure                 $next
     *
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::guard('admin')->check()) {
            return $next($request);
        }

        $params = [];
        if (url()->previous()){
            $params['redirect'] = base64_encode(url()->current());
        }

        $params = http_build_query($params);
        return redirect()->route('login', $params);
    }
}
