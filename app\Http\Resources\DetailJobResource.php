<?php

namespace App\Http\Resources;

use App\Helpers\Common;
use App\Http\Resources\Api\Frontend\CompanyResource;
use App\Models\LevelByJobTop;
use App\Models\LevelBySkillMain;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DetailJobResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $career = '';
        foreach (explode(',', $this->career) as $value) {
            $career .= config('job.career.' . app()->getLocale())[$value] . ', ';
        }

        $rank = '';


        foreach (explode(',', $this->rank) as $value) {
            if (in_array((int)$this->career, config('job.it_career'))) {
                $level = LevelBySkillMain::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            } else {
                $level = LevelByJobTop::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            }
        }


        $cities = Common::getCities();
        $address = isset($this['address']) ? json_decode($this['address']) : json_decode($this->address);
        $remote = isset($this['remote']) ? json_decode($this['remote']) : json_decode($this->remote);
        $area = [];
        $areaString = '';
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a->area) && isset($cities[$a->area]) && !empty($a->address)) {
                    $area[] = $cities[$a->area];
                }
            }
        }
        if (!empty($remote)) {
            $area = 'Remote';
            $areaString .= $area . ', ';
        } else {
            foreach (array_unique($area) as $a) {
                $areaString .= $a . ', ';
            }
        }

        return [
            'id' => $this->id,
            'bonus' => $this->bonus,
            'bonus_for_ctv' => intval($this->bonus_for_ctv),
            'bonus_currency' => $this->bonus_currency,
            'bonus_self_apply' => $this->bonus_self_apply,
            'bonus_self_apply_currency' => $this->bonus_self_apply_currency,
            'bonus_self_apply_incentive' => $this->bonus_self_apply_incentive,
            'bonus_type' => $this->bonus_type,
            'career' => rtrim($career, ', '),
            'company' => new CompanyResource($this->company),
            'created_at' => Carbon::parse($this->created_at)->format('d/m/Y'),
            'updated_at' => Carbon::parse($this->updated_at)->format('d/m/Y'),
            'expire_at' => Carbon::parse($this->expire_at)->format('d/m/Y'),
            'file_job' => gen_url_file_s3($this->file_jd),
            'full_path_slug' => $this->full_path_slug,
            'jd_description' => $this->jd_description,
            'jd_request' => $this->jd_request,
            'jd_welfare' => $this->jd_welfare,
            'name' => $this->name,
            'rank' => rtrim($rank, ', '),
            'remote' => $this->remote,
            'salary_currency' => $this->salary_currency,
            'salary_max' => $this->salary_max,
            'salary_min' => $this->salary_min,
            'skills' => explode(',', $this->skills),
            'status' => $this->status,
            'slug' => $this->slug,
            'area_string' => rtrim($areaString, ', '),
            'type' => Str::ucfirst($this->type),
            'urgent' => $this->urgent,
            'vacancies' => $this->vacancies,
            'auth_login' => auth('web')->user(),
            'url_detail' => route('job-detail', $this->slug),
            'priority' => optional($this->jobMeta)->priority,
            'address' => $this->address_value,
            'is_like' => Common::checkUserJobCare(auth('client')->id(), $this->id, 'like'),
            'is_save' => Common::checkUserJobCare(auth('client')->id(), $this->id, 'save'),
        ];
    }
}
