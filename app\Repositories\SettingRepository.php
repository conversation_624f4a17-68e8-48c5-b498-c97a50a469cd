<?php

namespace App\Repositories;

use App\Models\Setting;

class SettingRepository extends BaseRepository
{
    const MODEL = Setting::class;

    public function getListSetting()
    {
        return $this->getAll();
    }

    public function getShortSettingByKey($key)
    {
        return $this->query()->where('key', 'like', '%' . $key . '%')->get();
    }

    public function getAllByKey($key)
    {
        return $this->query()->where('key', 'like', '%' . $key . '%')->pluck('value', 'key')->toArray();
    }
    public function getFirstByKey($key)
    {
        return $this->query()->where('key', $key)->first();
    }

    public function getListSettingGlobal($arrKey = [])
    {
        //get by key
        if(count($arrKey) > 0){
            return $this->query()->whereIn('key', $arrKey)->pluck('value', 'key')->toArray();
        }else{
            //get all global
            return $this->query()->where('key', 'like', '%setting.global%')->pluck('value', 'key')->toArray();
        }

    }
}
