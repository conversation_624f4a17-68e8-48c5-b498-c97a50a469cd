<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SurveyField extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer'
    ];

    public function userResults()
    {
        return $this->hasMany(UserSurveyResult::class);
    }
} 