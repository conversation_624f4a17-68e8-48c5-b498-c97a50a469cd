<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use App\Services\Frontend\WareHouseCvSellingBuyService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PayInterview implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        //sau 24h thi Thanh toan tien tra CTV -> interview
        //pass, fail thi hoan tien
        if ($wareHouseCvSellingBuy->status_recruitment == 8 || $wareHouseCvSellingBuy->status_recruitment == 10  &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5) ) {

            $wareHouseCvSellingBuyService = resolve(WareHouseCvSellingBuyService::class);
            $wareHouseCvSellingBuyService->payCtv($wareHouseCvSellingBuy);
        }
    }




}
