<?php

namespace App\Http\Requests\Frontend;

use App\Services\Admin\Toast\Facades\Toast;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class JobRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case 'POST':
            {
                $arr = [
                    'name'                       => 'required',
//                    'company_id'                 => 'required',
//                    'employer_id'                => 'required',
                    'file_jd'                    => 'max:5120|mimes:pdf,docx',
                    'jd_description'             => 'required',
                    'jd_request'                 => 'required',
                    'jd_welfare'                 => 'required',
                    'vacancies'                  => 'required|numeric',
                    'career'                     => 'required',
                    'skill'                      => 'required',
                    'rank'                       => 'required',
                    'type'                       => 'required',
//                    'bonus_type'                 => 'required',
                    'salary_min'                 => 'required|numeric',
                    'salary_max'                 => 'required|numeric',
//                    'salary_currency'            => 'required',
                    'expire_at'                  => 'required',
                ];

                if (!$this->get('remote')) {
                    $arr['address.0.area'] = 'required';
                    $arr['address.0.address'] = 'required';
                }

                return $arr;
            }
            case 'PUT':
            {
                $arr = [
                    'name'                       => 'required',
//                    'company_id'                 => 'required',
//                    'employer_id'                => 'required',
                    'file_jd'                    => 'max:5120|mimes:pdf,docx',
                    'jd_description'             => 'required',
                    'jd_request'                 => 'required',
                    'jd_welfare'                 => 'required',
                    'vacancies'                  => 'required|numeric',
                    'career'                     => 'required',
                    'skill'                      => 'required',
                    'rank'                       => 'required',
                    'type'                       => 'required',
//                    'bonus_type'                 => 'required',
                    'salary_min'                 => 'required|numeric',
                    'salary_max'                 => 'required|numeric',
//                    'salary_currency'            => 'required',
                    'expire_at'                  => 'required',
                ];

                if (!$this->get('remote')) {
                    $arr['address.0.area'] = 'required';
                    $arr['address.0.address'] = 'required';
                }

                return $arr;
            }
            default:
                break;
        }
    }

//    public function messages()
//    {
//        return [
//            'required' => __('message.required'),
//            'numeric'  => __('message.number'),
//            'max'      => __('message.file_max'),
//            'mimes'    => __('message.format_cv'),
//        ];
//    }

    protected function failedValidation(Validator $validator)
    {
        Toast::error(__('frontend/validation.error_validate'));
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
