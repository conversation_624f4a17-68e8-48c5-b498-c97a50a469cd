/**
 * Bug Report Modal JavaScript
 * Handles modal interactions, file upload, drag & drop, and clipboard paste
 */

$(document).ready(function() {
    // Initialize bug report functionality
    initBugReport();
});

function initBugReport() {
    // Open modal when bug report link is clicked
    $('#bugReportLink').on('click', function(e) {
        e.preventDefault();
        openBugReportModal();
    });

    // Handle form submission
    $('#bugReportForm').on('submit', function(e) {
        e.preventDefault();
        submitBugReport();
    });

    // Handle file input change
    $('#bug_image').on('change', function() {
        handleFileSelect(this.files[0]);
    });

    // Handle remove image
    $('#removeImage').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        removeImage();
    });

    // Handle description character count
    $('#bug_description').on('input', function() {
        updateCharacterCount();
    });

    // Setup drag and drop
    setupDragAndDrop();

    // Setup clipboard paste
    setupClipboardPaste();

    // Setup click to upload - chỉ click vào placeholder, không phải toàn bộ upload area
    $('#uploadPlaceholder').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        $('#bug_image')[0].click(); // Sử dụng DOM element thay vì jQuery
    });
}

function openBugReportModal() {
    // Set current URL
    
    // Reset form
    resetForm();
    
    $('#bug_url').val(window.location.href);
    // Show modal
    $('#bugReportModal').modal('show');
}

function resetForm() {
    $('#bugReportForm')[0].reset();
    $('#description_count').text('0');
    removeImage();
    hideProgress();
}

function submitBugReport() {
    const form = $('#bugReportForm')[0];
    const formData = new FormData(form);
    
    // Disable submit button
    const submitBtn = $('#submitBugReport');
    const originalText = submitBtn.html();
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang gửi...');
    
    // Show progress
    showProgress();
    
    $.ajax({
        url: '/bug-report',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function(evt) {
                if (evt.lengthComputable) {
                    const percentComplete = (evt.loaded / evt.total) * 100;
                    updateProgress(percentComplete);
                }
            }, false);
            return xhr;
        },
        success: function(response) {
            if (response.success) {
                // Show success message
                showSuccessMessage(response.message);
                
                // Close modal
                $('#bugReportModal').modal('hide');
                
                // Reset form
                resetForm();
            } else {
                showErrorMessage('Có lỗi xảy ra khi gửi báo cáo. Vui lòng thử lại.');
            }
        },
        error: function(xhr) {
            let errorMessage = 'Có lỗi xảy ra khi gửi báo cáo. Vui lòng thử lại.';
            
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = xhr.responseJSON.errors;
                errorMessage = Object.values(errors).flat().join('<br>');
            }
            
            showErrorMessage(errorMessage);
        },
        complete: function() {
            // Re-enable submit button
            submitBtn.prop('disabled', false).html(originalText);
            hideProgress();
        }
    });
}

function handleFileSelect(file) {
    if (!file) return;
    
    // Validate file type
    if (!file.type.match('image.*')) {
        showErrorMessage('Vui lòng chọn file ảnh (JPG, PNG, GIF).');
        return;
    }
    
    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
        showErrorMessage('Kích thước file không được vượt quá 5MB.');
        return;
    }
    
    // Show preview
    const reader = new FileReader();
    reader.onload = function(e) {
        $('#previewImg').attr('src', e.target.result);
        $('#uploadPlaceholder').hide();
        $('#imagePreview').show();
    };
    reader.readAsDataURL(file);
}

function removeImage() {
    $('#bug_image').val('');
    $('#previewImg').attr('src', '');
    $('#imagePreview').hide();
    $('#uploadPlaceholder').show();
}

function updateCharacterCount() {
    const count = $('#bug_description').val().length;
    $('#description_count').text(count);
    
    if (count > 1800) {
        $('#description_count').addClass('text-warning');
    } else if (count > 1950) {
        $('#description_count').addClass('text-danger').removeClass('text-warning');
    } else {
        $('#description_count').removeClass('text-warning text-danger');
    }
}

function setupDragAndDrop() {
    const uploadArea = $('#imageUploadArea')[0];
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    function highlight() {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }
    
    uploadArea.addEventListener('drop', handleDrop, false);
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            handleFileSelect(files[0]);
            
            // Update file input
            const fileInput = $('#bug_image')[0];
            fileInput.files = files;
        }
    }
}

function setupClipboardPaste() {
    $(document).on('paste', function(e) {
        // Only handle paste when modal is open
        if (!$('#bugReportModal').hasClass('show')) return;
        
        const items = e.originalEvent.clipboardData.items;
        
        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                const blob = items[i].getAsFile();
                handleFileSelect(blob);
                
                // Create a new FileList with the blob
                const dt = new DataTransfer();
                dt.items.add(blob);
                $('#bug_image')[0].files = dt.files;
                
                break;
            }
        }
    });
}

function showProgress() {
    $('#uploadProgress').show();
    updateProgress(0);
}

function hideProgress() {
    $('#uploadProgress').hide();
}

function updateProgress(percent) {
    $('#uploadProgress .progress-bar').css('width', percent + '%');
}

function showSuccessMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.success(message);
    } else {
        alert(message);
    }
}

function showErrorMessage(message) {
    if (typeof toastr !== 'undefined') {
        toastr.error(message);
    } else {
        alert(message);
    }
}
