<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\WareHouseCvRequest;
use App\Models\WareHouseCv;
use App\Models\User;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Carbon\Carbon;

/**
 * Class WareHouseCvCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class WareHouseCvCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(WareHouseCv::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/warehouse-cv');
        CRUD::setEntityNameStrings('CV ứng viên', 'CV ứng viên');

        // Chỉ hiển thị CV đang active
        $this->crud->addClause('where', 'is_active', 1);
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Giới hạn số bản ghi mỗi trang
        $this->crud->setDefaultPageLength(25);
        $this->crud->setPageLengthMenu([10, 25, 50, 100]);

        // Tắt các operations không cần thiết
        $this->crud->denyAccess(['create', 'delete']);
        $this->crud->removeAllButtons();
        $this->crud->addButtonFromView('line', 'cv.custom_edit', 'admin.pages.cv.custom_edit', 'beginning');

        // Thêm widgets thống kê
        Widget::add([
            'type'         => 'div',
            'class'        => 'row',
            'content'      => [
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-primary col-md-12',
                    'value'        => $this->getTotalCvs(),
                    'description'  => 'Tổng số CV',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Tổng cộng tất cả CV trong hệ thống',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-success col-md-12',
                    'value'        => $this->getTotalCvsCurrentMonth(),
                    'description'  => 'CV tháng này',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số CV được tạo trong tháng này',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-info col-md-12',
                    'value'        => $this->getTotalRealCvs(),
                    'description'  => 'CV thật',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số CV thật trong hệ thống',
                ]
            ]
        ])->to('before_content');

        // Cột ID
        CRUD::column('id')
            ->label('ID')
            ->type('number');

        // Cột tên ứng viên
        CRUD::column('candidate_name')
            ->label('Tên ứng viên')
            ->type('custom_html')
            ->value(function ($entry) {
                $html = '<strong>' . e($entry->candidate_name) . '</strong>';
                if ($entry->is_real) {
                    $html .= '<br><small class="badge badge-success mt-2">CV thật</small>';
                } else {
                    $html .= '<br><small class="badge badge-danger mt-2">CV ảo</small>';
                }
                return $html;
            });

        // Cột email
        CRUD::column('candidate_email')
            ->label('Email')
            ->type('email');

        // Cột số điện thoại
        // CRUD::column('candidate_mobile')
        //     ->label('Số điện thoại')
        //     ->type('text');

        // Cột job title
        CRUD::column('candidate_job_title')
            ->label('Vị trí ứng tuyển')
            ->type('text');

        // Cột mức lương mong muốn
        CRUD::column('salary_info')
            ->label('Lương mong muốn')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->candidate_salary_expect) {
                    $salary = number_format($entry->candidate_salary_expect, 0, ',', '.');
                    if ($entry->candidate_salary_expect_to) {
                        $salaryTo = number_format($entry->candidate_salary_expect_to, 0, ',', '.');
                        return $salary . ' - ' . $salaryTo . ' ' . ($entry->candidate_currency ?? 'VNĐ');
                    }
                    return $salary . ' ' . ($entry->candidate_currency ?? 'VNĐ');
                }
                return '<span class="text-muted">Không có</span>';
            })
            ->orderable(false)
            ->searchLogic(false);

        // Cột CV files
        CRUD::column('cv_files')
            ->label('CV Files')
            ->type('custom_html')
            ->value(function ($entry) {
                $html = '';
                if ($entry->cv_public) {
                    $html .= '<a href="' . $entry->url_cv_public . '" target="_blank" class="btn btn-sm btn-info mr-1" title="Xem CV công khai"><i class="la la-eye"></i> CV Public</a>';
                }
                if ($entry->cv_private) {
                    $html .= '<a href="' . $entry->url_cv_private . '" target="_blank" class="btn btn-sm btn-warning" title="Xem CV riêng tư"><i class="la la-lock"></i> CV Private</a>';
                }
                if (!$entry->cv_public && !$entry->cv_private) {
                    $html = '<span class="text-muted">Không có CV</span>';
                }
                return $html;
            })
            ->orderable(false)
            ->searchLogic(false);

        // Cột người tạo (CTV)
        CRUD::column('user.name')
            ->label('Người tạo')
            ->type('text')
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhereHas('user', function ($q) use ($searchTerm) {
                    $q->where('name', 'like', '%' . $searchTerm . '%');
                });
            });

        // Cột ngày tạo
        CRUD::column('created_at');

        // Tùy chỉnh ordering
        $this->crud->orderBy('id', 'desc');

        // Thêm filters
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_real',
                'label' => 'Loại CV'
            ],
            [
                '1' => 'CV thật',
                '0' => 'CV ảo'
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_real', $value);
            }
        );

        $this->crud->addFilter(
            [
                'type'  => 'date_range',
                'name'  => 'created_at',
                'label' => 'Khoảng thời gian tạo'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from && $dates->to) {
                    $this->crud->addClause('whereBetween', 'created_at', [
                        $dates->from . ' 00:00:00',
                        $dates->to . ' 23:59:59'
                    ]);
                }
            }
        );

        // Filter theo kinh nghiệm
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'year_experience',
                'label' => 'Kinh nghiệm'
            ],
            config('constant.sonamkinhnghiem'),
            function ($value) {
                $this->crud->addClause('where', 'year_experience', $value);
            }
        );

        // Filter theo người tạo
        // $this->crud->addFilter(
        //     [
        //         'type'  => 'dropdown',
        //         'name'  => 'user_id',
        //         'label' => 'Người tạo'
        //     ],
        //     function () {
        //         // Lấy danh sách user có tạo CV
        //         $userIds = WareHouseCv::where('is_active', 1)
        //             ->distinct()
        //             ->pluck('user_id')
        //             ->toArray();

        //         return User::where('type', 'rec')
        //             ->whereIn('id', $userIds)
        //             ->orderBy('name')
        //             ->pluck('name', 'id')
        //             ->toArray();
        //     },
        //     function ($value) {
        //         $this->crud->addClause('where', 'user_id', $value);
        //     }
        // );
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        CRUD::setValidation(WareHouseCvRequest::class);

        // Tab thông tin cơ bản
        CRUD::field('candidate_name')
            ->label('Tên ứng viên')
            ->type('text')
            ->tab('Thông tin cơ bản');

        CRUD::field('candidate_email')
            ->label('Email')
            ->type('email')
            ->tab('Thông tin cơ bản');

        CRUD::field('candidate_mobile')
            ->label('Số điện thoại')
            ->type('text')
            ->tab('Thông tin cơ bản');

        CRUD::field('candidate_job_title')
            ->label('Vị trí ứng tuyển')
            ->type('text')
            ->tab('Thông tin cơ bản');

        CRUD::field('assessment')
            ->label('Đánh giá')
            ->type('textarea')
            ->tab('Thông tin cơ bản');

        // Tab thông tin nghề nghiệp
        CRUD::field('career')
            ->label('Ngành nghề')
            ->type('select_from_array')
            ->options(function () {
                $lang = app()->getLocale();
                return config('job.career.' . $lang);
            })
            ->allows_multiple(true)
            ->tab('Thông tin nghề nghiệp');

        CRUD::field('year_experience')
            ->label('Kinh nghiệm')
            ->type('select_from_array')
            ->options(config('constant.sonamkinhnghiem'))
            ->tab('Thông tin nghề nghiệp');

        CRUD::field('candidate_formwork')
            ->label('Hình thức làm việc')
            ->type('select_from_array')
            ->options([
                1 => 'Toàn thời gian',
                2 => 'Bán thời gian',
                3 => 'Thực tập',
                4 => 'Freelance'
            ])
            ->tab('Thông tin nghề nghiệp');

        // Tab thông tin lương
        CRUD::field('candidate_currency')
            ->label('Loại tiền tệ')
            ->type('select_from_array')
            ->options(config('constant.currency'))
            ->tab('Thông tin lương');

        CRUD::field('candidate_salary_expect')
            ->label('Lương mong muốn từ')
            ->type('number')
            ->tab('Thông tin lương');

        CRUD::field('candidate_salary_expect_to')
            ->label('Lương mong muốn đến')
            ->type('number')
            ->tab('Thông tin lương');

        // Tab CV Files
        CRUD::field('cv_public')
            ->label('CV Công khai')
            ->type('upload')
            ->upload(true)
            ->disk('s3')
            ->tab('CV Files');

        CRUD::field('cv_private')
            ->label('CV Riêng tư')
            ->type('upload')
            ->upload(true)
            ->disk('s3')
            ->tab('CV Files');

        // Tab trạng thái
        CRUD::field('is_real')
            ->label('Loại CV')
            ->type('select_from_array')
            ->options([
                1 => 'CV thật',
                0 => 'CV ảo'
            ])
            ->tab('Trạng thái');

        CRUD::field('is_active')
            ->label('Trạng thái hoạt động')
            ->type('select_from_array')
            ->options([
                1 => 'Kích hoạt',
                0 => 'Vô hiệu hóa'
            ])
            ->tab('Trạng thái');
    }

    /**
     * Define what happens when the Show operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-show
     * @return void
     */
    protected function setupShowOperation()
    {
        $this->setupListOperation();

        // Thêm thông tin chi tiết
        CRUD::column('candidate_address')
            ->label('Địa chỉ')
            ->type('text');

        CRUD::column('candidate_portfolio')
            ->label('Portfolio')
            ->type('url');

        CRUD::column('career_value')
            ->label('Ngành nghề')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->career_value && is_array($entry->career_value)) {
                    return implode(', ', $entry->career_value);
                }
                return '<span class="text-muted">Không có</span>';
            });

        CRUD::column('main_skill')
            ->label('Kỹ năng chính')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->main_skill) {
                    $skills = json_decode($entry->main_skill, true);
                    if ($skills && is_array($skills)) {
                        $skillList = '';
                        foreach ($skills as $skill => $description) {
                            $skillList .= '<strong>' . e($skill) . ':</strong> ' . e($description) . '<br>';
                        }
                        return $skillList;
                    }
                }
                return '<span class="text-muted">Không có</span>';
            });

        CRUD::column('updated_at')
            ->label('Cập nhật lần cuối')
            ->type('datetime')
            ->format('d/m/Y H:i');
    }

    /**
     * Helper methods cho thống kê
     */
    private function getTotalCvs()
    {
        return WareHouseCv::where('is_active', 1)->count();
    }

    private function getTotalCvsCurrentMonth()
    {
        return WareHouseCv::where('is_active', 1)
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }

    private function getTotalRealCvs()
    {
        return WareHouseCv::where('is_active', 1)
            ->where('is_real', 1)
            ->count();
    }
}
