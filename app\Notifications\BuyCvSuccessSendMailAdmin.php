<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class BuyCvSuccessSendMailAdmin extends Mailable
{
    use Queueable;

    protected $user;
    protected $cvBuy;
    protected $ntd;
    protected $wareHouseCvSellingBuyId;

    public function __construct($user, $cvBuy, $ntd, $wareHouseCvSellingBuyId)
    {
        $this->user = $user;
        $this->cvBuy = $cvBuy;
        $this->ntd = $ntd;
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $name = 'Admin';
        $candidateName = $this->cvBuy->wareHouseCvSelling->warehouseCv->candidate_name;
        $companyName = $this->ntd->name;
        $type = $this->cvBuy->wareHouseCvSelling->type_of_sale;
        $candidateJobTitle = '';
        if ($type == 'cv'){
            $candidateJobTitle = $this->cvBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->cvBuy->job)){
            $candidateJobTitle = $this->cvBuy->job->name;
        }
        $reward = number_format($this->cvBuy->wareHouseCvSelling->price);
        $linkadminLuotban = route('luot-ban.show', $this->wareHouseCvSellingBuyId);

        return new Content(
            view: 'email.mua_cv_thanhcong_uyquyen_admin',
            with: [
                'name' => $name,
                'mactv' => $this->cvBuy->wareHouseCvSelling->wareHouseCv->user_id,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'candidateJobTitle' => $candidateJobTitle,
                'type'  => $type,
                'reward' => $reward,
                'linkadminLuotban' => $linkadminLuotban,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $type = ucfirst($this->cvBuy->wareHouseCvSelling->type_of_sale);
        $message->subject('[UỶ QUYỀN] Thông báo có 1 ứng viên ' . $type . ' đã uỷ quyền');
        return $this;
    }

}
