<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;

class PolicyController extends Controller
{
    public function mechanismOfActionRec()
    {
        return view('vendor.policy.' . __FUNCTION__);
    }

    public function mechanismOfActionCollaborator()
    {
        return view('vendor.policy.' . __FUNCTION__);
    }

    public function termsOfService()
    {
        return view('vendor.policy.' . __FUNCTION__);
    }

    public function privacyPolicy()
    {
        return view('vendor.policy.' . __FUNCTION__);
    }

    public function guideDeleteData()
    {
        return view('vendor.policy.' . __FUNCTION__);
    }
}
