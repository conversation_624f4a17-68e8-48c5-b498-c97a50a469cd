<?php

namespace App\Repositories;

use App\Models\SubmitCvOnboard;
use Carbon\Carbon;

class SubmitCvOnboardRepository extends BaseRepository
{

    const MODEL = SubmitCvOnboard::class;

    public function getBySubmitCvId($submit_id){
        $query = $this->query()
            ->where('submit_cvs_id',$submit_id)
            ->with(['employer']);
        return $query->get();
    }

    public function getCurrentBySubmitCvId($submit_id){
        $query = $this->query()
            ->where('submit_cvs_id',$submit_id)
            ->where('status',0);
        return $query->first();
    }

    public function getFirstBySubmitCvId($submit_id){
        $query = $this->query()
            ->where('submit_cvs_id',$submit_id);
        return $query->first();
    }


    public function getBookBySubmitCvId($submit_id){
        return $this->query()->where('submit_cvs_id', $submit_id)->orderBy('id', 'desc')->first();
    }


}
