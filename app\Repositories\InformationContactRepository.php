<?php

namespace App\Repositories;

use App\Models\InformationContact;

class InformationContactRepository extends BaseRepository
{
    const MODEL = InformationContact::class;

    public function getListInformationContact($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

        $query->orderBy($order_by, $sort);

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('city', 'like', '%' . $params['search'] . '%')
                ->orWhere('phone', 'like', '%' . $params['search'] . '%')
                ->orWhere('email', 'like', '%' . $params['search'] . '%')
                ->orWhere('website', 'like', '%' . $params['search'] . '%')
                ->orWhere('address', 'like', '%' . $params['search'] . '%');
        }

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function getAllContact(){
        return $this->query()
            ->where('is_active',config('constant.active'))
            ->get();
    }

}
