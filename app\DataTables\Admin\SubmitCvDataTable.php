<?php

namespace App\DataTables\Admin;

use App\Services\Admin\PermissionService;
use App\Services\Admin\SubmitCvService;
use App\Traits\DataTableTrait;

use Illuminate\Support\Str;
use Yajra\DataTables\Services\DataTable;

class SubmitCvDataTable extends DataTable
{
    use DataTableTrait;

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        $this->ajaxData = [
            'type' => 'GET',
            'data' => 'function(d){
                d.search = $("#search").val();
                d.status = $("#status").val();
                d.authorize_status = $("#authorize_status").val();
                d.authorize = $("#authorize").val();
                d.start_date = $("#start_date").val();
                d.end_date = $("#end_date").val();
            }',
        ];

        $this->dataParameter = [
            'searching' => false,
        ];

        $this->submitCvService = $submitCvService;
    }

    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addIndexColumn()
            ->addColumn('action', function ($item) {;
                return view('admin.pages.submitcv.action', compact('item'))->render();
            })
            ->editColumn('status', function ($item) {
                $status = config('constant.status_recruitment.vi');
                // unset($status[7]);
                $selected_array = select_key(optional($item->job)->bonus_type);
                $statusNotAction = $this->statusNotAction();
                $adminChangeStatus = config('constant.admin_change_status.' . optional($item->job)->bonus_type);
                // dd($statusNotAction);
                $color = $this->getColor();
                // $color = $this->colorNotAction();
                return view('admin.pages.submitcv.select2', compact('item', 'status', 'selected_array', 'statusNotAction', 'color', 'adminChangeStatus'))->render();
            })
            ->addColumn('candidate_name', function ($item) {
                return optional($item->submitCvMeta)->candidate_name;
            })
            ->addColumn('candidate_mobile', function ($item) {
                return optional($item->submitCvMeta)->candidate_mobile;
            })
            ->addColumn('candidate_email', function ($item) {
                return optional($item->submitCvMeta)->candidate_email;
            })
            ->editColumn('created_at', function ($item) {
                return $item->created_at->format('d/m/Y ');
            })
            ->editColumn('authorize_status', function ($item) {
                return ($item->authorize) ? ' Ủy quyền' : 'Không ủy quyền';
            })
            ->addColumn('cv_public', function ($item) {
                $html = '';
                if (s3_file_exists(optional($item->submitCvMeta)->cv_public)) {
                    $cvUrl = gen_url_file_s3(optional($item->submitCvMeta)->cv_public);

                    if (strpos($cvUrl, '.pdf') !== false) {
                        $iframeSrc = $cvUrl;
                    } else {
                        $iframeSrc = "https://view.officeapps.live.com/op/embed.aspx?src=" . urlencode($cvUrl);
                    }

                    $html = '<a href="' . $iframeSrc . '" target="_blank">[XEM CV]</a> ';
                    $html .= '<i class="ti-import" role="button" style="font-size: 20px; margin-left: 10px;" data-value="' . gen_url_file_s3(optional($item->submitCvMeta)->cv_public, '', false) . ' " data-name="' . gen_url_file_s3(optional($item->submitCvMeta)->cv_public, '', false) . '" data-toggle="tooltip" title="" data-original-title="download"></i>';
                }
                return $html;
            })

            ->addColumn('cv_private', function ($item) {
                $html = '';
                if (s3_file_exists(optional($item->submitCvMeta)->cv_private)) {
                    $html = '<i class="fa fa-eye" role="button" data-value="' . gen_url_file_s3(optional($item->submitCvMeta)->cv_private) . '" data-toggle="tooltip" style="font-size: 20px" title="" data-original-title="fa fa-eye"></i>';
                    $html .= '<i class="ti-import" role="button" style="font-size: 20px; margin-left: 10px;" data-value="' . gen_url_file_s3(optional($item->submitCvMeta)->cv_private, '', false) . ' " data-name="' . gen_url_file_s3(optional($item->submitCvMeta)->cv_private, '', false) . '" data-toggle="tooltip" title="" data-original-title="download"></i>';
                }
                return $html;
            })
            ->addColumn('job', function ($item) {

                $html = '<a href="/admin/job/' . optional($item->job)->id . '/edit" target="_blank"> <i class="feather feather-edit-2  text-success" data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit"></i></a>';
                $html .= ' <a href="' . optional($item->job)->full_path_slug . '" target="_blank"> ' . optional($item->job)->name . '</a>';
                return $html;
            })
            ->addColumn('referral_define', function ($item) {
                $referral_define = optional($item->user)->name;
                $referral_define .= optional($item->user)->referral_define ? "\n<br><strong>" . optional($item->user)->referral_define . "</strong>" : '';
                if ($referral_define) {
                    return $referral_define;
                }
                return '-';
            })
            ->addColumn('company', function ($item) {
                $companyName = optional($item->company)->name;
                $jobNotes = '';
                if (isset($item->job)) {
                    $jobNotes = Str::limit($item->job->note, 20, '...');
                }

                return '<p class="font-weight-bold"><a href="/admin/company/' . optional($item->company)->id . '/edit" target="_blank">' . $companyName . '</a></p><div class="font-italic text-muted">' . $jobNotes . '</div>';
            })


            ->rawColumns(['action', 'status', 'cv_public', 'cv_private', 'job', 'company', 'referral_define']);
    }

    /**
     * Get query source of dataTable.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query()
    {
        $param = \request()->only(['search', 'start_date', 'end_date', 'status', 'authorize', 'authorize_status']);
        $param['authorized'] = true;
        return $this->submitCvService->indexService($param, [], false, true);
    }


    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            'action' => [
                'title' => 'Hành động',
                'printable' => false,
                'exportable' => false,
                'searchable' => false,
                'orderable' => false,
                'style' => PermissionService::checkPermission($this->request()->route()->getName()) ? 'width:90px !important;' : ''
            ],
            'DT_RowIndex' => ['title' => 'STT', 'data' => 'DT_RowIndex', 'orderable' => false, 'searchable' => false, 'style' => 'width:1% !important'],
            'candidate_name' => ['title' => 'Tên ứng viên'],
            'status' => ['title' => 'Trạng thái', 'style' => 'width:200px !important'],
            'candidate_mobile' => ['title' => 'Số Điện Thoại'],
            'candidate_email' => ['title' => 'Email', 'width:50px !important'],
            'authorize_status' => ['title' => 'Trạng thái ủy quyền'],
            'created_at' => ['title' => 'Thời gian sumbmit'],
            'referral_define' => ['title' => 'CTV'],
            'cv_public' => ['title' => 'CV public', 'style' => 'width:80px !important'],
            'cv_private' => ['title' => 'CV private', 'style' => 'width:80px !important'],
            'job' => ['title' => 'JD công việc'],
            'company' => ['title' => 'Tên Công Ty'],
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Admin/SubmitCv_' . date('YmdHis');
    }

    protected function selectKey($bonus_type)
    {
        $keys_to_select = [];
        if ($bonus_type == 'cv') {

            $keys_to_select = array(
                'pending-confirm',
                'pending-review',
                'accepted',
                'rejected',
                'pass-interview',
                'fail-interview',
                'cancel',
                'admin-review',
                'admin-rejected',
                'candidate-rejected'
            );
        }
        if ($bonus_type == 'onboard') {

            $keys_to_select = array(
                'pending-review',
                'pending-confirm',
                'accepted',
                'rejected',
                'pass-interview',
                'fail-interview',
                'offering',
                'onboarded',
                'cancel',
                'admin-review',
                'admin-rejected',
                'candidate-rejected'
            );
        }
        if ($bonus_type == 'interview') {
            $keys_to_select = array(
                'pending-review',
                'pending-confirm',
                'accepted',
                'rejected',
                'pass-interview',
                'fail-interview',
                'cancel',
                'admin-review',
                'admin-rejected',
                'candidate-rejected'
            );
        }

        return array_intersect_key(config('constant.submit_cvs_status_value'), array_flip($keys_to_select));
    }

    protected function getColor()
    {
        return config('constant.status_recruitment.color');
    }
    protected function colorNotAction()
    {
        return [
            1 => 'badge-secondary',
            2 => 'badge-success',
            3 => 'badge-warning',
            4 => 'badge-info',
            5 => 'badge-dark',
            6 => 'badge-danger',
            11 => 'badge-success',
            12 => 'badge-danger'
        ];
    }

    protected function statusNotAction()
    {
        return array_intersect_key(
            config('constant.status_recruitment_revert'),
            array_flip(
                array(
                    'CandidateCancelApply',
                    'RecruiterCancelInterview',
                    'CandidateCancelInterview',
                    'RejectOffer',
                    'Cancelonboard',
                    'Failtrailwork',
                    'CancelBuyCVdata',
                    'RecruiterRejectCV',
                )
                // []
            )
        );
    }
}
