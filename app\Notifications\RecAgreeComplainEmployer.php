<?php

namespace App\Notifications;

use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecAgreeComplainEmployer extends Notification implements ShouldQueue
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $cvSellingBuy;
    protected $point;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$cvSellingBuy,$point)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->cvSellingBuy = $cvSellingBuy;
        $this->point = $point;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $companyName = $this->employer->name;
        $type = $this->cvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->cvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->cvSellingBuy->job)){
            $position = $this->cvSellingBuy->job->name;
        }
        return (new MailMessage)
            ->view('email.ctv_dongy_ntd', [
                'name' => $this->employer->name,
                'companyName' => $companyName,
                'position' => $position,
                'urlWallet' => route('employer-wallet'),
                'urlMarket' => route('market-cv'),
                'point' => $this->point,
                'type' => $type,
            ])
            ->subject('[Recland][Case #'.$this->cvSellingBuy->id.'] Kết quả khiếu nại');
    }

}
