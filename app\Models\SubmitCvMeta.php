<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use OwenIt\Auditing\Contracts\Auditable;

class SubmitCvMeta extends BaseModel  implements Auditable 
{

    use \OwenIt\Auditing\Auditable;
    protected $guarded = ['id'];

    public $timestamps = true;

    protected $appends = ['url_cv_private', 'url_cv_public', 'name_cv_private', 'name_cv_public'];

    public function getUrlCvPrivateAttribute()
    {
        return gen_url_file_s3($this->cv_private, '', false);
    }

    public function getUrlCvPublicAttribute()
    {
        return gen_url_file_s3($this->cv_public, '', false);
    }

    public function getNameCvPrivateAttribute()
    {
        if ($this->cv_private) {
            $path = config('constant.sub_path_s3.cv') . '/';
            $name = str_replace($path, '', $this->cv_private);
            return $name;
        }
        return null;
    }

    public function getNameCvPublicAttribute()
    {
        if ($this->cv_public) {
            $path = config('constant.sub_path_s3.cv') . '/';
            $name = str_replace($path, '', $this->cv_public);
            return $name;

        }
        return null;
    }

    public function getCandidateCurrencyValueAttribute()
    {
        if ($this->candidate_currency == 'USD') {
            return '$';
        }
        return $this->candidate_currency;
    }

    public function getEmailFromCandidate() {
        // extract email from string
        
    }
}
