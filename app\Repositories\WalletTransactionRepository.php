<?php

namespace App\Repositories;

use App\Models\Wallet;
use App\Models\WalletTransaction;
use Carbon\Carbon;

class WalletTransactionRepository extends BaseRepository
{
    const MODEL = WalletTransaction::class;


    public function findByUser($userId, $params)
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_10');
        }

        if ($limit > 100) {
            $limit = 100;
        }
        $wallet = Wallet::where('user_id', $userId)->first();
        return $this->query()->where('wallet_id', $wallet->id)->paginate($limit, ['*'], 'page', $page);
    }
    public function spentThisMonth($userId)
    {
        $wallet = Wallet::where('user_id', $userId)->first();
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('wallet_id', $wallet->id)
            ->where('amount', '<', 0)
            ->whereYear('created_at', '=', $year)
            ->whereMonth('created_at', '=', $month)
            ->sum('amount');
        return $moneyOut;
    }

    public function spentThisYear($userId)
    {
        $wallet = Wallet::where('user_id', $userId)->first();
        $now = Carbon::now();
        $year = $now->year;
        $moneyOut = $this->query()
            ->where('wallet_id', $wallet->id)
            ->where('amount', '<', 0)
            ->whereYear('created_at', '=', $year)
            ->sum('amount');
        return $moneyOut;
    }
}