<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\ReportGovRequest;
use App\Services\Admin\JobService;
use App\Services\Admin\PermissionService;
use App\Services\Admin\UserService;
use App\Services\Frontend\WareHouseCvSellingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Shetabit\Visitor\Models\Visit;
use App\Repositories\WareHouseCvSellingRepository;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Repositories\WareHouseCvRepository;
use App\Services\FileServiceS3;
use App\Services\HideService;
use Zoha\Meta\Models\Meta;

class SellingCvController extends Controller
{
    protected $rec_id = 474;
    protected $wareHouseSubmitCvService;
    protected $wareHouseCvRepository;
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingRepository;

    public function __construct(
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvRepository $wareHouseCvRepository,
        WareHouseCvSellingRepository $wareHouseCvSellingRepository,
    ) {

        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->wareHouseCvRepository = $wareHouseCvRepository;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
    }

    public function create(Request $request)
    {
        // $wareHouseSubmitCvService = new WareHouseSubmitCvService();
        $params = $request->only(['id', 'cv_public', 'cv_private', 'candidate_name', 'candidate_mobile', 'candidate_email', 'year_experience', 'candidate_job_title', 'rank', 'candidate_portfolio', 'candidate_salary_expect', 'candidate_salary_expect_to', 'career', 'assessment', 'skills', 'level', 'type_of_sale', 'price', 'company_id', 'is_authority', 'selling_skill', 'candidate_currency']);
        // dd($params);
        $validator = Validator::make($request->all(), [
            'candidate_name' => 'required|string|max:255',
            'candidate_mobile' => 'required|string|max:255',
            'candidate_email' => 'required|string|email|max:255', // unique:users
            'year_experience' => 'required|integer',
            'candidate_job_title' => 'required|string|max:255',
        ]);
        if ($validator->fails()) {
            return response()->json([
                "error" => 'validation_error',
                "message" => $validator->errors(),
            ], 422);
        }



        $params['user_id'] = $this->rec_id;
        // $params['is_it'] = data_get($params, 'is_it', 'true');
        // $level = $this->wareHouseCvSellingService->getLevel(['group' => 2, 'is_it' => $params['is_it']]);
        // dd($level);
        // $level = $level->pluck('price_cv_min', 'id')->toArray();
        // dd($level);

        # tinh gia ban dua tren so nam kinh nghiem
        $params['year_experience']  = data_get($params, 'year_experience', 1);
        if ($params['year_experience'] > 7) {
            $params['year_experience'] = 5;
        }

        switch (true) {
            case $params['year_experience'] > 4:
                $params['price'] = 30000;
                $params['level'] = 12;
                break;
            case $params['year_experience'] > 2:
                $params['price'] = 20000;
                $params['level'] = 11;
                break;
            default:
                $params['price'] = 10000;
                $params['level'] = 10;
                break;
        }

        # Tao CV moi 

        $arrSkillDescription = [];
        if (isset($params['skills'])) {
            foreach ($params['skills'] as $k => $v) {
                if ($v) {
                    $arrSkillDescription[$v] = '';
                }
            }
        }
        $data = [
            'user_id'                    => data_get($params, 'user_id', ''),
            'candidate_name'             => data_get($params, 'candidate_name', ''),
            'candidate_email'            => data_get($params, 'candidate_email', ''),
            'candidate_mobile'           => data_get($params, 'candidate_mobile', ''),
            'assessment'                 => data_get($params, 'assessment', ''),
            'candidate_portfolio'        => data_get($params, 'candidate_portfolio', ''),
            'candidate_job_title'        => data_get($params, 'candidate_job_title', ''),
            'candidate_currency'         => data_get($params, 'candidate_currency', 'VND'),
            'candidate_salary_expect'    => data_get($params, 'candidate_salary_expect', ''),
            'candidate_salary_expect_to' => data_get($params, 'candidate_salary_expect_to', ''),
            'year_experience'            => data_get($params, 'year_experience', ''),
            'candidate_address'          => data_get($params, 'candidate_address', ''),
            'candidate_location'         => data_get($params, 'candidate_location', ''),
            'main_skill'                 => json_encode($arrSkillDescription),
            'career'                     => implode(',', data_get($params, 'career', '30,31')),         // IT phan mem , IT phan cung mang
            'candidate_formwork'         => data_get($params, 'candidate_formwork', ''),
            'candidate_est_timetowork'   => data_get($params, 'candidate_est_timetowork', ''),
            'source'                     => data_get($params, 'source', 'api'),
            'created_at'                 => data_get($params, 'created_at', date('Y-m-d H:i:s')),
            'updated_at'                 => data_get($params, 'updated_at', date('Y-m-d H:i:s')),
            'is_real'                    => data_get($params, 'is_real', 0),
        ];
        $create_cv_private = false;
        if (isset($params['cv_private']) && !empty($params['cv_private'])) {
            // Sử dụng link trực tiếp thay vì upload lên s3
            // $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3FromLink($params['cv_private'], config('constant.sub_path_s3.cv'));
            $data['cv_private'] = $params['cv_private'];
        } elseif (isset($params['cv_public']) && !empty($params['cv_public'])) {
            // Che cv
            $hideCvService = new HideService();
            $privateCv = $hideCvService->hideCv($params['cv_public']);
            if (!empty($privateCv)) {
                $data['cv_private'] = FileServiceS3::getInstance()->uploadToS3FromLink($privateCv, config('constant.sub_path_s3.cv'));
                $create_cv_private = true;
            }
        }

        if (isset($params['cv_public']) && !empty($params['cv_public'])) {
            // $data['cv_public'] = FileServiceS3::getInstance()->uploadToS3FromLink($params['cv_public'], config('constant.sub_path_s3.cv'));
            $data['cv_public'] = $params['cv_public'];
        }
        // dd($data);
        // return response()->json([
        //     'status' => 'success',
        //     'data' => $data,
        // ]);

        $meta = Meta::where('key', 'cv_from_crm_hr')->where('value', $params['id'])->first();
        if ($meta) {
            // Đã có CV thì bỏ qua không tạo mới nữa
            return response()->json([
                'status' => 'success',
                'data' => [
                    'id' => $meta->owner_id,
                    'create_cv_private' => $create_cv_private
                ],
            ]);
        }

        $wareHouseCv = $this->wareHouseCvRepository->create($data);
        $wareHouseCv->setMeta('cv_from_crm_hr', $params['id']);

        if (!$wareHouseCv) {
            throw new \Exception('create warehouse cv fail');
        }

        $data = [
            'warehouse_cv_id'         => $wareHouseCv->id,
            'level'                   => data_get($params, 'level', ''),
            'type_of_sale'            => data_get($params, 'type_of_sale', 'cv'),
            'candidate_salary_expect' => data_get($params, 'candidate_salary_expect', ''),
            'price'                   => data_get($params, 'price', 0),
            'exclude_company'         => isset($params['company_id']) ? implode(",", $params['company_id']) : null,
            'authority'               => isset($params['is_authority']) && !empty($params['is_authority']) ? $params['is_authority'] : 0,
            'skill'                   => data_get($params, 'selling_skill', ''),
            'candidate_mobile'        => $wareHouseCv->candidate_mobile,
            'candidate_email'         => $wareHouseCv->candidate_email,
            'user_id'                 => data_get($params, 'user_id', ''),
        ];

        //uy quyen thi status = 1
        if ($data['authority'] == 1) {
            //đợi admin xác nhận ủy quyền thì mới cho lên chợ cv
            // $data['status'] = 2;
            // lên luôn
            $data['status'] = 0;
        }
        //Tinh
        $point = $params['price'];
        if ($params['type_of_sale'] == 'cv') {
            // $point = (int) round($params['price'] * 2 / 1000);
        } elseif ($params['type_of_sale'] == 'interview') {
            // $point = (int) round($params['price'] * 3 / 1000);
        } elseif ($params['type_of_sale'] == 'onboard') {
            if ($params['candidate_salary_expect'] < 15000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.2);
            }

            if ($params['candidate_salary_expect'] >= 15000000 && $params['candidate_salary_expect'] < 35000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.4);
            }

            if ($params['candidate_salary_expect'] >= 35000000 && $params['candidate_salary_expect'] < 45000000) {
                $point = (int) round($params['candidate_salary_expect'] * 1.6);
            }

            if ($params['candidate_salary_expect'] >= 45000000) {
                $point = (int) round($params['candidate_salary_expect'] * 2);
            }
        }

        $data['point'] = $point;
        // dd($data);
        $cvSelling = $this->wareHouseCvSellingRepository->create($data);
        // dd($cvSelling);
        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $cvSelling->id,
                'create_cv_private' => $create_cv_private
            ],
        ]);
        // dd($wareHouseCv);
    }
}
