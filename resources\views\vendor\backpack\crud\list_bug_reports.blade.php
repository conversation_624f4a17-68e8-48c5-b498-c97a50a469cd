@extends(backpack_view('blank'))

@php
    $defaultPageLength = $crud->getDefaultPageLength();
    $crud->addButton('top', 'refresh', 'view', 'crud::buttons.refresh');
@endphp

@section('header')
    <section class="container-fluid">
        <h2>
            <span class="text-capitalize">{!! $crud->getHeading() ?? $crud->entity_name_plural !!}</span>
            <small id="datatable_info_stack">{!! $crud->getSubheading() ?? '' !!}</small>
        </h2>
    </section>
@endsection

@section('content')
    {{-- Default box --}}
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-tools">
                        @include('crud::inc.search_form')
                    </div>
                </div>

                <div class="card-body overflow-visible">
                    {{-- Backpack List Filters --}}
                    @include('crud::inc.filters_navbar')

                    <table
                        id="crudTable"
                        class="bg-white table table-striped table-hover nowrap rounded shadow-xs border-xs"
                        data-responsive-table="1"
                        data-has-details-row="{{ $crud->details_row ? 1 : 0 }}"
                        data-has-bulk-actions="{{ $crud->bulk_actions ? 1 : 0 }}"
                        cellspacing="0">
                        <thead>
                            <tr>
                                {{-- Table columns --}}
                                @foreach ($crud->columns() as $column)
                                    <th
                                        data-orderable="{{ var_export($column['orderable'], true) }}"
                                        data-priority="{{ $column['priority'] }}"
                                        data-column-name="{{ $column['name'] }}"
                                        data-visible-in-table="{{ var_export($column['visibleInTable'] ?? true, true) }}"
                                        data-can-be-visible-in-table="{{ var_export($column['canBeVisibleInTable'] ?? true, true) }}"
                                        data-visible-in-modal="{{ var_export($column['visibleInModal'] ?? true, true) }}"
                                        data-visible-in-export="{{ var_export($column['visibleInExport'] ?? true, true) }}"
                                        data-force-export="{{ var_export($column['forceExport'] ?? false, true) }}"
                                        >
                                        {{-- Bulk checkbox --}}
                                        @if($loop->first && $crud->bulk_actions)
                                            {!! View::make('crud::columns.inc.bulk_actions_checkbox')->render() !!}
                                        @endif
                                        <span>{!! $column['label'] !!}</span>
                                    </th>
                                @endforeach

                                @if ( $crud->buttons()->where('stack', 'line')->count() )
                                    <th data-orderable="false" data-priority="{{ $crud->getActionsColumnPriority() }}" data-visible-in-export="false">{{ trans('backpack::crud.actions') }}</th>
                                @endif
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                        <tfoot>
                            <tr>
                                {{-- Table columns --}}
                                @foreach ($crud->columns() as $column)
                                    <th>
                                        {{-- Bulk checkbox --}}
                                        @if($loop->first && $crud->bulk_actions)
                                            <span class="sr-only">{{ trans('backpack::crud.bulk_actions') }}</span>
                                        @endif
                                        <span>{!! $column['label'] !!}</span>
                                    </th>
                                @endforeach

                                @if ( $crud->buttons()->where('stack', 'line')->count() )
                                    <th>{{ trans('backpack::crud.actions') }}</th>
                                @endif
                            </tr>
                        </tfoot>
                    </table>

                </div>{{-- /.card-body --}}
            </div>{{-- /.card --}}
        </div>
    </div>

    {{-- Image Modal --}}
    <div class="modal fade" id="imageModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ảnh minh họa</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Bug Report Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

@endsection

@section('after_styles')
    {{-- DATA TABLES --}}
    @basset('https://cdn.datatables.net/1.13.1/css/dataTables.bootstrap4.min.css')
    @basset('https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.dataTables.min.css')
    @basset('https://cdn.datatables.net/responsive/2.4.0/css/responsive.dataTables.min.css')

    <style>
        .table td {
            vertical-align: middle;
        }
        
        .text-truncate {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        #imageModal .modal-body {
            padding: 0;
        }
        
        #imageModal img {
            max-width: 100%;
            height: auto;
        }
    </style>
@endsection

@section('after_scripts')
    @include('crud::inc.datatables_logic')
    
    <script>
        function showImageModal(src) {
            $('#modalImage').attr('src', src);
            $('#imageModal').modal('show');
        }
    </script>
@endsection
