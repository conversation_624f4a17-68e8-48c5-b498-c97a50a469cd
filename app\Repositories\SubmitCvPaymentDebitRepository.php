<?php

namespace App\Repositories;

use App\Models\SubmitCvPaymentDebit;

class SubmitCvPaymentDebitRepository extends BaseRepository
{

    const MODEL = SubmitCvPaymentDebit::class;

    public function getDebtBuyUserId($userId){
        return $this->query()->where('user_id',$userId)->where('status',0)->get();
    }
    public function getBySubmitId($submitCvId){
        $query = $this->query()
            ->where('submit_cv_id', $submitCvId)
        ->with(['user','submitCv']);

        return $query->first();
    }

}
