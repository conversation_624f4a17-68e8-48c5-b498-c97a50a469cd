<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class AboutUsAlbum extends BaseModel
{
    use CrudTrait, HasFactory;
//    protected $table = 'about_us_albums';
    protected $guarded = ['id'];

//    protected $fillable = [
//        'name',
//    ];
    public function photos()
    {
        return $this->hasMany(AboutUsAlbumPhoto::class, 'album_id', 'id');
    }

}
