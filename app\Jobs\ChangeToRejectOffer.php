<?php
namespace App\Jobs;

use App\Notifications\ChangeStatusCancelOnboardToAdmin;
use App\Notifications\ChangeStatusCancelOnboardToRec;
use App\Notifications\EmailRejectOnboard;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class ChangeToRejectOffer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wareHouseCvSellingBuyId;
    protected $user;
    protected $onboardId;

    public function __construct($wareHouseCvSellingBuyId,$onboardId,$user)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
        $this->user = $user;
        $this->onboardId = $onboardId;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyOnboardRepository = app(WareHouseCvSellingBuyOnboardRepository::class);
        $wareHouseCvSellingBuyRepository = app(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        if($wareHouseCvSellingBuy->status_recruitment != 20) return false;

        $onboard = $wareHouseCvSellingBuyOnboardRepository->find($this->onboardId);
        $onboard->update([
            'status' => 2
        ]);
        //12 => 'Reject Offer',
        $wareHouseCvSellingBuy->update([
            'status_recruitment' => 12,
        ]);
        $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy,$this->user);

        $employer = $wareHouseCvSellingBuy->employer;
        $employer->notify(new EmailRejectOnboard($employer,$onboard,$wareHouseCvSellingBuy));
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
            Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelOnboardToAdmin($wareHouseCvSellingBuy));
        }else{
            $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelOnboardToRec($wareHouseCvSellingBuy));
        }
        //todo hoàn cọc
        DepositRefundRejectOffer::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(48 * 60));;
    }

}
