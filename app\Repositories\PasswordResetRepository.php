<?php

namespace App\Repositories;

use App\Models\PasswordReset;

class PasswordResetRepository extends BaseRepository
{
    const MODEL = PasswordReset::class;

    public function updateOrCreate(array $attributes, array $values = []){
        return PasswordReset::updateOrCreate($attributes,$values);
    }

    public function findByToken($token){
        return $this->query()->where('token',$token)->first();
    }


}
