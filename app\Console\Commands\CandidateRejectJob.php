<?php

namespace App\Console\Commands;

use App\Models\SubmitCv;
use App\Services\Admin\SubmitCvService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CandidateRejectJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'candidate:reject-job';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ứng viên chuyển trạng thái candidate reject khi quá 48 tiếng';

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        parent::__construct();
        $this->submitCvService = $submitCvService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // return Command::FAILURE;
        $submitCvs = $this->submitCvService->indexService(['status' => config('constant.status_recruitment_revert')['Waitingcandidateconfirm'], 'confirm_candidate' => Carbon::now()]);
        foreach ($submitCvs as $submitCv) {
            if (Carbon::parse($submitCv->confirm_candidate)->lessThan(Carbon::now())) {
                $submitCv->update(['status' => config('constant.status_recruitment_revert')['CandidateCancelApply']]);
            }
        }
        return Command::SUCCESS;
    }
}