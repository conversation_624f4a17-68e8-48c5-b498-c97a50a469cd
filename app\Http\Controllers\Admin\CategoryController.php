<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CategoryRequest;
use App\Services\Admin\CategoryService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class CategoryController extends Controller
{

    protected $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index(): View|Factory|Application
    {
        $datatable = $this->categoryService->buildDatatable();
        return view('admin.pages.category.index',
            compact('datatable'));
    }

    /**
     * @param CategoryRequest $request
     * @return mixed
     */
    public function store(CategoryRequest $request): mixed
    {
        return $this->categoryService->createService($request->all());
    }

    public function show($id)
    {
        return $this->categoryService->detailService($id);
    }

    /**
     * @param CategoryRequest $request
     * @param $id
     * @return bool
     */
    public function update(CategoryRequest $request, $id): bool
    {
        return $this->categoryService->updateService($request->all(), $id);
    }

    public function datatable(Request $request)
    {
        $data = $this->categoryService->datatable($request->all(), true);
        return response($data);
    }
}
