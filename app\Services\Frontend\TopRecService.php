<?php

namespace App\Services\Frontend;

use App\Repositories\TopRecRepository;

class TopRecService
{

    protected $topRecRepository;

    public function __construct(TopRecRepository $topRecRepository)
    {
        $this->topRecRepository = $topRecRepository;
    }

    public function getTopRecList()
    {
        $params = [
            'size' => 10
        ];
        $data = $this->topRecRepository->getTopRecList($params);
        return $data;
    }

    public function indexService($params)
    {
        return $this->topRecRepository->getQuery($params);
    }

    public function insert($params)
    {
        return $this->topRecRepository->insert($params);
    }

    public function update($id, $options = [], $attributes = [])
    {
        return $this->topRecRepository->update($id, $options, $attributes);

    }

    public function delete($id, $options)
    {
        return $this->topRecRepository->delete($id, $options);
    }
}
