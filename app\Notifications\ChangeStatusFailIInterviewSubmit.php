<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailIInterviewSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $options;

    public function __construct($submitCv, $options = [])
    {
        $this->submitCv = $submitCv;
        $this->options = $options;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $recName = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $note = '';
        if (isset($this->options['note'])) {
            $note = $this->options['note'];
        }
        $companyName = $this->submitCv->company->name;
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id, 'open_comment' => 1]);
        return (new MailMessage)
            ->view('email.changeStatusFailIInterviewSubmit', [
            'recName'       => $recName,
            'candidateName' => $candidateName,
            'companyName'   => $companyName,
            'position'      => $position,
            'type'          => $type,
            'note'          => $note,
            'url'           => $url,
            ])
            ->subject('[Recland] Thông báo Ứng viên '.$candidateName.' đã Fail phỏng vấn của công ty '.$companyName);

    }




}
