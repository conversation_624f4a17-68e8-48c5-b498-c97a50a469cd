<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecEmployerUpdateStatusReportSystem extends Notification implements ShouldQueue
{
    use Queueable;

    protected $report;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($report)
    {
        $this->report = $report;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $name = $this->report->user->name;
        $code = $this->report->id;
        $type = $this->report->type_issue_value;
        $description = $this->report->description;
        $image = $this->report->file_url;

        return (new MailMessage)
            ->view('email.ctv_ntd_baocao_hethong_capnhat_trangthai', [
                'name' => $name,
                'code' => $code,
                'type' => $type,
                'description' => $description,
                'image' => $image,

            ])
            ->subject('[Recland] Thông báo đóng báo cáo hệ thống');
    }

}
