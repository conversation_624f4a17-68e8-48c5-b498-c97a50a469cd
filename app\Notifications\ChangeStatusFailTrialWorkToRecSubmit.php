<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusFailTrialWorkToRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;

    protected $options;

    public function __construct($submitCv, $options = [])
    {
        $this->submitCv = $submitCv;
        $this->options = $options;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName       = $this->submitCv->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $note = '';
        if (isset($this->options['note'])) {
            $note = $this->options['note'];
        }
        $url           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id]);
        return (new MailMessage)
            ->view('email.changeStatusFailTrialWorkToRecSubmit', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'note'          => $note,
                'url'           => $url,
            ])
            ->subject('[Recland] Thông báo kết quả thử việc Ứng viên '.$candidateName.' vị trí '.$position.' tại Công ty  '.$companyName);

    }




}
