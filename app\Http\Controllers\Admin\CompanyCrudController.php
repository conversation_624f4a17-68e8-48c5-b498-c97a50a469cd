<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Job;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Http\Request;
use Carbon\Carbon;
use App\Helpers\Common;

/**
 * Class CompanyCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CompanyCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(Company::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/companies');
        CRUD::setEntityNameStrings('công ty', 'công ty');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        // Giới hạn số bản ghi mỗi trang
        $this->crud->setDefaultPageLength(25);
        $this->crud->setPageLengthMenu([10, 25, 50, 100]);
        $this->crud->denyAccess(['create', 'delete']);
        $this->crud->removeAllButtons();
        $this->crud->addButtonFromView('top', 'company.create_company', 'admin.buttons.create_company', 'beginning');
        $this->crud->addButtonFromView('line', 'company.custom_edit', 'admin.pages.company.list_button', 'beginning');

        // Thêm widgets thống kê
        Widget::add([
            'type'         => 'div',
            'class'        => 'row',
            'content'      => [
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-primary col-md-12',
                    'value'        => $this->getTotalCompanies(),
                    'description'  => 'Tổng số công ty',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Tổng cộng tất cả công ty trong hệ thống',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-success col-md-12',
                    'value'        => $this->getActiveCompanies(),
                    'description'  => 'Công ty đang hoạt động',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số công ty đang hoạt động',
                ],
                [
                    'type'         => 'progress',
                    'class'        => 'card text-white bg-info col-md-12',
                    'value'        => $this->getCompaniesThisMonth(),
                    'description'  => 'Công ty tháng này',
                    'progress'     => 100,
                    'progressClass' => 'progress-bar bg-light',
                    'hint'         => 'Số công ty được tạo trong tháng này',
                ]
            ]
        ])->to('before_content');

        // Cột ID
        CRUD::column('id')
            ->label('ID')
            ->type('number');

        // Cột logo
        CRUD::column('logo')
            ->label('Logo')
            ->type('custom_html')
            ->value(function ($entry) {
                if ($entry->logo) {
                    return '<img src="' . asset($entry->logo) . '" style="max-width: 50px; max-height: 50px;" class="img-thumbnail">';
                }
                return '<span class="badge badge-secondary">Không có logo</span>';
            });

        // Cột tên công ty
        CRUD::column('name')
            ->label('Tên công ty')
            ->type('custom_html')
            ->value(function ($entry) {
                $name = \Illuminate\Support\Str::limit($entry->name, 50);
                $html = '<strong>' . e($name) . '</strong>';
                if ($entry->website) {
                    $html .= '<br><small><a href="' . $entry->website . '" target="_blank" class="text-info">' . $entry->website . '</a></small>';
                }
                if ($entry->is_real) {
                    // $html .= '<br><small class="badge badge-success">Công ty thật</small>';
                } else {
                    $html .= '<br><small class="badge badge-danger">Công ty ảo</small>';
                }
                return $html;
            })
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->orWhere('name', 'like', '%' . $searchTerm . '%');
            });

        // Cột địa chỉ
        // CRUD::column('address')
        //     ->label('Địa chỉ')
        //     ->type('custom_html')
        //     ->value(function ($entry) {
        //         $address = \Illuminate\Support\Str::limit($entry->address ?? 'N/A', 80);
        //         $city = $entry->city ? '<br><small class="badge badge-info">' . $entry->city . '</small>' : '';
        //         return e($address) . $city;
        //     })
        //     ->searchLogic(function ($query, $column, $searchTerm) {
        //         $query->orWhere('address', 'like', '%' . $searchTerm . '%')
        //             ->orWhere('city', 'like', '%' . $searchTerm . '%');
        //     });

        // // Cột email và phone
        // CRUD::column('contact_info')
        //     ->label('Liên hệ')
        //     ->type('custom_html')
        //     ->value(function ($entry) {
        //         $html = '';
        //         if ($entry->email) {
        //             $html .= '<small><i class="la la-envelope"></i> ' . e($entry->email) . '</small>';
        //         }
        //         if ($entry->phone) {
        //             $html .= $entry->email ? '<br>' : '';
        //             $html .= '<small><i class="la la-phone"></i> ' . e($entry->phone) . '</small>';
        //         }
        //         return $html ?: 'N/A';
        //     })
        //     ->searchLogic(function ($query, $column, $searchTerm) {
        //         $query->orWhere('email', 'like', '%' . $searchTerm . '%')
        //             ->orWhere('phone', 'like', '%' . $searchTerm . '%');
        //     });

        // Cột số lượng job
        CRUD::column('jobs_count')
            ->label('Số tin tuyển dụng')
            ->type('custom_html')
            ->value(function ($entry) {
                $totalJobs = $entry->jobs()->count();
                $activeJobs = $entry->jobs()->where('is_active', 1)->where('status', 1)->count();
                return '<span class="badge badge-primary">' . $totalJobs . '</span> / <span class="badge badge-success">' . $activeJobs . ' hoạt động</span>';
            });

        // Cột trạng thái
        CRUD::column('is_active')
            ->label('Trạng thái')
            ->type('custom_html')
            ->value(function ($entry) {
                $status = $entry->is_active ? 'Hoạt động' : 'Không hoạt động';
                $class = $entry->is_active ? 'badge-success' : 'badge-secondary';
                $html = '<span class="badge ' . $class . '">' . $status . '</span>';

                if ($entry->priority) {
                    $priorityText = '';
                    switch ($entry->priority) {
                        case 1:
                            $priorityText = 'Ưu tiên cao';
                            break;
                        case 2:
                            $priorityText = 'Ưu tiên vừa';
                            break;
                        case 3:
                            $priorityText = 'Ưu tiên thấp';
                            break;
                    }
                    if ($priorityText) {
                        $html .= '<br><small class="badge badge-warning">' . $priorityText . '</small>';
                    }
                }

                return $html;
            });
        CRUD::column('admin_id')
            ->label('Admin')
            ->type('custom_html')
            ->value(function ($entry) {
                return $entry->admin_id ? $entry->admin->name : 'N/A';
            });
        // Cột ngày tạo
        CRUD::column('created_at')
            ->label('Ngày tạo')
            ->type('closure')
            ->function(function ($entry) {
                return $entry->created_at ? $entry->created_at->format('d/m/Y H:i') : 'N/A';
            });

        // Tùy chỉnh ordering
        $this->crud->orderBy('id', 'desc');

        // Thêm filters
        $this->setupFilters();
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'website' => 'nullable|url|max:255',
            'description' => 'nullable|string',
        ]);

        CRUD::field('name')
            ->label('Tên công ty')
            ->type('text')
            ->attributes(['required' => true]);

        CRUD::field('email')
            ->label('Email')
            ->type('email');

        CRUD::field('phone')
            ->label('Số điện thoại')
            ->type('text');

        CRUD::field('website')
            ->label('Website')
            ->type('url');

        CRUD::field('address')
            ->label('Địa chỉ')
            ->type('textarea')
            ->attributes(['rows' => 3]);

        CRUD::field('city')
            ->label('Thành phố')
            ->type('select_from_array')
            ->options(Common::getCities() ?? []);

        CRUD::field('logo')
            ->label('Logo')
            ->type('upload')
            ->upload(true)
            ->disk('public');

        CRUD::field('description')
            ->label('Mô tả')
            ->type('textarea')
            ->attributes(['rows' => 5]);

        CRUD::field('priority')
            ->label('Độ ưu tiên')
            ->type('select_from_array')
            ->options([
                1 => 'Ưu tiên cao',
                2 => 'Ưu tiên vừa',
                3 => 'Ưu tiên thấp'
            ])
            ->default(3);

        CRUD::field('is_active')
            ->label('Trạng thái hoạt động')
            ->type('boolean')
            ->default(1);
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    /**
     * Setup các bộ lọc
     */
    private function setupFilters()
    {
        // Filter trạng thái hoạt động
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_active',
                'label' => 'Trạng thái hoạt động'
            ],
            [
                '1' => 'Hoạt động',
                '0' => 'Không hoạt động'
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_active', $value);
            }
        );

        // // Filter theo thành phố
        // $cities = Common::getCities();
        // if ($cities) {
        //     $this->crud->addFilter(
        //         [
        //             'type'  => 'dropdown',
        //             'name'  => 'city',
        //             'label' => 'Thành phố'
        //         ],
        //         $cities,
        //         function ($value) {
        //             $this->crud->addClause('where', 'city', $value);
        //         }
        //     );
        // }

        // Filter độ ưu tiên
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'priority',
                'label' => 'Độ ưu tiên'
            ],
            [
                '1' => 'Ưu tiên cao',
                '2' => 'Ưu tiên vừa',
                '3' => 'Ưu tiên thấp'
            ],
            function ($value) {
                $this->crud->addClause('where', 'priority', $value);
            }
        );

        // Filter tin thật/ảo
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_real',
                'label' => 'Công ty thật/ảo'
            ],
            [
                '1' => 'Công ty thật',
                '0' => 'Công ty ảo',
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_real', $value);
            }
        );

        // Filter thời gian tạo
        $this->crud->addFilter(
            [
                'type'  => 'date_range',
                'name'  => 'created_at',
                'label' => 'Thời gian tạo'
            ],
            false,
            function ($value) {
                $dates = json_decode($value);
                if ($dates->from && $dates->to) {
                    $this->crud->addClause('whereBetween', 'created_at', [
                        $dates->from . ' 00:00:00',
                        $dates->to . ' 23:59:59'
                    ]);
                }
            }
        );
    }

    /**
     * Helper methods cho thống kê
     */
    private function getTotalCompanies()
    {
        return Company::count();
    }

    private function getActiveCompanies()
    {
        return Company::where('is_active', 1)->count();
    }

    private function getCompaniesThisMonth()
    {
        return Company::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();
    }
}
