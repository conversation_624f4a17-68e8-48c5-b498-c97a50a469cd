<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class WareHouseCvRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $arr = [
            'candidate_name' => 'required',
            'candidate_email' => 'required',
            'candidate_mobile' => 'required|regex:/^[0-9()+.-]*$/|max:16',
            'assessment' => 'required',
            'candidate_job_title' => 'required',
            'candidate_currency' => 'required',
            'career' => 'required',
            'candidate_est_timetowork' => 'required',
            'candidate_salary_expect' => 'required|numeric',
            'skill' => 'required',
            'cv_private' => 'mimes:pdf,docx|max:5120',
            'cv_public' => 'mimes:pdf,docx|max:5120',
        ];

        if (!$this->get('cv_private_old')) {
            $arr['cv_private'] = 'required|mimes:pdf,docx';
        }

        if (!$this->get('cv_public_old')) {
            $arr['cv_public'] = 'required|mimes:pdf,docx';
        }
        return $arr;
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'mimes' => __('message.format_cv'),
            'candidate_mobile.regex' => __('message.regex_phone'),
            'max' => __('message.max', ['max' => 16]),
            'cv_public.max' => __('message.file_max'),
            'cv_private.max' => __('message.file_max'),
        ];
    }
}
