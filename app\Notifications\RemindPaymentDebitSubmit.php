<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindPaymentDebitSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $submitCvDiscuss;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName  = $this->submitCv->warehouseCv->candidate_name;
        $position       = $this->submitCv->job->name;
        $employerName   = $this->submitCv->employer->name;
        $point          = $this->submitCv->bonus_point;
        $point          = $point - (0.1 * $point);
        $employerWallet = route('employer-wallet',['deposit'=>1]);
        return (new MailMessage)
            ->view('email.remindPaymentDebitSubmit', [
                'candidateName'  => $candidateName,
                'position'       => $position,
                'employerName'   => $employerName,
                'point'          => $point,
                'employerWallet' => $employerWallet,
            ])
            ->subject('[Recland] Thông báo thanh toán chi phí tuyển dụng ứng viên '.$candidateName);
    }

}
