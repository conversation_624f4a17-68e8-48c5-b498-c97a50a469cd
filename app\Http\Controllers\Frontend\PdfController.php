<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\Frontend\CompanyResource;
use App\Models\LevelByJobTop;
use App\Models\LevelBySkillMain;
use App\Services\Frontend\JobService;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Dompdf\Dompdf;
use Dompdf\Options;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PdfController extends Controller
{
    protected $jobService;

    public function __construct(JobService $jobService,)
    {
        $this->jobService = $jobService;
    }
    public function decodeHtmlToUtf8($string){

        // Bước 1: Chuyển đổi các HTML entities phổ biến trong tiếng Việt
        $vietnamese_chars = array(
            '&aacute;' => 'á',
            '&agrave;' => 'à',
            '&atilde;' => 'ã',
            '&acirc;' => 'â',
            '&aring;' => 'å',
            '&eacute;' => 'é',
            '&egrave;' => 'è',
            '&ecirc;' => 'ê',
            '&euml;' => 'ë',
            '&iacute;' => 'í',
            '&igrave;' => 'ì',
            '&icirc;' => 'î',
            '&iuml;' => 'ï',
            '&oacute;' => 'ó',
            '&ograve;' => 'ò',
            '&otilde;' => 'õ',
            '&ocirc;' => 'ô',
            '&oslash;' => 'ø',
            '&uacute;' => 'ú',
            '&ugrave;' => 'ù',
            '&ucirc;' => 'û',
            '&uuml;' => 'ü',
            '&yacute;' => 'ý',
            '&yuml;' => 'ÿ',
            '&Aacute;' => 'Á',
            '&Agrave;' => 'À',
            '&Acirc;' => 'Â',
            '&Atilde;' => 'Ã',
            '&Eacute;' => 'É',
            '&Egrave;' => 'È',
            '&Ecirc;' => 'Ê',
            '&Euml;' => 'Ë',
            '&Iacute;' => 'Í',
            '&Igrave;' => 'Ì',
            '&Icirc;' => 'Î',
            '&Iuml;' => 'Ï',
            '&Oacute;' => 'Ó',
            '&Ograve;' => 'Ò',
            '&Ocirc;' => 'Ô',
            '&Otilde;' => 'Õ',
            '&Uacute;' => 'Ú',
            '&Ugrave;' => 'Ù',
            '&Ucirc;' => 'Û',
            '&Uuml;' => 'Ü',
            '&Yacute;' => 'Ý',
            '&nbsp;' => ' ',
            '&amp;' => '&',
            '&quot;' => '"',
            '&#039;' => "'",
            '&lt;' => '<',
            '&gt;' => '>'
        );

        // Thực hiện thay thế
        $decoded = strtr($string, $vietnamese_chars);

        // Bước 2: Decode các HTML entities còn lại nếu có
        $decoded = html_entity_decode($decoded, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $decoded;
    }
    public function downloadPDF($slug)
    {
        $job = $this->jobService->findBySlug($slug);
        $career = '';
        foreach (explode(',', $job->career) as $value) {
            $career .= config('job.career.' . app()->getLocale())[$value] . ', ';
        }
        $rank = '';


        foreach (explode(',', $job->rank) as $value) {
            if(in_array((int)$job->career, config('job.it_career'))){
                $level = LevelBySkillMain::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            }else {
                $level = LevelByJobTop::find($value);
                if ($level) {
                    $level = $level->toArray();
                    $rank .= $level['name_' . app()->getLocale()] . ', ';
                    // $rank .= config('job.rank_it.' . app()->getLocale())[$value] . ', ';
                }
            }
        }


        $cities = Common::getCities();
        $address = isset($job['address']) ? json_decode($job['address']) : json_decode($job->address);
        $remote = isset($job['remote']) ? json_decode($job['remote']) : json_decode($job->remote);
        $area = [];
        $areaString = '';
        if (count($address)) {
            foreach ($address as $a) {
                if (isset($a->area) && isset($cities[$a->area]) && !empty($a->address)) {
                    $area[] = $cities[$a->area];
                }
            }
        }
        if (!empty($remote)){
            $area = 'Remote';
            $areaString .= $area . ', ';
        }else{
            foreach (array_unique($area) as $a) {
                $areaString .= $a . ', ';
            }
        }
//        return view('frontend.pages.job.download-pdf', compact('job', 'career', 'rank', 'areaString'));
//        $pdf = PDF::loadView('frontend.pages.job.download-pdf', compact('job', 'career', 'rank', 'areaString'));
//        $pdf->getDomPDF()->set_option('isRemoteEnabled', true);
//        return $pdf->download($job->name . '.pdf');

        $options = new Options();
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('defaultFont', 'NotoSansJP');
        $options->set('isUnicode', true);
        $options->set('isFontSubsettingEnabled', true);
        

        $dompdf = new Dompdf($options);
        // Đăng ký font Noto Sans JP hỗ trợ tiếng Nhật
        $fontMetrics = $dompdf->getFontMetrics();
        $fontMetrics->registerFont(
            [
                'family' => 'NotoSansJP',
                'style' => 'normal',
                'weight' => 'normal'
            ],
            storage_path('fonts/NotoSansJP-Regular.ttf')
        );
        $fontMetrics->registerFont(
            [
                'family' => 'NotoSansJP',
                'style' => 'normal',
                'weight' => 'bold'
            ],
            storage_path('fonts/NotoSansJP-Bold.ttf')
        );

        // Tải view và chuyển đổi thành HTML
        $html = view('frontend.pages.job.download-pdf', compact('job', 'career', 'rank', 'areaString'))->render();
        $html = $this->decodeHtmlToUtf8($html);
        // dd($html);
        // dd($this->decodeHtmlToUtf8($html));
        // $html = mb_convert_encoding($html,  'UTF-8', 'HTML-ENTITIES');
        // dd($html);
        // Nạp HTML vào Dompdf
        $dompdf->setFontMetrics($fontMetrics);
        $dompdf->loadHtml($html);

        // Chuyển đổi HTML thành PDF
        $dompdf->render();

        // Xuất PDF ra trình duyệt
        return $dompdf->stream($job->name . '.pdf', ['Attachment' => false]); // true để tải xuống, false để xem trong trình duyệt
    }
}
