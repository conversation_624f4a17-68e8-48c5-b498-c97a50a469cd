<?php

namespace App\Repositories;

use App\Models\JobTop;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class JobTopRepository extends BaseRepository
{
    const MODEL = JobTop::class;

    public function getSkill($params)
    {
        $query = $this->query();

        if (isset($params['searchTerm'])) {
            $query->where('name_en', 'like', '%' . $params['searchTerm'] . '%')
                ->orWhere('name_vi', 'like', '%' . $params['searchTerm'] . '%');
        }

        return $query->get();
    }
}
