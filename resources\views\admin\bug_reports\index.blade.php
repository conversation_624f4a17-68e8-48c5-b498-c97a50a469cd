@extends('admin.layouts.app')

@section('content')
<div class="page-header">
    <div class="page-leftheader">
        <h4 class="page-title">Quản lý báo cáo lỗi</h4>
        <ol class="breadcrumb pl-0">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Báo cáo lỗi</li>
        </ol>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-bug"></i> Danh sách báo cáo lỗi
                </h3>
                <div class="card-options">
                    <div class="btn-group">
                        <select id="statusFilter" class="form-control">
                            <option value="">Tất cả trạng thái</option>
                            <option value="pending">Chờ xử lý</option>
                            <option value="resolved">Đã xử lý</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="bugReportsTable" class="table table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Người báo cáo</th>
                                <th>Email</th>
                                <th>URL</th>
                                <th>Mô tả</th>
                                <th>Ảnh</th>
                                <th>Trạng thái</th>
                                <th>Thời gian</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#bugReportsTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("bug-reports.datatable") }}',
            data: function(d) {
                d.status = $('#statusFilter').val();
            }
        },
        columns: [
            { data: 'id', name: 'id', width: '50px' },
            { data: 'user_name', name: 'user_name' },
            { data: 'user_email', name: 'user_email' },
            { 
                data: 'url', 
                name: 'url',
                render: function(data) {
                    return '<a href="' + data + '" target="_blank" class="text-truncate" style="max-width: 200px; display: inline-block;">' + data + '</a>';
                }
            },
            { 
                data: 'short_description', 
                name: 'description',
                render: function(data) {
                    return '<span class="text-truncate" style="max-width: 250px; display: inline-block;">' + data + '</span>';
                }
            },
            { data: 'image_preview', name: 'image_preview', orderable: false, searchable: false },
            { data: 'status_badge', name: 'status', orderable: false },
            { 
                data: 'created_at', 
                name: 'created_at',
                render: function(data) {
                    return moment(data).format('DD/MM/YYYY HH:mm');
                }
            },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[0, 'desc']],
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.10.24/i18n/Vietnamese.json'
        }
    });

    // Handle status filter change
    $('#statusFilter').on('change', function() {
        table.draw();
    });

    // Auto refresh every 30 seconds
    setInterval(function() {
        table.ajax.reload(null, false);
    }, 30000);
});
</script>
@endsection

@section('css_custom')
<style>
.table td {
    vertical-align: middle;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#statusFilter {
    width: 200px;
}

.card-options {
    margin-left: auto;
}
</style>
@endsection
