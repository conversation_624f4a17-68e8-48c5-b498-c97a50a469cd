<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmployerScheduleOnboardRecSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $rec;
    protected $submitCvOnboard;

    public function __construct($submitCv,$rec,$submitCvOnboard)
    {
        $this->submitCv = $submitCv;
        $this->rec = $rec;
        $this->submitCvOnboard = $submitCvOnboard;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $recName       = $this->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $timeInterview = $this->submitCvOnboard->time_book_format .' '. $this->submitCvOnboard->date_book_format;
        $address = $this->submitCvOnboard->address;
        $phone = $this->submitCv->employer->mobile;
        $email = $this->submitCv->employer->email;
        $link = route('rec-submitcv') . '?submit_id=' . $this->submitCv->id . '&open_comment=1';
        return (new MailMessage)
            ->view('email.employerScheduleOnboardRecSubmit', [
                'recName'       => $recName,
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'timeInterview' => $timeInterview,
                'address'       => $address,
                'link'          => $link,
                'phone'         => $phone,
                'email'         => $email,
                // 'salary'        => $salary,
            ])
            ->subject('[Recland] Lời mời nhận việc Ứng viên '.$candidateName.' vị trí  '.$position.' từ công ty '.$companyName);

    }

}

