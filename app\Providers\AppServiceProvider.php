<?php

namespace App\Providers;

use App\Models\Category;
use App\Models\JobLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (config('app.env') !== 'production') {
            DB::listen(function ($query) {
                if (stripos($query->sql, "information_schema") !== false) {
                    return;
                }

                //replace % in parameter
                $querySql = str_replace(['%'], ['$$$'], $query->sql);
                $querySql = str_replace(['?'], ['\'%s\''], $querySql);

                $queryRawSql = vsprintf($querySql, $query->bindings);
                $queryRawSql = str_replace(['$$$'], ['%'], $queryRawSql);

                Log::channel('sql')->debug('[SQL EXEC]', [
                    "SQL:" => "\n" . $queryRawSql . "\n-- ",
                    "Time:" => $query->time,
                ]);
            });
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        view()->composer(
            'frontend.inc_layouts.v2.home_header',
            function ($view) {
                $view->with('categories', Category::all());
            }
        );
        Queue::before(function (JobProcessing $event) {
            // $event->connectionName
            // $event->job
            // $event->job->payload()
            // Lưu vào database (tùy chọn)
            $job = $event->job;
            $data = $job->payload();
            $name = $data['displayName'] ?? 'Unknown';
            # if name contant string CreateEmailLog
            if (strpos($name, 'CreateEmailLog') !== false) {
                return;
            }
            
            JobLog::create([
                'job_id' => $job->getJobId(),
                'name' => $data['displayName'] ?? 'Unknown',
                'queue' => $job->getQueue(),
                'payload' => json_encode($data),
            ]);
        });
    }
}
