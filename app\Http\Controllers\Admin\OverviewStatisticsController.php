<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\OverviewStatisticsService;
use Illuminate\Http\Request;

class OverviewStatisticsController extends Controller
{
    protected $overviewStatisticsService;

    public function __construct(OverviewStatisticsService $overviewStatisticsService)
    {
        $this->overviewStatisticsService = $overviewStatisticsService;
    }

    /**
     * Hi<PERSON>n thị trang thống kê tổng quan
     */
    public function index(Request $request)
    {
        $filters = [
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'is_real' => $request->input('is_real'), // null = all, 1 = real, 0 = fake
            'cv_source' => $request->input('cv_source'), // null = all, 'internal' = nội bộ, 'collaborator' = cộng tác viên
        ];

        $statistics = $this->overviewStatisticsService->getOverviewStatistics($filters);

        return view('admin.pages.overview-statistics.index', compact('statistics', 'filters'));
    }

    /**
     * <PERSON><PERSON><PERSON> dữ liệu thống kê qua AJAX
     */
    public function filterData(Request $request)
    {
        $filters = [
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'is_real' => $request->input('is_real'),
            'cv_source' => $request->input('cv_source'),
        ];

        $statistics = $this->overviewStatisticsService->getOverviewStatistics($filters);

        $view = view('admin.pages.overview-statistics.filter-statistics', compact('statistics'))->render();

        return response()->json([
            'status' => 'success',
            'message' => 'Lọc dữ liệu thành công',
            'html' => $view,
        ]);
    }
}
