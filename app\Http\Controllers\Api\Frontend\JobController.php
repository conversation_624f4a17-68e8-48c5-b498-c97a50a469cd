<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\DetailJobResource;
use App\Http\Resources\Frontend\Api\JobCollection;
use App\Http\Resources\Frontend\Api\JobResource;
use App\Models\Job;
use Illuminate\Http\Request;
use App\Services\Frontend\JobService;

class JobController extends Controller
{
    protected $jobService;

    public function __construct(
        JobService $jobService,
    ) {
        $this->jobService = $jobService;
    }

    public function list(Request $request)
    {
        $limitJob = config('settings.global.limit_job_page_ctv');
        $skill = $request->skill;
        //get job ctv
        $arrJob = $this->jobService->getJobCtvCollection($limitJob, $skill);
        $top_skill_data = $this->jobService->getTopSkill();

        $top_skill = [];
        if (count($top_skill_data) > 0) {
            foreach ($top_skill_data as $item) {
                $item = explode(',', $item);
                foreach ($item as $value) {
                    $top_skill[] = ucfirst($value);
                }
            }
        }


        return response()->json(['data' => JobResource::collection($arrJob), 'top_skill' => $top_skill]);
    }

    public function listAllJob(Request $request)
    {
        $user = auth('client')->user();
        if ($user) {
            $level = $user->level;
            if ($level) {
                $request->merge(['level' => $level]);
            }
        }
        $list = $this->jobService->getJobSearch($request->all());
        return new JobCollection($list);
    }

    public function detailJob($slug)
    {
        return new DetailJobResource($this->jobService->findBySlug($slug));
    }
}
