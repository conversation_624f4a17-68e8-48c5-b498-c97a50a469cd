<?php

namespace App\Rules\Frontend;

use App\Services\Frontend\WareHouseSubmitCvService;
use Hash;
use Illuminate\Contracts\Validation\Rule;

class CheckDuplicateSubmitByInfo implements Rule
{

    protected $email;

    protected $phone;

    protected $jobId;

    public function __construct($email,$phone,$jobId)
    {
        $this->email = $email;
        $this->phone = $phone;
        $this->jobId = $jobId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $wareHouseSubmitCvService = app(WareHouseSubmitCvService::class);
//        $check = $wareHouseSubmitCvService->checkDuplicateCvForJob($this->email,$this->phone,$this->jobId);
        $check = $wareHouseSubmitCvService->checkDuplicateCvForCompany($this->email,$this->phone,$this->jobId);
        return !$check;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('frontend/job/message.error_duplicate_job');
    }
}
