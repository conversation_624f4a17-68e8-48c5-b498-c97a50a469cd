<?php

namespace App\Repositories;

use App\Models\SubmitCv;
use App\Models\SubmitCvDiscuss;
use App\Models\WareHouseCvSellingBuyDiscuss;

class SubmitCvDiscussRepository extends BaseRepository
{
    const MODEL = SubmitCvDiscuss::class;

    public function getBySubmitId($submitCvId){
    $query = $this->query()
            ->where('submit_cvs_id', $submitCvId)
            ->with(['employer' => function($query){
                $query->select('id');
            },'rec' => function($query){
                $query->select('id');
            }]);

        return $query->get();
    }

    public function setReadByType($submitId,$type){
        $user = auth('client')->user();
        if ($type == config('constant.role.employer')) {
            $query = $this->query()
                ->where('submit_cvs_id', $submitId)
                ->whereNull('ntd_id')
                ->update(['unread' => 0]);
        }elseif ($type == config('constant.role.rec')) {
            $query = $this->query()
                ->where('submit_cvs_id', $submitId)
                ->whereNull('ctv_id')
                ->update(['unread' => 0]);
        }
        $submitCv = SubmitCv::find($submitId);
        $submitCv->discuss()->where('sender_type', $type == config('constant.role.employer') ? 'rec' : 'employer')->update(['is_read' => 1]);
        $submitCv->setMeta($type . '_unread_discuss', 0);
        return $query;
    }
}
