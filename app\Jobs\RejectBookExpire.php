<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuyBook;
use App\Notifications\ChangeStatusCancelInterviewToAdmin;
use App\Notifications\ChangeStatusCancelInterviewToEmployer;
use App\Notifications\ChangeStatusCancelInterviewToRec;
use App\Notifications\EmailRejectInterView;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingHistoryBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class RejectBookExpire implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public $wareHouseCvSellingBuyBook;
    public $user;

    public function __construct(WareHouseCvSellingBuyBook $wareHouseCvSellingBuyBook, $user)
    {
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
        $this->user = $user;
    }


    public function handle()
    {
        //Trang thai đặt lịch, 0: vừa đặt, 1: đã được xác nhận, 2 :bị từ chối
        if ($this->wareHouseCvSellingBuyBook->status == 0) {
            $wareHouseCvSellingBuyBookRepository = app(WareHouseCvSellingBuyBookRepository::class);
            $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
            $wareHouseCvSellingHistoryBuyRepository = app(WareHouseCvSellingHistoryBuyRepository::class);
            $wareHouseCvSellingBuy = $this->wareHouseCvSellingBuyBook->wareHouseCvSellingBuy;
            //4 => 'Waiting confirm calendar',
            if ($wareHouseCvSellingBuy->status_recruitment == 4) {
                $this->wareHouseCvSellingBuyBook->update([
                    'status' => 2 //tu choi
                ]);
                $employer = $this->wareHouseCvSellingBuyBook->employer;
                $employer->notify(new EmailRejectInterView($employer, $this->wareHouseCvSellingBuyBook, $wareHouseCvSellingBuy));
                $statusRecruitment = 5; //5 => 'Reject Interview schedule', chưa đủ 3 lần thì = 5, nếu lần 3 thì bằng 9
                $countBookReject = $wareHouseCvSellingBuyBookRepository->countBookReject($this->wareHouseCvSellingBuyBook->ntd_id, $wareHouseCvSellingBuy->id);
                $point = 0;
                if ($countBookReject >= 3) {
                    $statusRecruitment = 9; //9 => 'Candidate Cancel Interview',
                    // Từ chối quá 3 lần
                    //Hoan tien tra NTD
                    //CV interview
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                        //ghi log hoan tien
                        $wareHouseCvSellingHistoryBuyRepository->create([
                            'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                            'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                            'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                            'percent'                       =>  100, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                            'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                            'point'                         =>  $wareHouseCvSellingBuy->point,
                            'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point,
                            'status'                        =>  0
                        ]);
                        //update
                        $wareHouseCvSellingBuy->status_recruitment = $statusRecruitment;
                        $wareHouseCvSellingBuy->status_payment = 3; //Hoan tien
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        $point = $wareHouseCvSellingBuy->point;
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $wareHouseCvSellingBuy->point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($wareHouseCvSellingBuy->point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'reject_book_expire');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }
                    //cv onboard
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {
                        $wareHouseCvSellingHistoryBuyData = $wareHouseCvSellingHistoryBuyRepository->finBySellingTypeStatus($wareHouseCvSellingBuy->id, 0, 0); //type = 0, status = 0, neu status = 1 thi la Hoan tien roi
                        //hoàn bao nhiêu point
                        $point = 0;
                        if ($wareHouseCvSellingHistoryBuyData) {
                            foreach ($wareHouseCvSellingHistoryBuyData as $key => $value) {
                                //update status = 1 để lần hoàn tiền sau ko bị hoàn lại nhiều lần
                                $value->status = 1;
                                $value->save();
                                //ghi log hoan tien
                                $wareHouseCvSellingHistoryBuyRepository->create([
                                    'user_id'                       =>  $wareHouseCvSellingBuy->employer->id,
                                    'warehouse_cv_selling_buy_id'   =>  $wareHouseCvSellingBuy->id,
                                    'type'                          =>  1, //0 trừ tiền, 1 hoàn tiền
                                    'percent'                       =>  $value->percent, //10,90% type_of_sale onboard, 100%: type_of_sale interview, cv
                                    'type_of_sale'                  =>  $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale,
                                    'point'                         =>  $value->point,
                                    'balance'                       =>  $wareHouseCvSellingBuy->employer->wallet->amount + $value->point,
                                    'status'                        =>  0
                                ]);
                                $point += $value->point;
                            }
                        }
                        $wareHouseCvSellingBuy->status_recruitment = $statusRecruitment;
                        $wareHouseCvSellingBuy->status_payment = 2; //2 => 'Hoàn cọc'
                        $wareHouseCvSellingBuy->save();
                        //Cộng point của NTD
                        // $wareHouseCvSellingBuy->employer->wallet->amount = $wareHouseCvSellingBuy->employer->wallet->amount + $point;
                        $wareHouseCvSellingBuy->employer->wallet->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $wareHouseCvSellingBuy->id]);
                        $wareHouseCvSellingBuy->employer->wallet->addAmount($point, $wareHouseCvSellingBuy, 'Cộng tiền vào ví', 'reject_book_expire');
                        // $wareHouseCvSellingBuy->employer->wallet->save();
                    }

                    $wareHouseCvSellingBuy->employer->notify(new ChangeStatusCancelInterviewToEmployer($wareHouseCvSellingBuy, $point));
                    if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority == 2) {
                        Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusCancelInterviewToAdmin($wareHouseCvSellingBuy, $point));
                    } else {
                        $wareHouseCvSellingBuy->rec->notify(new ChangeStatusCancelInterviewToRec($wareHouseCvSellingBuy, $point));
                    }
                } else {
                    OutOfDateBookInterview::dispatch($wareHouseCvSellingBuy, auth()->user())->delay(now()->addMinutes(7 * 24 * 60));
                    $wareHouseCvSellingBuy->update(['status_recruitment' => $statusRecruitment]);
                }

                $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy, $this->user);
            }
        }
    }
}
