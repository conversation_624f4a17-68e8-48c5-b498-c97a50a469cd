<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\WareHouseSubmitCvRequest;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\FileServiceS3;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\JobService;
use App\Services\Frontend\SettingService;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Services\HideService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WareHouseSubmitCvController extends Controller
{

    protected $wareHouseSubmitCvService;
    protected $hideCvSerivce;
    protected $jobService;
    protected $seoService;
    protected $routeName;
    protected $settingService;

    public function __construct(
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        HideService              $hideCvSerivce,
        JobService $jobService,
        SeoService $seoService,
        SettingService $settingService,
    ) {
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->hideCvSerivce = $hideCvSerivce;
        $this->jobService = $jobService;
        $this->seoService = $seoService;
        $this->settingService = $settingService;
        $this->routeName = \Route::currentRouteName();
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);
    }

    /**
     * @param WareHouseSubmitCvRequest $request
     * @return RedirectResponse
     */
    public function index(WareHouseSubmitCvRequest $request): RedirectResponse
    {
        $lang = app()->getLocale();
        $this->seoService->getConfig($this->routeName, $lang);

        try {
            DB::beginTransaction();

            $this->wareHouseSubmitCvService->saveData($request->all());

            DB::commit();
            /*if (strtolower($request->status) != strtolower(config('constant.submit_cvs_status.7'))) {
                Toast::success(__('frontend/login/message.save_cv_success'));
            } else {
                Toast::success(__('frontend/login/message.save_cv_draft_success'));
            }
            ->with('submit_cv_status','success')->with('candidate_name', $request->candidate_name)*/

            return back()->with(['submit_cv_status' => 'success', 'candidate_name' => $request->candidate_name]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log: ', [
                'content: ' => $e->getMessage()
            ]);
            Toast::error(__('frontend/login/message.save_cv_error'));
            return back();
        }
    }

    public function candidateIntroduction(WareHouseSubmitCvRequest $request)
    {
        try {
            DB::beginTransaction();
            $this->wareHouseSubmitCvService->candidateIntroduction($request->all());

            DB::commit();
            return back()->with(['submit_cv_status' => 'success', 'candidate_name' => $request->candidate_name]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log: ', [
                'content: ' => $e->getMessage()
            ]);
            Toast::error(__('frontend/login/message.save_cv_error'));
            return back();
        }
    }

    //lấy danh sach CV theo CTV
    public function getWareHouseCvByCtv(Request $request)
    {
        try {
            $user = auth('client')->user();
            $keyWord = isset($request->searchTerm) ? $request->searchTerm : '';
            $data = $this->wareHouseSubmitCvService->getWareHouseCvByCtv($user->id, $keyWord);
            $response = [];
            if (count($data)) {
                foreach ($data as $key => $value) {
                    $response[$key]['id'] = $value->id;
                    $response[$key]['text'] = $value->candidate_name . ' - ' . $value->candidate_job_title . ' - ' . $value->candidate_email;
                    $response[$key]['candidate_name'] = $value->candidate_name;
                    $response[$key]['candidate_job_title'] = $value->candidate_job_title;
                    $response[$key]['candidate_email'] = $value->candidate_email;
                    $response[$key]['candidate_mobile'] = $value->candidate_mobile;
                }
            }

            return json_encode($response);
        } catch (\Exception $e) {

            Log::info('error log: ', [
                'content: ' => $e->getMessage()
            ]);
        }
    }

    public function getWareHouseCvSelling(Request $request)
    {
        try {
            $user = auth('client')->user();

            $keyWord = isset($request->searchTerm) ? $request->searchTerm : '';
            $data = $this->wareHouseSubmitCvService->getWareHouseCvNotSelling($user->id, $keyWord);
            $response = [];
            if (count($data)) {
                foreach ($data as $key => $value) {
                    $response[$key]['id'] = $value->id;
                    $response[$key]['text'] = $value->candidate_name . ' - ' . $value->candidate_job_title . ' - ' . $value->candidate_email;
                    $response[$key]['candidate_name'] = $value->candidate_name;
                    $response[$key]['candidate_job_title'] = $value->candidate_job_title;
                    $response[$key]['candidate_email'] = $value->candidate_email;
                    $response[$key]['candidate_mobile'] = $value->candidate_mobile;
                }
            }

            return json_encode($response);
        } catch (\Exception $e) {

            Log::info('error log: ', [
                'content: ' => $e->getMessage()
            ]);
        }
    }

    public function getWareHouseCvById(Request $request)
    {
        $user = auth('client')->user();
        $data = $this->wareHouseSubmitCvService->getWareHouseCvById($user->id, $request->id);

        return response()->json($data);
    }

    public function uploadPublicCv(Request $request)
    {
        $file_public = FileServiceS3::getInstance()->uploadToS3($request->file, config('constant.sub_path_s3.cv'));
        $params['file'] = gen_url_file_s3($file_public);
        $response = $this->hideCvSerivce->sendRequest($params);
        $statusCode = isset($response['statusCode']) ? $response['statusCode'] : '';
        $data = [
            'code' => config('constant.code.reverse_code_status.CODE_FAIL'),
            'status' => 'error',
            'message' => 'Error',
        ];
        if (!empty($statusCode) && $statusCode == 200) {
            $file_private = isset($response['data']['file_private']) ? $response['data']['file_private'] : '';
            $email = isset($response['data']['info']['emails'][0]) ? $response['data']['info']['emails'][0] : '';
            $phones = isset($response['data']['info']['phones'][0]) ? $response['data']['info']['phones'][0] : '';
            $name = isset($response['data']['info']['name'][0]) ? $response['data']['info']['name'] : '';
            $skills = isset($response['data']['info']['skills']) ? $response['data']['info']['skills'] : [];
            $privateCv = '';
            try {
                if (!empty($file_private)) {
                    $privateCv = FileServiceS3::getInstance()->uploadToS3FromLink($file_private, config('constant.sub_path_s3.cv'));
                }
                $data = [
                    'code' => config('constant.code.reverse_code_status.CODE_SUCCESS'),
                    'status' => 'success',
                    'cv_public' => $file_public,
                    'cv_private_link' => gen_url_file_s3($privateCv),
                    'cv_private' => $privateCv,
                    'email' => $email,
                    'phone' => $phones,
                    'skills' => $skills,
                    'name' => $name,
                ];
            } catch (\Exception $exception) {
            }
        } elseif (!empty($statusCode) && $statusCode == 400) {
            $data = [
                'code' => config('constant.code.reverse_code_status.CODE_SUCCESS'),
                'status' => 'success',
                'cv_public' => $file_public,
                'cv_private_link' => gen_url_file_s3($file_public),
                'cv_private' => $file_public,
                'email' => '',
                'phone' => '',
                'name' => '',
                'skills' => [],
            ];
        }
        return response()->json($data);
    }

    public function reUploadPublicCv(Request $request)
    {
        $cv_id = $request->cv_id;
        $publicCv = FileServiceS3::getInstance()->uploadToS3($request->public_cv, config('constant.sub_path_s3.cv'));
        $this->wareHouseSubmitCvService->updatePublicCv($cv_id, $publicCv);
        $data = [
            'code' => config('constant.code.reverse_code_status.CODE_SUCCESS'),
            'status' => 'success',
            'cv_public' => $publicCv,
            'cv_public_link' => gen_url_file_s3($publicCv),
        ];
        return response()->json($data);
    }

    public function uploadPrivateCv(Request $request)
    {
        $privateCv = FileServiceS3::getInstance()->uploadToS3($request->private_cv, config('constant.sub_path_s3.cv'));
        if ($request->cv_id) {
            $this->wareHouseSubmitCvService->updatePrivateCv($request->cv_id, $privateCv);
        }
        $data = [
            'code' => config('constant.code.reverse_code_status.CODE_SUCCESS'),
            'status' => 'success',
            'cv_private' => $privateCv,
            'cv_private_link' => gen_url_file_s3($privateCv),
        ];
        return response()->json($data);
    }

    public function uploadPrivateCvForSubmitCv(Request $request)
    {
        $privateCv = FileServiceS3::getInstance()->uploadToS3($request->private_cv, config('constant.sub_path_s3.cv'));
        if ($request->submit_id) {
            $this->wareHouseSubmitCvService->updatePrivateCvForSubmitCv($request->submit_id, $privateCv);
        }
        $data = [
            'code' => config('constant.code.reverse_code_status.CODE_SUCCESS'),
            'status' => 'success',
            'cv_private' => $privateCv,
            'cv_private_link' => gen_url_file_s3($privateCv),
        ];
        return response()->json($data);
    }

    public function market(Request $request)
    {

        $this->generateWarehouseCvs();

        $lang = app()->getLocale();
        $str = 'market_cv';
        $arrLang = $this->settingService->getAllByKey($str, $lang);

        $data = $this->wareHouseSubmitCvService->getMarket($request->all());

        $maxSalary = $this->wareHouseSubmitCvService->getMaxSalary();

        $view_id = $request->get('view_id');
        $view_token = $request->get('view_token');
        $from = $request->get('from');
        $user = auth('client')->user();

        return view('frontend.pages.market.index', compact('data', 'arrLang', 'maxSalary', 'view_id', 'view_token', 'from', 'user'));
    }

    private function generateWarehouseCvs()
    {
        $lang = app()->getLocale();
        $submit_cvs_status_value = config('constant.submit_cvs_status_value');
        $cities = Common::getCities();
        $yearOfExperience = config('constant.sonamkinhnghiem');
        $candidateStatus = config('constant.thoigiandilamdukien');
        $career = config('job.career.' . $lang);
        unset($career[0]);

        $rank = config('job.rank.' . $lang);
        $skills = $this->jobService->getSkill();

        $sortPrice = config('constant.sort_price.' . $lang);
        $sortBy = config('constant.sort_by_market.' . $lang);
        $typeOfSale = config('constant.bonus_type');

        view()->share([
            'submit_cvs_status_value' => $submit_cvs_status_value,
            'cities' => $cities,
            'career' => $career,
            'yearOfExperience' => $yearOfExperience,
            'candidateStatus' => $candidateStatus,
            'rank'  => $rank,
            'skills' => $skills,
            'sortPrice' => $sortPrice,
            'sortBy' => $sortBy,
            'typeOfSale' => $typeOfSale
            //TODO
        ]);
    }
}
