<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailRejectOnboardSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $submitCvBook;
    protected $submitCv;

    public function __construct($employer,$submitCvBook,$submitCv)
    {
        $this->employer = $employer;
        $this->submitCvBook = $submitCvBook;
        $this->submitCv = $submitCv;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position      = '';
        $employerName  = $this->employer->name;
        $timeInterview = $this->submitCvBook->date_hour;
        if (!empty($this->submitCv->warehouseCv)){
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            $position = $this->submitCv->job->name;
        }
        $link = route('employer-submitcv',['discuss' => $this->submitCv->id]);
        $linkMarket = route('market-cv');

        return (new MailMessage)
            ->view('email.emailRejectOnboardSubmit', [
                'employerName'  => $employerName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'timeInterview' => $timeInterview,
                'link'          => $link,
                'linkMarket'    => $linkMarket,
            ])
            ->subject('[RECLAND] Thông báo từ chối offer của ứng viên '.$candidateName.' vị trí '.$position);
    }


}
