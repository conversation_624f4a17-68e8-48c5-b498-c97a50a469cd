<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerScheduleOnboardAdminSubmit extends Mailable
{
    use Queueable;

    protected $submitCv;
    protected $rec;
    protected $submitCvOnboard;

    public function __construct($submitCv,$rec,$submitCvOnboard)
    {
        $this->submitCv = $submitCv;
        $this->rec = $rec;
        $this->submitCvOnboard = $submitCvOnboard;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $recName       = $this->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $timeInterview = $this->submitCvOnboard->time_book_format .' '. $this->submitCvOnboard->date_book_format;
        $address = $this->submitCvOnboard->address;
        $phone = $this->submitCv->employer->mobile;
        $email = $this->submitCv->employer->email;
        // $linkAdminDiscuss = route('luot-ban.show', $this->submitCv->id) . '?tab=thaoluan';
        $linkAdminDiscuss = route('submit-cv.edit', ['id' => $this->submitCv->id]) . '?tab=thaoluan';
        // $salary = $this->submitCv->candidate_salary_expect;

        return new Content(
            view: 'email.employerScheduleOnboardAdminSubmit',
            with: [
                'recName'          => $recName,
                'candidateName'    => $candidateName,
                'companyName'      => $companyName,
                'position'         => $position,
                'type'             => $type,
                'timeInterview'    => $timeInterview,
                'address'          => $address,
                'linkAdminDiscuss' => $linkAdminDiscuss,
                'phone'            => $phone,
                'email'            => $email,
                // 'salary'           => $salary,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $companyName = $this->submitCv->employer->name;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $message->subject('[Recland] Lời mời nhận việc Ứng viên '.$candidateName.' vị trí  '.$position.' từ công ty '.$companyName);
        return $this;
    }

}
