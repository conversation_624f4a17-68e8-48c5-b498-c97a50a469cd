<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use Illuminate\Foundation\Http\FormRequest;

class ResetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password'         => 'required|min:8|max:20',
            'confirm_password' => 'required|same:password',
        ];
    }

    public function messages()
    {
        return [
            'required'              => __('message.required'),
            'confirm_password.same' => __('message.same'),
            'min'                   => __('message.min'),
            'max'                   => __('message.max'),
        ];
    }
}
