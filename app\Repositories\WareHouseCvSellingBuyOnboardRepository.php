<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingBuyBook;
use App\Models\WarehouseCvSellingBuyOnboard;
use Carbon\Carbon;

class WareHouseCvSellingBuyOnboardRepository extends BaseRepository
{

    const MODEL = WarehouseCvSellingBuyOnboard::class;

    public function getByWarehouseCvBuyId($warehouseCvBuyId){
        $query = $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId)
            ->with(['employer']);
        return $query->get();
    }

    public function getCurrentByWarehouseCvBuyId($warehouseCvBuyId){
        $query = $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId)
            ->where('status',0);
        return $query->first();
    }

    public function getFirstByWarehouseCvBuyId($warehouseCvBuyId){
        $query = $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId);
        return $query->first();
    }


    public function getBookBySellingBuyId($sellingBuyId){
        return $this->query()->where('warehouse_cv_selling_buy_id',$sellingBuyId)->orderBy('id', 'desc')->first();
    }


}
