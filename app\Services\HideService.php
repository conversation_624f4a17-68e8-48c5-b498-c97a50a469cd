<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class HideService
{

    public function __construct()
    {
    }

    public function sendRequest($params, $uri = "", $method = "POST", $headers = [], $chanel_log='') {
        try {
            $client = new Client([
                'headers' => [ 'Content-Type' => 'application/json' ]
            ]);

            $response = $client->post(empty($uri) ? Config('constant.API_AI_HIDE_CV') : $uri,
                ['body' => json_encode(
                    [
                        'file' => $params['file']
                    ]
                )]
            );

            return self::json2Array($response->getBody()->getContents());

        }
        catch (\GuzzleHttp\Exception\ClientException $exception) {
            return self::json2Array($exception->getResponse()->getBody()->getContents());
        }
    }

    /**
     * Json to array
     */
    public static function json2Array($contents) {
        $configData = json_decode($contents, true);
        return $configData;
    }

    public function hideCv($public_cv) {
        $params['file'] = $public_cv;
        $response = $this->sendRequest($params);
        $statusCode = isset($response['statusCode']) ? $response['statusCode'] : '';
        if ($statusCode == 200) {
            return $response['data']['file_private'];
        }
        return '';
    }
}
