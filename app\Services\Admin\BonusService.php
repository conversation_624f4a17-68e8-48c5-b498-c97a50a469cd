<?php

namespace App\Services\Admin;

use App\Repositories\BonusRepository;
use App\Repositories\UserRepository;

class BonusService
{

    protected $bonusRepository;
    protected $userRepository;

    public function __construct(BonusRepository $bonusRepository, UserRepository $userRepository)
    {
        $this->bonusRepository = $bonusRepository;
        $this->userRepository = $userRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        try {
            $data = $this->bonusRepository->getListBonus($params, $order, $paginate);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function updateStatus($id)
    {
        $data = [
            'payment_status' => 1
        ];

        return $this->bonusRepository->update($id, [], $data);
    }

    public function checkStatusBonus($userId)
    {
        return $this->bonusRepository->findByIdPaymentStatus($userId);
    }

    public function getTotalTurnoverCurrentMonthByUserId($userId){
        return $this->bonusRepository->getTotalTurnoverCurrentMonthByUserId($userId);
    }

    public function getTotalTurnoverCurrentYearByUserId($userId){
        return $this->bonusRepository->getTotalTurnoverCurrentYearByUserId($userId);
    }
}
