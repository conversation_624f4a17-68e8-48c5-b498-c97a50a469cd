<?php

namespace App\Services\Frontend;

use App\Repositories\WalletTransactionRepository;

class WalletTransactionService
{

    protected $walletTransactionRepository;

    public function __construct(WalletTransactionRepository $walletTransactionRepository)
    {
        $this->walletTransactionRepository = $walletTransactionRepository;
    }

    public function getHistory($params)
    {
        $userId = auth('client')->user()->id;
        $params['page'] = $params['page'] ?? 1;
        $params['size'] = $params['size'] ?? config('constant.paginate_10');
        return $this->walletTransactionRepository->findByUser($userId, $params);
    }

    public function spentThisMonth()
    {
        $userId = auth('client')->user()->id;
        return $this->walletTransactionRepository->spentThisMonth($userId);
    }

    public function spentThisYear()
    {
        $userId = auth('client')->user()->id;
        return $this->walletTransactionRepository->spentThisYear($userId);
    }
}