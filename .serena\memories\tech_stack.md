# RecLand - Tech Stack

## Backend
- **Framework:** Laravel 9.x (PHP 8.1+)
- **Database:** MySQL 8.0
- **Cache:** Redis
- **Queue:** Database driver với <PERSON>is (optional)
- **Storage:** Amazon S3
- **Search:** MySQL Full-text search
- **Admin Panel:** Backpack for Laravel

## Frontend
- **Framework:** Vue.js 3.x
- **Build Tool:** Laravel Mix với Webpack
- **UI Components:** Bootstrap Vue Next
- **CSS Framework:** Bootstrap 5 + Custom SCSS
- **JavaScript Libraries:** jQuery, Select2, Swiper, Moment.js

## Infrastructure
- **Web Server:** Nginx/Apache
- **Application Server:** PHP-FPM
- **Database:** MySQL 8.0
- **Cache:** Redis
- **File Storage:** Amazon S3
- **Email:** SMTP/Mailgun

## Development Tools
- **Package Manager:** Composer (PHP), NPM (JavaScript)
- **Testing:** PHPUnit
- **Code Style:** Laravel conventions
- **Version Control:** Git

## External Integrations
- **Payment:** ZaloPay, Bank Transfer
- **Email:** SMTP, Mailgun
- **File Storage:** Amazon S3
- **CV Parser:** n8n webhook
- **External APIs:** ITNavi API