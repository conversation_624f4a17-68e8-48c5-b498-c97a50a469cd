<?php

namespace App\Jobs;

use App\Notifications\RemindScheduleInterview;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendemailBook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $wareHouseCvSellingBuy;
    protected $user;
    protected $wareHouseCvSellingBuyBook;

    public function __construct($wareHouseCvSellingBuy,$user,$wareHouseCvSellingBuyBook)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->user = $user;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
    }


    public function handle()
    {
        //status "waiting interview" mới gửi mail nhé, tr<PERSON>h việc CTV từ chối lịch rồi, hoặc NTD update thủ công rồi vẫn nhận đc mail
        if ($this->wareHouseCvSellingBuy->status_recruitment == 7) {
            $this->user->notify(new RemindScheduleInterview($this->wareHouseCvSellingBuy, $this->user, $this->wareHouseCvSellingBuyBook));
        }
    }


}
