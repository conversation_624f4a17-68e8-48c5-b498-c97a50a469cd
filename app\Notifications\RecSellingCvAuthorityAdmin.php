<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class RecSellingCvAuthorityAdmin extends Mailable
{
    use Queueable;

    protected $cvSelling;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($cvSelling)
    {
        $this->cvSelling = $cvSelling;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function content(): Content
    {
        $candidateName = $this->cvSelling->wareHouseCv->candidate_name;
        $codeCTV = $this->cvSelling->user_id;
        $type = $this->cvSelling->type_of_sale;
        $price = $this->cvSelling->price;
        $link = route('cv-selling.index');
        return new Content(
            view: 'email.recSellingCvAuthorityAdmin',
            with: [
                'candidateName' => $candidateName,
                'codeCTV' => $codeCTV,
                'type' => $type,
                'price' => $price,
                'link' => $link,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $message->subject('[Ủy quyền] Xác nhận ủy quyền ứng viên mới');
        return $this;
    }

}
