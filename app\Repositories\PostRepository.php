<?php

namespace App\Repositories;

use App\Models\Category;
use App\Models\Post;
use Illuminate\Support\Carbon;
use function GuzzleHttp\Promise\all;

class PostRepository extends BaseRepository
{
    const MODEL = Post::class;

    public function getListPost($params, $orders = array(), $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('title_vi', 'like', '%' . $params['search'] . '%')
                ->orWhere('title_en', 'like', '%' . $params['search'] . '%');
        }
        if (isset($params['category_id'])) {
            $query = $query->where('category_id', $params['category_id']);
        }

        $query->with('category', 'postMeta');

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    /**
     * @return mixed
     */

    public function getPostByPositon($position, $type, $limit = 1){
        $now = Carbon::now()->format('Y-m-d');
        return $this->query()
            ->select('posts.*', 'post_meta.position')
            ->join('post_meta', 'posts.id', '=', 'post_meta.post_id')
            ->where('post_meta.position', $position)
            ->where('posts.status', config('constant.status_post.Published'))
            ->where('posts.date', '<=', $now)
            ->where('posts.type', $type)
            ->orderBy('posts.updated_at', 'desc')
            ->take($limit)
            ->with('category')
            ->get();
    }
    /**
     * @return mixed
     */
    public function getPostByPositonCategory($category_id, $position, $type, $limit = 10){

        $now = Carbon::now()->format('Y-m-d');

        return $this->query()
            ->select('posts.*', 'post_meta.position')
            ->join('post_meta', 'posts.id', '=', 'post_meta.post_id')
            ->when($position, function ($query, $position) {
                return $query->where('post_meta.position', $position);
            })
            // ->where('post_meta.position', $position)
            ->where('posts.status', config('constant.status_post.Published'))
            ->where('posts.category_id', $category_id)
            ->where('posts.date', '<=', $now)
            ->where('posts.type', $type)
            ->orderBy('posts.created_at', 'desc')
            ->take($limit)
            ->get();
    }

    public function findBySlug($arrslug)
    {
        $now = Carbon::now()->format('Y-m-d');
        return $this->query()
            ->where('slug_' . $arrslug['lang'], $arrslug['slug'])
            ->where('date', '<=', $now)
            ->where('status', config('constant.status_post.Published'))
            ->with('category', 'postMeta', 'postSeo')
            ->firstOrFail();
    }

    public function findNextPrevBySlug($params)
    {
        $now = Carbon::now()->format('Y-m-d');
        $query = $this->query()
            ->where('date', '<=', $now)
            ->where('status', config('constant.status_post.Published'));

        if (isset($params['search'])) {
            if ($params['search'] == 'max') {
                $query->where('id', '<', $params['id'])->orderBy('id','desc');
            } else if ($params['search'] == 'min') {
                $query->where('id', '>', $params['id'])->orderBy('id','asc');
            }
        }

        return $query->with('category', 'postMeta', 'postSeo')->first();
    }

    /**
     * @return mixed
     */
    public function getPostByView($limit = 10){
        $now = Carbon::now()->format('Y-m-d');

        return $this->query()
            ->select('posts.*', 'post_meta.view')
            ->join('post_meta', 'posts.id', '=', 'post_meta.post_id')
            ->where('posts.status', config('constant.status_post.Published'))
            ->where('posts.date', '<=', $now)
//            ->where('posts.type', $type)
            ->orderBy('post_meta.view', 'desc')
            ->take($limit)
            ->get();
    }

    public function getPostRandom($limit = 10){
        $now = Carbon::now()->format('Y-m-d');
        return $this->query()
            ->inRandomOrder()
            ->orderBy('created_at', 'desc')
            ->select('posts.*', 'post_meta.view')
            ->join('post_meta', 'posts.id', '=', 'post_meta.post_id')
            ->where('posts.status', config('constant.status_post.Published'))
            ->where('posts.date', '<=', $now)
//            ->where('posts.type', $type)
            ->orderBy('post_meta.view', 'desc')
            ->take($limit)
            ->get();
    }

    /**
     * @return mixed
     */
    public function getRandom($params,$limit = 0)
    {
        $query =  $this->query()
            ->select('*')
            ->inRandomOrder()
            ->where('posts.status', config('constant.status_post.Published'));

        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }

        if ($limit == 0){
            $limit = config('job.paginate');
        }
        if (isset($params['category_id'])){
            $query = $query->where('category_id',$params['category_id']);
        }
        if (isset($params['keyword'])){
            $query = $query->where('title_'.app()->getLocale(),'like', '%'. $params['keyword']. '%');
        }

        $query = $query->where('status',3)->orderBy('created_at', 'desc')->paginate($limit, $page);

        return $query;
    }

    public function findByChange($arr)
    {
        $now = Carbon::now()->format('Y-m-d');
        $query = $this->query()
            ->where('date', '<=', $now)
            ->where('status', config('constant.status_post.Published'));
        if($arr['type'] == 'next'){
            $query->where('posts.id', '>', $arr['id']);
            $query->orderBy('id', 'asc');
        }elseif ($arr['type'] == 'prev'){
            $query->where('posts.id', '<', $arr['id']);
            $query->orderBy('id', 'desc');
        }

        return $query->first()->toArray();
    }
    public function getPostNewFirst(){
        return $this->query()->orderBy('id', 'desc')->first();
    }

    public function getPostNew($take = 10){
        return  $this->query()
            ->select('*')
            ->inRandomOrder()
            ->where('posts.status', config('constant.status_post.Published'))->orderBy('created_at', 'desc')->take($take)->get();
    }

    public function searchPost($keyword)
    {
        return $this->query()
            ->where('slug_vi', 'LIKE', '%' . $keyword . '%')
            ->orWhere('slug_en', 'LIKE', '%' . $keyword . '%')
            ->orWhere('title_vi', 'LIKE', '%' . $keyword . '%')
            ->orWhere('title_en', 'LIKE', '%' . $keyword . '%')
            ->orWhere('description_vi', 'LIKE', '%' . $keyword . '%')
            ->orWhere('description_en', 'LIKE', '%' . $keyword . '%')
            ->get();
    }

}
