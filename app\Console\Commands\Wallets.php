<?php

namespace App\Console\Commands;

use App\Repositories\WalletRepository;
use App\Repositories\UserRepository;
use Carbon\Carbon;
use Illuminate\Console\Command;

class Wallets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallets:command';


    public function __construct(
    ){
        parent::__construct();
    }
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('Command start');
        $userRepo = resolve(UserRepository::class);
        $walletRepo = resolve(WalletRepository::class);
        $numRow = 500;
        $i = 0;
        do {
            $limit = $numRow;
            $offset = $i * $numRow;

            $listUser = $userRepo->findType($limit, $offset);
            if($listUser){
                foreach ($listUser as $value) {
                    $save = [];
                    $save['user_id']    = $value->id;
                    $save['type']       = $value->type;
                    $walletRepo->create($save);
                }
            }

            $i++;
        } while (count($listUser) == $numRow);
        \Log::info('Command end');
    }

}
