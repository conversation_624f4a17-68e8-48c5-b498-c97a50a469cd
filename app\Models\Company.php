<?php

namespace App\Models;

use App\Eloquent\Deactivate;
use App\Helpers\Common;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class Company extends BaseModel
{
    use HasFactory;
    use CrudTrait;


    // /**
    //  * The primary key associated with the table.
    //  *
    //  * @var string
    //  */
    // protected $primaryKey = 'id';

    // /**
    //  * Indicates if the model's ID is auto-incrementing.
    //  *
    //  * @var bool
    //  */
    // public $incrementing = true;

    // /**
    //  * The data type of the auto-incrementing ID.
    //  *
    //  * @var string
    //  */
    // protected $keyType = 'int';
    protected $fillable = [
        'name',
        'slug',
        'address',
        'logo',
        'banner',
        'scale',
        'website',
        'mst',
        'about',
        'is_active',
        'home',
        'priority',
        'career',
        'image',
        'video',
        'business_registration_number',
        'is_real',
        'created_at',
        'updated_at'
    ];

    protected $appends = [
        'path_logo',
        'scale_value',
        'home_value',
        'list_image',
        'path_banner',
        'path_business_registration_number',
    ];

    protected $casts = [
        'image' => 'array',
        'video' => 'array'
    ];

    public function jobs()
    {
        return $this->hasMany(Job::class, 'company_id', 'id');
    }
    public function submit_cvs()
    {
        return $this->hasMany(SubmitCv::class, 'company_id', 'id');
    }

    public function jobsActive()
    {
        return $this->hasMany(Job::class, 'company_id', 'id')->where("status", 1)->where('is_active', 1);
    }

    public function getAddressValueAttribute()
    {
        return json_decode($this->address);
    }

    public function getPathLogoAttribute()
    {
        return gen_url_file_s3($this->logo, '', false);
    }

    public function getPathBannerAttribute()
    {
        return gen_url_file_s3($this->banner, '', false);
    }

    public function getPathBusinessRegistrationNumberAttribute()
    {
        return gen_url_file_s3($this->business_registration_number, '', false);
    }

    public function getScaleValueAttribute()
    {
        return config('constant.scale.' . $this->scale);
    }

    public function getHomeValueAttribute()
    {
        return $this->home ? 'Có' : 'Không';
    }

    public function getCareerValueAttribute()
    {
        $lang = app()->getLocale();
        $career = config('job.career.' . $lang);
        $careerArr = explode(',', $this->career);
        $careers = [];
        foreach ($careerArr as $item) {
            if (isset($career[$item])) {
                $careers[] = $career[$item];
            }
        }
        return $careers;
    }

    public function getGroupAddressAttribute()
    {
        $addresses = json_decode($this->address, true);
        $group = [];
        $cities = Common::getCities();
        foreach ($addresses as $address) {
            if (!isset($address['area'])) {
                continue;
            }
            $group[$address['area']]['city'] = $cities[$address['area']];
            $group[$address['area']]['address'] = $address['address'];
        }
        return $group;
    }

    public function getAddressCityValueAttribute()
    {
        $addresses = json_decode($this->address, true);
        $group = [];
        $cities = Common::getCities();
        foreach ($addresses as $k => $address) {
            if (!isset($address['area'])) {
                continue;
            }

            $group[] = $cities[$address['area']];
        }
        return $group;
    }

    public function getListImageAttribute()
    {
        $image = json_decode($this->image, true);

        $arr = [];
        if (!empty($image) && count($image)) {
            foreach ($image as $k => $v) {
                if ($v) {
                    $path = gen_url_file_s3($v, '', false);
                    array_push($arr, $path);
                }
            }
        }
        return $arr;
    }

    public function getImageValueAttribute()
    {
        $image = json_decode($this->image, true);

        return $image;
    }

    public function getVideoValueAttribute()
    {
        $video = json_decode($this->video, true);

        return $video ? array_values($video) : [];
    }
}
