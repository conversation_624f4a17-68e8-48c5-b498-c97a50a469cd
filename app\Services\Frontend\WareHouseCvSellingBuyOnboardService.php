<?php

namespace App\Services\Frontend;

use App\Jobs\ChangeToRejectOffer;
use App\Jobs\ChangeToTrailWork;
use App\Jobs\RejectBookExpire;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\EmployerScheduleOnboardAdmin;
use App\Notifications\EmployerScheduleOnboardRec;
use App\Notifications\RecRefuseComplain;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyOnboardRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingBuyOnboardService
{

    protected $wareHouseCvSellingBuyOnboardRepository;
    protected $houseCvSellingBuyRepository;
    protected $wareHouseCvSellingBuyHistoryStatusRepository;
    public function __construct(WareHouseCvSellingBuyOnboardRepository $wareHouseCvSellingBuyOnboardRepository,
                                WareHouseCvSellingBuyRepository $houseCvSellingBuyRepository,
                                WareHouseCvSellingBuyHistoryStatusRepository $wareHouseCvSellingBuyHistoryStatusRepository
    )
    {
        $this->wareHouseCvSellingBuyOnboardRepository = $wareHouseCvSellingBuyOnboardRepository;
        $this->houseCvSellingBuyRepository = $houseCvSellingBuyRepository;
        $this->wareHouseCvSellingBuyHistoryStatusRepository = $wareHouseCvSellingBuyHistoryStatusRepository;
    }

    public function scheduleOnboard($data){
        $wareHouseCvSellingBuy = $this->houseCvSellingBuyRepository->find($data['warehouse_cv_selling_buy_id']);
        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard'){
            if (!in_array($wareHouseCvSellingBuy->status_recruitment,[11])){
                //3 => 'Waiting setup interview',
                throw new \Exception('Không đủ điều kiện đặt lịch onboard');
            }
            $user = auth('client')->user();
            $data = [
                'ntd_id' => $user->id,
                'address' => $data['address'],
                'name' => $data['name'],
                'warehouse_cv_selling_buy_id' => $data['warehouse_cv_selling_buy_id'],
                'date_book' => Carbon::createFromFormat('d/m/Y',$data['date']),
                'time_book' => $data['hour'].':'.$data['minute'],
            ];
            $book = $this->wareHouseCvSellingBuyOnboardRepository->create($data);
            $book->load('rec');
            $wareHouseCvSellingBuy->update([
                'status_recruitment' => 20
            ]);

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority){
                Mail::to(config('settings.global.email_admin'))->send(new EmployerScheduleOnboardAdmin($wareHouseCvSellingBuy,$wareHouseCvSellingBuy->rec,$book));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new EmployerScheduleOnboardRec($wareHouseCvSellingBuy,$wareHouseCvSellingBuy->rec,$book));
            }
            $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy,$user);
            $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $book->date_book);
            ChangeToTrailWork::dispatch($wareHouseCvSellingBuy->id,$user)->delay($carbonDate->addMinutes(7 * 24 * 60));
            ChangeToRejectOffer::dispatch($wareHouseCvSellingBuy->id,$book->id,auth('client')->user())->delay(now()->addMinutes(2 * 24 * 60));
        }

        //log
        return $book;
    }

    public function getByWarehouseCvBuyId($warehouseCvBuyId)
    {
        return $this->wareHouseCvSellingBuyOnboardRepository->getByWarehouseCvBuyId($warehouseCvBuyId);
    }





}
