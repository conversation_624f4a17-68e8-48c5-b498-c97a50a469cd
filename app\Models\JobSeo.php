<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobSeo extends BaseModel
{
    use HasFactory;

    protected $table = 'job_seo';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = [
        'image_url'
    ];

    public function getImageUrlAttribute()
    {
        return gen_url_file_s3($this->image);
    }
}
