<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\Api\AboutUsAlbumCollection;
use App\Http\Resources\Frontend\Api\AboutUsAlbumResource;
use App\Services\Frontend\AboutUsAlbumService;
use Illuminate\Http\Request;

class AboutUsAlbumController extends Controller
{
    protected $aboutUsAlbumService;

    public function __construct(AboutUsAlbumService $aboutUsAlbumService)
    {
        $this->aboutUsAlbumService = $aboutUsAlbumService;
    }

    public function list(){
        $list = $this->aboutUsAlbumService->list(\request()->all(),true,1);
        return new AboutUsAlbumCollection($list);
    }

}
