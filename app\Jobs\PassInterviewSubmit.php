<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Models\WareHouseCvSellingBuyBook;
use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\ChangeStatusPassInterviewAdmin;
use App\Notifications\ChangeStatusPassInterviewAdminSubmit;
use App\Notifications\ChangeStatusPassInterviewSubmit;
use App\Notifications\EmailRejectInterView;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class PassInterviewSubmit implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user;
    public $submitCvId;

    public function __construct($submitCvId,$user)
    {
        $this->user = $user;
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {
        $submitCvRepository = resolve(SubmitCvRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);
        //7 => 'Waiting Interview',
        //Trạng thái khiếu nại: NTD khiếu nại = 1, CTV từ chối = 2, CTV xác nhận = 3, Admin xác nhân = 4, Admin từ chối = 5
        if ($submitCv->status == 7 &&
            ($submitCv->status_complain == 0 || $submitCv->status_complain == 5)
        ){
            $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
            //8 => 'Pass Interview',
            $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
            $submitCv->update(['status' => 8]);

            $submitCvHistoryStatusRepository->logStatus($submitCv,$this->user);
            //sau 24h thi Thanh toan tien tra CTV -> interview
            if ($submitCv->bonus_type == 'interview') {
                PayInterviewSubmit::dispatch($submitCv->id)->delay(now()->addMinutes(24 * 60));
            }

            if ($submitCv->bonus_type == 'onboard') {

                $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
                //offering 11
                $submitCv->update([
                    'status' => 11
                ]);
                $submitCvHistoryStatusRepository->logStatus($submitCv,$this->user);
            }

            if ($submitCv->authorize > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdminSubmit($submitCv));
            }else{
                $submitCv->rec->notify(new ChangeStatusPassInterviewSubmit($submitCv));
            }

        }
    }




}
