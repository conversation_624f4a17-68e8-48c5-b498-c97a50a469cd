<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WareHouseCv;


class ReProcessCv extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:re-process-cv {--sync : Process CVs synchronously}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-process CV';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $warehouseCv = WareHouseCv::where('cv_public', 'like', 'pending_process::%')->get();
        $isSync = $this->option('sync');

        if ($warehouseCv) {
            foreach ($warehouseCv as $item) {
                $this->info('Processing CV: ' . $item->id . ' (' . ($isSync ? 'sync' : 'async') . ')');

                if ($isSync) {
                    \App\Jobs\ProcessWarehouseCvJob::dispatchSync($item->id);
                } else {
                    \App\Jobs\ProcessWarehouseCvJob::dispatch($item->id);
                }
            }
        }
        return Command::SUCCESS;
    }
}
