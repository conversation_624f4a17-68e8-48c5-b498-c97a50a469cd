<?php

namespace App\Http\Resources\Admin;

use App\Models\SubmitCvBook;
use Illuminate\Http\Resources\Json\JsonResource;

class DiscussResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'sender_name' => $this->sender->name ?? 'Không xác định',
            'created_at' => $this->created_at->format('d/m/Y H:i:s'),
            'message' => $this->full_message,
            'is_read' => (bool) $this->is_read,
        ];
    }
} 