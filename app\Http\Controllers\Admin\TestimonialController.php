<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TestimonialRequest;
use App\Services\Admin\TestimonialService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{

    protected $testimonialService;

    public function __construct(TestimonialService $testimonialService)
    {
        $this->testimonialService = $testimonialService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->testimonialService->buildDatatable();
        return view('admin.pages.testimonial.index',
            compact('datatable'));
    }

    public function store(TestimonialRequest $request)
    {
        return $this->testimonialService->createService($request->all());
    }

    public function show($id)
    {
        return $this->testimonialService->detailService($id);
    }

    public function update(TestimonialRequest $request, $id)
    {
        return $this->testimonialService->updateService($request->all(), $id);
    }

    public function datatable(Request $request)
    {
        $data = $this->testimonialService->datatable($request->all());
        return response($data);
    }
}
