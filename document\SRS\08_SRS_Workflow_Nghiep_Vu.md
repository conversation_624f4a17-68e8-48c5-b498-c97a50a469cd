# Tài liệu Đặc tả Workflow Nghiệp vụ - RecLand

**Phi<PERSON><PERSON> bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> <PERSON><PERSON>:** AI Assistant

---

## 1. Tổng quan Workflow System

### 1.1. Các Workflow Chính

RecLand có 5 workflow nghiệp vụ chính:

1. **MarketCV Workflow** - Quy trình mua bán CV từ kho
2. **Submit CV Workflow** - Quy trình ứng tuyển vào tin tuyển dụng
3. **Payment & Commission Workflow** - Quy trình thanh toán và hoa hồng
4. **Job Management Workflow** - Quy trình quản lý tin tuyển dụng
5. **User Onboarding Workflow** - Quy trình đăng ký và xác thực người dùng

### 1.2. Workflow Actors

| Actor | <PERSON><PERSON> trò | Quyền hạn |
|-------|---------|-----------|
| **Admin** | Quản trị viên | <PERSON><PERSON>, quản lý toàn bộ quy trình |
| **Employer (NTD)** | Nhà tuyển dụng | Đăng tin, mua CV, thanh toán |
| **Candidate (UV)** | Ứng viên | Tạo CV, ứng tuyển, xác nhận phỏng vấn |
| **Collaborator (CTV)** | Cộng tác viên | Bán CV, submit CV, nhận hoa hồng |
| **System** | Hệ thống tự động | Xử lý jobs, gửi thông báo, tính toán |

---

## 2. MarketCV Workflow

### 2.1. Workflow Overview

```mermaid
graph TD
    A[CTV đăng bán CV] --> B{Admin duyệt?}
    B -->|Approved| C[CV hiển thị trên marketplace]
    B -->|Rejected| D[Thông báo từ chối]
    C --> E[NTD tìm kiếm CV]
    E --> F[NTD xem preview CV]
    F --> G{NTD quyết định mua?}
    G -->|Yes| H[Thanh toán CV Data]
    G -->|No| I[Kết thúc]
    H --> J{Loại dịch vụ?}
    J -->|CV Data| K[Nhận thông tin liên hệ]
    J -->|Interview| L[Gửi offer phỏng vấn]
    J -->|Onboard| M[Gửi offer tuyển dụng]
    L --> N{UV xác nhận PV?}
    N -->|Yes| O[Thanh toán Interview]
    N -->|No| P[Hoàn tiền]
    M --> Q{UV xác nhận TD?}
    Q -->|Yes| R[Chuyển Trial Work]
    Q -->|No| S[Hoàn tiền]
    R --> T[Thanh toán Onboard theo giai đoạn]
```

### 2.2. Chi tiết các giai đoạn

#### 2.2.1. Giai đoạn 1: Đăng bán CV

**Actors:** CTV, Admin  
**Duration:** 1-3 ngày  
**Trigger:** CTV tạo CV selling

**Steps:**
1. CTV chọn CV từ warehouse
2. Thiết lập thông tin bán:
   - Loại dịch vụ (CV Data/Interview/Onboard)
   - Giá bán và điểm thưởng
   - Công ty loại trừ
   - Cấp độ CV
3. Submit để admin duyệt
4. Admin review và approve/reject
5. Nếu approved: CV hiển thị trên marketplace

**Business Rules:**
- CV phải có đầy đủ thông tin bắt buộc
- Giá bán phải trong khoảng cho phép
- Không được bán CV đã bán trước đó
- Authority CV cần admin duyệt

#### 2.2.2. Giai đoạn 2: Mua CV Data

**Actors:** NTD, System  
**Duration:** Tức thì  
**Trigger:** NTD click "Mua CV"

**Steps:**
1. Kiểm tra số dư ví NTD
2. Trừ tiền ví theo giá CV
3. Cập nhật status = "BuyCVdatasuccessfull"
4. Gửi thông báo cho CTV
5. Schedule job thanh toán hoa hồng (sau 7 ngày)
6. NTD nhận thông tin liên hệ ứng viên

**Business Rules:**
- Phải có đủ số dư trong ví
- Không mua lại CV đã mua
- Không mua CV của công ty bị loại trừ
- Hoa hồng thanh toán sau 7 ngày không khiếu nại

#### 2.2.3. Giai đoạn 3: Interview Process

**Actors:** NTD, UV, CTV, System  
**Duration:** 2-30 ngày  
**Trigger:** NTD chọn "Interview" cho CV đã mua

**Detailed Flow:**
```mermaid
sequenceDiagram
    participant NTD as Nhà tuyển dụng
    participant UV as Ứng viên
    participant CTV as Cộng tác viên
    participant SYS as System
    
    NTD->>SYS: Chọn Interview cho CV
    SYS->>SYS: Update status = "Waitingcandidateconfirm"
    SYS->>UV: Gửi email offer phỏng vấn
    SYS->>SYS: Schedule auto-reject sau 48h
    
    alt UV xác nhận trong 48h
        UV->>SYS: Xác nhận đồng ý phỏng vấn
        SYS->>SYS: Kiểm tra số dư ví NTD
        SYS->>SYS: Trừ tiền interview fee
        SYS->>SYS: Update status = "Waitingsetupinterview"
        SYS->>NTD: Thông báo UV đồng ý
        SYS->>CTV: Thông báo UV đồng ý
        SYS->>SYS: Schedule thanh toán hoa hồng sau 24h
        
        NTD->>SYS: Setup lịch phỏng vấn
        SYS->>UV: Gửi thông tin lịch PV
        SYS->>SYS: Update status = "WaitingInterview"
        
        alt Phỏng vấn thành công
            NTD->>SYS: Update "PassInterview"
            SYS->>CTV: Thanh toán hoa hồng interview
        else Phỏng vấn thất bại
            NTD->>SYS: Update "FailInterview"
            Note over SYS: Kết thúc quy trình
        end
        
    else UV không xác nhận sau 48h
        SYS->>SYS: Auto-reject recruitment
        SYS->>SYS: Hoàn tiền 100% cho NTD
        SYS->>NTD: Thông báo UV từ chối
        SYS->>CTV: Thông báo UV từ chối
    end
```

#### 2.2.4. Giai đoạn 4: Onboard Process

**Actors:** NTD, UV, CTV, System  
**Duration:** 67-90 ngày  
**Trigger:** NTD chọn "Onboard" hoặc chuyển từ Interview

**Detailed Flow:**
```mermaid
graph TD
    A[NTD chọn Onboard] --> B[UV xác nhận trong 48h?]
    B -->|Yes| C[Chuyển Trial Work]
    B -->|No| D[Auto-reject + Hoàn tiền]
    
    C --> E[Kiểm tra số dư ví NTD]
    E -->|Đủ 90%| F[Trừ tiền ngay]
    E -->|Không đủ| G[Tạo ghi nợ]
    
    F --> H[Bắt đầu Trial Work - 60 ngày]
    G --> H
    
    H --> I[Nhắc nhở từ ngày 55-59]
    I --> J[Sau 67 ngày]
    J --> K{NTD cập nhật status?}
    K -->|Success| L[Tuyển dụng thành công]
    K -->|Fail| M[Thất bại]
    K -->|No action| N[Auto Success]
    
    L --> O[Thanh toán hoa hồng theo giai đoạn]
    N --> O
    
    O --> P[15% sau 30 ngày]
    P --> Q[10% sau 45 ngày]
    Q --> R[75% sau 67 ngày]
```

**Payment Schedule:**
- **Ngay khi chuyển Trial:** 10% deposit
- **Sau 30 ngày:** 15% hoa hồng cho CTV
- **Sau 45 ngày:** 10% hoa hồng cho CTV
- **Sau 67 ngày:** 75% hoa hồng cho CTV + Auto success nếu không có action

---

## 3. Submit CV Workflow

### 3.1. Workflow Overview

```mermaid
graph TD
    A[NTD đăng tin tuyển dụng] --> B[Tin hiển thị công khai]
    B --> C[CTV/UV tìm thấy tin]
    C --> D[CTV submit CV vào tin]
    D --> E[NTD nhận thông báo]
    E --> F{NTD review CV}
    F -->|Accept| G[Thanh toán theo bonus_type]
    F -->|Reject| H[Từ chối]
    G --> I{Bonus Type?}
    I -->|CV Data| J[Nhận thông tin - Kết thúc]
    I -->|Interview| K[Quy trình phỏng vấn]
    I -->|Onboard| L[Quy trình tuyển dụng]
    K --> M[Tương tự MarketCV Interview]
    L --> N[Tương tự MarketCV Onboard]
```

### 3.2. Chi tiết Submit CV Process

#### 3.2.1. Giai đoạn Submit

**Actors:** CTV, NTD, System  
**Duration:** 1-7 ngày  
**Trigger:** CTV submit CV vào job

**Steps:**
1. CTV chọn CV từ warehouse
2. Chọn job phù hợp
3. Điền thông tin bổ sung (nếu cần)
4. Submit CV với status = "pending-review"
5. System gửi thông báo cho NTD
6. NTD review danh sách CV
7. NTD accept/reject CV

**Business Rules:**
- Job phải còn active và chưa hết hạn
- CV chưa được submit vào job này trước đó
- CTV không submit vào job của công ty mình
- Có thể submit nhiều CV vào cùng 1 job

#### 3.2.2. Payment Flow

```mermaid
sequenceDiagram
    participant CTV as Cộng tác viên
    participant NTD as Nhà tuyển dụng
    participant SYS as System
    
    CTV->>SYS: Submit CV vào Job
    SYS->>NTD: Thông báo có CV mới
    
    NTD->>SYS: Accept CV
    SYS->>SYS: Kiểm tra job.bonus_type
    
    alt bonus_type = "cv"
        SYS->>SYS: Trừ tiền ngay
        SYS->>SYS: Update status = "BuyCVdatasuccessfull"
        SYS->>SYS: Schedule thanh toán CTV sau 7 ngày
        
    else bonus_type = "interview"
        SYS->>SYS: Update status = "Waitingsetupinterview"
        Note over SYS: Chuyển sang quy trình Interview
        
    else bonus_type = "onboard"
        SYS->>SYS: Update status = "Waitingsetupinterview"
        Note over SYS: Chuyển sang quy trình Onboard
    end
```

---

## 4. Payment & Commission Workflow

### 4.1. Commission Calculation Flow

```mermaid
graph TD
    A[Giao dịch hoàn thành] --> B{Authority Status?}
    B -->|Authority = 0| C[CTV nhận 100%]
    B -->|Authority = 1,2| D[CTV nhận 20%]
    
    C --> E[Tính toán theo giai đoạn]
    D --> E
    
    E --> F{Loại dịch vụ?}
    F -->|CV Data| G[100% ngay]
    F -->|Interview| H[100% sau 24h]
    F -->|Onboard| I[Chia 3 giai đoạn]
    
    I --> J[15% sau 30 ngày]
    J --> K[10% sau 45 ngày]
    K --> L[75% sau 67 ngày]
    
    G --> M[Cập nhật ví CTV]
    H --> M
    L --> M
    
    M --> N[Ghi nhận payin_month]
    N --> O[Gửi thông báo]
```

### 4.2. Wallet Management Flow

```mermaid
graph TD
    A[NTD nạp tiền] --> B{Phương thức?}
    B -->|ZaloPay| C[Redirect ZaloPay]
    B -->|Bank Transfer| D[Chuyển khoản thủ công]
    
    C --> E[Callback từ ZaloPay]
    E --> F[Verify transaction]
    F --> G[Cộng tiền vào ví]
    
    D --> H[Admin xác nhận]
    H --> G
    
    G --> I[Ghi transaction log]
    I --> J[Gửi thông báo thành công]
    
    K[NTD mua CV/Service] --> L[Kiểm tra số dư]
    L -->|Đủ| M[Trừ tiền ngay]
    L -->|Không đủ| N[Tạo ghi nợ]
    
    M --> O[Ghi transaction log]
    N --> P[Gửi email nhắc nhở]
    P --> Q[Khóa tài khoản nếu quá hạn]
```

---

## 5. Job Management Workflow

### 5.1. Job Lifecycle

```mermaid
graph TD
    A[NTD tạo job] --> B[Job status = Draft]
    B --> C[NTD hoàn thiện thông tin]
    C --> D[Publish job]
    D --> E[Job status = Active]
    
    E --> F[Job hiển thị công khai]
    F --> G[Nhận CV ứng tuyển]
    G --> H{Hết hạn?}
    H -->|No| G
    H -->|Yes| I[Auto expire]
    
    I --> J[Job status = Expired]
    J --> K[Hoàn tiền CV chưa xử lý]
    
    L[NTD tạm dừng] --> M[Job status = Paused]
    M --> N[Ẩn khỏi tìm kiếm]
    
    O[NTD xóa job] --> P[Soft delete]
    P --> Q[Hoàn tiền toàn bộ]
```

### 5.2. Job Expiration Handling

```mermaid
sequenceDiagram
    participant CRON as Cron Job
    participant SYS as System
    participant NTD as Nhà tuyển dụng
    participant CTV as Cộng tác viên
    
    CRON->>SYS: Chạy daily job check expiration
    SYS->>SYS: Tìm jobs hết hạn
    
    loop Mỗi job hết hạn
        SYS->>SYS: Update job.status = 2 (expired)
        SYS->>NTD: Gửi thông báo job hết hạn
        
        SYS->>SYS: Tìm submit_cvs chưa xử lý
        loop Mỗi submit_cv pending
            SYS->>SYS: Hoàn tiền cho NTD
            SYS->>CTV: Thông báo job hết hạn
        end
    end
```

---

## 6. User Onboarding Workflow

### 6.1. Employer Registration Flow

```mermaid
graph TD
    A[Employer đăng ký] --> B[Điền form đăng ký]
    B --> C[Gửi email xác thực]
    C --> D{Click link xác thực?}
    D -->|Yes| E[Account activated]
    D -->|No| F[Account pending]

    E --> G[Đăng nhập lần đầu]
    G --> H[Setup company profile]
    H --> I{Upload business license?}
    I -->|Yes| J[Admin review]
    I -->|No| K[Limited access]

    J --> L{Admin approve?}
    L -->|Yes| M[Full access granted]
    L -->|No| N[Request more info]

    K --> O[Có thể xem job, không thể đăng]
    M --> P[Có thể đăng job, mua CV]

    F --> Q[Auto-delete sau 7 ngày]
```

### 6.2. Collaborator Registration Flow

```mermaid
graph TD
    A[CTV đăng ký] --> B[Điền thông tin cơ bản]
    B --> C[Xác thực email]
    C --> D[Account activated]

    D --> E[Đăng nhập lần đầu]
    E --> F[Setup profile]
    F --> G[Thêm thông tin ngân hàng]
    G --> H[Level = 0 (Basic)]

    H --> I[Bắt đầu hoạt động]
    I --> J[Submit CV/Bán CV]
    J --> K[Tích lũy doanh thu]
    K --> L{Đạt milestone?}
    L -->|Yes| M[Tự động nâng level]
    L -->|No| N[Giữ level hiện tại]

    M --> O[Level 1: Standard]
    O --> P[Level 2: Premium]
    P --> Q[Level 3: VIP]
```

### 6.3. Level Upgrade Criteria

| Level | Doanh thu/tháng | Quyền lợi |
|-------|-----------------|-----------|
| **0 - Basic** | < $500 | Chức năng cơ bản |
| **1 - Standard** | $500 - $1,999 | Ưu tiên hiển thị CV |
| **2 - Premium** | $2,000 - $4,999 | Hoa hồng cao hơn, support ưu tiên |
| **3 - VIP** | $5,000+ | Hoa hồng tối đa, account manager riêng |

---

## 7. Complaint & Dispute Workflow

### 7.1. Complaint Process Flow

```mermaid
graph TD
    A[NTD tạo khiếu nại] --> B[Điền lý do + evidence]
    B --> C[Submit complaint]
    C --> D[System gửi thông báo CTV]
    D --> E[CTV có 48h phản hồi]

    E --> F{CTV phản hồi?}
    F -->|Yes| G[CTV gửi explanation]
    F -->|No| H[Auto-escalate to Admin]

    G --> I[NTD xem phản hồi]
    I --> J{NTD đồng ý?}
    J -->|Yes| K[Đóng khiếu nại]
    J -->|No| L[Escalate to Admin]

    H --> M[Admin review]
    L --> M

    M --> N{Admin decision?}
    N -->|Accept complaint| O[Hoàn tiền NTD]
    N -->|Reject complaint| P[Giữ nguyên]

    O --> Q[Trừ hoa hồng CTV]
    O --> R[Ghi nhận vi phạm]
    P --> S[Đóng khiếu nại]
```

### 7.2. Dispute Resolution Timeline

| Giai đoạn | Thời gian | Actor | Action |
|-----------|-----------|-------|--------|
| **Tạo khiếu nại** | 0h | NTD | Submit complaint với evidence |
| **Thông báo CTV** | +1h | System | Auto-notify CTV |
| **CTV phản hồi** | 48h | CTV | Provide explanation/evidence |
| **NTD review** | 24h | NTD | Accept/Reject CTV response |
| **Admin review** | 72h | Admin | Final decision |
| **Resolution** | +24h | System | Execute decision |

---

## 8. Notification & Communication Workflow

### 8.1. Notification Trigger Flow

```mermaid
graph TD
    A[Business Event] --> B{Event Type?}

    B -->|CV Purchase| C[BuyCvSuccess]
    B -->|Payment| D[PaymentNotification]
    B -->|Status Change| E[StatusChangeNotification]
    B -->|Reminder| F[ReminderNotification]

    C --> G[Determine Recipients]
    D --> G
    E --> G
    F --> G

    G --> H{Notification Channels?}
    H -->|Email| I[Queue Email Job]
    H -->|Database| J[Store in notifications table]
    H -->|SMS| K[Queue SMS Job]

    I --> L[Send Email]
    J --> M[Show in-app notification]
    K --> N[Send SMS]

    L --> O[Log email status]
    M --> P[Mark as read when viewed]
    N --> Q[Log SMS status]
```

### 8.2. Email Workflow

```mermaid
sequenceDiagram
    participant EVENT as Business Event
    participant NOTIF as Notification
    participant QUEUE as Queue System
    participant MAIL as Mail Service
    participant USER as User

    EVENT->>NOTIF: Trigger notification
    NOTIF->>NOTIF: Build notification data
    NOTIF->>QUEUE: Queue email job

    QUEUE->>MAIL: Process email job
    MAIL->>MAIL: Render email template
    MAIL->>MAIL: Send via SMTP

    alt Email sent successfully
        MAIL->>QUEUE: Mark job as completed
        MAIL->>USER: Email delivered
    else Email failed
        MAIL->>QUEUE: Mark job as failed
        QUEUE->>QUEUE: Retry job (max 3 times)
    end
```

---

## 9. Data Synchronization Workflow

### 9.1. CV Data Sync Flow

```mermaid
graph TD
    A[External CV Source] --> B[API Webhook]
    B --> C[Validate CV Data]
    C --> D{Data Valid?}
    D -->|Yes| E[Parse CV Content]
    D -->|No| F[Log Error]

    E --> G[Extract Information]
    G --> H[Hide Sensitive Data]
    H --> I[Store in Warehouse]
    I --> J[Generate CV Preview]
    J --> K[Update Search Index]

    K --> L[Notify CTV]
    L --> M[Available for Selling]
```

### 9.2. ITNavi Integration Flow

```mermaid
sequenceDiagram
    participant ITNAVI as ITNavi System
    participant API as RecLand API
    participant PARSER as CV Parser
    participant DB as Database
    participant CTV as Collaborator

    ITNAVI->>API: Send CV data via webhook
    API->>API: Validate API key
    API->>PARSER: Parse CV content
    PARSER->>PARSER: Extract structured data
    PARSER->>API: Return parsed data

    API->>DB: Store CV in warehouse
    API->>DB: Create metadata
    API->>CTV: Notify new CV available
    API->>ITNAVI: Return success response
```

---

## 10. Automated System Workflows

### 10.1. Daily Maintenance Flow

```mermaid
graph TD
    A[Daily Cron 00:00] --> B[Check Expired Jobs]
    B --> C[Update Job Status]
    C --> D[Refund Pending Payments]
    D --> E[Send Expiration Notifications]

    E --> F[Check Expired CVs]
    F --> G[Remove from Marketplace]
    G --> H[Notify CV Owners]

    H --> I[Process Payment Debts]
    I --> J[Send Reminder Emails]
    J --> K[Lock Overdue Accounts]

    K --> L[Update CTV Levels]
    L --> M[Calculate Monthly Revenue]
    M --> N[Assign New Levels]

    N --> O[Clean Temporary Data]
    O --> P[Archive Old Logs]
    P --> Q[Generate Daily Reports]
```

### 10.2. Payment Processing Flow

```mermaid
graph TD
    A[Payment Due] --> B{Payment Type?}

    B -->|Commission| C[Check Conditions]
    B -->|Refund| D[Process Refund]
    B -->|Debt Reminder| E[Send Reminder]

    C --> F{Conditions Met?}
    F -->|Yes| G[Calculate Amount]
    F -->|No| H[Skip Payment]

    G --> I[Update CTV Wallet]
    I --> J[Record Transaction]
    J --> K[Send Notification]

    D --> L[Update NTD Wallet]
    L --> M[Record Refund]
    M --> N[Send Notification]

    E --> O[Check Overdue Days]
    O --> P{> 30 days?}
    P -->|Yes| Q[Lock Account]
    P -->|No| R[Send Email Only]
```

---

## 11. Error Handling & Recovery Workflows

### 11.1. Payment Failure Recovery

```mermaid
graph TD
    A[Payment Failed] --> B[Log Error Details]
    B --> C{Error Type?}

    C -->|Insufficient Balance| D[Create Debt Record]
    C -->|Gateway Error| E[Retry Payment]
    C -->|System Error| F[Manual Review]

    D --> G[Send Balance Reminder]
    G --> H[Set Payment Due Date]

    E --> I{Retry Count < 3?}
    I -->|Yes| J[Schedule Retry]
    I -->|No| K[Mark as Failed]

    F --> L[Admin Notification]
    L --> M[Manual Processing]

    K --> N[Refund if Applicable]
    N --> O[Notify All Parties]
```

### 11.2. Data Consistency Check

```mermaid
graph TD
    A[Hourly Consistency Check] --> B[Validate Wallet Balances]
    B --> C{Balance Mismatch?}
    C -->|Yes| D[Log Discrepancy]
    C -->|No| E[Check Transaction Logs]

    D --> F[Calculate Correct Balance]
    F --> G[Update Wallet]
    G --> H[Notify Admin]

    E --> I{Missing Transactions?}
    I -->|Yes| J[Reconstruct from Logs]
    I -->|No| K[Check Job Status]

    J --> L[Update Missing Records]
    L --> M[Verify Consistency]

    K --> N{Orphaned Jobs?}
    N -->|Yes| O[Clean Up Jobs]
    N -->|No| P[System Healthy]
```

---

## 12. Performance Monitoring Workflow

### 12.1. System Health Check

```mermaid
graph TD
    A[Every 5 minutes] --> B[Check Database Connection]
    B --> C[Check Redis Connection]
    C --> D[Check S3 Storage]
    D --> E[Check Queue Status]

    E --> F{All Services OK?}
    F -->|Yes| G[Update Health Status]
    F -->|No| H[Identify Failed Service]

    H --> I[Attempt Auto-Recovery]
    I --> J{Recovery Successful?}
    J -->|Yes| K[Log Recovery]
    J -->|No| L[Alert Admin]

    L --> M[Send Critical Alert]
    M --> N[Escalate to On-Call]
```

### 12.2. Performance Metrics Collection

```mermaid
graph TD
    A[API Request] --> B[Start Timer]
    B --> C[Process Request]
    C --> D[End Timer]
    D --> E[Calculate Duration]

    E --> F[Log Metrics]
    F --> G{Duration > Threshold?}
    G -->|Yes| H[Flag Slow Request]
    G -->|No| I[Normal Processing]

    H --> J[Analyze Query Performance]
    J --> K[Identify Bottlenecks]
    K --> L[Auto-Optimize if Possible]

    I --> M[Update Performance Stats]
    M --> N[Generate Hourly Reports]
```

---

*Tài liệu Workflow Nghiệp vụ hoàn tất với đầy đủ sơ đồ và mô tả chi tiết cho tất cả quy trình chính của hệ thống RecLand.*
