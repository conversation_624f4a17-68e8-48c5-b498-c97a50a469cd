<?php

namespace App\Rules\Frontend;

use App\Services\Frontend\WareHouseSubmitCvService;
use Hash;
use Illuminate\Contracts\Validation\Rule;

class CheckDuplicateSubmitByCv implements Rule
{

    protected $cvId;

    protected $jobId;

    public function __construct($cvId,$jobId)
    {
        $this->cvId = $cvId;
        $this->jobId = $jobId;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $wareHouseSubmitCvService = app(WareHouseSubmitCvService::class);
//        $check = $wareHouseSubmitCvService->checkDuplicateCvForJobById($this->cvId,$this->jobId);
        $check = $wareHouseSubmitCvService->checkDuplicateCvCompanyJobById($this->cvId,$this->jobId);
        return !$check;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('frontend/job/message.error_duplicate_job');
    }
}
