<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\User;
use App\Models\Job;
use App\Models\WareHouseCv;
use App\Models\Fake\FakeCompany;
use App\Models\Fake\FakeEmployer;
use App\Models\Fake\FakeJob;
use App\Models\Fake\FakeJobAddress;
use App\Models\Fake\FakeLead;
use App\Models\WareHouseCvSelling;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GenerateFakeDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fake:generate 
                            {--companies=200 : Số lượng công ty fake cần tạo}
                            {--employers-per-company=1-1 : Số lượng employer mỗi công ty (min-max)}
                            {--jobs-per-employer=1-3 : Số lượng job mỗi employer (min-max)}
                            {--rec-users=700 : S<PERSON> lượng cộng tác viên (rec users) cần tạo}
                            {--cvs-per-rec=6-10 : Số lượng warehouse_cvs mỗi rec user (min-max)}
                            {--start_date=2025-06-01 : Ngày bắt đầu tạo dữ liệu}
                            {--end_date=2025-06-31 : Ngày kết thúc tạo dữ liệu}';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tạo dữ liệu fake từ database fake_info sang database chính';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Bắt đầu tạo dữ liệu fake...');

        // Lấy tham số
        $companiesCount = (int) $this->option('companies');
        $employersRange = $this->parseRange($this->option('employers-per-company'));
        $jobsRange = $this->parseRange($this->option('jobs-per-employer'));
        $recUsersCount = (int) $this->option('rec-users');
        $cvsRange = $this->parseRange($this->option('cvs-per-rec'));
        $startDate = $this->option('start_date');
        $endDate = $this->option('end_date');

        $this->info("Cấu hình:");
        $this->info("- Số công ty: {$companiesCount}");
        $this->info("- Số employer mỗi công ty: {$employersRange['min']}-{$employersRange['max']}");
        $this->info("- Số job mỗi employer: {$jobsRange['min']}-{$jobsRange['max']}");
        $this->info("- Số cộng tác viên (rec): {$recUsersCount}");
        $this->info("- Số warehouse_cvs mỗi rec: {$cvsRange['min']}-{$cvsRange['max']}");
        $this->info("- Khoảng thời gian: {$startDate} - {$endDate}");

        try {
            // DB::beginTransaction();

            // Lấy dữ liệu fake từ database fake_info
            $fakeCompanies = FakeCompany::with(['addresses', 'metas'])
                ->inRandomOrder()
                ->limit($companiesCount)
                ->get();

            if ($fakeCompanies->isEmpty()) {
                $this->error('Không tìm thấy dữ liệu fake companies!');
                return Command::FAILURE;
            }

            $totalJobs = 0;
            $totalEmployers = 0;
            $totalRecUsers = 0;
            $totalWarehouseCvs = 0;

            foreach ($fakeCompanies as $fakeCompany) {
                // Tạo company
                $company = $this->createCompany($fakeCompany, $startDate, $endDate);
                $this->info("Đã tạo công ty: {$company->name}");

                // Lấy fake leads cho company này
                $employersCount = rand($employersRange['min'], $employersRange['max']);
                $fakeLeads = FakeLead::whereNotNull('email')
                    ->where('email', '!=', '')
                    ->whereNotNull('contact_name')
                    ->inRandomOrder()
                    ->limit($employersCount)
                    ->get();

                // Nếu không có lead phù hợp, lấy từ bất kỳ lead nào có đủ thông tin
                if ($fakeLeads->isEmpty()) {
                    $fakeLeads = FakeLead::whereNotNull('email')
                        ->whereNotNull('contact_name')
                        ->inRandomOrder()
                        ->limit($employersCount)
                        ->get();
                }

                foreach ($fakeLeads as $fakeLead) {
                    // Tạo employer (user) từ lead data
                    $employer = $this->createEmployerFromLead($fakeLead, $company->id, $startDate, $endDate);
                    $totalEmployers++;
                    $this->info("  - Đã tạo employer: {$employer->name}");

                    // Tạo jobs cho employer này
                    $jobsCount = rand($jobsRange['min'], $jobsRange['max']);
                    $fakeJobs = FakeJob::with('addresses')
                        ->where('company_id', $fakeCompany->id)
                        ->inRandomOrder()
                        ->limit($jobsCount)
                        ->get();

                    // Nếu không có job fake, lấy từ bất kỳ company nào
                    if ($fakeJobs->isEmpty()) {
                        $fakeJobs = FakeJob::with('addresses')
                            ->inRandomOrder()
                            ->limit($jobsCount)
                            ->get();
                    }

                    foreach ($fakeJobs as $fakeJob) {
                        $job = $this->createJob($fakeJob, $company->id, $employer->id, $startDate, $endDate);
                        $totalJobs++;
                        $this->info("    - Đã tạo job: {$job->name}");
                    }
                }
            }

            // Tạo cộng tác viên (rec users) và warehouse_cvs
            $this->info('');
            $this->info('Bắt đầu tạo cộng tác viên và warehouse_cvs...');

            // Tính toán số lượng warehouse_cvs cần thiết
            $totalCvsNeeded = $recUsersCount * $cvsRange['max']; // Tính theo max để đảm bảo đủ
            $limitWarehouseCvs = max($totalCvsNeeded * 10, 1000); // Lấy gấp 10 lần hoặc tối thiểu 1000 record

            $this->info("Số warehouse_cvs cần thiết: ~{$totalCvsNeeded}, sẽ lấy {$limitWarehouseCvs} records để đảm bảo đa dạng");

            // Lấy số lượng warehouse_cvs hợp lý để clone
            $existingWarehouseCvs = WareHouseCv::where('is_real', 1)
                ->whereNotNull('candidate_name')
                ->whereNotNull('candidate_email')
                ->whereNotNull('candidate_mobile')
                ->inRandomOrder()
                ->limit($limitWarehouseCvs)
                ->get();

            if ($existingWarehouseCvs->isEmpty()) {
                $this->warn('Không có warehouse_cvs nào để tạo rec users!');
            } else {
                $this->info("Đã lấy {$existingWarehouseCvs->count()} warehouse_cvs để làm nguồn dữ liệu");

                for ($i = 0; $i < $recUsersCount; $i++) {
                    // Lấy ngẫu nhiên 1 warehouse_cv để lấy thông tin
                    $sourceWarehouseCv = $existingWarehouseCvs->random();

                    // Tạo rec user từ thông tin warehouse_cv
                    $recUser = $this->createRecUser($sourceWarehouseCv, $startDate, $endDate);
                    $totalRecUsers++;
                    $this->info("Đã tạo cộng tác viên: {$recUser->name}");

                    // Tạo warehouse_cvs cho rec user này
                    $cvsCount = rand($cvsRange['min'], $cvsRange['max']);
                    for ($j = 0; $j < $cvsCount; $j++) {
                        $sourceForClone = $existingWarehouseCvs->random();
                        $clonedCv = $this->cloneWarehouseCv($sourceForClone, $recUser->id, $startDate, $endDate);
                        $totalWarehouseCvs++;
                        $this->info("  - Đã tạo warehouse_cv: {$clonedCv->candidate_name}");
                    }
                }
            }

            // DB::commit();

            $this->info('');
            $this->info('Hoàn thành!');
            $this->info("Tổng kết:");
            $this->info("- Đã tạo {$companiesCount} công ty");
            $this->info("- Đã tạo {$totalEmployers} employer");
            $this->info("- Đã tạo {$totalJobs} job");
            $this->info("- Đã tạo {$totalRecUsers} cộng tác viên (rec)");
            $this->info("- Đã tạo {$totalWarehouseCvs} warehouse_cvs fake");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            // DB::rollBack();
            $this->error('Lỗi: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Parse range string like "1-3" to array
     */
    private function parseRange(string $range): array
    {
        $parts = explode('-', $range);
        return [
            'min' => (int) $parts[0],
            'max' => (int) ($parts[1] ?? $parts[0])
        ];
    }

    /**
     * Tạo company từ fake data
     */
    private function createCompany(FakeCompany $fakeCompany, string $startDate, string $endDate): Company
    {
        $slug = $this->generateUniqueSlug($fakeCompany->slug, Company::class);

        // Random thời gian trong khoảng days
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);

        return Company::create([
            'name' => $fakeCompany->name,
            'slug' => $slug,
            'about' => $fakeCompany->overview,
            'scale' => $fakeCompany->size,
            'mst' => $fakeCompany->mst,
            'logo' => $fakeCompany->img_logo,
            'banner' => $fakeCompany->img_outstanding,
            'is_active' => 1,
            'is_real' => 0, // Đánh dấu là fake data
            'created_at' => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at' => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Tạo employer từ lead data
     */
    private function createEmployerFromLead(FakeLead $fakeLead, int $companyId, string $startDate, string $endDate): User
    {
        // Sử dụng clean email để loại bỏ khoảng trắng
        $email = $fakeLead->clean_email;

        // Kiểm tra email đã tồn tại chưa, nếu có thì tạo unique email
        if (User::where('email', $email)->exists()) {
            $email = $this->generateUniqueEmail($email);
        }

        // Random thời gian trong khoảng days
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);

        return User::create([
            'name' => $fakeLead->clean_contact_name,
            'email' => $email,
            'password' => bcrypt('password123'), // Password mặc định
            'mobile' => $fakeLead->clean_phone,
            'company_id' => $companyId,
            'type' => 'employer',
            'is_active' => 1,
            'is_real' => 0, // Đánh dấu là fake data
            'email_verified_at' => now(),
            'created_at' => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at' => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Tạo employer từ fake data (method cũ - giữ lại để tương thích)
     */
    private function createEmployer(FakeEmployer $fakeEmployer, int $companyId, string $startDate, string $endDate): User
    {
        // $email = $this->generateUniqueEmail($fakeEmployer->email);
        $email = $fakeEmployer->email;

        // Random thời gian trong khoảng days
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);

        return User::create([
            'name' => $fakeEmployer->name,
            'email' => $email,
            'password' => bcrypt('password123'), // Password mặc định
            'mobile' => $fakeEmployer->mobile,
            'company_id' => $companyId,
            'type' => 'employer',
            'is_active' => 1,
            'is_real' => 0, // Đánh dấu là fake data
            'email_verified_at' => now(),
            'created_at' => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at' => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Tạo job từ fake data
     */
    private function createJob(FakeJob $fakeJob, int $companyId, int $employerId, string $startDate, string $endDate): Job
    {
        $slug = $this->generateUniqueSlug($fakeJob->slug, Job::class);

        // Random thời gian trong khoảng days
        //publishedAt sẽ có phần H:i:s là khoảng từ 8h sáng đến 20h30 tối
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);
        $expiredAt = $publishedAt->copy()->addDays(rand(30, 90));

        // Xử lý địa chỉ từ FakeJobAddress
        $addressJson = $this->generateJobAddress($fakeJob);
        $randomJob = Job::inRandomOrder()->first();

        $job = Job::create([
            'name'                       => $fakeJob->name,
            'slug'                       => $slug,
            'jd_description'             => $fakeJob->description,
            'jd_request'                 => $fakeJob->requirement,
            'jd_welfare'                 => $fakeJob->benefit,
            'company_id'                 => $companyId,
            'employer_id'                => $employerId,
            'is_real'                    => 0,
            'address'                    => $addressJson,
            'vacancies'                  => $randomJob->vacancies,
            'career'                     => $randomJob->career,
            'skills'                     => $randomJob->skills,
            'skill_id'                   => $randomJob->skill_id,
            'rank'                       => $randomJob->rank,
            'type'                       => $randomJob->type,
            'remote'                     => $randomJob->remote,
            'urgent'                     => $randomJob->urgent,
            'bonus_type'                 => $randomJob->bonus_type,
            'job_type'                   => $randomJob->job_type,
            'salary_min'                 => $randomJob->salary_min,
            'salary_max'                 => $randomJob->salary_max,
            'salary_currency'            => $randomJob->salary_currency,
            'bonus'                      => $randomJob->bonus,
            'manual_bonus_for_ctv'       => $randomJob->manual_bonus_for_ctv,
            'incentive'                  => $randomJob->incentive,
            'bonus_currency'             => $randomJob->bonus_currency,
            'bonus_self_apply'           => $randomJob->bonus_self_apply,
            'bonus_self_apply_incentive' => $randomJob->bonus_self_apply_incentive,
            'bonus_self_apply_currency'  => $randomJob->bonus_self_apply_currency,
            // 'publish_at'                 => $randomJob->publish_at,
            'expire_at'                  => $expiredAt->format('Y-m-d H:i:s'),
            // 'is_active'                  => $randomJob->is_active,
            'status'                     => 0, // dừng tuyển
            'flg'                        => $randomJob->flg,
            'created_at'                 => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at'                 => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);

        $jobMeta = $job->jobMeta()->create([
            'priority' => null,
            'is_show' => 1,
            'is_active' => 1,
            'created_at' => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at' => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);
        // dd($jobMeta);
        return $job;
    }

    /**
     * Tạo slug unique
     */
    private function generateUniqueSlug(string $originalSlug, string $modelClass): string
    {
        $slug = $originalSlug;
        $counter = 1;

        while ($modelClass::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Tạo email unique
     */
    private function generateUniqueEmail(string $originalEmail): string
    {
        $email = $originalEmail;
        $counter = 1;

        while (User::where('email', $email)->exists()) {
            $parts = explode('@', $originalEmail);
            $email = $parts[0] . $counter . '@' . $parts[1];
            $counter++;
        }

        return $email;
    }

    /**
     * Tạo rec user từ warehouse_cv data
     */
    private function createRecUser(WareHouseCv $wareHouseCv, string $startDate, string $endDate): User
    {
        // Clean và tạo unique email
        $email = trim($wareHouseCv->candidate_email);
        if (User::where('email', $email)->exists()) {
            $email = $this->generateUniqueEmail($email);
        }

        // Random thời gian trong khoảng days
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);

        return User::create([
            'name' => trim($wareHouseCv->candidate_name),
            'email' => $email,
            'password' => bcrypt('password123'), // Password mặc định
            'mobile' => trim($wareHouseCv->candidate_mobile),
            'type' => 'rec',
            'is_active' => 1,
            'is_real' => 0, // Đánh dấu là fake data
            'email_verified_at' => now(),
            'created_at' => $publishedAt->format('Y-m-d H:i:s'),
            'updated_at' => $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Clone warehouse_cv và gắn cho rec user
     */
    private function cloneWarehouseCv(WareHouseCv $sourceWarehouseCv, int $recUserId, string $startDate, string $endDate): WareHouseCv
    {
        // Random thời gian trong khoảng days
        $publishedAt = Carbon::parse($startDate)->addDays(rand(0, Carbon::parse($endDate)->diffInDays(Carbon::parse($startDate))))->setTime(rand(8, 20), rand(0, 30), 0);

        // Clone dữ liệu từ source
        $clonedData = $sourceWarehouseCv->toArray();

        // Loại bỏ các field không cần thiết
        unset($clonedData['id']);
        unset($clonedData['created_at']);
        unset($clonedData['updated_at']);
        unset($clonedData['deleted_at']);

        // Cập nhật thông tin mới
        $clonedData['user_id'] = $recUserId;
        $clonedData['is_real'] = 0; // Đánh dấu là fake data
        $clonedData['created_at'] = $publishedAt->format('Y-m-d H:i:s');
        $clonedData['updated_at'] = $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s');

        // Có thể thay đổi một chút thông tin để tạo sự đa dạng
        if (rand(1, 3) === 1) {
            // 1/3 khả năng thay đổi tên một chút
            $clonedData['candidate_name'] = $this->randomizeName($clonedData['candidate_name']);
        }

        if (rand(1, 2) === 1) {
            // 1/2 khả năng thay đổi email
            $clonedData['candidate_email'] = $this->randomizeEmail($clonedData['candidate_email']);
        }

        $wareHouseCv = WareHouseCv::create($clonedData);

        $selling = WareHouseCvSelling::where('warehouse_cv_id', $sourceWarehouseCv->id)->first();
        if ($selling) {
            $cloneSelling = $selling->toArray();
            unset($cloneSelling['id']);
            unset($cloneSelling['created_at']);
            unset($cloneSelling['updated_at']);
            unset($cloneSelling['deleted_at']);
            $cloneSelling['warehouse_cv_id'] = $wareHouseCv->id;
            $cloneSelling['is_real'] = 0;
            $cloneSelling['created_at'] = $publishedAt->format('Y-m-d H:i:s');
            $cloneSelling['updated_at'] = $publishedAt->addMinutes(rand(60, 1440))->format('Y-m-d H:i:s');
            WareHouseCvSelling::create($cloneSelling);
        }

        return $wareHouseCv;
    }

    /**
     * Tạo biến thể tên ngẫu nhiên
     */
    private function randomizeName(string $originalName): string
    {
        $prefixes = ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Vũ', 'Võ', 'Đặng', 'Bùi', 'Đỗ'];
        $suffixes = ['Anh', 'Minh', 'Hồng', 'Hương', 'Linh', 'Nam', 'Hải', 'Tuấn', 'Thảo', 'Lan'];

        $nameParts = explode(' ', trim($originalName));
        if (count($nameParts) >= 2) {
            // Thay đổi họ hoặc tên
            if (rand(1, 2) === 1) {
                $nameParts[0] = $prefixes[array_rand($prefixes)];
            } else {
                $nameParts[count($nameParts) - 1] = $suffixes[array_rand($suffixes)];
            }
            return implode(' ', $nameParts);
        }

        return $originalName . ' ' . $suffixes[array_rand($suffixes)];
    }

    /**
     * Tạo biến thể email ngẫu nhiên
     */
    private function randomizeEmail(string $originalEmail): string
    {
        $parts = explode('@', $originalEmail);
        if (count($parts) !== 2) {
            return $originalEmail;
        }

        $localPart = $parts[0];
        $domain = $parts[1];

        // Thêm số random vào cuối local part
        $newLocalPart = $localPart . rand(100, 999);

        return $newLocalPart . '@' . $domain;
    }

    /**
     * Tạo địa chỉ JSON cho job từ FakeJobAddress
     */
    private function generateJobAddress(FakeJob $fakeJob): string
    {
        // Lấy address đầu tiên từ fake job addresses
        $fakeJobAddress = $fakeJob->addresses->first();

        if (!$fakeJobAddress) {
            // Nếu không có address, tạo default
            return json_encode([
                [
                    'area' => 'ha-noi',
                    'address' => 'Địa chỉ không xác định'
                ],
                null
            ], JSON_UNESCAPED_UNICODE);
        }

        // Xác định area dựa vào location_id
        $area = ($fakeJobAddress->location_id == 1) ? 'ha-noi' : 'ho-chi-minh';

        // Tạo cấu trúc address JSON
        $addressData = [
            [
                'area' => $area,
                'address' => $fakeJobAddress->address
            ],
            null
        ];

        return json_encode($addressData, JSON_UNESCAPED_UNICODE);
    }
}
