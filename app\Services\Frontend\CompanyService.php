<?php

namespace App\Services\Frontend;

use App\Repositories\CompanyRepository;
use App\Services\FileServiceS3;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Support\Facades\Cache;

class CompanyService
{

    protected $companyRepository;

    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    public function getListByHome()
    {
        return $this->companyRepository->getListByHome();
    }

    public function getListForSelect()
    {
        return Cache::remember('company.getListForSelect', CACHE_TTL, function () {
            return $this->companyRepository->getListForSelect();
        });
    }

    public function getListByTypePriority($priority = 0, $paginate = false, $params = [], $limit = 10)
    {
        return $this->companyRepository->getListByTypePriority($priority, $paginate, $params, $limit);
    }

    public function getListCompanyAnalysis($params)
    {
        return $this->companyRepository->getListCompanyAnalysis($params);
    }

    public function detailCompany($slug)
    {
        return $this->companyRepository->detailWithSlug($slug);
    }

    public function updateCompany($companyId, $params)
    {
        $data = [];
        $arrImg = [];
        $arrVideo = null;
        if (isset($params['image']) && count($params['image'])) {
            ksort($params['image']);
            foreach ($params['image'] as $image) {
                if (is_file($image)) {
                    $img = FileServiceS3::getInstance()->uploadToS3($image, config('constant.sub_path_s3.company'));
                    array_push($arrImg, $img);
                } else if (is_string($image)) {
                    array_push($arrImg, $image);
                }
            }
        }
        $data['image'] = json_encode($arrImg);

        if (isset($params['video']) && count(array_filter($params['video']))) {
            $arrVideo = json_encode(array_filter($params['video']));
        }
        $data['video'] = $arrVideo;

        if (!empty($params['logo'])) {
            $data['logo'] = FileServiceS3::getInstance()->uploadToS3($params['logo'], config('constant.sub_path_s3.company'));
        }

        if (!empty($params['banner'])) {
            $data['banner'] = FileServiceS3::getInstance()->uploadToS3($params['banner'], config('constant.sub_path_s3.company'));
        }

        if (!empty($params['business_registration_number'])) {
            $data['business_registration_number'] = FileServiceS3::getInstance()->uploadToS3($params['business_registration_number'], config('constant.sub_path_s3.company'));
        }

        $data['name'] = $params['name'];
        $data['website'] = $params['website'];
        $data['scale'] = $params['scale'];
        $data['about'] = $params['about'];
        $data['address'] = json_encode($params['address']);
        $data['career'] = implode(',', $params['career']);

        return $this->companyRepository->update($companyId, [], $data);

    }

    public function getConfig($slug)
    {
        $dataSlugCompany = $this->companyRepository->findBySlug($slug);
        $response = [];

//            $response['key'] = $dataSlugCompany->key;
            $response['name'] = $dataSlugCompany->name;
            $response['about'] = $dataSlugCompany->about;
            $response['logo'] = isset($dataSlugCompany->logo) ? gen_url_file_s3($dataSlugCompany->logo, '', false) : gen_url_file_s3($dataSlugCompany->image, '', false);


        $this->setConfig($response);
        return $response;
    }

    public function setConfig($slug)
    {
        $name = strip_tags(html_entity_decode($slug['name']));
        $about = strip_tags(html_entity_decode($slug['about']));

        SEOMeta::setTitle($name);
        SEOMeta::setDescription($about);
        SEOMeta::addMeta('url',  url()->full());
        SEOMeta::addMeta('language', app()->getLocale());

        OpenGraph::setTitle($name);
        OpenGraph::setDescription($about);
        OpenGraph::setUrl(url()->full());
        OpenGraph::addProperty('locale', app()->getLocale());
        OpenGraph::addProperty('type', 'article');
        OpenGraph::addProperty('image', $slug['logo']);
        OpenGraph::addProperty('content', 'text');
    }

}
