<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\BannerRequest;
use App\Services\Admin\ReportService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ReportController extends Controller
{

    protected $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * @return Application|Factory|View
     */
    public function index()
    {
        $datatable = $this->reportService->buildDatatable();
        return view('admin.pages.report.index',
            compact('datatable'));
    }

    /**
     * @param BannerRequest $request
     * @return mixed
     */
    public function store(BannerRequest $request)
    {
        return $this->reportService->createService($request->all());
    }

    public function show($id)
    {
        return $this->reportService->detailService($id);
    }

    public function update(BannerRequest $request, $id)
    {
        return $this->reportService->updateService($request->all(), $id);
    }

    /**
     * @param Request $request
     * @return Application|ResponseFactory|Response
     */
    public function datatable(Request $request)
    {
        $data = $this->reportService->datatable($request->all());
        return response($data);
    }

    public function changeStatus(Request $request)
    {
        $result = $this->reportService->changeStatus($request->all());
        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);;
    }
}
