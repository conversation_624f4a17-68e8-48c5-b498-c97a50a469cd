<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecAgreeComplainSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $rec;
    protected $employer;
    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($rec,$employer,$submitCv)
    {
        $this->rec = $rec;
        $this->employer = $employer;
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

        $candidateName = '';
        $position      = '';
        $type          = '';
        $content       = $this->submitCv->txt_complain;
        $image         = $this->submitCv->img_complain;
        $companyName   = $this->employer->name;
        $type          = $this->submitCv->bonus_type;
        if (!empty($this->submitCv->warehouseCv)){
            $candidateName = $this->submitCv->warehouseCv->candidate_name;
            if ($type == 'cv'){
                $position = $this->submitCv->warehouseCv->candidate_job_title;
            }
            if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
                $position = $this->submitCv->job->name;
            }
        }
        $link = route('rec-submitcv',['submit_id' =>  $this->submitCv->id,'open_complain' => 1]);
        return (new MailMessage)
            ->view('email.ctv_dongy_submit', [
                'name'          => $this->rec->name,
                'companyName'   => $companyName,
                'candidateName' => $candidateName,
                'position'      => $position,
                'type'          => $type,
                'content'       => $content,
                'image'         => gen_url_file_s3($image),
                'link'          => $link,
            ])
            ->subject('[Recland][Case #'.$this->submitCv->id.'] Kết quả khiếu nại');
    }

}
