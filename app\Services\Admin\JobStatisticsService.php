<?php

namespace App\Services\Admin;

use App\Models\Job;
use App\Models\Company;
use App\Models\SubmitCv;
use Illuminate\Support\Facades\DB;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class JobStatisticsService
{
    /**
     * L<PERSON>y thống kê danh sách job
     */
    public function getJobStatistics($filters = [])
    {
        $query = Job::with(['company', 'submitCv'])
            ->select([
                'job.*',
                'companies.name as company_name',
                'companies.scale as company_scale',
                DB::raw('(SELECT COUNT(*) FROM submit_cvs WHERE submit_cvs.job_id = job.id) as total_apply'),
            ])
            ->leftJoin('companies', 'job.company_id', '=', 'companies.id')
            ->where('job.is_active', 1);

        // Apply filters
        if (!empty($filters['from_date'])) {
            $query->where('job.created_at', '>=', $filters['from_date']);
        }

        if (!empty($filters['to_date'])) {
            $query->where('job.created_at', '<=', $filters['to_date'] . ' 23:59:59');
        }

        if (!empty($filters['company_id'])) {
            $query->where('job.company_id', $filters['company_id']);
        }

        if (!empty($filters['status'])) {
            $query->where('job.status', $filters['status']);
        }

        // Filter theo bonus_type (service_type)
        if (!empty($filters['service_type'])) {
            $query->where('job.bonus_type', $filters['service_type']);
        }

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('job.name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('companies.name', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Set current page if provided
        if (!empty($filters['page'])) {
            \Illuminate\Pagination\Paginator::currentPageResolver(function () use ($filters) {
                return $filters['page'];
            });
        }

        $jobs = $query->orderBy('job.created_at', 'desc')->paginate(30);

        // Tính toán thống kê cho từng job
        foreach ($jobs as $job) {
            $job = $this->calculateJobStatistics($job);
        }

        return $jobs;
    }

    /**
     * Tính toán thống kê cho một job
     */
    private function calculateJobStatistics($job)
    {
        // Sử dụng bonus_type của job để tính toán thống kê submit_cvs
        $jobBonusType = $job->bonus_type;

        // Lấy các trạng thái được duyệt theo bonus_type của job
        $approvedStatuses = $this->getApprovedStatuses($jobBonusType);

        // Tính số lượng CV được duyệt dựa trên bonus_type của job
        $approvedCvQuery = SubmitCv::where('job_id', $job->id);

        // Chỉ lấy submit_cvs có bonus_type giống với job
        if (!empty($jobBonusType)) {
            $approvedCvQuery->where('bonus_type', $jobBonusType);
        }

        if (!empty($approvedStatuses)) {
            $approvedCvQuery->whereIn('status', $approvedStatuses);
        }

        $job->approved_cv_count = $approvedCvQuery->count();

        // Tính thành tiền (tổng bonus_ntd của CV được duyệt)
        $job->total_amount = $approvedCvQuery->sum('bonus_ntd') ?? 0;

        // Thông tin bổ sung
        $job->service_type_display = $this->getServiceTypeDisplay($job);
        $job->status_display = $this->getJobStatusDisplay($job->status);
        $job->location_display = $this->getJobLocationDisplay($job);
        $job->frontend_url = $this->getJobFrontendUrl($job);
        $job->career_display = $this->getCareerDisplay($job->career);
        $job->created_date_display = $this->getCreatedDateDisplay($job->created_at);

        return $job;
    }

    /**
     * Lấy các trạng thái được duyệt theo service type
     */
    private function getApprovedStatuses($serviceType)
    {
        $config = config('constant.status_recruitment_approved');
        
        if ($serviceType === 'all') {
            // Lấy tất cả trạng thái được duyệt
            $allStatuses = [];
            foreach ($config as $type => $statuses) {
                $allStatuses = array_merge($allStatuses, array_values($statuses));
            }
            return array_unique($allStatuses);
        }
        
        return isset($config[$serviceType]) ? array_values($config[$serviceType]) : [];
    }

    /**
     * Lấy hiển thị service type
     */
    private function getServiceTypeDisplay($job)
    {
        // Logic để xác định service type dựa trên job bonus_type hoặc submit_cv
        $submitCvs = $job->submitCv;
        $serviceTypes = $submitCvs->pluck('bonus_type')->unique()->filter()->toArray();

        // Nếu không có submit_cv nào, lấy từ job bonus_type
        if (empty($serviceTypes) && !empty($job->bonus_type)) {
            $serviceTypes = [$job->bonus_type];
        }

        if (empty($serviceTypes)) {
            return 'Chưa xác định';
        }

        $displayTypes = [];
        foreach ($serviceTypes as $type) {
            switch ($type) {
                case 'cv':
                    $displayTypes[] = 'CV Data';
                    break;
                case 'interview':
                    $displayTypes[] = 'CV Interview';
                    break;
                case 'onboard':
                    $displayTypes[] = 'CV Onboard';
                    break;
                default:
                    $displayTypes[] = ucfirst($type);
            }
        }

        return implode(', ', $displayTypes);
    }

    /**
     * Lấy hiển thị trạng thái job
     */
    private function getJobStatusDisplay($status)
    {
        $statusConfig = config('job.status.vi');
        return $statusConfig[$status] ?? 'Không xác định';
    }

    /**
     * Lấy hiển thị địa điểm làm việc
     */
    private function getJobLocationDisplay($job)
    {
        if (!empty($job->address)) {
            $address = json_decode($job->address, true);
            if (is_array($address) && !empty($address[0]['area'])) {
                return $address[0]['area'];
            }
        }
        return 'Chưa cập nhật';
    }

    /**
     * Lấy URL frontend của job
     */
    private function getJobFrontendUrl($job)
    {
        return config('constant.url') . '/job/' . $job->slug;
    }

    /**
     * Lấy hiển thị ngành nghề
     */
    private function getCareerDisplay($careerId)
    {
        $careers = config('job.career.vi');
        return $careers[$careerId] ?? 'Chưa xác định';
    }

    /**
     * Lấy hiển thị ngày đăng
     */
    private function getCreatedDateDisplay($createdAt)
    {
        if (!$createdAt) {
            return 'Chưa xác định';
        }
        return $createdAt->format('d/m/Y H:i');
    }

    /**
     * Lấy danh sách công ty cho filter
     */
    public function getCompaniesForFilter()
    {
        return Company::select('id', 'name')
            ->whereHas('jobs')
            ->orderBy('name')
            ->get();
    }

    /**
     * Lấy toàn bộ dữ liệu job cho export (không phân trang)
     */
    private function getAllJobsForExport($filters = [])
    {
        $query = Job::with(['company', 'submitCv'])
            ->select([
                'job.*',
                'companies.name as company_name',
                'companies.scale as company_scale',
                DB::raw('(SELECT COUNT(*) FROM submit_cvs WHERE submit_cvs.job_id = job.id) as total_apply'),
            ])
            ->leftJoin('companies', 'job.company_id', '=', 'companies.id')
            ->where('job.is_active', 1);

        // Apply filters (same as getJobStatistics but without pagination)
        if (!empty($filters['from_date'])) {
            $query->where('job.created_at', '>=', $filters['from_date']);
        }

        if (!empty($filters['to_date'])) {
            $query->where('job.created_at', '<=', $filters['to_date'] . ' 23:59:59');
        }

        if (!empty($filters['company_id'])) {
            $query->where('job.company_id', $filters['company_id']);
        }

        // Filter theo bonus_type (service_type)
        if (!empty($filters['service_type'])) {
            $query->where('job.bonus_type', $filters['service_type']);
        }

        if (!empty($filters['status'])) {
            $query->where('job.status', $filters['status']);
        }

        if (!empty($filters['search'])) {
            $query->where(function($q) use ($filters) {
                $q->where('job.name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('companies.name', 'like', '%' . $filters['search'] . '%');
            });
        }

        // Lấy tất cả dữ liệu không phân trang
        $jobs = $query->orderBy('job.created_at', 'desc')->get();

        // Tính toán thống kê cho từng job
        foreach ($jobs as $job) {
            $job = $this->calculateJobStatistics($job);
        }

        return $jobs;
    }

    /**
     * Xuất dữ liệu ra CSV
     */
    public function exportToCsv($filters = [])
    {
        $jobs = $this->getAllJobsForExport($filters);

        $filename = 'job_statistics_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($jobs) {
            $file = fopen('php://output', 'w');
            
            // UTF-8 BOM for Excel compatibility
            fwrite($file, "\xEF\xBB\xBF");
            
            // Header row
            fputcsv($file, [
                'ID Job',
                'Công ty',
                'Ngành nghề',
                'Dịch vụ',
                'Vị trí đang tuyển',
                'Link Frontend',
                'Ngày đăng',
                'Đơn giá',
                'Số lượng Apply',
                'Số lượng CV được duyệt',
                'Thành tiền',
                'Địa điểm làm việc',
                'Tình trạng'
            ]);
            
            // Data rows
            foreach ($jobs as $job) {
                fputcsv($file, [
                    $job->id,
                    $job->company_name,
                    $job->career_display,
                    $job->service_type_display,
                    $job->name,
                    $job->frontend_url,
                    $job->created_date_display,
                    number_format($job->bonus ?? 0),
                    $job->total_apply,
                    $job->approved_cv_count,
                    number_format($job->total_amount),
                    $job->location_display,
                    $job->status_display
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
}
