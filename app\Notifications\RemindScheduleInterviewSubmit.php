<?php

namespace App\Notifications;


use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindScheduleInterviewSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $user;
    protected $book;

    public function __construct($submitCv, $user, $book)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
        $this->book = $book;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $name = $this->user->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;

        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $timeInterview = $this->book->date_time_book_format;
        $address       = $this->book->address;
        $phone         = $this->book->phone;
        $type_of_sale  = $type;
        $link          = route('employer-submitcv', ['discuss' => $this->submitCv->id]);;

        return (new MailMessage)
            ->view('email.remindScheduleInterview_submit', [
                'name'          => $name,
                'candidateName' => $candidateName,
                'position'      => $position,
                'type'          => $type_of_sale,
                'time'          => $timeInterview,
                'address'       => $address,
                'phone'         => $phone,
                'link'          => $link,
            ])
            ->subject('[Recland] Cập nhật kết quả phỏng vấn ứng viên ' . $candidateName . ' vị trí ' . $position);
    }
}
