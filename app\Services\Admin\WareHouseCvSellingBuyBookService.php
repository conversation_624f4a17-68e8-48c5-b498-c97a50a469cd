<?php

namespace App\Services\Admin;

use App\Jobs\RejectBookExpire;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\RecRefuseComplain;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingBuyBookService
{

    protected $wareHouseCvSellingBuyBookRepository;

    public function __construct(WareHouseCvSellingBuyBookRepository $wareHouseCvSellingBuyBookRepository,WareHouseCvSellingBuyRepository $houseCvSellingBuyRepository)
    {
        $this->wareHouseCvSellingBuyBookRepository = $wareHouseCvSellingBuyBookRepository;
    }

    public function getLastBookBySellingBuyId($sellingBuyId){
        return $this->wareHouseCvSellingBuyBookRepository->getLastBookBySellingBuyId($sellingBuyId);
    }







}
