<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ActivateRecAccount extends Notification
{
    use Queueable;

    protected $result;
    protected $view_id;
    protected $view_token;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($result, $view_id = null, $view_token = null)
    {
        $this->result = $result;
        $this->view_id = $view_id;
        $this->view_token = $view_token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $employer = $this->result;
        $url = '';
        if ($this->view_id && $this->view_token) {
            $url = route('verify-email-employer', ['token' => $employer->token, 'view_id' => $this->view_id, 'view_token' => $this->view_token]);
        } else {
            $url = route('verify-email-employer', ['token' => $employer->token]);
        }
        return (new MailMessage)
            ->view('email.activateRecAccount', compact('employer', 'url'))
            ->subject('[HRI RECLAND] XÁC THỰC TÀI KHOẢN NHÀ TUYỂN DỤNG');
    }


    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}