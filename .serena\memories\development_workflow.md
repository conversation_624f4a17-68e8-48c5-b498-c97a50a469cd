# RecLand - Development Workflow

## Git Workflow

### Branch Strategy
```
main/master     # Production-ready code
├── develop     # Integration branch (if used)
├── feature/*   # Feature development
├── hotfix/*    # Production hotfixes
└── release/*   # Release preparation
```

### Feature Development Process

#### 1. Start New Feature
```powershell
# Create and switch to feature branch
git checkout -b feature/user-profile-management

# Or from develop branch
git checkout develop
git pull origin develop
git checkout -b feature/user-profile-management
```

#### 2. Development Cycle
```powershell
# Regular commits during development
git add .
git commit -m "feat: add user profile form validation"

# Push to remote regularly
git push origin feature/user-profile-management
```

#### 3. Before Merge
```powershell
# Update from main/develop
git checkout main
git pull origin main
git checkout feature/user-profile-management
git rebase main

# Or merge if preferred
git merge main
```

#### 4. Create Pull Request
- Descriptive title và description
- Link related issues
- Add reviewers
- Include testing instructions

#### 5. Code Review Process
- Address reviewer comments
- Update code based on feedback
- Ensure all checks pass

#### 6. Merge & Cleanup
```powershell
# After merge, cleanup
git checkout main
git pull origin main
git branch -d feature/user-profile-management
git push origin --delete feature/user-profile-management
```

## Development Environment Setup

### Initial Setup
```powershell
# Clone repository
git clone <repository-url>
cd Recland

# Install PHP dependencies
composer install

# Install JavaScript dependencies
npm install

# Copy environment file
copy .env.example .env

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate

# Seed database (if needed)
php artisan db:seed

# Build frontend assets
npm run dev
```

### Daily Development

#### Start Development Session
```powershell
# Start Laravel development server
php artisan serve

# In another terminal, watch for asset changes
npm run watch

# Start queue worker (if needed)
php artisan queue:work
```

#### During Development
```powershell
# Clear cache when needed
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Run tests frequently
php artisan test

# Check code style
# (Add code style checker if configured)
```

## Code Review Guidelines

### What to Review

#### Functionality
- [ ] Code meets requirements
- [ ] Edge cases handled
- [ ] Error handling implemented
- [ ] Performance considerations

#### Code Quality
- [ ] Follows coding conventions
- [ ] Proper naming conventions
- [ ] Code is readable và maintainable
- [ ] No code duplication

#### Security
- [ ] Input validation
- [ ] Authorization checks
- [ ] No sensitive data exposure
- [ ] SQL injection prevention

#### Testing
- [ ] Tests included
- [ ] Tests cover edge cases
- [ ] Tests are meaningful
- [ ] All tests pass

### Review Process

#### For Reviewers
1. Understand the requirements
2. Review code thoroughly
3. Test the changes locally
4. Provide constructive feedback
5. Approve when satisfied

#### For Authors
1. Respond to all comments
2. Make requested changes
3. Re-request review
4. Merge after approval

## Testing Strategy

### Test Types

#### Unit Tests
```php
// Example unit test
class UserServiceTest extends TestCase
{
    public function test_can_create_user()
    {
        $userData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];
        
        $user = $this->userService->createUser($userData);
        
        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('John Doe', $user->name);
    }
}
```

#### Feature Tests
```php
// Example feature test
class UserRegistrationTest extends TestCase
{
    public function test_user_can_register()
    {
        $response = $this->post('/register', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ]);
        
        $response->assertRedirect('/dashboard');
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>'
        ]);
    }
}
```

### Testing Commands
```powershell
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/UserTest.php

# Run specific test method
php artisan test --filter test_user_can_register

# Run with coverage
php artisan test --coverage

# Run tests in parallel (if configured)
php artisan test --parallel
```

## Deployment Process

### Development Deployment
```powershell
# Automated via script
.\dev-deploy.sh

# Manual steps
git pull origin develop
composer install
php artisan migrate
php artisan optimize:clear
npm run dev
```

### Staging Deployment
```powershell
# Automated via script
.\staging-deploy.sh

# Manual steps
git pull origin staging
composer install --no-dev
php artisan migrate
php artisan optimize
npm run prod
```

### Production Deployment
```powershell
# Automated via script
.\prod-deploy.sh

# Manual steps with extra care
git pull origin main
composer install --no-dev --optimize-autoloader
php artisan migrate --force
php artisan optimize
npm run prod
php artisan version:generate
```

## Issue Management

### Bug Reports
1. Reproduce the issue
2. Create detailed bug report
3. Assign priority và labels
4. Create hotfix branch if critical
5. Fix và test thoroughly
6. Deploy fix

### Feature Requests
1. Analyze requirements
2. Create feature specification
3. Estimate effort
4. Plan implementation
5. Create feature branch
6. Develop và test
7. Code review và merge

## Communication

### Daily Standups
- What did you work on yesterday?
- What will you work on today?
- Any blockers or issues?

### Code Review Comments
- Be constructive và specific
- Explain the "why" behind suggestions
- Acknowledge good code
- Focus on code, not the person

### Documentation Updates
- Update README for setup changes
- Document new APIs
- Update user guides
- Maintain changelog

## Best Practices

### Development
- Write tests first (TDD when possible)
- Commit early và often
- Use meaningful commit messages
- Keep pull requests small
- Review your own code first

### Collaboration
- Communicate early về blockers
- Ask questions when unclear
- Share knowledge với team
- Help others when possible
- Document decisions và rationale