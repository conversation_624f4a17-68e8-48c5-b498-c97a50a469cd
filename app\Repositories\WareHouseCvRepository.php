<?php

namespace App\Repositories;

use App\Models\WareHouseCv;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WareHouseCvRepository extends BaseRepository
{
    const MODEL = WareHouseCv::class;

    public function getListCv($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

//        if (!empty($orders)){
//            foreach ($orders as $key => $order){
//                $query->orderBy($key,$order);
//            }
//        }

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('candidate_name', 'like', '%' . $params['search'] . '%');
        }

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function total($params = array())
    {
        $query = $this->query();
        foreach ($params as $key => $param){
            switch ($key){
                case 'after_date':
                    $query->where('created_at','>=',$param);
                    break;
                case 'user_id':
                    $query->where('user_id',$param);
                    break;
                default:
                    break;
            }
        }
        $query->where('is_active',config('constant.active'));
        return $query->count();
    }

    public function statisticalByMonth($fromDate = null, $toDate = null){
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();

        return $query->where('is_active', config('constant.active'))
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('COUNT(id) as total, DATE_FORMAT(created_at, "%m") as month')
            ->groupByRaw('DATE_FORMAT(created_at, "%m")')
            ->orderByRaw('DATE_FORMAT(created_at, "%m")')
            ->get();
    }

    public function getWareHouseCvByCtv($userId, $keyWord = ''){

        $query = $this->query();
        $query
            ->select('id', 'candidate_name', 'candidate_job_title', 'candidate_email','candidate_mobile')
            ->where('user_id', $userId)
            ->where('is_active',config('constant.active'));

        if($keyWord != ''){
            $query->where(function($query) use ($keyWord){
                $query->orWhere('candidate_name', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_job_title', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_email', 'like', '%' . $keyWord . '%');
            });
        }

        return $query->orderBy('id')->limit(config('constant.limit_search_ajax_selectbox'))->get();
    }

    public function getWareHouseCvNotSelling($userId, $keyWord = ''){

        $query = $this->query();
        $query
            ->select('warehouse_cvs.*')
            ->leftJoin('warehouse_cv_sellings', function ($join) {
                $join->on('warehouse_cv_sellings.warehouse_cv_id', '=', 'warehouse_cvs.id');
            })
            ->where('user_id', $userId)
            ->where('is_active',config('constant.active'))
            ->whereNull('warehouse_cv_sellings.warehouse_cv_id');

        if($keyWord != ''){
            $query->where(function($query) use ($keyWord){
                $query->orWhere('candidate_name', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_job_title', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_email', 'like', '%' . $keyWord . '%');
            });
        }

        return $query->orderBy('id')->limit(config('constant.limit_search_ajax_selectbox'))->get();
    }



    public function getWareHouseCvById($userId,$id){
        return $this->query()->where('user_id',$userId)->where('id',$id)->first();
    }



    /**
     * @param $params
     *
     * @return mixed
     * Kho ứng viên
     * /rec/warehousecv
     */
    public function getListWarehouseCvSearch($params)
    {
        $query =  $this->query()->select('*');

        $query = $this->querySearchCv($query, $params);

        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }

        $query = $query->orderBy('id', 'DESC')
            ->paginate(5, $page);

        return $query;
    }

    public function countWareHouseCvSearch($params)
    {
        $query =  $this->query();
        $query = $this->querySearchCv($query, $params);
        return $query->count();
    }

    public function querySearchCv($query, $params)
    {
        $query->where('user_id', $params['user_id']);

        if (isset($params['search'])) {
            $keyWord = $params['search'];
            $query->where(function($query) use ($keyWord){
                $query->orWhere('candidate_name', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_job_title', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_email', 'like', '%' . $keyWord . '%');
                $query->orWhere('candidate_mobile', 'like', '%' . $keyWord . '%');
            });
        }

        if (isset($params['career'])  && reset($params['career'])) {
            $query->where(function ($query) use ($params) {
                foreach ($params['career'] as $career) {
                    $query->orWhereRaw("CONCAT(',', career, ',') like '%,". $career .",%'");
                }
            });
        }

        if (isset($params['location']) && reset($params['location'])) {
            $query->where(function ($query) use ($params) {
                foreach ($params['location'] as $address) {
                    $query->orWhere('candidate_location', 'like', '%' . $address . '%');
                }
            });
        }

        if (isset($params['skill']) && reset($params['skill'])) {
            $query->where(function ($query) use ($params) {
                foreach ($params['skill'] as $skill) {
                    $query->orWhere('main_skill', 'like', '%' . $skill . '%');
                }
            });
        }

        if (isset($params['year_experience'])) {
            $query->where('year_experience', $params['year_experience']);
        }

        if (isset($params['candidate_est_timetowork'])) {
            $query->where('candidate_est_timetowork', $params['candidate_est_timetowork']);
        }

        return $query;
    }

    public function getWarehousecvByIdUser($id, $userId){
        return $this->query()
            ->where('id', $id)
            ->where('user_id', $userId)
            ->first();
    }

    public function getByEmailPhone($email,$phone){
        return $this->query()->where('candidate_email',$email)
            ->where('candidate_mobile',$phone)
            ->where('is_active',config('constant.active'))
            ->get();
    }

    public function getWithEmail($email)
    {
        return $this->query()->where('candidate_email', $email)->pluck('id')->toArray();
    }

    public function changeCvFile($id, $params)
    {
        $submitCvRepository = new SubmitCvRepository();
        $submitCvRepository->changeCvFile($id, $params);
    }
}
