---
description:
globs:
alwaysApply: false
---
# Middleware Guide

RecLand sử dụng các middleware để kiểm soát và xử lý HTTP requests.

## Core Middleware

- [Authenticate](mdc:app/Http/Middleware/Authenticate.php) - <PERSON><PERSON><PERSON> tra xác thực người dùng
- [CheckRole](mdc:app/Http/Middleware/CheckRole.php) - Kiể<PERSON> tra quyền người dùng
- [VerifyEmail](mdc:app/Http/Middleware/VerifyEmail.php) - Ki<PERSON>m tra xác thực email

## API Middleware

- [ApiAuthenticate](mdc:app/Http/Middleware/ApiAuthenticate.php) - Xác thực API token
- [ApiThrottling](mdc:app/Http/Middleware/ApiThrottling.php) - Giới hạn tần suất request API

## Special Middleware

- [CompanySetup](mdc:app/Http/Middleware/CompanySetup.php) - <PERSON><PERSON><PERSON> tra thông tin công ty đã setup
- [SubscriptionActive](mdc:app/Http/Middleware/SubscriptionActive.php) - <PERSON><PERSON><PERSON> tra gói dịch vụ còn active
- [CvLimitCheck](mdc:app/Http/Middleware/CvLimitCheck.php) - Kiểm tra giới hạn số CV

## Route Middleware Groups

Middleware được đăng ký trong [Kernel.php](mdc:app/Http/Kernel.php) và sử dụng trong các route groups:

```php
protected $middlewareGroups = [
    'web' => [
        \App\Http\Middleware\EncryptCookies::class,
        \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
        \Illuminate\Session\Middleware\StartSession::class,
        // ...
    ],
    'api' => [
        'throttle:api',
        \Illuminate\Routing\Middleware\SubstituteBindings::class,
    ],
    'admin' => [
        'auth',
        'role:admin',
        // ...
    ],
    'employer' => [
        'auth',
        'role:employer',
        'company.setup',
        // ...
    ],
];
```

## Cách sử dụng

Middleware được áp dụng cho routes trong các file routes:

```php
// Single middleware
Route::get('/profile', [ProfileController::class, 'show'])->middleware('auth');

// Multiple middleware
Route::get('/admin/users', [Admin\UserController::class, 'index'])
    ->middleware(['auth', 'role:admin']);

// Middleware group
Route::middleware('employer')->group(function () {
    Route::resource('jobs', JobController::class);
});
```
