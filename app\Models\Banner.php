<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Banner extends BaseModel
{

    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = [
        'image_url_vn',
        'image_url_en',
        'position_value',
        'type_value',
    ];

    public function getImageUrlVnAttribute()
    {
        return gen_url_file_s3($this->img_vn);
    }

    public function getImageUrlEnAttribute()
    {
        return gen_url_file_s3($this->img_en);
    }

    public function getPositionValueAttribute()
    {
        return config('constant.position_banner.' . $this->position);
    }

    public function getTypeValueAttribute()
    {
        return config('constant.role_frontend.' . $this->type);
    }
}
