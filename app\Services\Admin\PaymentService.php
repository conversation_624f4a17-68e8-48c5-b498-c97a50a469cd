<?php

namespace App\Services\Admin;

use App\Repositories\SubmitCvRepository;
use App\Repositories\TransactionRepository;
use App\Services\FileServiceS3;

class PaymentService
{
    protected $transactionRepository;
    protected $submitCvRepository;

    public function __construct(TransactionRepository $transactionRepository, SubmitCvRepository $submitCvRepository)
    {
        $this->transactionRepository = $transactionRepository;
        $this->submitCvRepository = $submitCvRepository;
    }

    public function indexService($submitCvId)
    {
        return $this->transactionRepository->getTransactionWithSubmitCvId($submitCvId);
    }

    public function create($params)
    {
        if (isset($params['file'])) {
            $arrPathFile = [];
            foreach ($params['file'] as $k => $file) {
                $pathFile =  FileServiceS3::getInstance()->uploadToS3($file, config('constant.sub_path_s3.transaction'));
                array_push($arrPathFile, $pathFile);
            }
            $params['file'] = json_encode($arrPathFile);
        }
        return $this->transactionRepository->create($params);
    }

    public function updatePaymentActual($submitCvId)
    {
        $countTotalMoney = $this->transactionRepository->countTotalMoneyWithSubmitCv($submitCvId);
        return $this->submitCvRepository->update($submitCvId, [], ['payment_actual' => $countTotalMoney]);
    }
}
