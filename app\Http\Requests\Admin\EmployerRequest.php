<?php

namespace App\Http\Requests\Admin;

use App\Rules\Admin\CheckEmailTypeRule;
use Illuminate\Foundation\Http\FormRequest;

class EmployerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'          => 'required',
            'email'         => ['required', 'email', new CheckEmailTypeRule('employer', $this->route('employer'))],
            'mobile'        => 'required|regex:/^[0-9()+.-]*$/|max:16',
            'mst'           => 'required',
            'company_id'    => 'required',
            'type_employer' => 'required',
            'user_role'     => $this->get('type_employer') == 'employee' ? 'required' : '',
        ];
    }

    public function messages()
    {
        return [
            'required'     => __('message.required'),
            'mobile.regex' => __('message.regex_phone'),
            'max'          => __('message.max', ['max' => 16]),
            'email'        => __('message.email'),
        ];
    }
}
