<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UpdateProfileEmployer extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($this->user->type == config('constant.role.rec')) {
            return (new MailMessage)
                ->view('email.updateProfileRec', ['name' => $this->user->name])
                ->subject('[HRI RECLAND] [CẬP NHẬT HỒ SƠ CÁ NHÂN THÀNH CÔNG]');

        } else {
            return (new MailMessage)
                ->view('email.updateProfileEmployer', ['name' => $this->user->name])
                ->subject('[HRI RECLAND] [CẬP NHẬT TÀI KHOẢN THÀNH CÔNG]');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        if ($this->user->type == config('constant.role.rec')) {
            $contentVi = Common::transLang($arrLangVi['ctv_congtacviencapnhathoso']);
            $contentEn = Common::transLang($arrLangEn['ctv_congtacviencapnhathoso']);
        } else {
            $contentVi = Common::transLang($arrLangVi['ntd_nhatuyendungcapnhathoso']);
            $contentEn = Common::transLang($arrLangEn['ntd_nhatuyendungcapnhathoso']);
        }

        return [
            'content_vi' => $contentVi,
            'content_en' => $contentEn,
            //            'routename'       => 'rec-warehousecv'
        ];
    }
}
