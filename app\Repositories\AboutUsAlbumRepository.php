<?php

namespace App\Repositories;

use App\Models\AboutUsAlbum;

class AboutUsAlbumRepository extends BaseRepository
{
    const MODEL = AboutUsAlbum::class;

    public function list($param, $paginate = false, $limit = 10)
    {
        $query = $this->query()->orderByDesc('id')->with('photos');
        if ($paginate){
            return $query->paginate($limit);
        }
        return $query;

    }

    public function getQuery($param)
    {
        return $this->query()->orderByDesc('id');
    }
}
