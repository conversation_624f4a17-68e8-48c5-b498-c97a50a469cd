<?php

namespace App\Services\Admin;

use App\Repositories\TestimonialRepository;
use App\Services\FileServiceS3;

class TestimonialService
{

    protected $testimonialRepository;

    public function __construct(TestimonialRepository $testimonialRepository)
    {
        $this->testimonialRepository = $testimonialRepository;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Tên khách hàng Vi'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Tên khách hàng En'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderLogo',
                    'data-mdata' => 'avatar_url',
                ),
                'value' => 'Avatar'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'type_value',
                ),
                'value' => 'Role'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'content_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Nội dung Vi'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'content_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Nội dung En'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'position_vi',
                    'data-orderable' => 'false'
                ),
                'value' => 'Vị trí công việc Vi'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'position_en',
                    'data-orderable' => 'false'
                ),
                'value' => 'Vị trí công việc En'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('testimonial-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction);
    }

    public function indexService($params, $order = array(), $paginate = false)
    {
        return $this->testimonialRepository->getListTestimonial($params, $order, $paginate);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function createService(array $request)
    {
        $request['avatar'] = FileServiceS3::getInstance()->uploadToS3($request['avatar'], config('constant.sub_path_s3.testimonial'));
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');

        return $this->testimonialRepository->create($request);
    }

    public function detailService($id)
    {
        return $this->testimonialRepository->find($id);
    }

    public function updateService(array $request, $id)
    {
        if (isset($request['avatar'])) {
            $request['avatar'] = FileServiceS3::getInstance()->uploadToS3($request['avatar'], config('constant.sub_path_s3.testimonial'));
        }
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        return $this->testimonialRepository->update($id, [], $request);
    }

}
