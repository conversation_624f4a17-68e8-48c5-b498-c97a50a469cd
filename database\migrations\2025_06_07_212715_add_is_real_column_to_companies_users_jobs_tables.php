<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Thêm trường is_real vào bảng companies
        Schema::table('companies', function (Blueprint $table) {
            $table->tinyInteger('is_real')->default(1)->after('id')->comment('1: thật, 0: fake');
            $table->index('is_real');
        });

        // Thêm trường is_real vào bảng users
        Schema::table('users', function (Blueprint $table) {
            $table->tinyInteger('is_real')->default(1)->after('id')->comment('1: thật, 0: fake');
            $table->index('is_real');
        });

        // Thêm trường is_real vào bảng job
        Schema::table('job', function (Blueprint $table) {
            $table->tinyInteger('is_real')->default(1)->after('id')->comment('1: thật, 0: fake');
            $table->index('is_real');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropIndex(['is_real']);
            $table->dropColumn('is_real');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['is_real']);
            $table->dropColumn('is_real');
        });

        Schema::table('job', function (Blueprint $table) {
            $table->dropIndex(['is_real']);
            $table->dropColumn('is_real');
        });
    }
};
