<?php

namespace App\Models;

use App\Helpers\Common;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class WareHouseCvSelling extends BaseModel implements Auditable
{
    use HasFactory, Notifiable, SoftDeletes;
    use \OwenIt\Auditing\Auditable;

    protected $table = 'warehouse_cv_sellings';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $audit_tags = [];

    protected $appends = [
        'cv_download',
        'cv_buy_user',
        'cv_evaluate_user',
        'created_at_value',
        'status_value',
        'authority_value',
        'ma_ctv_value',
        'price_format',
        'text_type_of_sale',
    ];
    public function addAuditTag($tag)
    {
        if (is_array($tag)) {
            $this->audit_tags = array_merge($this->audit_tags, $tag);
        } else {
            $this->audit_tags[] = $tag;
        }
    }

    public function wareHouseCv()
    {
        return $this->belongsTo(WareHouseCv::class, 'warehouse_cv_id', 'id');
    }

    public function wareHouseCvSave()
    {
        return $this->belongsTo(WareHouseCvSellingSave::class, 'id', 'warehouse_cv_selling_id');
    }

    public function wareHouseCvSaveWithUser()
    {
        if (auth('client')->user()) {
            return $this->belongsTo(WareHouseCvSellingSave::class, 'id', 'warehouse_cv_selling_id')->where('user_id', auth('client')->user()->id);
        }
        return $this->belongsTo(WareHouseCvSellingSave::class, 'id', 'warehouse_cv_selling_id');
    }

    public function wareHouseCvSellingQa()
    {
        return $this->hasMany(WareHouseCvSellingQa::class, 'warehouse_cv_selling_id', 'id');
    }

    public function wareHouseCvSellingBuy()
    {
        return $this->hasMany(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_id', 'id');
    }

    public function wareHouseCvSellingBuyNtd()
    {
        if (auth('client')->user()) {
            return $this->hasOne(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_id', 'id')->where('user_id', auth('client')->user()->id);
        }
        return $this->hasOne(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_id', 'id')->where('user_id', 0);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function getCvDownloadAttribute()
    {
        if (!empty($this->wareHouseCvSellingBuyNtd)) {
            if (($this->type_of_sale == 'cv' && $this->wareHouseCvSellingBuyNtd->status_recruitment == 18)
                || ($this->type_of_sale == 'interview' && ($this->wareHouseCvSellingBuyNtd->status_recruitment == 8 || $this->wareHouseCvSellingBuyNtd->status_recruitment == 10))
                || ($this->type_of_sale == 'onboard' && ($this->wareHouseCvSellingBuyNtd->status_recruitment == 14 || $this->wareHouseCvSellingBuyNtd->status_recruitment == 16))
            ) {
                return $this->wareHouseCv->url_cv_public;
            }
        }

        return $this->wareHouseCv ? $this->wareHouseCv->url_cv_public : '';
    }

    /**
     * @return int
     * check xem NTD Đã mua CV selling hay chưa
     */
    public function getCvBuyUserAttribute()
    {
        if (!empty($this->wareHouseCvSellingBuyNtd)) {
            return 1; //NTD đã mua cv
        }

        return 0; //NTD chưa mua cv
    }


    public function wareHouseCvSellingEvaluateNtd()
    {
        if (auth('client')->user()) {
            return $this->hasOne(WareHouseCvSellingEvaluate::class, 'warehouse_cv_selling_id', 'id')->where('user_id', auth('client')->user()->id);
        }
        return $this->hasOne(WareHouseCvSellingEvaluate::class, 'warehouse_cv_selling_id', 'id');
    }

    /**
     * @return int
     * check xem NTD Đã Đánh giá CV selling hay chưa
     */
    public function getCvEvaluateUserAttribute()
    {
        if (!empty($this->wareHouseCvSellingEvaluateNtd)) {
            return 1; //NTD đã đánh giá cv
        }

        return 0; //NTD chưa đánh giá cv
    }


    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i') : null;
    }
    public function getStatusValueAttribute()
    {
        return $this->status == 1 ? 'Hủy đăng bán' : ($this->status == 0 ? 'Đăng bán' : 'Chờ duyệt');
    }
    public function getAuthorityValueAttribute()
    {
        return $this->authority == 0 ? 'Không ủy quyền' : ($this->authority == 1 ? 'Chờ duyệt ủy quyền' : ($this->authority == 2 ? 'Đã xác nhận uỷ quyền' : 'Từ chối ủy quyền'));
    }
    public function getMaCtvValueAttribute()
    {
        return !empty($this->wareHouseCv->user_id) && !empty($this->user) ? '#' . str_pad($this->wareHouseCv->user_id, 6, 0, STR_PAD_LEFT) . '<br />' . $this->user->name : '';
    }
    public function getPriceFormatAttribute()
    {
        return Common::formatNumber($this->price) . ' VNĐ';
    }

    public function getTextTypeOfSaleAttribute()
    {
        return $this->type_of_sale != 'cv' ? ($this->type_of_sale == 'onboard' ? config('settings.' . app()->getLocale() . '.market_cv.cho_onboard') : config('settings.' . app()->getLocale() . '.market_cv.cho_phongvan')) : config('settings.' . app()->getLocale() . '.market_cv.muadexem');
    }
}
