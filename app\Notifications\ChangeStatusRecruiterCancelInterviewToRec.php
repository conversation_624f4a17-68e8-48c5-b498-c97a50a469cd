<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusRecruiterCancelInterviewToRec extends Notification implements ShouldQueue
{

    use Queueable;

    protected $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }


    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $employerName = $this->wareHouseCvSellingBuy->employer->name;
        $recName = $this->wareHouseCvSellingBuy->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $position = $this->wareHouseCvSellingBuy->job->name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $url = route('rec-cv-sold',['cv_sold' =>  $this->wareHouseCvSellingBuy->id,'open_complain' => 1]);
        $link = route('rec-cv-selling', ['open_popup' => 1]);
        return (new MailMessage)
            ->view('email.changeStatusRecruiterCancelInterviewToRec', [
                'recName' => $recName,
                'employerName' => $employerName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'url' => $url,
                'link' => $link,
            ])
            ->subject('[Recland] Thông báo đóng trường hợp giới thiệu ứng viên '.$candidateName.' vị trí '.$position. '  của công ty  ' .$companyName);

    }


}

