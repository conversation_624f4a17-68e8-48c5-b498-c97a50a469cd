# Recruitment Workflow

This document outlines the recruitment workflow in the Recland application, from CV submission by a collaborator to the final hiring decision by an employer.

## User Roles

*   **Collaborators (CTV):** Recruiters who find and submit candidates for jobs.
*   **Employers (NTD):** Companies that post jobs and hire candidates.
*   **Administrators:** Users who manage the platform.

## Recruitment Process

The recruitment process consists of the following stages:

1.  **CV Submission:** A collaborator submits a candidate's CV for a job. The CV can be submitted in one of three ways:
    *   **Data CV:** The collaborator provides the candidate's information directly.
    *   **Interview:** The collaborator submits the candidate's CV and schedules an interview.
    *   **Onboard:** The collaborator submits the candidate's CV and schedules an onboarding session.
2.  **Candidate Confirmation:** The candidate receives an email asking them to confirm their application. If the candidate does not confirm within 7 days, the application is automatically rejected.
3.  **Employer Review:** The employer reviews the candidate's CV and decides whether to proceed with the recruitment process.
4.  **Interview:** If the employer decides to proceed, they schedule an interview with the candidate. The collaborator and candidate are notified of the interview schedule.
5.  **Onboarding:** If the interview is successful, the employer schedules an onboarding session with the candidate. The collaborator and candidate are notified of the onboarding schedule.
6.  **Hiring:** If the onboarding is successful, the employer hires the candidate. The collaborator is paid a commission based on the type of submission.

## Statuses

The recruitment process has the following statuses:

*   **Waiting candidate confirm:** The candidate has not yet confirmed their application.
*   **Waiting setup interview:** The employer has not yet scheduled an interview.
*   **Waiting confirm calendar:** The collaborator has not yet confirmed the interview schedule.
*   **Waiting Interview:** The interview is scheduled but has not yet taken place.
*   **Pass Interview:** The candidate has passed the interview.
*   **Fail Interview:** The candidate has failed the interview.
*   **Offering:** The employer has made an offer to the candidate.
*   **Reject Offer:** The candidate has rejected the offer.
*   **Waiting onboard:** The onboarding session is scheduled but has not yet taken place.
*   **Trial work:** The candidate is in a trial period.
*   **Cancel onboard:** The candidate has canceled the onboarding session.
*   **Success Recruitment:** The candidate has been hired.
*   **Fail trail work:** The candidate has failed the trial period.

## Email Notifications

The application sends email notifications to all parties involved in the recruitment process, including the collaborator, employer, and candidate. The following email notifications are sent:

*   **Candidate Confirmation:** Sent to the candidate when their CV is submitted.
*   **Interview Schedule:** Sent to the collaborator and candidate when an interview is scheduled.
*   **Onboarding Schedule:** Sent to the collaborator and candidate when an onboarding session is scheduled.
*   **Status Change:** Sent to all parties when the status of the recruitment process changes.

## Automated Processes

The application uses automated processes to handle time-based events, such as:

*   **Candidate Confirmation:** If the candidate does not confirm their application within 7 days, the application is automatically rejected.
*   **Interview Payment:** The collaborator is paid for the interview 24 hours after the interview takes place.
*   **Onboarding Payment:** The collaborator is paid for the onboarding in three installments: 15% after 30 days, 10% after 45 days, and 75% after 67 days.
*   **Successful Recruitment:** If the employer does not change the status of the recruitment process within 67 days of the onboarding session, the candidate is automatically considered hired.