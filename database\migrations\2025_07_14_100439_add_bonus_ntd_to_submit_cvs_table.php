<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('submit_cvs', 'bonus_ntd')) {
            Schema::table('submit_cvs', function (Blueprint $table) {
                $table->integer('bonus_ntd')->nullable()->after('bonus')->comment('Chi phí mua của NTD');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('submit_cvs', function (Blueprint $table) {
            $table->dropColumn('bonus_ntd');
        });
    }
};
