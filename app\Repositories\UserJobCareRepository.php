<?php

namespace App\Repositories;

use App\Models\UserJobCare;

class UserJobCareRepository extends BaseRepository
{

    const MODEL = UserJobCare::class;

    public function getJobCare($userId, $jobId, $type)
    {
        return $this->query()
            ->where('user_id', $userId)
            ->where('job_id', $jobId)
            ->where('type', $type)
            ->first();
    }

    public function createJobCare($userId, $jobId, $type)
    {
        return $this->create([
            'user_id' => $userId,
            'job_id' => $jobId,
            'type' => $type,
        ]);
    }

    public function countUserJobCare($userId, $jobId, $type)
    {
        return $this->query()->where('user_id', $userId)
            ->where('job_id', $jobId)
            ->where('type', $type)->count() > 0;
    }

    /**
     * <PERSON><PERSON><PERSON> danh sách job đã save của user với pagination và search
     * 
     * @param int $userId
     * @param array $params
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getSavedJobs($userId, $params = [])
    {
        $query = $this->query()
            ->with(['job.company', 'job.user'])
            ->where('user_id', $userId)
            ->where('type', 'save');

        // Search theo tên job
        if (isset($params['search']) && !empty($params['search'])) {
            $search = $params['search'];
            $query->whereHas('job', function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%');
            });
        }

        // Filter theo company
        if (isset($params['company_id']) && !empty($params['company_id'])) {
            $query->whereHas('job', function ($q) use ($params) {
                $q->where('company_id', $params['company_id']);
            });
        }

        // Filter theo career
        if (isset($params['career']) && !empty($params['career'])) {
            $query->whereHas('job', function ($q) use ($params) {
                $q->where('career', 'like', '%' . $params['career'] . '%');
            });
        }

        // Filter theo job status
        if (isset($params['job_status']) && $params['job_status'] !== '') {
            $query->whereHas('job', function ($q) use ($params) {
                $q->where('status', $params['job_status']);
            });
        }

        // Sắp xếp
        $orderBy = isset($params['order_by']) ? $params['order_by'] : 'created_at';
        $orderDir = isset($params['order_dir']) ? $params['order_dir'] : 'desc';
        $query->orderBy($orderBy, $orderDir);

        // Pagination
        $perPage = isset($params['per_page']) ? $params['per_page'] : config('constant.limitPaginate', 10);

        return $query->paginate($perPage);
    }
}
