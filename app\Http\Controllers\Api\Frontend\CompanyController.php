<?php

namespace App\Http\Controllers\Api\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Http\Resources\Frontend\Api\CompanyCollection;
use App\Http\Resources\Frontend\Api\JobCollection;
use App\Models\Job;
use Illuminate\Http\Request;
use App\Services\Frontend\CompanyService;
use App\Services\Frontend\SettingService;
use App\Http\Resources\Frontend\Api\CompanyResource;
use App\Http\Resources\Frontend\Api\JobResource;

class CompanyController extends Controller
{
    protected $settingService;
    protected $companyService;
    protected $jobService;

    public function __construct(
        SettingService $settingService,
        CompanyService $companyService,
    ) {
        $this->settingService = $settingService;
        $this->companyService = $companyService;
    }

    public function search(Request $request)
    {
        $limit             = config('settings.global.limit_company_search_page');
        $lang              = app()->getLocale();
        $str               = 'company';
        $priority          = config('constant.priority');
        $request_data      = $request->all();
        $page_out_standing = $request_data['page_out_standing'] ?? 1;
        $page              = $request_data['page'] ?? 1;
        if ($page_out_standing < 1) {
            $page_out_standing = 1;
        }
        if ($page < 1) {
            $page = 1;
        }
        $request->merge(['page' => $page_out_standing]);
        $companyOutStanding   = $this->companyService->getListByTypePriority(array_keys($priority)[0], true, $request->all(), 4);
        $request->merge(['page' => $page]);
        $company              = $this->companyService->getListByTypePriority(0, true, $request_data, $limit);
        $companyOutStanding   = new CompanyCollection($companyOutStanding);
        $company              = new CompanyCollection($company);
        return response()->json([
            'company'            => $company->response()->getData(),
            'companyOutStanding' => $companyOutStanding->response()->getData(),
        ]);
    } 

    public function detail($slug)
    {
        $company = $this->companyService->detailCompany($slug);
        $company = new CompanyResource($company);
        return response()->json([
            'company' => $company->response()->getData(),
            'job_list' => JobResource::collection($company->jobsActive),
        ]);
    }
}
