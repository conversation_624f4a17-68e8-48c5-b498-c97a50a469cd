<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class BuyCvSuccess extends Notification implements ShouldQueue
{
    use Queueable;

    protected $user;
    protected $cvSelling;
    protected $ntd;
    protected $wareHouseCvSellingBuy;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $cvSelling, $ntd, $wareHouseCvSellingBuy)
    {
        $this->user = $user;
        $this->cvSelling = $cvSelling;
        $this->ntd = $ntd;
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->user->name;
        $candidateName = $this->cvSelling->warehouseCv->candidate_name;
        $companyName = $this->ntd->name;

        $type = $this->cvSelling->type_of_sale;

        if ($type == 'cv'){
            $candidateJobTitle = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $candidateJobTitle = $this->wareHouseCvSellingBuy->job->name;
        }

        $reward = number_format($this->cvSelling->price);
        $linkLuotban = route('rec-cv-sold') . '?cv_sold=' . $this->wareHouseCvSellingBuy->id;
        if ($this->cvSelling->authority == 2) {
            return (new MailMessage)
                ->view('email.mua_cv_thanhcong_uyquyen_admin', [
                    'name' => $name,
                    'candidateName' => $candidateName,
                    'companyName' => $companyName,
                    'candidateJobTitle' => $candidateJobTitle,
                    'type'  => $type,
                    'reward' => $reward,
                    'linkLuotban' => $linkLuotban
                ])
                ->subject('[UỶ QUYỀN] Thông báo có 1 ứng viên ' . $candidateName . ' ' . $type . ' đã uỷ quyền');
        } else {
            $linkCTV = route('market-cv');
            if($type == 'cv'){
                return (new MailMessage)
                    ->view('email.mua_cv_thanhcong_ctv', [
                        'name' => $name,
                        'candidateName' => $candidateName,
                        'companyName' => $companyName,
                        'candidateJobTitle' => $candidateJobTitle,
                        'type'  => $type,
                        'reward' => $reward,
                        'linkLuotban' => $linkLuotban
                    ])
                    ->subject('[Recland] Thông báo bạn có 1 lượt mở CV ứng viên ' . $candidateName);
            }else{
                return (new MailMessage)
                    ->view('email.mua_cv_inteview_onboard_thanhcong_ctv', [
                        'name' => $name,
                        'candidateName' => $candidateName,
                        'companyName' => $companyName,
                        'candidateJobTitle' => $candidateJobTitle,
                        'type'  => $type,
                        'reward' => $reward,
                        'linkLuotban' => $linkLuotban
                    ])
                    ->subject('[Recland] Thông báo bạn có 1 lượt mở CV ứng viên ' . $candidateName . ' ' . $type );
            }

        }

    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [

        ];
    }
}
