<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\WalletTransaction;
use App\Models\Company;
use App\Models\Job;
use App\Models\SubmitCv;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TransactionListController extends Controller
{
    /**
     * Hiển thị trang danh sách giao dịch
     */
    public function index(Request $request)
    {
        // Lấy danh sách admin có quyền company.rotation
        $admins = $this->getAdminsWithCompanyRotationPermission();

        // Lấy widget tổng hợp
        $summary = $this->getSummaryData($request);

        // Lấy danh sách giao dịch
        $transactions = $this->getTransactions($request);

        return view('admin.pages.transaction-list.index', compact('admins', 'summary', 'transactions'));
    }

    /**
     * L<PERSON>y danh sách giao dịch với bộ lọc
     */
    private function getTransactions(Request $request)
    {
        $query = WalletTransaction::query()
            ->with([
                'wallet.user.company.admin',
                'wallet.user.company',
                'object' // Để lấy thông tin object liên quan (SubmitCv, etc.)
            ])
            ->join('wallets', 'wallet_transactions.wallet_id', '=', 'wallets.id')
            ->join('users', 'wallets.user_id', '=', 'users.id')
            ->select(
                'wallet_transactions.*',
                'users.name as user_name',
                'users.email as user_email',
                'users.type as user_type',
                'users.company_id'
            );

        // Bộ lọc theo thời gian
        if ($request->filled('date_from')) {
            $query->where('wallet_transactions.created_at', '>=', $request->date_from . ' 00:00:00');
        }
        if ($request->filled('date_to')) {
            $query->where('wallet_transactions.created_at', '<=', $request->date_to . ' 23:59:59');
        }

        // Bộ lọc theo admin
        if ($request->filled('admin_id')) {
            $query->join('companies', 'users.company_id', '=', 'companies.id')
                ->where('companies.admin_id', $request->admin_id);
        }

        // Bộ lọc theo loại tài khoản
        if ($request->filled('user_type')) {
            $query->where('users.type', $request->user_type);
        }

        // Bộ lọc theo công ty
        if ($request->filled('company_id')) {
            $query->where('users.company_id', $request->company_id);
        }

        // Bộ lọc theo loại giao dịch (tiền vào/tiền ra)
        if ($request->filled('transaction_type')) {
            switch ($request->transaction_type) {
                case 'income':
                    $query->where('wallet_transactions.amount', '>', 0);
                    break;
                case 'expense':
                    $query->where('wallet_transactions.amount', '<', 0);
                    break;
            }
        }

        // Sắp xếp mới nhất lên đầu
        $query->orderBy('wallet_transactions.created_at', 'desc');

        $transactions = $query->paginate(20);

        // Thêm thông tin ứng tuyển cho mỗi giao dịch
        $transactions->getCollection()->transform(function ($transaction) {
            $transaction->job_info = $this->getJobInfo($transaction);
            return $transaction;
        });

        return $transactions;
    }

    /**
     * Lấy thông tin job và ứng tuyển liên quan đến giao dịch
     */
    private function getJobInfo($transaction)
    {
        $info = [
            'job' => null,
            'submit_cv' => null,
            'candidate_name' => null,
            'candidate_email' => null,
            'job_name' => null,
            'status' => null
        ];

        // Kiểm tra object liên quan đến giao dịch
        if ($transaction->object && $transaction->object instanceof SubmitCv) {
            $submitCv = $transaction->object;
            $job = $submitCv->job;

            if ($job) {
                $info['job'] = $job;
                $info['job_name'] = $job->name;
                $info['submit_cv'] = $submitCv;
                $info['status'] = $submitCv->status_value ?? '';

                // Lấy thông tin ứng viên
                if ($submitCv->submitCvMeta) {
                    $info['candidate_name'] = $submitCv->submitCvMeta->candidate_name;
                    $info['candidate_email'] = $submitCv->submitCvMeta->candidate_email;
                } elseif ($submitCv->user) {
                    $info['candidate_name'] = $submitCv->user->name;
                    $info['candidate_email'] = $submitCv->user->email;
                }
            }
        }

        return $info;
    }

    /**
     * Lấy dữ liệu tổng hợp
     */
    private function getSummaryData(Request $request)
    {
        $query = WalletTransaction::query()
            ->join('wallets', 'wallet_transactions.wallet_id', '=', 'wallets.id')
            ->join('users', 'wallets.user_id', '=', 'users.id');

        // Áp dụng cùng bộ lọc như danh sách
        if ($request->filled('date_from')) {
            $query->where('wallet_transactions.created_at', '>=', $request->date_from . ' 00:00:00');
        }
        if ($request->filled('date_to')) {
            $query->where('wallet_transactions.created_at', '<=', $request->date_to . ' 23:59:59');
        }

        if ($request->filled('admin_id')) {
            $query->join('companies', 'users.company_id', '=', 'companies.id')
                ->where('companies.admin_id', $request->admin_id);
        }

        if ($request->filled('user_type')) {
            $query->where('users.type', $request->user_type);
        }

        if ($request->filled('company_id')) {
            $query->where('users.company_id', $request->company_id);
        }

        // Áp dụng bộ lọc loại giao dịch vào widget tổng hợp
        if ($request->filled('transaction_type')) {
            switch ($request->transaction_type) {
                case 'income':
                    $query->where('wallet_transactions.amount', '>', 0);
                    break;
                case 'expense':
                    $query->where('wallet_transactions.amount', '<', 0);
                    break;
            }
        }

        $summary = $query->select(
            DB::raw('SUM(CASE WHEN wallet_transactions.amount > 0 THEN wallet_transactions.amount ELSE 0 END) as total_income'),
            DB::raw('SUM(CASE WHEN wallet_transactions.amount < 0 THEN ABS(wallet_transactions.amount) ELSE 0 END) as total_expense')
        )->first();

        return [
            'total_income' => $summary->total_income ?? 0,
            'total_expense' => $summary->total_expense ?? 0
        ];
    }

    /**
     * Lấy danh sách admins có quyền company.rotation
     */
    private function getAdminsWithCompanyRotationPermission()
    {
        $admins = User::where('type', 'admin')
            ->where('is_active', 1)
            ->with('roles')
            ->get()
            ->filter(function ($user) {
                return $this->hasCompanyRotationPermission($user);
            });

        return $admins->pluck('name', 'id')->prepend('-- Tất cả --', '');
    }

    /**
     * Kiểm tra user có quyền company.rotation không
     */
    private function hasCompanyRotationPermission(User $user): bool
    {
        $roles = $user->roles;

        foreach ($roles as $role) {
            $permissions = json_decode($role->permission, true);

            if (is_array($permissions) && in_array('company.rotation', $permissions)) {
                return true;
            }
        }

        return false;
    }

    /**
     * AJAX: Tìm kiếm công ty cho autocomplete
     */
    public function searchCompanies(Request $request)
    {
        $search = $request->get('q', '');

        $companies = Company::select('id', 'name', 'mst')
            ->where('is_active', 1)
            ->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('mst', 'like', '%' . $search . '%');
            })
            ->orderBy('name')
            ->limit(20)
            ->get();

        return response()->json($companies->map(function ($company) {
            return [
                'id' => $company->id,
                'text' => $company->name . ' - ' . $company->mst
            ];
        }));
    }
}
