<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecSendDiscusToEmployer extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $warehouseCvSellingBuyDiscuss;

    public function __construct($wareHouseCvSellingBuy,$warehouseCvSellingBuyDiscuss)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->warehouseCvSellingBuyDiscuss = $warehouseCvSellingBuyDiscuss;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $link = route('employer-cv-bought',['discuss' => $this->wareHouseCvSellingBuy->id]);;
        return (new MailMessage)
            ->view('email.recSendDiscusToEmployer', [
                'link' => $link,
                'companyName' => $companyName,
                'position' => $position,
                'candidateName' => $candidateName,
                'comment' => $this->warehouseCvSellingBuyDiscuss->comment,
            ])
            ->subject('[Recland] Bạn nhận được 1 thảo luận mới ứng viên '.$candidateName);

    }


}
