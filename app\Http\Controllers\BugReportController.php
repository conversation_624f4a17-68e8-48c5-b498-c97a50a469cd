<?php

namespace App\Http\Controllers;

use App\Models\BugReport;
use App\Notifications\BugReportNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Notification;
use App\Models\User;
use App\Services\FileServiceS3;

class BugReportController extends Controller
{
    /**
     * Store a newly created bug report in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'description' => 'required|string|max:2000',
            'url' => 'required|string|max:500',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
        ]);

        $imagePath = null;
        if ($request->hasFile('image')) {
            // $imagePath = $request->file('image')->store('bug-reports', 'public');
            $file_public = FileServiceS3::getInstance()->uploadToS3($request->image, config('constant.sub_path_s3.bug-report'));
            // dd($file_public);
            $imagePath = gen_url_file_s3($file_public);
        }
        // dd($imagePath);

        $bugReport = BugReport::create([
            'user_id' => Auth()->guard('client')->user()->id,
            'url' => $request->url,
            'description' => $request->description,
            'image_path' => $imagePath,
            'status' => 'pending'
        ]);

        // Gửi notification cho admin
        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        if ($admins->count() > 0) {
            Notification::send($admins, new BugReportNotification($bugReport));
        }

        return response()->json([
            'success' => true,
            'message' => 'Báo cáo lỗi đã được gửi thành công. Chúng tôi sẽ xem xét và phản hồi sớm nhất có thể.'
        ]);
    }
}
