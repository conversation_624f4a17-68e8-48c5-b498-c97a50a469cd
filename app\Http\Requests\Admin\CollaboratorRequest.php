<?php

namespace App\Http\Requests\Admin;

use App\Rules\Admin\CheckEmailTypeRule;
use Illuminate\Foundation\Http\FormRequest;

class CollaboratorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $this->session()->flash('action', 'edit');
        return [
            'name' => 'required',
            'email' => ['required','email', new CheckEmailTypeRule('rec',$this->route('collaborator'))],
            'birthday' => 'required',
            'avatar' => 'mimes:jpeg,jpg,png|max:5120'
        ];
    }

    public function messages()
    {
        return [
            'required' => __('message.required'),
            'mimes' => __('message.mimes'),
            'max' => __('message.file_max'),
        ];
    }


}
