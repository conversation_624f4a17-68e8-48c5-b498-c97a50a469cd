<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class Localization
{
    public function handle(Request $request, Closure $next): mixed
    {
        $language = Session::get('language', config('app.locale'));

        $language = match ($language) {
            config('constant.language.en') => config('constant.language.en'),
            default => config('constant.language.vi'),
        };

        app()->setLocale($language);


        return $next($request);
    }
}
