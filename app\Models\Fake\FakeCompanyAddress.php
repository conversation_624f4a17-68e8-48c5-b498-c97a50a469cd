<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeCompanyAddress extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'company_addresses';

    protected $fillable = [
        'company_id',
        'location_id',
        'address',
        'map',
        'is_headquarter',
    ];

    public function company()
    {
        return $this->belongsTo(FakeCompany::class, 'company_id', 'id');
    }
}
