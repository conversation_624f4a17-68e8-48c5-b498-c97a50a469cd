<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class WalletTransaction extends Model
{
    use HasFactory, CrudTrait;

    protected $table = 'wallet_transactions';
    protected $guarded = ['id'];

    /**
     * Lấy ví liên quan đến giao dịch
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function wallet()
    {
        return $this->belongsTo(Wallet::class);
    }

    /**
     * Lấy đối tượng liên quan đến giao dịch (polymorphic)
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function object()
    {
        return $this->morphTo();
    }

    public function getObjectNameAttribute()
    {
        $object = $this->object;
        // dd($object);
        if ($object && $object instanceof SubmitCv) {
            return $object->submitCvMeta->candidate_name;
        } elseif ($object && $object instanceof WareHouseCvSellingBuy) {
            return $object->wareHouseCvSelling->wareHouseCv->candidate_name;
        }
        return null;
    }

    /**
     * Lấy người dùng liên quan đến giao dịch thông qua ví
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function user()
    {
        return $this->hasOneThrough(
            User::class,
            Wallet::class,
            'id', // Khóa ngoại trên bảng trung gian (wallets)
            'id', // Khóa chính trên bảng đích (users)
            'wallet_id', // Khóa ngoại trên bảng nguồn (wallet_transactions)
            'user_id' // Khóa ngoại trên bảng trung gian (wallets) liên kết đến bảng đích (users)
        );
    }

    /**
     * Lấy loại người dùng (employer/recruitment)
     *
     * @return string
     */
    public function getUserTypeAttribute()
    {
        $user = $this->wallet->user ?? null;
        if (!$user) return 'N/A';

        switch ($user->type) {
            case 'employer':
                return 'Nhà tuyển dụng';
            case 'rec':
                return 'Cộng tác viên';
            case 'admin':
                return 'Quản trị viên';
            default:
                return 'N/A';
        }
    }

    /**
     * Lấy tên người dùng
     *
     * @return string
     */
    public function getUserNameAttribute()
    {
        $user = $this->wallet->user ?? null;
        if (!$user) return 'N/A';

        return $user->name ?? $user->email ?? 'N/A';
    }

    /**
     * Format số tiền giao dịch
     *
     * @return string
     */
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 0, ',', '.') . ' đ';
    }

    /**
     * Format số dư sau giao dịch
     *
     * @return string
     */
    public function getFormattedBalanceAfterAttribute()
    {
        return number_format($this->balance_after, 0, ',', '.') . ' đ';
    }

    /**
     * Tạo giao dịch mới
     *
     * @param Wallet $wallet Ví liên quan
     * @param float $amount Số tiền thay đổi (dương: cộng, âm: trừ)
     * @param string $note Ghi chú
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan
     * @return WalletTransaction
     */
    public static function createTransaction(
        Wallet $wallet,
        float $amount,
        string $note,
        ?string $type = null,
        $transactionable = null,
        $balance_after = null,
        $created_at = null
    ): self {
        $transaction = new self();
        $transaction->wallet_id = $wallet->id;
        $transaction->amount = $amount;
        $transaction->balance_after = $balance_after ?? $wallet->amount;
        $transaction->note = $note;
        $transaction->type = $type;
        $transaction->created_at = $created_at ?? now();

        if ($transactionable) {
            $transaction->object()->associate($transactionable);
        }

        $transaction->save();

        return $transaction;
    }
    /**
     * Tạo giao dịch mới
     *
     * @param Wallet $wallet Ví liên quan
     * @param float $amount Số tiền thay đổi (dương: cộng, âm: trừ)
     * @param string $note Ghi chú
     * @param string|null $type Loại giao dịch
     * @param mixed|null $transactionable Đối tượng liên quan
     * @return WalletTransaction
     */
    public static function createTransactionPrice(
        Wallet $wallet,
        float $price,
        string $note,
        ?string $type = null,
        $transactionable = null,
        $balance_after = null,
        $created_at = null
    ): self {
        $transaction = new self();
        $transaction->wallet_id = $wallet->id;
        $transaction->amount = $price;
        $transaction->balance_after = $balance_after ?? $wallet->price;
        $transaction->note = $note;
        $transaction->type = $type;
        $transaction->created_at = $created_at ?? now();
        if ($transactionable) {
            $transaction->object()->associate($transactionable);
        }

        $transaction->save();

        return $transaction;
    }
}