<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Mail\ResendEmailLog as ResendEmailLogEmail;
use App\Models\EmailLog;
use Illuminate\Support\Facades\Mail;

class ResendEmailLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:resend-log {log_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi lại mail trong log trong trường hợp gửi lỗi';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $log_id = $this->argument('log_id');
        $email = EmailLog::findOrfail($log_id);
        if (!$email) {
            $this->error("Email not found");
            return false;
        }
        $to = json_decode($email->to);
        if (count($to)) {
            foreach ($to as $t) {
                try {
                    Mail::send([], [], function ($message) use ($email, $t) {
                        $message->to($t)
                            ->subject($email->subject)
                            ->html($email->html_content);
                    });
                    $this->info($t . ":\t\t" . $email->subject);
                } catch (\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
        }



        $this->info($this->description);

        return Command::SUCCESS;
    }
}