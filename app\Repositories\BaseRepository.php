<?php

namespace App\Repositories;

use App\Models\User;
use DB;

class BaseRepository
{
    const MODEL = User::class;

    public function optionQuery($options, $query = null)
    {
        if (is_null($query)) {
            $query = $this->query();
        }
        foreach ($options as $key => $option) {
            if (is_array($option)) {
                if (strtolower($option[1]) == "in") {
                    $query->whereIn($option[0], $option[2]); //["start","in",[1,2,3]]
                } else {
                    $query->where($option[0], $option[1], $option[2]); //["start",">",666]
                }
            } else {
                $query->where($key, $option);
            }
        }
        return $query;
    }

    public function getListRelationShip(array $relations = [], array $where = [], $orderBy = null, $paginate = false, $limit = null, $sort = 'asc', $orderBys= [])
    {
        $builder = $this->optionQuery($where)->with($relations);
        if (!empty($orderBy)) {
            $builder = $builder->orderBy($orderBy, $sort);
        }
        if(!empty($orderBys) && count($orderBys)){
            foreach ($orderBys as $key=>$value){
                $builder = $builder->orderBy($key, $value);
            }
        }
        if ($paginate) {
            $result = $builder->paginate(!empty($limit) ? $limit : 15);
        } else {
            $result = $builder->get();
        }

        return $result;
    }

    public function getAll($options = [], $order_by = null, $sort = 'asc')
    {
        $query = $this->optionQuery($options);
        if (!empty($order_by)) {
            $query = $query->orderBy($order_by, $sort);
        }
        return $query->get();
    }

    public function getCount($options = [])
    {
        $query = $this->optionQuery($options);
        return $query->count();
    }

    public function paginated($options = [], $page = 1, $limit = 15, $order_by = 'created_at', $sort = 'desc')
    {
        $page = (int)$page;
        $limit = (int)$limit;
        if ($page < 0) {
            $page = 1;
        }
        if ($limit > 100) {
            $limit = 100;
        }
        $query = $this->optionQuery($options);
        $query->orderBy($order_by, $sort);
        $query->skip(($page - 1) * $limit);
        $query->take($limit);
        return $query->get();
    }

    public function paginationOffset($options = [], $limit = 15, $offset_position = 0, $condition = "<", $order_by = 'created_at', $sort = 'desc')
    {
        $limit = (int)$limit;
        if ($limit > 100) {
            $limit = 100;
        }
        $query = $this->optionQuery($options);
        if ($offset_position > 0) {
            $offset_position = date("Y-m-d H:i:s", $offset_position);
            $query->where($order_by, $condition, $offset_position);
        }
        $query->orderBy($order_by, $sort);
        $query->take($limit);
        return $query->get();
    }

    public function getFirst($options = [])
    {
        $query = $this->optionQuery($options);
        return $query->first();
    }

    public function getFirstRelation($relation = [], $options = [])
    {
        $query = $this->optionQuery($options);
        if (!empty($relation)) {
            $query = $query->with($relation);
        }
        return $query->first();
    }

    public function find($id, $options = [])
    {
        $query = $this->optionQuery($options);
//        dd($query->findOrFail($id));
        return $query->find($id);
        // return $query->findOrFail($id);
    }

    public function create($attributes = [])
    {
        return $this->query()->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function insert($attributes = [])
    {
        return $this->query()->insert($attributes);
    }

    public function update($id, $options = [], $attributes = [])
    {
        $result = $this->find($id, $options);

        if ($result) {
            $result->addAuditTag([get_class($this) . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'id' => $id]);
            $result->update($attributes);
            return $result;
        }
        return false;
    }

    public function delete($id, $options = [])
    {
        $result = $this->find($id, $options);
        if ($result) {
            $result->delete();
            return true;
        }
        return false;
    }

    public function increment($id, $attribute, $valueIncrement = 1)
    {
        $result = $this->find($id);
        if ($result) {
            $result->increment($attribute, $valueIncrement);
            return $result;
        }
        return false;
    }

    public function decrement($id, $attribute, $valueDecrement = 1)
    {
        $result = $this->find($id);
        if ($result) {
            $result->decrement($attribute, $valueDecrement);
            return $result;
        }
        return false;
    }

    public function query()
    {
        return call_user_func(static::MODEL . '::query');
    }

}
