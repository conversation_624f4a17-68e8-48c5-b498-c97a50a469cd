# Tài liệu Đặc tả Yêu cầu <PERSON>ần mềm (SRS) - RecLand
## Bộ Tài liệu Hoàn chỉnh

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> g<PERSON>:** AI Assistant  
**<PERSON><PERSON><PERSON> đích:** Tài liệu đặc tả chi tiết để tái xây dựng hệ thống RecLand

---

## 📋 Mục lục Tài liệu

### 1. [Tổng quan Hệ thống](01_SRS_Tong_Quan_He_Thong.md)
- **M<PERSON><PERSON> đích:** Giới thiệu tổng quan về hệ thống RecLand
- **Nội dung chính:**
  - Mục đích và tầm nhìn của hệ thống
  - Phạm vi hoạt động và đối tượng người dùng
  - Kiến trúc tổng thể và technology stack
  - Các module chính và luồng nghiệp vụ
  - Tích hợp bên ngoài và bảo mật
  - <PERSON><PERSON><PERSON> nghĩa thuật ngữ

### 2. [C<PERSON><PERSON> trú<PERSON> sở Dữ liệu](02_SRS_Cau_Truc_Co_So_Du_Lieu.md)
- **Mục đích:** Mô tả chi tiết cấu trúc database và mối quan hệ
- **Nội dung chính:**
  - Tổng quan database engine và naming convention
  - Bảng core entities (users, companies, jobs)
  - Hệ thống Warehouse CV và CV Selling
  - Hệ thống Submit CV và metadata
  - Payment & Wallet system
  - Bảng history, audit và supporting data
  - Indexes, constraints và optimization

### 3. [Chức năng Nghiệp vụ Chính](03_SRS_Chuc_Nang_Nghiep_Vu_Chinh.md)
- **Mục đích:** Mô tả chi tiết logic nghiệp vụ và business rules
- **Nội dung chính:**
  - Luồng MarketCV (mua bán CV từ kho)
  - Luồng Submit CV (ứng tuyển vào job)
  - Hệ thống commission và payment
  - Business rules và validation
  - Status management và error handling
  - Workflow automation và integration

### 4. [Hệ thống Jobs/Queue/Scheduler](04_SRS_He_Thong_Jobs_Queue_Scheduler.md)
- **Mục đích:** Tài liệu hóa tất cả background jobs và scheduled tasks
- **Nội dung chính:**
  - Payment & commission jobs
  - Status transition jobs
  - Timeout & expiry jobs
  - Notification & email jobs
  - Refund & complaint jobs
  - Scheduled commands (cron jobs)
  - Job dependencies và error handling
  - Performance monitoring

### 5. [Hệ thống Notification/Email](05_SRS_He_Thong_Notification_Email.md)
- **Mục đích:** Mô tả hệ thống thông báo và email templates
- **Nội dung chính:**
  - User management notifications
  - CV & job management notifications
  - Payment & commission notifications
  - Interview & onboard notifications
  - Complaint & support notifications
  - Email templates và configuration
  - In-app notification system
  - Performance optimization

### 6. [Authentication/Authorization](06_SRS_He_Thong_Authentication_Authorization.md)
- **Mục đích:** Tài liệu hóa hệ thống bảo mật và phân quyền
- **Nội dung chính:**
  - Authentication configuration và guards
  - Middleware system và role-based authorization
  - Permission system cho admin và employer
  - User model và authentication methods
  - Session management và security measures
  - Password security và multi-factor authentication
  - API authentication và audit logging
  - Security headers và rate limiting

### 7. [API Endpoints](07_SRS_API_Endpoints.md)
- **Mục đích:** Tài liệu hóa tất cả API endpoints và integration
- **Nội dung chính:**
  - Public APIs (job listing, company search)
  - External integration APIs (API key required)
  - Authenticated APIs (Sanctum token)
  - Internal AJAX APIs
  - Error handling và response format
  - API testing và documentation
  - Performance monitoring

### 8. [Workflow Nghiệp vụ](08_SRS_Workflow_Nghiep_Vu.md)
- **Mục đích:** Sơ đồ workflow chi tiết cho các quy trình chính
- **Nội dung chính:**
  - MarketCV workflow với 3 giai đoạn
  - Submit CV workflow
  - Payment & commission workflow
  - Job management workflow
  - User onboarding workflow
  - Complaint & dispute workflow
  - Automated system workflows
  - Error handling & recovery workflows

### 9. [Deployment & Configuration](09_SRS_Deployment_Configuration.md)
- **Mục đích:** Hướng dẫn deployment và cấu hình hệ thống
- **Nội dung chính:**
  - System requirements và software stack
  - Environment configuration
  - Dependencies management
  - Server configuration (Nginx, PHP-FPM, MySQL, Redis)
  - Deployment scripts và SSL setup
  - Monitoring & logging
  - Performance optimization

---

## 🎯 Mục đích Sử dụng

Bộ tài liệu SRS này được thiết kế để:

### 1. **Tái xây dựng Hệ thống**
- Cung cấp đầy đủ thông tin để rebuild hệ thống từ đầu
- Chi tiết về database schema, business logic, và workflows
- Không cần truy cập source code gốc

### 2. **Chuyển đổi Công nghệ**
- Hỗ trợ migration sang technology stack khác
- Mô tả rõ business requirements độc lập với implementation
- API specifications cho integration

### 3. **Onboarding Team mới**
- Tài liệu đầy đủ cho developers mới
- Hiểu rõ business domain và technical architecture
- Best practices và security considerations

### 4. **Maintenance và Enhancement**
- Reference documentation cho bug fixes
- Impact analysis cho new features
- Performance optimization guidelines

---

## 📊 Thống kê Tài liệu

| Tài liệu | Số trang | Nội dung chính |
|----------|----------|----------------|
| **01_Tổng quan** | ~15 | Architecture, modules, integrations |
| **02_Database** | ~25 | 40+ tables, relationships, constraints |
| **03_Business Logic** | ~20 | 2 main workflows, 3 service types |
| **04_Jobs/Queue** | ~18 | 25+ background jobs, scheduling |
| **05_Notifications** | ~15 | 50+ notification types, templates |
| **06_Auth/Security** | ~12 | Multi-role auth, security measures |
| **07_API** | ~15 | 30+ endpoints, authentication |
| **08_Workflows** | ~20 | Detailed process flows, diagrams |
| **09_Deployment** | ~18 | Server config, deployment scripts |
| **Tổng cộng** | **~158 trang** | **Comprehensive documentation** |

---

## 🔧 Công nghệ và Dependencies

### Backend Stack
- **Framework:** Laravel 9.x (PHP 8.1+)
- **Database:** MySQL 8.0 với Redis cache
- **Queue:** Database driver với Supervisor
- **Storage:** Amazon S3 với CloudFront CDN
- **Email:** SMTP/Mailgun
- **Payment:** ZaloPay integration

### Frontend Stack
- **Framework:** Vue.js 3.x với Laravel Mix
- **UI:** Bootstrap 5 + Custom SCSS
- **Admin Panel:** Backpack for Laravel

### Infrastructure
- **Web Server:** Nginx với SSL (Let's Encrypt)
- **Application Server:** PHP-FPM
- **Process Manager:** Supervisor cho queue workers
- **Monitoring:** Custom scripts + log rotation

---

## 📈 Business Model

### Hai Luồng Chính
1. **MarketCV:** NTD mua CV từ kho có sẵn
2. **Submit CV:** UV/CTV ứng tuyển vào tin tuyển dụng

### Ba Hình thức Dịch vụ
1. **CV Data:** Xem thông tin liên hệ ($50)
2. **Interview:** Phỏng vấn thành công ($200)
3. **Onboard:** Tuyển dụng thành công ($1000)

### Commission Model
- **Non-Authority:** CTV nhận 100% hoa hồng
- **Authority:** CTV nhận 20%, Admin quản lý 80%
- **Payment Schedule:** Theo giai đoạn với delay để tránh khiếu nại

---

## 🚀 Quick Start Guide

### 1. Đọc Tài liệu theo Thứ tự
1. Bắt đầu với **Tổng quan Hệ thống** để hiểu big picture
2. Đọc **Cấu trúc Database** để hiểu data model
3. Tìm hiểu **Business Logic** cho core workflows
4. Xem **Deployment** để setup environment

### 2. Implementation Approach
1. **Phase 1:** Setup infrastructure và database
2. **Phase 2:** Implement core business logic
3. **Phase 3:** Add background jobs và notifications
4. **Phase 4:** Implement frontend và API
5. **Phase 5:** Testing, security và optimization

### 3. Key Integration Points
- **Payment Gateway:** ZaloPay cho online payment
- **File Storage:** S3 cho CV và documents
- **Email Service:** SMTP/Mailgun cho notifications
- **External APIs:** CV parser, ITNavi integration

---

## ⚠️ Lưu ý Quan trọng

### Security Considerations
- Tất cả sensitive data phải được encrypt
- Implement proper access control và audit logging
- Regular security updates và vulnerability scanning
- HTTPS mandatory cho production

### Performance Requirements
- Page load time < 3 seconds
- API response time < 500ms
- Support 100+ concurrent users
- Database query optimization với proper indexing

### Compliance & Privacy
- CV data anonymization để protect privacy
- GDPR compliance cho data export/deletion
- Audit trail cho tất cả data changes
- Regular backup và disaster recovery plan

---

**📞 Support Contact:**
- **Email:** <EMAIL>
- **Hotline:** 0987 090 336
- **Zalo Group:** https://zalo.me/g/nhudpu900

---

*Tài liệu này được tạo tự động từ source code analysis và được review để đảm bảo tính chính xác và đầy đủ.*
