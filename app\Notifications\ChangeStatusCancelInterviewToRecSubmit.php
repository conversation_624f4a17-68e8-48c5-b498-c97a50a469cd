<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelInterviewToRecSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $bonus;

    public function __construct($submitCv, $bonus)
    {
        $this->submitCv = $submitCv;
        $this->bonus = $bonus;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $recName = $this->submitCv->rec->name;
        $position = '';
        if ($type == 'cv') {
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->employer->name;

        $linkDetail           = route('rec-submitcv', ['submit_id' =>  $this->submitCv->id]);
        $bonus = $this->bonus;
        return (new MailMessage)
            ->view('email.changeStatusCancelInterviewToRec', [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                // 'point' => $point,
                'urlWallet' => route('employer-wallet'),
                'urlMarket' => route('market-cv'),
                'urlWarehouseCv' => route('job-index'),
                'linkDetail' => $linkDetail,
            ])
            ->subject('[Recland] Thông báo hủy phỏng vấn ứng viên ' . $candidateName . ' vị trí ' . $position);
    }
}
