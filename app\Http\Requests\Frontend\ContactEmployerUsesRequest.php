<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class ContactEmployerUsesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'company_name'   => 'required|string|max:255',
            'first_name'     => 'required|string|max:255',
            'email_employer' => 'required',
            'phone'          => ['required', 'regex:/^[0-9()+.-]*$/', 'max:16', 'min:10',],
            'tax_code'       => 'required|string',
        ];
    }

}
