<?php

namespace App\Services\Frontend;



use App\Repositories\WalletRepository;

class WalletService
{

    protected $walletRepository;

    public function __construct(WalletRepository $walletRepository)
    {
        $this->walletRepository = $walletRepository;
    }


    public function insert($params)
    {
        return $this->walletRepository->insert($params);
    }

    public function create($attributes = [])
    {
        //        $a = json_encode($attributes);
        return $this->walletRepository->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function update($id, $options = [], $attributes = [])
    {
        return $this->walletRepository->update($id, $options, $attributes);
    }

    public function delete($id, $options)
    {
        return $this->walletRepository->delete($id, $options);
    }

    public function addAmount($userId, $amount, $note = 'Cộng tiền vào ví', $type = null)
    {
        $userWallet = $this->walletRepository->findByUser($userId);
        return $userWallet->addAmount($amount, null, $note, $type);
        // $newAmount = $userWallet->amount + $amount;
        // return $this->walletRepository->updateByUser($userId, ['amount' => $newAmount]);
    }

    public function addAmountTransaction($userId, $amount, $transactionable = null, $note = 'Cộng tiền vào ví', $type = null)
    {
        $userWallet = $this->walletRepository->findByUser($userId);
        return $userWallet->addAmount($amount, $transactionable, $note, $type);
        // $newAmount = $userWallet->amount + $amount;
        // return $this->walletRepository->updateByUser($userId, ['amount' => $newAmount]);
    }

    public function getCurrentBalance($userId)
    {
        $userWallet = $this->walletRepository->findByUser($userId);
        return $userWallet->amount;
    }

    public function createDepositLog($params)
    {
        return $this->walletRepository->createDepositLog($params);
    }
}