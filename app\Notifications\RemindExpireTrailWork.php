<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindExpireTrailWork extends Notification implements ShouldQueue
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $remaining;

    public function __construct($wareHouseCvSellingBuy,$remaining)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->remaining = $remaining;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $employerName  = $this->wareHouseCvSellingBuy->employer->name;
        $position      = $this->wareHouseCvSellingBuy->job->name;
        $link          = route('employer-cv-bought',['update-status' => $this->wareHouseCvSellingBuy->id]);
        return (new MailMessage)
            ->view('email.remindExpireTrailWork', [
                'candidateName' => $candidateName,
                'employerName' => $employerName,
                'position' => $position,
                'remaining' => 60 - $this->remaining,
                'link' => $link,
            ])
            ->subject('[Recland] Thông báo cập nhập kết quả thử việc ứng viên '.$candidateName.' vị trí '.$position);
    }

}
