<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendEmailEmployerRegistration extends Mailable
{
    use Queueable, SerializesModels;

    protected $employer;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($employer)
    {
        $this->employer = $employer;
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */


    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }

    public function build()
    {
        return $this
            ->subject('[RecLand] [THÔNG BÁO NHÀ TUYỂN DỤNG MỚI]')
            ->view('email.send-email-employer-register', ['employer' => $this->employer]);
    }
}
