<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SubmitCvResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // dd($this);
        // dd($this->id);
        $wavehouseCv = $this->warehouseCv;
        // dd($wavehouseCv);
        $result = [
            'id'                         => $this->id,
            // 'code'                       => $this->code,
            'user_id'                    => $this->user_id,
            'job_id'                     => $this->job_id,
            'company_id'                 => $this->company_id,
            'candidate_salary_expect'    => $this->candidate_salary_expect,
            'candidate_currency'         => $this->candidate_currency,
            'warehouse_cv_id'            => $this->warehouse_cv_id,
            'status'                     => $this->status,
            'status_complain'            => $this->status_complain,
            'is_self_apply'              => $this->is_self_apply,
            'expected_date'              => $this->expected_date,
            'date_change_status'         => $this->date_change_status,
            'bonus_type'                 => $this->bonus_type,
            'bonus'                      => $this->bonus,
            'incentive'                  => $this->incentive,
            // 'bonus_currency'             => $this->bonus_currency,
            // 'bonus_self_apply'           => $this->bonus_self_apply,
            // 'bonus_self_apply_incentive' => $this->bonus_self_apply_incentive,
            // 'bonus_self_apply_currency'  => $this->bonus_self_apply_currency,
            'assessment'                 => $this->assessment,
            // 'is_active'                  => $this->is_active,
            'created_at'                 => $this->created_at,
            'updated_at'                 => $this->updated_at,
            // 'deleted_at'                 => $this->deleted_at,
            'payment_fee'                => $this->payment_fee,
            'payment_actual'             => $this->payment_actual,
            'status_payment'             => $this->status_payment,
            'date_change_status_payment' => $this->date_change_status_payment,
            'interest_rate'              => $this->interest_rate,
            'interest_money'             => $this->interest_money,
            // 'authorize'                  => $this->authorize,
            'authorize_status'           => $this->authorize_status,
            'percent_bonus'              => $this->percent_bonus,
            'percent_bonus_value'        => $this->percent_bonus_value,
            'confirm_candidate'          => $this->confirm_candidate,
            'rank'                       => $this->rank,
            'year_experience'            => $this->year_experience,
            'candidate_est_timetowork'   => $this->candidate_est_timetowork,
            'career'                     => $this->career,
            'confirm_token'              => $this->confirm_token,
            'expected_date_value'        => $this->expected_date_value,
            'status_value'               => $this->status_value,
            'payment_actual_format'      => $this->payment_actual_format,
            'payment_fee_format'         => $this->payment_fee_format,
            // 'bonus_authority'            => $this->bonus_authority,
            // 'bonus_val'                  => $this->bonus_val,
            'status_payment_value'       => $this->status_payment_value,
            'status_complain_value'      => $this->status_complain_value,
            'status_recruitment_value'   => $this->status_recruitment_value,
            'date_hour'                  => $this->date_hour,
            'warehouse_cv'              => new WarehouseCvResource($wavehouseCv),
        ];
        return $result;
    }
}
