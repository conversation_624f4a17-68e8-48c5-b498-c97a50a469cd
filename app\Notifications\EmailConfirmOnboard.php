<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailConfirmOnboard extends Notification implements ShouldQueue
{

    use Queueable;

    protected $employer;
    protected $wareHouseCvSellingBuyBook;
    protected $wareHouseCvSellingBuy;

    public function __construct($employer,$wareHouseCvSellingBuyBook,$wareHouseCvSellingBuy)
    {
        $this->employer = $employer;
        $this->wareHouseCvSellingBuyBook = $wareHouseCvSellingBuyBook;
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }


    public function toMail($notifiable)
    {
        $candidateName = '';
        $position = '';
        $employerName = $this->employer->name;
        $timeInterview = $this->wareHouseCvSellingBuyBook->time_book_format .' '. $this->wareHouseCvSellingBuyBook->date_book_format;
        if (!empty($this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv)){
            $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
            $position = $this->wareHouseCvSellingBuy->job->name;
        }

        $link = route('employer-cv-bought',['view-detail' => $this->wareHouseCvSellingBuy->wareHouseCvSelling->id]);

        return (new MailMessage)
            ->view('email.emailConfirmOnboard', [
                'employerName' => $employerName,
                'candidateName' => $candidateName,
                'position' => $position,
                'timeInterview' => $timeInterview,
                'link' => $link,
            ])
            ->subject('[RECLAND] Thông báo đồng ý lời mời làm việc của ứng viên '.$candidateName.' đối với vị trí '.$position);
    }


}
