<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingBuyBook;
use Carbon\Carbon;

class WareHouseCvSellingBuyBookRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingBuyBook::class;


    public function getByWarehouseCvBuyId($warehouseCvBuyId){
        $query = $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId)
            ->with(['employer']);
        return $query->get();
    }

    public function findByCvSellingBuyId($warehouseCvBuyId){
        return $this->query()
            ->where('warehouse_cv_selling_buy_id',$warehouseCvBuyId)
            ->first();
    }

    public function countBookReject($recId,$warehouseCvSellingBuyId){
        return $this->query()
            ->where('ntd_id',$recId)
            ->where('warehouse_cv_selling_buy_id',$warehouseCvSellingBuyId)
            ->where('status',2)
            ->count();
    }

    public function getBookExpire(){
        $now = Carbon::now()->subDays(2);
        return $this->query()
            ->where('status',0)
            ->where('created_at','<=',$now)
            ->get();
    }

    public function getLastBookBySellingBuyId($sellingBuyId){
        return $this->query()
            ->where('warehouse_cv_selling_buy_id',$sellingBuyId)
            ->where('status',0)->first();
    }

    public function getWaitingInterviewBook($sellingBuyId){
        return $this->query()
            ->where('warehouse_cv_selling_buy_id',$sellingBuyId)
            ->where('status',1)->first();
    }





}
