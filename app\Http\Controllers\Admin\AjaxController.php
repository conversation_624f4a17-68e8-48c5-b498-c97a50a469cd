<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\User;
use Illuminate\Http\Request;

class AjaxController extends Controller
{
    /**
     * Ajax endpoint để load companies cho select2
     */
    public function companies(Request $request)
    {
        $search = $request->q;

        $query = Company::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('mst', 'like', '%' . $search . '%');
            });
        }

        $companies = $query->orderBy('name')
            ->limit(20)
            ->get(['id', 'name', 'mst']);

        $results = [];
        $data = [];
        foreach ($companies as $company) {
            // $results[] = [
            //     'id' => $company->id,
            //     'text' => $company->name . ($company->mst ? ' - ' . $company->mst : '')
            // ];
            $data[$company->id] = $company->name . ($company->mst ? ' - ' . $company->mst : '');
        }
        return response()->json($data);
        // return response()->json([
        //     'results' => $results,
        //     'pagination' => ['more' => false]
        // ]);
    }

    /**
     * Ajax endpoint để load employers cho select2
     */
    public function employers(Request $request)
    {
        $search = $request->q;

        $query = User::where('type', 'employer');

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('mobile', 'like', '%' . $search . '%');
            });
        }

        $employers = $query->orderBy('name')
            ->limit(20)
            ->get(['id', 'name', 'email']);

        $results = [];
        $data = [];
        foreach ($employers as $employer) {
            // $results[] = [
            //     'id' => $employer->id,
            //     'text' => $employer->name . ' - ' . $employer->email
            // ];
            $data[$employer->id] = $employer->name . ' - ' . $employer->email;
        }

        return response()->json($data);
        // return response()->json([
        //      'results' => $results,
        //     'pagination' => ['more' => false]
        // ]);
    }
}
