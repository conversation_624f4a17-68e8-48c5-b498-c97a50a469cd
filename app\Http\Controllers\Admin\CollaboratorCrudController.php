<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\AlbumAboutUsRequest;
use App\Services\Admin\PermissionService;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;

/**
 * Class CompanyCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class CollaboratorCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    //    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;


    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        if (!PermissionService::checkPermission('new-collaborator.index')) {
            CRUD::denyAccess(['index', 'create', 'update', 'delete', 'show']);
            abort(403, 'Unauthorized');
        }
        CRUD::setModel(\App\Models\User::class);

        CRUD::setRoute(config('backpack.base.route_prefix') . '/new-collaborator');
        CRUD::setEntityNameStrings('Collaborator', 'Collaborators');
        CRUD::denyAccess(['create', 'update', 'delete', 'show']);
        CRUD::setListView('admin.pages.collaborator.backpack_list');
        $this->crud->addButtonFromView('line', 'custom_action', 'custom_action', 'beginning');
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        $this->crud->query->where('type', 'rec');

        $this->filterData();
        $this->crud->addColumn([
            'name' => 'id', // The db column name
            'label' => "ID", // Table column heading
            'type' => 'Text'
        ]);
        $this->crud->addColumn([
            'name' => 'name', // The db column name
            'label' => "Name", // Table column heading
            'type' => 'custom_html',
            'value' => function ($entry) {
                $html = '<strong>' . e($entry->name) . '</strong>';
                if ($entry->is_real) {
                    // $html .= '<br><small class="badge badge-success mt-2">Cộng tác viên thật</small>';
                } else {
                    $html .= '<br><small class="badge badge-danger mt-2">Cộng tác viên ảo</small>';
                }
                return $html;
            },
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('name', 'like', '%' . $searchTerm . '%');
            }
        ]);
        $this->crud->addColumn([
            'name' => 'email', // The db column name
            'label' => "Email", // Table column heading
            'type' => 'Text',
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('email', 'like', '%' . $searchTerm . '%');
            }
        ]);
        $this->crud->addColumn([
            'name' => 'mobile', // The db column name
            'label' => "Mobile", // Table column heading
            'type' => 'Text',
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('mobile', 'like', '%' . $searchTerm . '%');
            }

        ]);
        $this->crud->addColumn([
            'name' => 'referral_define', // The db column name
            'label' => "Referral Definde", // Table column heading
            'type' => 'Text',
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('referral_define', 'like', '%' . $searchTerm . '%');
            }
        ]);
        $this->crud->addColumn([
            'name' => 'referral_code', // The db column name
            'label' => "Referral Code", // Table column heading
            'type' => 'Text',
            'searchLogic' => function ($query, $column, $searchTerm) {
                $query->orWhere('referral_code', 'like', '%' . $searchTerm . '%');
            }
        ]);
        $this->crud->addColumn([
            'name' => 'birthday', // The db column name
            'label' => "Birthday", // Table column heading
            'type' => 'Text'
        ]);
        $this->crud->addColumn([
            'name' => 'created_at', // The db column name
            'label' => "Created At", // Table column heading
            'type' => 'date'
        ]);
        $this->crud->addColumn([
            'name' => 'level', // The db column name
            'label' => "Level", // Table column heading
            'type'     => 'custom_html',
            'value' => function ($entry) {
                if ($entry->level == 1) {
                    return '<span class="badge bg-primary">Level 1 (Cao cấp)</span>';
                } else {
                    return '<span class="badge bg-secondary">Level 0 (Thường)</span>';
                }
            }
        ]);
        $this->crud->addColumn([
            'name' => 'is_active', // The db column name
            'label' => "Trạng thái", // Table column heading
            'type'     => 'custom_html',
            'value' => function ($entry) {
                return $entry->is_active == 1 ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>';
            }
        ]);

        //        $this->crud->column('action')->wrapper([
        //            'href' => function ($crud, $column, $entry, $related_key) {
        //                return backpack_url('collaborator/'.$related_key.'/edit');
        //            },
        //        ]);
        //        $this->crud->column('action')->wrapper([
        //            'href' => function ($crud, $column, $entry, $related_key) {
        //                return backpack_url('collaborator/'.$related_key.'/history-bonus');
        //            },
        //        ]);
        //        $this->crud->column('action')->wrapper([
        //            'href' => function ($crud, $column, $entry, $related_key) {
        //                return backpack_url('collaborator/'.$related_key.'/history-bonus');
        //            },
        //        ]);
        /**
         * Columns can be defined using the fluent syntax:
         * - CRUD::column('price')->type('number');
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        //        CRUD::addFields($this->fieldData());

        /**
         * Fields can be defined using the fluent syntax:
         * - CRUD::field('price')->type('number');
         */
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        //        $this->setupCreateOperation();
        $this->crud->setEditView('admin.pages.collaborator.edit');
    }

    public function filterData()
    {
        $this->crud->addFilter(
            [
                'name' => 'is_active',
                'type' => 'select2',
                'label' => 'Trạng thái'
            ],
            function () {
                return [
                    1 => 'Active',
                    0 => 'Inactive',
                ];
            },
            function ($value) {
                $this->crud->addClause('where', 'is_active', $value);
            }
        );
        // Filter cộng tác viên thật/ảo
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'is_real',
                'label' => 'Cộng tác viên thật/ảo'
            ],
            [
                '1' => 'Cộng tác viên thật',
                '0' => 'Cộng tác viên ảo',
            ],
            function ($value) {
                $this->crud->addClause('where', 'is_real', $value);
            }
        );

        // Filter level CTV
        $this->crud->addFilter(
            [
                'type'  => 'dropdown',
                'name'  => 'level',
                'label' => 'Level CTV'
            ],
            [
                '1' => 'Level 1 (Cao cấp)',
                '0' => 'Level 0 (Thường)',
            ],
            function ($value) {
                $this->crud->addClause('where', 'level', $value);
            }
        );


        CRUD::filter('from_to')
            ->type('date_range')
            ->label('Khoảng thời gian')
            ->whenActive(function ($value) {
                $dates = json_decode($value);
                if ($dates->from) {
                    CRUD::addClause('where', 'created_at', '>=', $dates->from);
                }
                if ($dates->to) {
                    CRUD::addClause('where', 'created_at', '<=', $dates->to . ' 23:59:59');
                }
            });
    }
    //    protected function setupShowOperation()
    //    {
    ////        $this->setupListOperation();
    //        $this->crud->setShowView('admins.candidates.show');
    ////        Widget::add()->type('script')->content('assets/js/admin/custom.js');
    //    }
}
