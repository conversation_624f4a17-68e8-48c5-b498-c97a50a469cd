<?php

namespace App\Services\Admin;

use App\Helpers\Common;
use App\Repositories\CompanyRepository;
use App\Repositories\EmployeeRoleRepository;
use App\Repositories\JobRepository;
use App\Repositories\UserRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;

class CompanyService
{

    protected $companyRepository;
    protected $userRepository;
    protected $jobRepository;
    protected $employeeRoleRepository;

    public function __construct(CompanyRepository $companyRepository, UserRepository $userRepository, JobRepository $jobRepository, EmployeeRoleRepository $employeeRoleRepository)
    {
        $this->companyRepository = $companyRepository;
        $this->userRepository = $userRepository;
        $this->jobRepository = $jobRepository;
        $this->employeeRoleRepository = $employeeRoleRepository;
    }

    public function indexService($params, $order = array(), $paginate = false, $orderBy = 'id', $sort = 'desc', $columns = ['*'])
    {
        return $this->companyRepository->getListCompany($params, $order, $paginate, $orderBy, $sort, $columns);
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total()];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = array(
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ),
                'value' => 'No'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'id',
                ),
                'value' => 'ID'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'name',
                ),
                'value' => 'Tên công ty'
            ),
            array(
                'attributes' => array(
                    'data-fn' => 'renderUrl',
                    'data-mdata' => 'website',
                ),
                'value' => 'Website'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'path_logo',
                    'data-fn' => 'renderLogo',
                ),
                'value' => 'Logo'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'scale_value',
                ),
                'value' => 'Quy mô'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'about',
                    'data-fn' => 'renderContent',
                ),
                'value' => 'Giới thiệu'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'home_value',
                    'data-fn' => 'renderHome',
                ),
                'value' => 'Hiển thị trang chủ'
            ),
            array(
                'attributes' => array(
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ),
                'value' => 'Trạng thái hoạt động'
            )
        );
        $renderAction = [];
        if (PermissionService::checkPermission('company.edit')) {
            $renderAction[] = 'actionEdit';
        }

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('company-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('Danh sách công ty');
    }

    public function total()
    {
        return $this->companyRepository->total();
    }

    public function totalCurrentMonth()
    {
        $start = Carbon::now()->startOfMonth();
        return $this->companyRepository->total(['after_date' => $start]);
    }

    public function create(array $request)
    {
        $logo = FileServiceS3::getInstance()->uploadToS3($request['logo'], config('constant.sub_path_s3.company'));
        $banner = FileServiceS3::getInstance()->uploadToS3($request['banner'], config('constant.sub_path_s3.company'));

        $request['address'] = json_encode($request['address']);
        $request['logo'] = $logo;
        $request['banner'] = $banner;
        $request['slug'] = Common::buildSlug($request['name']);
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        $request['home'] = isset($request['home']) ? config('constant.active') : config('constant.inActive');

        return $this->companyRepository->create($request);
    }

    public function detail($id)
    {
        $company = $this->companyRepository->find($id);
        $employer = $this->userRepository->getUserByType(config('constant.role.employer'), ['company_id' => $id]);
        return [$company, $employer];
    }

    public function detailOnlyCompany($id)
    {
        return $this->companyRepository->find($id);
    }

    public function update(array $request, $id)
    {
        $company = $this->detailOnlyCompany($id);
        if (!empty($request['logo'])) {
            $request['logo'] = FileServiceS3::getInstance()->uploadToS3($request['logo'], config('constant.sub_path_s3.company'));
        }

        if (!empty($request['banner'])) {
            $request['banner'] = FileServiceS3::getInstance()->uploadToS3($request['banner'], config('constant.sub_path_s3.company'));
        }

        $request['address'] = json_encode($request['address']);
        if ($company->name != $request['name']) {
            $request['slug'] = Common::buildSlug($request['name']);
        }
        $request['is_active'] = isset($request['is_active']) ? config('constant.active') : config('constant.inActive');
        $request['home'] = isset($request['home']) ? config('constant.active') : config('constant.inActive');
        $request['career'] = implode(',', $request['career']);

        $this->companyRepository->update($id, [], $request);
        //update user employer, job
        $option = [
            'company_id' => $id
        ];
        //company active -> inactive
        if ($company->is_active == config('constant.active') && $request['is_active'] == config('constant.inActive')) {
            $option['is_active'] = config('constant.active');
            $userList = $this->userRepository->getUserByType('employer', $option);
            if (count($userList)) {
                //update is_active user
                foreach ($userList as $value) {
                    $this->userRepository->update($value->id, [], ['is_active' => config('constant.inActive'), 'flg' => 1]);
                    //update is_active user
                    $option['employer_id'] = $value->id;
                    $jobList = $this->jobRepository->totalJob($option, 1);
                    if (count($jobList)) {
                        foreach ($jobList as $job) {
                            $this->jobRepository->update($job->id, [], ['is_active' => config('constant.inActive'), 'flg' => 1]);
                        }
                    }
                }
            }
        }
        //company inactive -> active
        if ($company->is_active == config('constant.inActive') && $request['is_active'] == config('constant.active')) {
            $option['is_active'] = config('constant.inActive');
            $option['flg'] = 1;
            $userList = $this->userRepository->getUserByType('employer', $option);
            if (count($userList)) {
                //update is_active user
                foreach ($userList as $value) {
                    $this->userRepository->update($value->id, [], ['is_active' => config('constant.active'), 'flg' => 1]);
                    //update is_active user
                    $option['employer_id'] = $value->id;
                    $jobList = $this->jobRepository->totalJob($option, 1);
                    if (count($jobList)) {
                        foreach ($jobList as $job) {
                            $this->jobRepository->update($job->id, [], ['is_active' => config('constant.active'), 'flg' => 1]);
                        }
                    }
                }
            }
        }

    }

    public function getRoleCompany($id)
    {
        return $this->employeeRoleRepository->getListRole($id);
    }

    public function getCompany()
    {
        return $this->companyRepository->getListCompanyPluck();
    }

    public function totalRecruitingCompany($fromDate = null, $toDate = null)
    {
        return $this->companyRepository->numberRecruitingCompany($fromDate, $toDate);
    }
}
