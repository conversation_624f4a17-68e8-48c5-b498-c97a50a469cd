<?php

namespace App\Jobs;

use App\Models\WareHouseCvSellingBuy;
use App\Models\WareHouseCvSellingBuyBook;
use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\ChangeStatusPassInterviewAdmin;
use App\Notifications\EmailRejectInterView;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class PassInterview implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user;
    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId,$user)
    {
        $this->user = $user;
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = resolve(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
        //7 => 'Waiting Interview',
        //Trạng thái khiếu nại: NTD khiếu nại = 1, CTV từ chối = 2, CTV xác nhận = 3, Admin xác nhân = 4, Admin từ chối = 5
        if ($wareHouseCvSellingBuy->status_recruitment == 7 &&
            ($wareHouseCvSellingBuy->status_complain == 0 || $wareHouseCvSellingBuy->status_complain == 5)
        ){
            $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
            //8 => 'Pass Interview',
            $wareHouseCvSellingBuy->update(['status_recruitment' => 8]);

            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy,$this->user);
            //sau 24h thi Thanh toan tien tra CTV -> interview
            if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview') {
                PayInterview::dispatch($wareHouseCvSellingBuy->id)->delay(now()->addMinutes(24 * 60));
            }

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard') {

                //offering 11
                $wareHouseCvSellingBuy->update([
                    'status_recruitment' => 11
                ]);
                $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy,$this->user);
            }

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusPassInterviewAdmin($wareHouseCvSellingBuy));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new ChangeStatusPassInterview($wareHouseCvSellingBuy));
            }

        }
    }




}
