<?php

namespace App\Http\Requests\Admin;

use App\Rules\Admin\CheckCurrentAdminOldPassword;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class AdminChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'old_password'     => ['required',new CheckCurrentAdminOldPassword()],
            'password'         => 'required|min:6|max:20',
            'password_confirm' => 'required|same:password',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'required' => __('message.required'),
            'password_confirm.same' => __('message.same'),
            'min' => __('message.min'),
            'max' => __('message.max'),
        ];
    }
}
