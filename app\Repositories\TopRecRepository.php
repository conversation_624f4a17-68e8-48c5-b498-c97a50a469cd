<?php

namespace App\Repositories;

use App\Models\TopCollaborators;
use App\Models\TopRec;
use Illuminate\Support\Facades\Cache;

class TopRecRepository extends BaseRepository
{
    const MODEL = TopRec::class;


    public function getTopRecList($params, $orders = array(), $paginate = false, $order_by = 'amount', $sort = 'desc')
    {
        return Cache::tags([__CLASS__])
            ->remember(__CLASS__ . __FUNCTION__, CACHE_TTL, function () use ($params, $orders, $paginate, $order_by, $sort) {
                if (isset($params['page'])) {
                    $page = (int)$params['page'];
                } else {
                    $page = 1;
                }
                if (isset($params['size'])) {
                    $limit = (int)$params['size'];
                } else {
                    $limit = config('constant.limitPaginate');
                }

                if ($limit > 100) {
                    $limit = 100;
                }

                $query = $this->query();

                if (isset($params['start'])) {
                    $query->offset($params['start']);
                }

                $query->orderBy($order_by, $sort);

                if ($paginate) {
                    return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
                } else {
                    return $query->get();
                }
            });
    }

    public function getQuery($param)
    {
        return $this->query()->orderByDesc('id');
    }

}
