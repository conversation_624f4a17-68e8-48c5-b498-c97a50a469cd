<?php

namespace App\Services\Frontend;

use App\Jobs\RejectBookExpire;
use App\Jobs\SendemailBook;
use App\Models\WareHouseCvSellingBuy;
use App\Notifications\ConfirmSupportJob;
use App\Notifications\EmployerScheduleInterviewAdmin;
use App\Notifications\EmployerScheduleInterviewRec;
use App\Notifications\RecRefuseComplain;
use App\Repositories\WareHouseCvSellingBuyBookRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class WareHouseCvSellingBuyBookService
{

    protected $wareHouseCvSellingBuyBookRepository;
    protected $houseCvSellingBuyRepository;
    protected $wareHouseCvSellingBuyHistoryStatusRepository;

    public function __construct(WareHouseCvSellingBuyBookRepository $wareHouseCvSellingBuyBookRepository,
                                WareHouseCvSellingBuyRepository $houseCvSellingBuyRepository,
                                WareHouseCvSellingBuyHistoryStatusRepository $wareHouseCvSellingBuyHistoryStatusRepository
    )
    {
        $this->wareHouseCvSellingBuyBookRepository = $wareHouseCvSellingBuyBookRepository;
        $this->houseCvSellingBuyRepository = $houseCvSellingBuyRepository;
        $this->wareHouseCvSellingBuyHistoryStatusRepository = $wareHouseCvSellingBuyHistoryStatusRepository;
    }

    /**
     * @param $data
     * @return mixed
     * @throws \Exception
     * NTD đặt lich phỏng vấn
     * HT NTD
     */
    public function scheduleInterview($data){
        $wareHouseCvSellingBuy = $this->houseCvSellingBuyRepository->find($data['warehouse_cv_selling_buy_id']);

        if ($wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'interview' || $wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale == 'onboard'){
            if (!in_array($wareHouseCvSellingBuy->status_recruitment,[3,5])){
                //3 => 'Waiting setup interview',
                //5 => 'Reject Interview schedule',
                throw new \Exception('Không đủ điều kiện đặt lịch phỏng vấn');
            }
            $user = auth('client')->user();
            $data = [
                'ntd_id' => $user->id,
                'address' => $data['address'],
                'name' => $data['name'],
                'warehouse_cv_selling_buy_id' => $data['warehouse_cv_selling_buy_id'],
                'date_book' => Carbon::createFromFormat('d/m/Y',$data['date']),
                'time_book' => $data['hour'].':'.$data['minute'],
            ];
            $book = $this->wareHouseCvSellingBuyBookRepository->create($data);
            $book->load('rec');
            $wareHouseCvSellingBuy->update([
                'status_recruitment' => 4//4 => 'Waiting confirm calendar',
            ]);

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority){
                Mail::to(config('settings.global.email_admin'))->send(new EmployerScheduleInterviewAdmin($wareHouseCvSellingBuy,$wareHouseCvSellingBuy->rec,$book));
            }else{
                $wareHouseCvSellingBuy->rec->notify(new EmployerScheduleInterviewRec($wareHouseCvSellingBuy,$wareHouseCvSellingBuy->rec,$book));
            }
            // - sau 48h mà CTV ko xác nhận hay Hủy thì cũng update về  status_recruitment = 5 => 'Reject Interview schedule', và warehouse_cv_selling_buy_books.status = 2
            RejectBookExpire::dispatch($book,$user)->delay(now()->addMinutes(48 * 60));
            //sau 1 ngay Lich phong vấn thi gui email cho NTD
            $timeInterval = Carbon::createFromFormat('Y-m-d H:i:s',$book->date_book)
                ->startOfDay()
                ->addMinutes($book->book_time_minute + 24 * 60);
            SendemailBook::dispatch($wareHouseCvSellingBuy, $user, $book)->delay($timeInterval);
        }

        //log
        $this->wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy,$user);
        return $book;
    }
    public function getByWarehouseCvBuyId($warehouseCvBuyId)
    {
        return $this->wareHouseCvSellingBuyBookRepository->getByWarehouseCvBuyId($warehouseCvBuyId);
    }




}
