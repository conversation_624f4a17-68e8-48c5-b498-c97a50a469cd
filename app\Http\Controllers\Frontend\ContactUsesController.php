<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\ContactEmployerUsesRequest;
use App\Http\Requests\Frontend\ContactUsesRequest;
use App\Mail\ContactEmployerNotification;
use App\Mail\ContactUsNotification;
use App\Repositories\ContactUsesRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;

class ContactUsesController extends Controller
{
    protected $contactUsesService;

    public function __construct(ContactUsesRepository $contactUsesService)
    {
        $this->contactUsesService = $contactUsesService;
    }

    public function contactUses(ContactUsesRequest $request)
    {
        $request->validate([
            // ... các validation rules khác
            'g-recaptcha-response' => 'required|captcha'
        ],  [
            'g-recaptcha-response.required' => 'Vui lòng xác nhận captcha',
            'g-recaptcha-response.captcha' => 'Captcha không hợp lệ'
        ]);
        try {
            DB::beginTransaction();
            $data = $request->except('_token');
            $this->contactUsesService->insert($data);
            Session::flash('message', config('settings.' . app()->getLocale() . '.contact-us.thankscontact'));
            DB::commit();
            Mail::to(config('settings.global.email_admin'))->send(new ContactUsNotification($data));
            return redirect()->route('contact-us');
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }

    public function contactEmployerUses(ContactEmployerUsesRequest $request)
    {
        $request->validate([
            // ... các validation rules khác
            'g-recaptcha-response' => 'required|captcha'
        ],  [
            'g-recaptcha-response.required' => 'Vui lòng xác nhận captcha',
            'g-recaptcha-response.captcha' => 'Captcha không hợp lệ'
        ]);
        try {
            DB::beginTransaction();
            $data = $request->only([
                'company_name',
                'first_name',
                'phone',
                'tax_code',
            ]);
            $email = $request->input('email_employer');
            $data['email'] = $email;
            Session::flash('message', config('settings.' . app()->getLocale() . '.contact-us.thankscontact'));
            $this->contactUsesService->insert($data);
            DB::commit();

            Mail::to(config('settings.global.email_admin'))->send(new ContactEmployerNotification($data));

            return back();
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json(['error' => $exception->getMessage()], 500);
        }
    }
}
