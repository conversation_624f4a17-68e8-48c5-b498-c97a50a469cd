<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserSurveyResult extends Model
{
    protected $fillable = [
        'user_id',
        'survey_field_id',
        'surveyed_at'
    ];

    protected $casts = [
        'surveyed_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function surveyField()
    {
        return $this->belongsTo(SurveyField::class);
    }
} 