<?php

namespace App\Services\Frontend;



use App\Models\UserJobCare;
use App\Repositories\UserJobCareRepository;
use Illuminate\Support\Facades\Auth;

class UserJobCareService
{

    protected $userJobCareRepository;

    public function __construct(
        UserJobCareRepository $userJobCareRepository,
    ) {
        $this->userJobCareRepository = $userJobCareRepository;
    }

    public function changeStatus($jobId, $type)
    {
        $user = Auth::guard('client')->user();

        if ($user->type !=  config('constant.role.rec')) {
            throw new \Exception(__('frontend/job/message.job_permission'), config('constant.code.reverse_code_status.CODE_PERMISTION'));
        }
        $userJobCare = $this->userJobCareRepository->getJobCare($user->id, $jobId, $type);
        if ($userJobCare) {
            $userJobCare->delete();
            return 'inactive_' . $type;
        } else {
            $this->userJobCareRepository->createJobCare($user->id, $jobId, $type);
            return 'active_' . $type;
        }
    }

    public function checkUserJobCare($userId, $jobId, $type)
    {
        return $this->userJobCareRepository->countUserJobCare($userId, $jobId, $type);
    }

    /**
     * Lấy danh sách job đã save của user
     * 
     * @param int $userId
     * @param array $params
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getSavedJobs($userId, $params = [])
    {
        return $this->userJobCareRepository->getSavedJobs($userId, $params);
    }
}
