<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RemindExpireTrailWorkSubmit extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $remaining;

    public function __construct($submitCv,$remaining)
    {
        $this->submitCv = $submitCv;
        $this->remaining = $remaining;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {

        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $employerName  = $this->submitCv->employer->name;
        $position      = $this->submitCv->job->name;
        $link          = route('employer-submitcv', ['update-status' => $this->submitCv->id]);;
        return (new MailMessage)
            ->view('email.remindExpireTrailWorkSubmit', [
                'candidateName' => $candidateName,
                'employerName'  => $employerName,
                'position'      => $position,
                'remaining'     => 60 - $this->remaining,
                'link'          => $link,
            ])
            ->subject('[Recland] Thông báo cập nhập kết quả thử việc ứng viên '.$candidateName.' vị trí '.$position);
    }

}
