<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Job;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Services\Frontend\JobService;

class JobController extends Controller
{
    protected $jobService;

    public function __construct(JobService $jobService)
    {
        $this->jobService = $jobService;
    }
    /**
     * Search công ty theo tham số "q"
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createJob(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $data = $request->only(
                'name',
                'company_id',
                'employer_id',
                'expire_at',
                'vacancies',
                'type',
                'career',
                'rank',
                'skill',
                'bonus',
                'bonus_type',
                'city_slug',
                'address',
                'jd_description',
                'jd_request',
                'jd_welfare',
                'salary_min',
                'salary_max',
                'salary_currency',
            );

            $data['address'] = [
                ['area' => $data['city_slug'], 'address' => $data['address']]
            ];
            unset($data['city_slug']);
            // unset($data['address']);

            // $job = Job::create($data);
            $job = $this->jobService->createJobApi($data);
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Tạo công việc thành công',
                'data' => ['id' => $job->id, 'slug' => $job->slug]
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo công việc',
                'error' => $e->getMessage()
            ], 500);
        }
    }

} 