# Workflow: <PERSON><PERSON><PERSON> tiến tính năng tra cứu nhà tuyển dụng

**Ng<PERSON><PERSON> tạo:** 16/01/2025  
**<PERSON><PERSON><PERSON> tiêu:** Thêm tính năng tự động tra cứu theo email từ URL parameter và thêm link tra cứu vào cột số dư ví

## Yêu cầu từ người dùng

1. **Tự động tra cứu theo email**: Nếu có request `?email=<EMAIL>` thì tự động tra cứu theo email trong request này luôn
2. **Thêm link tra cứu**: Ở `EmployerCrudController.php`, thêm link tra cứu employer vào cột số dư ví ở trang danh sách

## Các thay đổi đã thực hiện

### 1. Cập nhật EmployerLookupController

**File:** `app/Http/Controllers/Admin/EmployerLookupController.php`

**Thay đổi:**
- C<PERSON><PERSON> nhật method `index()` để nhận parameter `email` từ request
- <PERSON><PERSON><PERSON><PERSON>n `$autoSearchEmail` vào view để hỗ trợ tự động tìm kiếm

```php
public function index(Request $request)
{
    // Kiểm tra nếu có email trong request thì tự động tra cứu
    $autoSearchEmail = $request->get('email');
    
    return view('admin.pages.employer-lookup.index', compact('autoSearchEmail'));
}
```

### 2. Cập nhật View tra cứu nhà tuyển dụng

**File:** `resources/views/admin/pages/employer-lookup/index.blade.php`

**Thay đổi:**
- Thêm `value="{{ $autoSearchEmail ?? '' }}"` vào input email để tự động điền email từ URL
- Thêm JavaScript để tự động tìm kiếm khi có email trong URL:

```javascript
// Tự động tìm kiếm nếu có email trong URL
@if(isset($autoSearchEmail) && $autoSearchEmail)
    searchEmployer('{{ $autoSearchEmail }}');
@endif
```

### 3. Cập nhật EmployerCrudController

**File:** `app/Http/Controllers/Admin/EmployerCrudController.php`

**Thay đổi:**
- Cập nhật cột `wallet_balance` từ `closure` thành `custom_html`
- Thêm link tra cứu vào cột số dư ví:

```php
CRUD::column('wallet_balance')
    ->label('Số dư')
    ->type('custom_html')
    ->value(function ($entry) {
        $balance = $entry->wallet ? number_format($entry->wallet->amount, 0, ',', '.') . ' VNĐ' : '0 VNĐ';
        $lookupUrl = route('admin.employer-lookup.index', ['email' => $entry->email]);
        
        return '<a href="' . $lookupUrl . '" target="_blank" class="btn btn-sm btn-outline-primary" title="Tra cứu thông tin chi tiết của ' . e($entry->name) . '">' 
            . $balance . ' <i class="fa fa-external-link-alt"></i></a>';
    })
    ->orderable(false)
    ->searchLogic(false);
```

## Tính năng mới

### 1. Tự động tra cứu theo email từ URL
- **URL mẫu:** `/admin/employer-lookup?email=<EMAIL>`
- **Hoạt động:** Trang sẽ tự động điền email vào form và thực hiện tìm kiếm ngay khi tải trang
- **Ứng dụng:** Có thể tạo link trực tiếp để tra cứu một nhà tuyển dụng cụ thể

### 2. Link tra cứu trong danh sách nhà tuyển dụng
- **Vị trí:** Cột "Số dư" trong trang danh sách nhà tuyển dụng (`/admin/employers`)
- **Hiển thị:** Số dư ví được hiển thị dưới dạng button có icon external link
- **Hoạt động:** Click vào button sẽ mở tab mới với trang tra cứu chi tiết của nhà tuyển dụng đó
- **Tooltip:** Hiển thị tên nhà tuyển dụng khi hover

## Routes liên quan

- `GET /admin/employer-lookup` - Trang tra cứu nhà tuyển dụng (hỗ trợ parameter `email`)
- `POST /admin/employer-lookup/search` - API tìm kiếm nhà tuyển dụng
- `GET /admin/employer-lookup/job-submits/{jobId}` - API lấy danh sách ứng tuyển của job

## Trạng thái: ✅ HOÀN THÀNH

Tất cả các yêu cầu đã được thực hiện thành công:
- ✅ Tự động tra cứu theo email từ URL parameter
- ✅ Thêm link tra cứu vào cột số dư ví trong danh sách nhà tuyển dụng
- ✅ Giao diện thân thiện với tooltip và icon
- ✅ Mở trong tab mới để không làm gián đoạn workflow hiện tại
