<?php

namespace App\Console\Commands;

use App\Models\Job;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExtendJobExpiration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job:extend-expiration {--days=14 : Số ngày gia hạn}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tự động gia hạn thêm 14 ngày cho những tin đang ở trạng thái "Đang tuyển" và hết hạn ngày hôm nay';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Bắt đầu gia hạn tin tuyển dụng');

        try {
            $today = Carbon::today()->format('Y-m-d');
            $extendDays = $this->option('days');

            $this->info("Đang tìm kiếm các tin tuyển dụng hết hạn ngày: {$today}");

            // Tìm các job đang ở trạng thái "Đang tuyển" (status = 1) 
            // và hết hạn ngày hôm nay
            $expiredJobs = Job::where('status', 1) // 1 = Đang tuyển
                ->where('expire_at', $today)
                ->get();

            $this->info("Tìm thấy {$expiredJobs->count()} tin tuyển dụng cần gia hạn");

            if ($expiredJobs->isEmpty()) {
                $this->info('Không có tin tuyển dụng nào cần gia hạn');
                Log::info('Không có tin tuyển dụng nào cần gia hạn');
                return Command::SUCCESS;
            }

            $updatedCount = 0;

            // Cập nhật từng job
            foreach ($expiredJobs as $job) {
                $oldExpireAt = $job->expire_at;
                $newExpireAt = Carbon::parse($job->expire_at)->addDays($extendDays)->format('Y-m-d');

                $job->expire_at = $newExpireAt;
                $job->save();

                $updatedCount++;

                $this->line("✅ Đã gia hạn job ID: {$job->id} - '{$job->name}' từ {$oldExpireAt} đến {$newExpireAt}");

                Log::info("Gia hạn job thành công", [
                    'job_id' => $job->id,
                    'job_name' => $job->name,
                    'company_id' => $job->company_id,
                    'old_expire_at' => $oldExpireAt,
                    'new_expire_at' => $newExpireAt,
                    'extend_days' => $extendDays
                ]);
            }

            $this->info("✅ Hoàn thành! Đã gia hạn {$updatedCount} tin tuyển dụng thêm {$extendDays} ngày");
            Log::info("Hoàn thành gia hạn tin tuyển dụng", [
                'total_updated' => $updatedCount,
                'extend_days' => $extendDays
            ]);

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("❌ Lỗi khi gia hạn tin tuyển dụng: " . $e->getMessage());
            Log::error('Lỗi khi gia hạn tin tuyển dụng', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Command::FAILURE;
        }
    }
}
