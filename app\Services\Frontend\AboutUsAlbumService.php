<?php

namespace App\Services\Frontend;

use App\Repositories\AboutUsAlbumRepository;

class AboutUsAlbumService
{

    protected $aboutUsAlbumRepository;

    public function __construct(AboutUsAlbumRepository $aboutUsAlbumRepository)
    {
        $this->aboutUsAlbumRepository = $aboutUsAlbumRepository;
    }

    public function list($param, $paginate = false, $limit = 10)
    {
        return $this->aboutUsAlbumRepository->list($param, $paginate, $limit);
    }


}
