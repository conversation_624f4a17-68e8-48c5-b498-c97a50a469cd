<?php

namespace App\Repositories;

use App\Models\WareHouseCvSellingBuy;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WareHouseCvSellingBuyRepository extends BaseRepository
{
    const MODEL = WareHouseCvSellingBuy::class;

    public function getCvBought($userId, $params, $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_5');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();
        $query->where('user_id',$userId);

        if (!empty($params['candidate_name'])){
            $query->whereHas('wareHouseCvSelling.wareHouseCv',function ($q) use ($params){
                return $q->where('candidate_name', 'LIKE' , "%".$params['candidate_name']."%");
            });
        }

        if (isset($params['status_payment'])){
            $query->where('status_payment', $params['status_payment']);
        }

        if (isset($params['status_recruitment'])){
            $query->where('status_recruitment', $params['status_recruitment']);
        }

        if (isset($params['status_complain'])){
            $query->where('status_complain', $params['status_complain']);
        }

        if (isset($params['type_of_sale'])){
            $query->whereHas('wareHouseCvSelling', function ($q) use ($params){
                return $q->where('type_of_sale', $params['type_of_sale']);
            });
        }

        if (isset ($params['start_time']) && isset($params['end_time'])) {
            $startBuy = Carbon::createFromFormat('d/m/Y', $params['start_time'])->startOfDay();
            $endBuy = Carbon::createFromFormat('d/m/Y', $params['end_time'])->endOfDay();
            $query->where('created_at', '>=', $startBuy)->where('created_at', '<=', $endBuy);
        }

        $query->with('wareHouseCvSelling.wareHouseCv', 'employer');
        $query->orderBy($order_by, $sort);
        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function getCvSold($userId, $params, $paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.paginate_5');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query()->select('warehouse_cv_selling_buys.*');
        $query->selectRaw('coalesce((select status from payin_months where warehouse_cv_selling_buy_id = warehouse_cv_selling_buys.id order by id desc limit 1), 0) as status_payment_ctv');
        $query->where('ctv_id',$userId);

        if (!empty($params['candidate_name'])){
            $query->whereHas('wareHouseCvSelling.wareHouseCv',function ($q) use ($params){
                return $q->where('candidate_name', 'LIKE' , "%".$params['candidate_name']."%");
            });
        }


        if (!empty($params['status_payments'])){
            $query->where('status_payment', $params['status_payment']);
        }

        if (!empty($params['status_recruitment'])){
            $query->where('status_recruitment', $params['status_recruitment']);
        }

        if (!empty($params['status_complain'])){
            $query->where('status_complain', $params['status_complain']);
        }

        if (!empty($params['type_of_sale'])){
            $query->whereHas('wareHouseCvSelling', function ($q) use ($params){
                return $q->where('type_of_sale', $params['type_of_sale']);
            });
        }

        if (!empty($params['status_selling_buy'])){
            if ($params['status_selling_buy'] == 1){
                $query->having('status_payment_ctv', 8);
            }
            if ($params['status_selling_buy'] == 2){
                $query->having('status_payment_ctv','!=', 8);
            }
            if ($params['status_selling_buy'] == 3){
                $query->where('status_complain', 1);
            }
        }


        if (isset ($params['start_time']) && isset($params['end_time'])) {
            $startBuy = Carbon::createFromFormat('d/m/Y', $params['start_time'])->startOfDay();
            $endBuy = Carbon::createFromFormat('d/m/Y', $params['end_time'])->endOfDay();
            $query->where('created_at', '>=', $startBuy)->where('created_at', '<=', $endBuy);
        }

        $query->with('wareHouseCvSelling.wareHouseCv');
        $query->with('employer');

        if (isset ($params['status_payment'])) {
            $query->having('status_payment_ctv', '=', $params['status_payment']);
        }

        $query->orderBy($order_by, $sort);
        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function getListCvSellingBuyAll($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query()->select('warehouse_cv_selling_buys.*');
        $query->selectRaw('coalesce((select status from payin_months where warehouse_cv_selling_buy_id = warehouse_cv_selling_buys.id order by id desc limit 1), 0) as status_payment_ctv');

//        if (isset($params['start'])){
//            $query->offset($params['start']);
//        }
//
//        if (isset($params['search'])) {
//            $query->where('id', $params['search'])
//                ->orWhere('candidate_name', 'like', '%' . $params['search'] . '%');
//        }
        if (isset ($params['id_cv'])) {
            $query->where('id', '=', $params['id_cv']);
        }

        if (isset ($params['id_ctv'])) {
            $query->whereHas('wareHouseCvSelling.wareHouseCv', function($query) use ($params){
                $query->where('user_id', '=', $params['id_ctv']);
            });
        }

        if (isset ($params['email_phone'])) {
            $query->whereHas('wareHouseCvSelling.wareHouseCv', function($query) use ($params){
                $query->where('candidate_email', 'like', '%' . $params['email_phone'] . '%')->orWhere('candidate_mobile', 'like', '%' . $params['email_phone'] . '%');
            });
        }

        if (isset ($params['start_time']) && isset($params['end_time'])) {
            $startBuy = Carbon::createFromFormat('d/m/Y', $params['start_time'])->startOfDay();
            $endBuy = Carbon::createFromFormat('d/m/Y', $params['end_time'])->endOfDay();
            $query->where('created_at', '>=', $startBuy)->where('created_at', '<=', $endBuy);
        }

        if (isset ($params['status_recruitment']) && $params['status_recruitment'] != 'all') {
            $query->where('status_recruitment', '=', $params['status_recruitment']);
        }

        if (isset ($params['status_payment_ntd'])  && $params['status_payment_ntd'] != 'all') {
            $query->where('status_payment', '=', $params['status_payment_ntd']);
        }

        if (isset ($params['status_authority'])  && $params['status_authority'] != 'all') {
            $query->whereHas('wareHouseCvSelling', function($query) use ($params) {
                $query->where('authority', '=', $params['status_authority']);
            });
        }

        if (isset ($params['status_complain']) && $params['status_complain'] != 'all') {
            $query->where('status_complain', '=', $params['status_complain']);
        }

        if (isset ($params['company']) && $params['company'] != 'all') {
            $query->whereHas('employer.company', function($query) use ($params) {
                $query->where('id', $params['company']);
            });
        }

        $query->with('wareHouseCvSelling.wareHouseCv', 'employer.company');

        if (isset ($params['status_payment_ctv']) && $params['status_payment_ctv'] != 'all') {
            $query->having('status_payment_ctv', '=', $params['status_payment_ctv']);
        }

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findCvId($id)
    {
        $query = $this->query()->select('warehouse_cv_selling_buys.*');
        $query->selectRaw('coalesce((select status from payin_months where warehouse_cv_selling_buy_id = warehouse_cv_selling_buys.id order by id desc limit 1), 0) as status_payment_ctv');

        $query->where('id', $id);
        $query->with('wareHouseCvSelling.wareHouseCv', 'employer.company');

        return $query->first();
    }

    public function getTotalComplain()
    {
        $query = $this->query();

        $query->where('status_complain', '=', 2);

        return $query->count();
    }

    public function getTotalInterview()
    {
        $query = $this->query();

        $query->where('status_recruitment', '=', 7);

        return $query->count();
    }

    public function getTotalOnboard()
    {
        $query = $this->query();

        $query->where('status_recruitment', '=', 11);

        return $query->count();
    }

    public function findToken($token)
    {
        return $this->query()->where('token', $token)->first();
    }

    public function findByUserSelling($userId, $sellingId)
    {
        $query = $this->query();

        return $query->where('user_id', $userId)->where('warehouse_cv_selling_id', $sellingId)->first();;
    }

    public function findByRec($id, $userId)
    {
        $query = $this->query()->select('warehouse_cv_selling_buys.*');
        $query->selectRaw('coalesce((select status from payin_months where warehouse_cv_selling_buy_id = warehouse_cv_selling_buys.id order by id desc limit 1), 0) as status_payment_ctv');

        return $query->where('id', $id)->where('ctv_id', $userId)->first();
    }

}
