<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerScheduleOnboardAdmin extends Mailable
{
    use Queueable;

    protected $wareHouseCvSellingBuy;
    protected $rec;
    protected $wareHouseCvSellingBuyOnboard;

    public function __construct($wareHouseCvSellingBuy,$rec,$wareHouseCvSellingBuyOnboard)
    {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
        $this->rec = $rec;
        $this->wareHouseCvSellingBuyOnboard = $wareHouseCvSellingBuyOnboard;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $recName = $this->rec->name;
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $timeInterview = $this->wareHouseCvSellingBuyOnboard->time_book_format .' '. $this->wareHouseCvSellingBuyOnboard->date_book_format;
        $address = $this->wareHouseCvSellingBuyOnboard->address;
        $phone = $this->wareHouseCvSellingBuy->employer->mobile;
        $email = $this->wareHouseCvSellingBuy->employer->email;
        $linkAdminDiscuss = route('luot-ban.show', $this->wareHouseCvSellingBuy->id) . '?tab=thaoluan';
        $salary = $this->wareHouseCvSellingBuy->wareHouseCvSelling->candidate_salary_expect;

        return new Content(
            view: 'email.employerScheduleOnboardAdmin',
            with: [
                'recName' => $recName,
                'candidateName' => $candidateName,
                'companyName' => $companyName,
                'position' => $position,
                'type' => $type,
                'timeInterview' => $timeInterview,
                'address' => $address,
                'linkAdminDiscuss' => $linkAdminDiscuss,
                'phone' => $phone,
                'email' => $email,
                'salary' => $salary,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $candidateName = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_name;
        $type = $this->wareHouseCvSellingBuy->wareHouseCvSelling->type_of_sale;
        $companyName = $this->wareHouseCvSellingBuy->employer->name;
        $position = '';
        if ($type == 'cv'){
            $position = $this->wareHouseCvSellingBuy->wareHouseCvSelling->wareHouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->wareHouseCvSellingBuy->job)){
            $position = $this->wareHouseCvSellingBuy->job->name;
        }
        $message->subject('[Recland] Lời mời nhận việc Ứng viên '.$candidateName.' vị trí  '.$position.' từ công ty '.$companyName);
        return $this;
    }

}
