<?php

namespace App\Jobs;

use App\Notifications\RemindScheduleInterview;
use App\Notifications\RemindScheduleInterviewSubmit;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendemailBookSubmit implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $submitCv;
    protected $user;
    protected $book;

    public function __construct($submitCv,$user, $book)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
        $this->book = $book;
    }


    public function handle()
    {
        //status "waiting interview" mới gửi mail nhé, tránh việc CTV từ chối lịch rồi, hoặc NTD update thủ công rồi vẫn nhận đc mail
        if ($this->submitCv->status == 7) {
            $this->user->notify(new RemindScheduleInterviewSubmit($this->submitCv, $this->user, $this->book));
        }
    }


}
