# Tài liệu Đặc tả Chức năng Nghiệp vụ Ch<PERSON>h - RecLand

**Phiên bản:** 2.0  
**Ngày:** 2025-01-09  
**Tác gi<PERSON>:** AI Assistant

---

## 1. Tổng quan Luồng <PERSON>hi<PERSON> vụ

### 1.1. <PERSON> Luồng Chính

RecLand hoạt động dựa trên hai luồng nghiệp vụ chính:

1. **MarketCV (Mua bán CV):** NTD chủ động tìm kiếm và mua CV từ kho CV có sẵn
2. **Submit CV (Ứng tuyển):** UV/CTV nộp hồ sơ vào tin tuyển dụng cụ thể

### 1.2. Ba Hình thức Dịch vụ

Mỗi luồng đều có 3 hình thức dịch vụ với mức giá tăng dần:

| <PERSON><PERSON><PERSON> thức | <PERSON><PERSON> tả | Thời điểm thanh toán |
|-----------|-------|---------------------|
| **CV Data** | Xem thông tin liên hệ cơ bản | Ngay khi mua/chấp nhận |
| **Interview** | Ứng viên đồng ý phỏng vấn | Khi ứng viên xác nhận phỏng vấn |
| **Onboard** | Ứng viên được tuyển dụng thành công | Theo giai đoạn thử việc |

---

## 2. Luồng MarketCV (Mua bán CV)

### 2.1. Quy trình Tổng quan

```
CTV đăng bán CV → NTD tìm kiếm → NTD mua CV → Thanh toán → Quy trình tuyển dụng
```

### 2.2. Giai đoạn 1: CV Data

#### 2.2.1. Controller & Endpoint
- **Controller:** `AjaxController@buyCV`
- **Method:** POST
- **Route:** `/ajax/buy-cv`
- **Service:** `WareHouseCvSellingBuyService@buyCV`

#### 2.2.2. Business Logic Flow

```php
public function buyCV($request) {
    DB::beginTransaction();
    
    // 1. Validation
    $wareHouseCvSelling = $this->wareHouseCvSellingRepository->find($request['warehouse_cv_selling_id']);
    $user = auth('client')->user();
    
    // 2. Kiểm tra điều kiện
    if (!$this->canBuyCV($wareHouseCvSelling, $user)) {
        return false;
    }
    
    // 3. Kiểm tra số dư ví
    $wallet = $user->wallet;
    if ($wallet->point < $wareHouseCvSelling->point) {
        throw new Exception('Insufficient balance');
    }
    
    // 4. Tạo giao dịch mua
    $buyRecord = $this->wareHouseCvSellingBuyRepository->create([
        'user_id' => $user->id,
        'ctv_id' => $wareHouseCvSelling->user_id,
        'warehouse_cv_selling_id' => $wareHouseCvSelling->id,
        'status_recruitment' => config('constant.status_recruitment_revert.BuyCVdatasuccessfull'),
        'price' => $wareHouseCvSelling->price,
        'point' => $wareHouseCvSelling->point,
    ]);
    
    // 5. Trừ tiền ví NTD
    $wallet->decrement('point', $wareHouseCvSelling->point);
    
    // 6. Ghi lịch sử thanh toán
    $this->wareHouseCvSellingHistoryBuyRepository->create([
        'user_id' => $user->id,
        'warehouse_cv_selling_buy_id' => $buyRecord->id,
        'type' => 0, // Trừ tiền
        'type_of_sale' => 'cv',
        'percent' => 100,
        'price' => $wareHouseCvSelling->price,
        'point' => $wareHouseCvSelling->point,
        'balance' => $wallet->point,
    ]);
    
    // 7. Ghi audit log
    $buyRecord->addAuditTag(['action' => 'buy_cv_data', 'user_id' => $user->id]);
    
    // 8. Dispatch notifications
    $wareHouseCvSelling->user->notify(new BuyCvSuccess($buyRecord));
    Mail::to(config('settings.global.email_admin'))->send(new BuyCvSuccessSendMailAdmin($buyRecord));
    
    // 9. Dispatch job thanh toán hoa hồng (sau 7 ngày)
    RecSumPoint::dispatch($buyRecord->id)->delay(now()->addDays(7));
    
    DB::commit();
    return $buyRecord;
}
```

#### 2.2.3. Status Transitions

| Trạng thái | Giá trị | Mô tả |
|------------|---------|-------|
| `BuyCVdatasuccessfull` | 18 | Mua CV Data thành công |
| `Waitingcandidateconfirm` | 1 | Chờ ứng viên xác nhận (Interview/Onboard) |
| `CandidateCancelApply` | 2 | Ứng viên từ chối |
| `Waitingsetupinterview` | 3 | Chờ setup phỏng vấn |

### 2.3. Giai đoạn 2: Interview

#### 2.3.1. Quy trình Interview

```php
// 1. NTD chọn "Interview" cho CV đã mua
public function requestInterview($buyId) {
    $buy = $this->wareHouseCvSellingBuyRepository->find($buyId);
    
    // Update status chờ ứng viên xác nhận
    $buy->update([
        'status_recruitment' => config('constant.status_recruitment_revert.Waitingcandidateconfirm')
    ]);
    
    // Gửi thông báo cho ứng viên
    $this->notifyCandidate($buy);
    
    // Dispatch job tự động từ chối sau 48h nếu không xác nhận
    RejectRecruitment::dispatch($buy->id)->delay(now()->addHours(48));
}

// 2. Ứng viên xác nhận phỏng vấn
public function confirmInterview($buyId) {
    DB::beginTransaction();
    
    $buy = $this->wareHouseCvSellingBuyRepository->find($buyId);
    
    // Kiểm tra số dư ví NTD
    $wallet = $buy->user->wallet;
    $interviewPrice = $this->getInterviewPrice($buy);
    
    if ($wallet->point < $interviewPrice) {
        throw new Exception('Insufficient balance for interview');
    }
    
    // Trừ tiền ví
    $wallet->decrement('point', $interviewPrice);
    
    // Update status
    $buy->update([
        'status_recruitment' => config('constant.status_recruitment_revert.Waitingsetupinterview'),
        'status_payment' => 2 // Đã thanh toán
    ]);
    
    // Ghi lịch sử thanh toán
    $this->logPaymentHistory($buy, 'interview');
    
    // Dispatch job thanh toán hoa hồng cho CTV (sau 24h)
    PayInterview::dispatch($buy->id)->delay(now()->addHours(24));
    
    DB::commit();
}
```

### 2.4. Giai đoạn 3: Onboard

#### 2.4.1. Quy trình Onboard

```php
// 1. Chuyển sang thử việc
public function moveToTrialWork($buyId) {
    DB::beginTransaction();
    
    $buy = $this->wareHouseCvSellingBuyRepository->find($buyId);
    $onboardPrice = $this->getOnboardPrice($buy);
    $depositAmount = $onboardPrice * 0.1; // 10% đặt cọc
    $remainingAmount = $onboardPrice * 0.9; // 90% còn lại
    
    $wallet = $buy->user->wallet;
    
    // Kiểm tra số dư cho phần còn lại
    if ($wallet->point < $remainingAmount) {
        // Tạo ghi nợ
        $this->paymentDebitRepository->create([
            'user_id' => $buy->user_id,
            'warehouse_cv_selling_buy_id' => $buy->id,
            'amount' => $remainingAmount,
            'due_date' => now()->addDays(15)
        ]);
        
        // Gửi email nhắc nhở
        $this->sendDebitReminder($buy->user);
    } else {
        // Trừ tiền ngay
        $wallet->decrement('point', $remainingAmount);
        $this->logPaymentHistory($buy, 'onboard', 90);
    }
    
    // Update status
    $buy->update([
        'status_recruitment' => 14, // Trial work
        'date_book' => now()
    ]);
    
    // Dispatch jobs thanh toán hoa hồng theo giai đoạn
    PayOnboard::dispatch($buy->id, 15)->delay(now()->addDays(30)); // 15% sau 30 ngày
    PayOnboard::dispatch($buy->id, 10)->delay(now()->addDays(45)); // 10% sau 45 ngày  
    PayOnboard::dispatch($buy->id, 75)->delay(now()->addDays(67)); // 75% sau 67 ngày
    
    // Tự động chuyển thành "Success Recruitment" sau 67 ngày
    SuccessRecruitment::dispatch($buy->id)->delay(now()->addDays(67));
    
    DB::commit();
}
```

---

## 3. Luồng Submit CV (Ứng tuyển)

### 3.1. Quy trình Tổng quan

```
NTD đăng tin → UV/CTV submit CV → NTD xem danh sách → NTD chấp nhận → Thanh toán → Quy trình tuyển dụng
```

### 3.2. Giai đoạn 1: Submit CV

#### 3.2.1. Controller & Endpoint
- **Controller:** `WareHouseSubmitCvController@submitCv`
- **Method:** POST
- **Route:** `/submit-cv`
- **Service:** `WareHouseSubmitCvService@submitCv`

#### 3.2.2. Business Logic Flow

```php
public function submitCv($request) {
    DB::beginTransaction();
    
    // 1. Validation
    $job = $this->jobRepository->find($request['job_id']);
    $warehouseCv = $this->wareHouseCvRepository->find($request['warehouse_cv_id']);
    $user = auth('client')->user();
    
    // 2. Kiểm tra điều kiện submit
    if (!$this->canSubmitCV($job, $warehouseCv, $user)) {
        return false;
    }
    
    // 3. Tạo bản ghi submit CV
    $submitCv = $this->submitCvRepository->create([
        'code' => $this->generateSubmitCode(),
        'user_id' => $user->id,
        'job_id' => $job->id,
        'company_id' => $job->company_id,
        'warehouse_cv_id' => $warehouseCv->id,
        'status' => 0, // Pending review
        'bonus' => $this->calculateBonus($job, $user),
        'authorize' => $request['authorize'] ?? 0,
    ]);
    
    // 4. Tạo metadata
    $this->submitCvMetaRepository->create([
        'submit_cv_id' => $submitCv->id,
        'candidate_name' => $warehouseCv->candidate_name,
        'candidate_mobile' => $warehouseCv->candidate_mobile,
        'candidate_email' => $warehouseCv->candidate_email,
        // ... other candidate info
    ]);
    
    // 5. Ghi lịch sử trạng thái
    $this->submitCvHistoryStatusRepository->logStatus($submitCv, $user);
    
    // 6. Dispatch notifications
    $job->user->notify(new EmployerIntroduceCandidate($submitCv));
    $user->notify(new RecIntroduceCandidate($submitCv));
    Mail::to(config('settings.global.email_admin'))->send(new RecNewCv($submitCv));
    
    DB::commit();
    return $submitCv;
}
```

### 3.3. Giai đoạn 2: NTD Chấp nhận và Thanh toán

#### 3.3.1. Payment Flow

```php
public function paymentSubmitCv($submitCvId) {
    DB::beginTransaction();
    
    $submitCv = $this->submitCvRepository->find($submitCvId);
    $user = auth('client')->user();
    
    // Xác định loại dịch vụ và status tương ứng
    $bonusType = $submitCv->job->bonus_type;
    
    switch ($bonusType) {
        case 'cv':
            $statusRecruitment = config('constant.status_recruitment_revert.BuyCVdatasuccessfull'); // 18
            break;
        case 'interview':
        case 'onboard':
            $statusRecruitment = config('constant.status_recruitment_revert.Waitingsetupinterview'); // 3
            break;
    }
    
    // Kiểm tra số dư ví
    $wallet = $user->wallet;
    $price = $this->calculatePrice($submitCv);
    
    if ($wallet->amount < $price) {
        throw new Exception('Insufficient balance');
    }
    
    // Trừ tiền ví
    $wallet->decrement('amount', $price);
    
    // Update status
    $submitCv->update([
        'status' => $statusRecruitment,
        'status_payment' => 2, // Đã thanh toán
        'payment_actual' => $price,
        'date_change_status_payment' => now()
    ]);
    
    // Ghi lịch sử thanh toán
    $this->submitCvHistoryPaymentRepository->create([
        'user_id' => $user->id,
        'submit_cv_id' => $submitCv->id,
        'type' => 0, // Trừ tiền
        'type_of_sale' => $bonusType,
        'percent' => 100,
        'price' => $price,
        'balance' => $wallet->amount,
    ]);
    
    // Dispatch job thanh toán hoa hồng cho CTV (sau 7 ngày)
    RecSumPointSubmit::dispatch($submitCv->id)->delay(now()->addDays(7));
    
    // Gửi thông báo
    $this->sendPaymentNotifications($submitCv);
    
    DB::commit();
    return true;
}
```

---

## 4. Hệ thống Commission (Hoa hồng)

### 4.1. Cơ chế Thanh toán Hoa hồng

#### 4.1.1. Thời điểm Thanh toán
- **CV Data:** Sau 7 ngày không khiếu nại
- **Interview:** Sau 24 giờ khi ứng viên xác nhận phỏng vấn
- **Onboard:** Theo 3 giai đoạn (30, 45, 67 ngày)

#### 4.1.2. Tỷ lệ Hoa hồng

| Loại | Authority = 0 | Authority = 1,2 |
|------|---------------|-----------------|
| **CV Data** | 100% | 20% |
| **Interview** | 100% | 20% |
| **Onboard** | 100% | 20% |

#### 4.1.3. Job Logic - RecSumPoint

```php
public function handle() {
    $cvSellingBuy = $this->wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);
    
    // Kiểm tra điều kiện thanh toán
    if ($cvSellingBuy->status_recruitment != config('constant.status_recruitment_revert.BuyCVdatasuccessfull') ||
        ($cvSellingBuy->status_complain != 0 && $cvSellingBuy->status_complain != 5)) {
        return; // Không thanh toán nếu có khiếu nại
    }
    
    // Tính toán hoa hồng
    $commissionRate = ($cvSellingBuy->authority == 2) ? 0.2 : 1.0;
    $commissionAmount = $cvSellingBuy->point * $commissionRate;
    
    // Cộng tiền vào ví CTV
    $ctvWallet = $cvSellingBuy->ctv->wallet;
    $ctvWallet->increment('price', $commissionAmount);
    
    // Ghi nhận vào bảng payin
    $this->payinRepository->updateOrCreate([
        'user_id' => $cvSellingBuy->ctv_id,
        'month' => now()->month,
        'year' => now()->year,
    ], [
        'total_amount' => DB::raw("total_amount + {$commissionAmount}")
    ]);
    
    // Ghi chi tiết vào payin_month
    $this->payinMonthRepository->create([
        'user_id' => $cvSellingBuy->ctv_id,
        'warehouse_cv_selling_buy_id' => $cvSellingBuy->id,
        'type' => 'cv',
        'amount' => $commissionAmount,
        'percent' => $commissionRate * 100,
    ]);
    
    // Gửi thông báo
    $cvSellingBuy->ctv->notify(new PaymentOpenTurnCv($cvSellingBuy, $commissionAmount));
}
```

---

## 5. Business Rules và Validation

### 5.1. Validation Rules

#### 5.1.1. MarketCV - Mua CV
```php
// Điều kiện mua CV
public function canBuyCV($wareHouseCvSelling, $user) {
    // 1. Kiểm tra CV còn available
    if ($wareHouseCvSelling->status != 0) {
        return false; // CV đã bị hủy hoặc chờ duyệt
    }

    // 2. Kiểm tra không tự mua CV của mình
    if ($wareHouseCvSelling->user_id == $user->id) {
        return false;
    }

    // 3. Kiểm tra không mua lại CV đã mua
    $existingBuy = $this->wareHouseCvSellingBuyRepository->getFirst([
        'user_id' => $user->id,
        'warehouse_cv_selling_id' => $wareHouseCvSelling->id
    ]);
    if ($existingBuy) {
        return false;
    }

    // 4. Kiểm tra exclude company
    if ($wareHouseCvSelling->exclude_company) {
        $excludedCompanies = explode(',', $wareHouseCvSelling->exclude_company);
        if (in_array($user->company_id, $excludedCompanies)) {
            return false;
        }
    }

    // 5. Kiểm tra số dư ví
    if ($user->wallet->point < $wareHouseCvSelling->point) {
        return false;
    }

    return true;
}
```

#### 5.1.2. Submit CV - Ứng tuyển
```php
public function canSubmitCV($job, $warehouseCv, $user) {
    // 1. Kiểm tra job còn active
    if ($job->status != 1 || $job->is_active != 1) {
        return false;
    }

    // 2. Kiểm tra job chưa hết hạn
    if (Carbon::parse($job->expire_at)->isPast()) {
        return false;
    }

    // 3. Kiểm tra không submit vào job của công ty mình
    if ($job->company_id == $user->company_id) {
        return false;
    }

    // 4. Kiểm tra không submit lại CV đã submit
    $existingSubmit = $this->submitCvRepository->getFirst([
        'user_id' => $user->id,
        'job_id' => $job->id,
        'warehouse_cv_id' => $warehouseCv->id
    ]);
    if ($existingSubmit) {
        return false;
    }

    // 5. Kiểm tra CV còn active
    if ($warehouseCv->is_active != 1) {
        return false;
    }

    return true;
}
```

### 5.2. Business Rules

#### 5.2.1. Pricing Rules
```php
public function calculatePrice($object, $type) {
    // Lấy giá từ cấu hình level_by_skill_mains
    $skillMain = $this->getSkillMainFromCV($object);
    $level = $this->getCVLevel($object);

    $priceConfig = $this->levelBySkillMainRepository->getFirst([
        'skill_main_id' => $skillMain->id,
        'level' => $level
    ]);

    switch ($type) {
        case 'cv':
            return $priceConfig->price_cv ?? 50; // Default $50
        case 'interview':
            return $priceConfig->price_interview ?? 200; // Default $200
        case 'onboard':
            return $priceConfig->price_onboard ?? 1000; // Default $1000
        default:
            return 0;
    }
}
```

#### 5.2.2. Commission Rules
```php
public function calculateCommission($transaction, $percent = 100) {
    $baseAmount = $transaction->point ?? $transaction->bonus;

    // Authority rules
    if ($transaction->authority == 2) {
        $commissionRate = 0.2; // 20% cho authority
    } else {
        $commissionRate = 1.0; // 100% cho non-authority
    }

    // Percent for onboard stages
    $stagePercent = $percent / 100;

    return $baseAmount * $commissionRate * $stagePercent;
}
```

### 5.3. Status Management

#### 5.3.1. MarketCV Status Flow
```php
// Status constants cho MarketCV
const STATUS_RECRUITMENT = [
    'Waitingcandidateconfirm' => 1,      // Chờ ứng viên xác nhận
    'CandidateCancelApply' => 2,         // Ứng viên từ chối
    'Waitingsetupinterview' => 3,        // Chờ setup phỏng vấn
    'RejectInterviewschedule' => 5,      // Từ chối lịch phỏng vấn
    'CancelInterview' => 6,              // Hủy phỏng vấn
    'WaitingInterview' => 7,             // Chờ phỏng vấn
    'PassInterview' => 8,                // Đạt phỏng vấn
    'FailInterview' => 9,                // Trượt phỏng vấn
    'Offering' => 11,                    // Đang offer
    'RejectOffer' => 12,                 // Từ chối offer
    'TrialWork' => 14,                   // Thử việc
    'SuccessRecruitment' => 16,          // Tuyển dụng thành công
    'BuyCVdatasuccessfull' => 18,        // Mua CV Data thành công
    'WaitingPayment' => 21,              // Chờ thanh toán
];
```

#### 5.3.2. Submit CV Status Flow
```php
const SUBMIT_CV_STATUS = [
    'pending-review' => 0,        // Chờ review
    'accepted' => 1,              // Chấp nhận
    'rejected' => 2,              // Từ chối
    'pass-interview' => 3,        // Đạt phỏng vấn
    'fail-interview' => 4,        // Trượt phỏng vấn
    'onboarded' => 5,            // Đã onboard
    'cancel' => 6,               // Hủy
    'draft' => 7,                // Nháp
    'offering' => 8,             // Đang offer
    'pending-confirm' => 9,      // Chờ xác nhận
    'admin-review' => 10,        // Admin review
    'admin-rejected' => 11,      // Admin từ chối
    'candidate-rejected' => 12,   // Ứng viên từ chối
];
```

---

## 6. Error Handling và Exception Management

### 6.1. Custom Exceptions
```php
class InsufficientBalanceException extends Exception {
    public function __construct($required, $available) {
        $message = "Insufficient balance. Required: {$required}, Available: {$available}";
        parent::__construct($message);
    }
}

class CVAlreadyPurchasedException extends Exception {
    public function __construct() {
        parent::__construct("CV has already been purchased by this user");
    }
}

class JobExpiredException extends Exception {
    public function __construct() {
        parent::__construct("Job posting has expired");
    }
}
```

### 6.2. Error Response Format
```php
public function handleException($exception) {
    $response = [
        'success' => false,
        'error' => [
            'code' => $exception->getCode(),
            'message' => $exception->getMessage(),
            'type' => get_class($exception)
        ]
    ];

    // Log error for debugging
    Log::error('Business Logic Error', [
        'exception' => $exception,
        'user_id' => auth('client')->id(),
        'request' => request()->all()
    ]);

    return response()->json($response, 422);
}
```

---

## 7. Workflow Automation

### 7.1. Scheduled Jobs

#### 7.1.1. Job Expiration
```php
// Command: php artisan schedule:run
public function handle() {
    // Tự động hết hạn các job
    $expiredJobs = Job::where('expire_at', '<', now())
                     ->where('status', 1)
                     ->get();

    foreach ($expiredJobs as $job) {
        $job->update(['status' => 2]); // Hết hạn

        // Thông báo cho employer
        $job->user->notify(new JobExpiredNotification($job));
    }
}
```

#### 7.1.2. Payment Reminder
```php
// Nhắc nhở thanh toán nợ
public function handle() {
    $overdueDebts = PaymentDebit::where('due_date', '<', now())
                                ->where('status', 0)
                                ->with('user')
                                ->get();

    foreach ($overdueDebts as $debt) {
        $debt->user->notify(new PaymentReminderNotification($debt));

        // Tăng số lần nhắc nhở
        $debt->increment('reminder_count');

        // Khóa tài khoản nếu quá 30 ngày
        if ($debt->due_date->diffInDays(now()) > 30) {
            $debt->user->update(['is_active' => 0]);
        }
    }
}
```

### 7.2. Event-Driven Actions

#### 7.2.1. CV Purchase Events
```php
// Event: CVPurchased
class CVPurchased {
    public $wareHouseCvSellingBuy;

    public function __construct($wareHouseCvSellingBuy) {
        $this->wareHouseCvSellingBuy = $wareHouseCvSellingBuy;
    }
}

// Listener: SendPurchaseNotifications
class SendPurchaseNotifications {
    public function handle(CVPurchased $event) {
        $buy = $event->wareHouseCvSellingBuy;

        // Thông báo cho CTV
        $buy->ctv->notify(new CVSoldNotification($buy));

        // Thông báo cho Admin
        Mail::to(config('settings.global.email_admin'))
            ->send(new CVPurchaseAdminNotification($buy));

        // Log analytics
        Analytics::track('cv_purchased', [
            'buyer_id' => $buy->user_id,
            'seller_id' => $buy->ctv_id,
            'amount' => $buy->point,
            'cv_id' => $buy->warehouse_cv_selling->warehouse_cv_id
        ]);
    }
}
```

---

## 8. Integration Points

### 8.1. External API Integrations

#### 8.1.1. CV Parser Integration
```php
public function parseCV($cvFile) {
    $apiKey = config('app.match_cv_api_key');
    $endpoint = config('app.cv_parser_endpoint');

    $response = Http::withHeaders([
        'Authorization' => "Bearer {$apiKey}",
        'Content-Type' => 'multipart/form-data'
    ])->attach('file', $cvFile, 'cv.pdf')
      ->post($endpoint);

    if ($response->successful()) {
        return $response->json();
    }

    throw new CVParsingException('Failed to parse CV: ' . $response->body());
}
```

#### 8.1.2. Payment Gateway Integration
```php
public function processZaloPayment($amount, $description) {
    $config = config('payment');

    $order = [
        'app_id' => $config['zalo_appid'],
        'app_trans_id' => date('ymd') . '_' . uniqid(),
        'app_user' => auth('client')->user()->email,
        'amount' => $amount,
        'description' => $description,
        'bank_code' => '',
        'item' => json_encode([]),
    ];

    $data = $order['app_id'] . '|' . $order['app_trans_id'] . '|' .
            $order['app_user'] . '|' . $order['amount'] . '|' .
            $order['app_time'] . '|' . $order['embed_data'] . '|' .
            $order['item'];

    $order['mac'] = hash_hmac('sha256', $data, $config['zalo_key1']);

    $response = Http::post($config['zalo_endpoint'], $order);

    return $response->json();
}
```

---

## 9. Performance Optimization

### 9.1. Database Optimization

#### 9.1.1. Query Optimization
```php
// Optimized query for CV search
public function searchCVs($filters) {
    return WareHouseCv::select([
            'id', 'candidate_name', 'candidate_job_title',
            'year_experience', 'main_skill', 'cv_private'
        ])
        ->when($filters['skill'] ?? null, function($query, $skill) {
            $query->whereRaw("JSON_CONTAINS(main_skill, ?)", [json_encode($skill)]);
        })
        ->when($filters['experience'] ?? null, function($query, $exp) {
            $query->where('year_experience', '>=', $exp);
        })
        ->when($filters['salary_min'] ?? null, function($query, $salary) {
            $query->where('candidate_salary_expect', '>=', $salary);
        })
        ->with(['wareHouseCvSellings' => function($query) {
            $query->where('status', 0)->select(['id', 'warehouse_cv_id', 'price', 'point']);
        }])
        ->paginate(20);
}
```

#### 9.1.2. Caching Strategy
```php
public function getCachedJobList($filters) {
    $cacheKey = 'jobs_' . md5(serialize($filters));

    return Cache::remember($cacheKey, 300, function() use ($filters) {
        return Job::with(['company:id,name,logo', 'jobMeta'])
                  ->where('status', 1)
                  ->where('is_active', 1)
                  ->where('expire_at', '>', now())
                  ->when($filters['category'] ?? null, function($query, $category) {
                      $query->where('career', 'like', "%{$category}%");
                  })
                  ->orderBy('created_at', 'desc')
                  ->paginate(20);
    });
}
```

---

## 10. Security Measures

### 10.1. Access Control
```php
// Middleware kiểm tra quyền truy cập CV
public function handle($request, Closure $next) {
    $cvId = $request->route('id');
    $user = auth('client')->user();

    // Kiểm tra user đã mua CV này chưa
    $hasPurchased = WareHouseCvSellingBuy::where('user_id', $user->id)
                                        ->whereHas('wareHouseCvSelling', function($query) use ($cvId) {
                                            $query->where('warehouse_cv_id', $cvId);
                                        })
                                        ->exists();

    if (!$hasPurchased) {
        abort(403, 'Access denied. CV not purchased.');
    }

    return $next($request);
}
```

### 10.2. Data Sanitization
```php
public function sanitizeInput($data) {
    return array_map(function($value) {
        if (is_string($value)) {
            return strip_tags(trim($value));
        }
        return $value;
    }, $data);
}
```

---

*Tài liệu chức năng nghiệp vụ chính hoàn tất với đầy đủ business logic, validation rules, error handling và security measures.*
