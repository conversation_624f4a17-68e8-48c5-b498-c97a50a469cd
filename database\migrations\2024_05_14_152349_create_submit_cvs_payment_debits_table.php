<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('submit_cvs_payment_debits', function (Blueprint $table) {
            $table->id();
            $table->integer('submit_cv_id')->default(0);
            $table->integer('user_id')->default(0)->comment('ntd');
            $table->integer('amount')->default(0);
            $table->integer('paid_amount')->default(0);
            $table->integer('status')->default(0)->comment('default 0, khi nạp tien trừ nợ xong thi = 1');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('submit_cvs_payment_debits');
    }
};
