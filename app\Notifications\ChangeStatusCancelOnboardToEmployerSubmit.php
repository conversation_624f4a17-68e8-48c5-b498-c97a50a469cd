<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelOnboardToEmployerSubmit extends Notification implements ShouldQueue
{

    use Queueable;

    protected $submitCv;
    protected $bonus;

    public function __construct($submitCv, $bonus)
    {
        $this->submitCv = $submitCv;
        $this->bonus = $bonus;
    }

    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    public function toMail($notifiable)
    {
        $candidateName  = $this->submitCv->warehouseCv->candidate_name;
        $employerName   = $this->submitCv->employer->name;
        $position       = $this->submitCv->job->name;
        $url            = route('employer-submitcv', ['view-detail' => $this->submitCv->id]);
        $employerWallet = route('employer-wallet');
        $linkMarket     = route('market-cv');
        return (new MailMessage)
            ->view('email.changeStatusCancelOnboardToEmployerSubmit', [
                'candidateName'  => $candidateName,
                'employerName'   => $employerName,
                'position'       => $position,
                'url'            => $url,
                'employerWallet' => $employerWallet,
                'linkMarket'     => $linkMarket,
                'bonus'          => $this->bonus,
            ])
            ->subject('[Recland] Thông báo ứng viên '.$candidateName.' đã “Cancel Onboard” vị trí '.$position);
    }

}
