@extends('admin.layouts.app')

@section('title', 'Thống kê danh sách Job')

@section('content')
<div class="page-header">
    <div class="page-leftheader">
        <h4 class="page-title mb-0">Thống kê danh sách Job</h4>
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('dashboard') }}"><i class="fe fe-home mr-2 fs-14"></i>Dashboard</a></li>
            <li class="breadcrumb-item active" aria-current="page">Thống kê Job</li>
        </ol>
    </div>
    <div class="page-rightheader">
        <div class="btn-list">
            <a href="#" id="export-csv" class="btn btn-success">
                <i class="fe fe-download mr-1"></i> Xuất CSV
            </a>
        </div>
    </div>
</div>

<!-- Alert container for export validation -->
<div id="export-alert" class="alert alert-warning alert-dismissible fade" role="alert" style="display: none;">
    <span id="export-alert-message"></span>
    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
    </button>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Bộ lọc thống kê</h4>
            </div>
            <div class="card-body">
                <form id="filter-form" method="GET">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="from_date">Từ ngày</label>
                                <input type="date" id="from_date" name="from_date" class="form-control" 
                                       value="{{ $filters['from_date'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="to_date">Đến ngày</label>
                                <input type="date" id="to_date" name="to_date" class="form-control" 
                                       value="{{ $filters['to_date'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="company_id">Công ty</label>
                                <select id="company_id" name="company_id" class="form-control select2">
                                    <option value="">Tất cả công ty</option>
                                    @foreach($companies as $company)
                                        <option value="{{ $company->id }}" 
                                                {{ ($filters['company_id'] ?? '') == $company->id ? 'selected' : '' }}>
                                            {{ $company->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="service_type">Dịch vụ</label>
                                <select id="service_type" name="service_type" class="form-control">
                                    <option value="">Tất cả dịch vụ</option>
                                    <option value="cv" {{ ($filters['service_type'] ?? '') == 'cv' ? 'selected' : '' }}>CV Data</option>
                                    <option value="interview" {{ ($filters['service_type'] ?? '') == 'interview' ? 'selected' : '' }}>CV Interview</option>
                                    <option value="onboard" {{ ($filters['service_type'] ?? '') == 'onboard' ? 'selected' : '' }}>CV Onboard</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="status">Trạng thái</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="1" {{ ($filters['status'] ?? '') == '1' ? 'selected' : '' }}>Đang tuyển</option>
                                    <option value="0" {{ ($filters['status'] ?? '') == '0' ? 'selected' : '' }}>Dừng tuyển</option>
                                    <option value="2" {{ ($filters['status'] ?? '') == '2' ? 'selected' : '' }}>Hết hạn tuyển</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label for="search">Tìm kiếm</label>
                                <input type="text" id="search" name="search" class="form-control" 
                                       placeholder="Tên job, công ty..." value="{{ $filters['search'] ?? '' }}">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fe fe-search mr-1"></i> Lọc dữ liệu
                            </button>
                            <a href="{{ route('job-statistics.index') }}" class="btn btn-secondary">
                                <i class="fe fe-refresh-cw mr-1"></i> Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Bảng thống kê Job</h4>
                <div class="card-options">
                    <span class="badge badge-primary">Tổng: {{ $statistics->total() }} job</span>
                </div>
            </div>
            <div class="card-body">
                <div id="statistics-container">
                    @include('admin.pages.job-statistics.filter-statistics')
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'Chọn công ty...',
        allowClear: true
    });

    // Filter form submission
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        filterData();
    });

    // Export CSV
    $('#export-csv').on('click', function(e) {
        e.preventDefault();

        var fromDate = $('#from_date').val();
        var toDate = $('#to_date').val();

        // Validation: Phải có filter ngày
        if (!fromDate || !toDate) {
            showExportAlert('Vui lòng chọn khoảng thời gian để xuất dữ liệu (từ ngày - đến ngày).');
            return;
        }

        // Validation: Khoảng ngày không được lớn hơn 3 tháng
        var fromDateTime = new Date(fromDate);
        var toDateTime = new Date(toDate);

        if (fromDateTime > toDateTime) {
            showExportAlert('Từ ngày không được lớn hơn đến ngày.');
            return;
        }

        var diffTime = Math.abs(toDateTime - fromDateTime);
        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        var diffMonths = diffDays / 30; // Tính gần đúng

        if (diffMonths > 3) {
            showExportAlert('Khoảng thời gian xuất dữ liệu không được vượt quá 3 tháng.');
            return;
        }

        // Nếu validation pass, thực hiện export
        var params = $('#filter-form').serialize();

        // Sử dụng AJAX để kiểm tra response từ server
        $.ajax({
            url: '{{ route("job-statistics.export-csv") }}?' + params,
            type: 'GET',
            xhrFields: {
                responseType: 'blob'
            },
            success: function(data, status, xhr) {
                // Kiểm tra content type
                var contentType = xhr.getResponseHeader('content-type');
                if (contentType && contentType.indexOf('application/json') !== -1) {
                    // Nếu là JSON, có lỗi từ server
                    var reader = new FileReader();
                    reader.onload = function() {
                        var response = JSON.parse(reader.result);
                        showExportAlert(response.message);
                    };
                    reader.readAsText(data);
                } else {
                    // Nếu là CSV, download file
                    var blob = new Blob([data], { type: 'text/csv' });
                    var url = window.URL.createObjectURL(blob);
                    var a = document.createElement('a');
                    a.href = url;
                    a.download = 'job_statistics_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.csv';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    showExportAlert(xhr.responseJSON.message);
                } else {
                    showExportAlert('Có lỗi xảy ra khi xuất dữ liệu.');
                }
            }
        });
    });

    // Bind pagination links on page load
    bindPaginationLinks();

    function filterData(url) {
        var formData = $('#filter-form').serialize();
        var ajaxUrl = url || '{{ route("job-statistics.filter-data") }}';

        $.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: formData + '&_token={{ csrf_token() }}',
            beforeSend: function() {
                $('#statistics-container').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Đang tải...</div>');
            },
            success: function(response) {
                if (response.status === 'success') {
                    $('#statistics-container').html(response.html);
                    // Bind pagination links
                    bindPaginationLinks();
                }
            },
            error: function() {
                $('#statistics-container').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu!</div>');
            }
        });
    }

    function bindPaginationLinks() {
        $(document).off('click', '.pagination a').on('click', '.pagination a', function(e) {
            e.preventDefault();
            var url = $(this).attr('href');
            if (url) {
                // Convert GET pagination URL to POST filter-data URL with page parameter
                var urlParams = new URLSearchParams(url.split('?')[1]);
                var page = urlParams.get('page');
                if (page) {
                    var formData = $('#filter-form').serialize() + '&page=' + page;
                    $.ajax({
                        url: '{{ route("job-statistics.filter-data") }}',
                        type: 'POST',
                        data: formData + '&_token={{ csrf_token() }}',
                        beforeSend: function() {
                            $('#statistics-container').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Đang tải...</div>');
                        },
                        success: function(response) {
                            if (response.status === 'success') {
                                $('#statistics-container').html(response.html);
                                bindPaginationLinks();
                            }
                        },
                        error: function() {
                            $('#statistics-container').html('<div class="alert alert-danger">Có lỗi xảy ra khi tải dữ liệu!</div>');
                        }
                    });
                }
            }
        });
    }

    // Function to show export alert
    function showExportAlert(message) {
        $('#export-alert-message').text(message);
        $('#export-alert').removeClass('fade').addClass('show').show();

        // Auto hide after 5 seconds
        setTimeout(function() {
            $('#export-alert').removeClass('show').addClass('fade');
            setTimeout(function() {
                $('#export-alert').hide();
            }, 150);
        }, 5000);
    }
});
</script>
@endsection

@section('styles')
<style>
.statistics-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.statistics-table th,
.statistics-table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    font-size: 13px;
}

.statistics-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.statistics-table tbody tr:hover {
    background-color: #f8f9fa;
}

.number-cell {
    text-align: right;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-expired {
    background-color: #fff3cd;
    color: #856404;
}

.job-link {
    color: #007bff;
    text-decoration: none;
}

.job-link:hover {
    text-decoration: underline;
}
</style>
@endsection
