<?php

namespace App\Services\Admin;

use App\Notifications\ConfirmEmployer;
use App\Repositories\BonusRepository;
use App\Repositories\EmployerTypeRepository;
use App\Repositories\JobRepository;
use App\Repositories\UserRepository;
use App\Repositories\UserRoleRepository;
use App\Services\FileServiceS3;
use Illuminate\Support\Carbon;
use Hash;

class UserService
{
    protected $userRepository;
    protected $userRoleRepository;
    protected $jobRepository;
    protected $bonusService;
    protected $bonusRepository;
    protected $employeeTypeRepository;

    public function __construct(
        UserRepository         $userRepository,
        UserRoleRepository     $userRoleRepository,
        JobRepository          $jobRepository,
        BonusService           $bonusService,
        BonusRepository        $bonusRepository,
        EmployerTypeRepository $employeeTypeRepository
    )
    {
        $this->userRepository = $userRepository;
        $this->userRoleRepository = $userRoleRepository;
        $this->jobRepository = $jobRepository;
        $this->bonusService = $bonusService;
        $this->bonusRepository = $bonusRepository;
        $this->employeeTypeRepository = $employeeTypeRepository;
    }

    public function indexService($params, $order = [], $paginate = false)
    {
        try {
            $data = $this->userRepository->getListUser($params, $order, $paginate);
            return $data;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function datatable($params)
    {
        $params = Datatable::convertRequest($params);
        $data = $this->indexService($params['request'], $params['orders'], true);
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $data->total(),
            'iTotalDisplayRecords' => $data->total(),
        ];
        return $response;
    }

    public function buildDatatable()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                    'data-orderable' => 'false',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-orderable' => 'false',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'name',
                    'data-orderable' => 'false',
                ],
                'value' => 'Tên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'user_role',
                    'data-orderable' => 'false',
                    'data-fn' => 'renderUserRole',
                ],
                'value' => 'Role',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                    'data-orderable' => 'false',
                ],
                'value' => 'Trạng thái hoạt động',
            ],
        ];

        $renderAction = [
            'actionEditAjax',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('user-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('');
    }

    public function createService($params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $dataUser = [
            'name' => $params['name'],
            'email' => $params['email'],
            'type' => config('constant.role.admin'),
            'password' => Hash::make(config('settings.global.password')),
            'is_active' => $isActive,
        ];

        $user = $this->userRepository->create($dataUser);

        foreach ($params['role'] as $role) {
            $dataUserRole = [
                'user_id' => $user->id,
                'role_id' => $role,
            ];

            $this->userRoleRepository->create($dataUserRole);
        }

        return $user;
    }

    public function detailService($id)
    {
        return $this->userRepository->findAdminById($id);
    }

    public function updateService($params, $id)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $dataUser = [
            'name' => $params['name'],
            'email' => $params['email'],
            'email_verified_at' => $params['email_verified_at'] ?? null,
            'type' => config('constant.role.admin'),
            'is_active' => $isActive,
        ];

        $this->userRoleRepository->deleteUserRole($id);
        foreach ($params['role'] as $role) {
            $dataUserRole = [
                'user_id' => $id,
                'role_id' => $role,
            ];

            $this->userRoleRepository->create($dataUserRole);
        }

        return $this->userRepository->update($id, [], $dataUser);
    }

    public function buildDatatableEmployer()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'name',
                ],
                'value' => 'Họ tên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'email',
                    'data-orderable' => 'false',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'mobile',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'company_value',
                ],
                'value' => 'Tên công ty',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'user_employer_type.type',
                ],
                'value' => 'Loại người dùng',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'source_value',
                ],
                'value' => 'Nguồn',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'verify_status',
                    'data-fn' => 'renderVerifyStatus',
                ],
                'value' => 'Verify',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsActive',
                ],
                'value' => 'Trạng thái hoạt động',
            ],
        ];

        $renderAction = [
            'actionEdit',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('employer-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('Danh sách nhà tuyển dụng');
    }

    public function datatableEmployer($params)
    {
        $params = Datatable::convertRequest($params);
        $params['request']['type'] = config('constant.role.employer');
        $data = $this->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $data = $data->load(['company' => function ($q) {
            $q->select('id', 'name');
        }]);
        $data = $data->append(['company_value']);
        $response = [
            'data' => $data->toArray(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];
        return $response;
    }

    public function totalEmployer()
    {
        return $this->userRepository->total(['type' => config('constant.role.employer')]);
    }

    public function totalEmployerCurrentMonth()
    {
        $start = Carbon::now()->startOfMonth();
        return $this->userRepository->total(['after_date' => $start, 'type' => config('constant.role.employer')]);
    }

    public function createEmployer(array $params)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $dataUser = [
            'name' => $params['name'],
            'email' => $params['email'],
            'mobile' => $params['mobile'],
            'mst' => $params['mst'],
            'company_id' => $params['company_id'],
            'type' => config('constant.role.employer'),
            'password' => Hash::make(config('settings.global.password')),
            'is_active' => $isActive,
            'source' => isset($params['source']) ? $params['source'] : '',
            'email_verified_at' => Carbon::now(),
        ];

        $user = $this->userRepository->create($dataUser);

        $this->employeeTypeRepository->create([
            'user_id' => $user->id,
            'employee_role_id' => $params['user_role'],
            'type' => $params['type_employer'],
        ]);

        return $user;
    }

    public function updateEmployer(array $params, $id)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $employer = $this->userRepository->find($id);
        $dataUser = [
            'name' => $params['name'],
            'email' => $params['email'],
            'mobile' => $params['mobile'],
            'mst' => $params['mst'],
            'company_id' => $params['company_id'],
            'work_position' => $params['work_position'] ?? null,
            'work_location' => $params['work_location'] ?? null,
            'is_active' => $isActive,
            'flg' => 0,
            'source' => isset($params['source']) ? $params['source'] : '',
        ];

        if (!$employer->email_verified_at && isset($params['company_id'])) {
            $dataUser['email_verified_at'] = Carbon::now();
        }

        $this->userRepository->update($id, [], $dataUser);
        //send noti + email NTD
        if (!$employer->email_verified_at && isset($params['company_id'])) {
            $employer->notify(new ConfirmEmployer($employer));
        }
        //update user employer, job
        $option = [
            'employer_id' => $id
        ];
        //employer active -> inactive
        if ($employer->is_active == config('constant.active') && $isActive == config('constant.inActive')) {
            $option['is_active'] = config('constant.active');
            //update is_active user
            $jobList = $this->jobRepository->totalJob($option, 1);
            if (count($jobList)) {
                foreach ($jobList as $job) {
                    $this->jobRepository->update($job->id, [], ['is_active' => config('constant.inActive'), 'flg' => 1]);
                }
            }
        }
        //employer inactive -> active
        if ($employer->is_active == config('constant.inActive') && $isActive == config('constant.active')) {
            $option['is_active'] = config('constant.inActive');
            $option['flg'] = 1;
            $jobList = $this->jobRepository->totalJob($option, 1);
            if (count($jobList)) {
                foreach ($jobList as $job) {
                    $this->jobRepository->update($job->id, [], ['is_active' => config('constant.active'), 'flg' => 1]);
                }
            }
        }

        $this->employeeTypeRepository->updateWithUserId($id, [
            'employee_role_id' => $params['type_employer'] == 'manager' ? null : $params['user_role'],
            'type' => $params['type_employer'],
        ]);

    }

    public function changePasswordById($id, $password)
    {
        return $this->userRepository->update($id, [], ['password' => Hash::make($password)]);
    }

    public function totalCollaborator()
    {
        return $this->userRepository->total(['type' => config('constant.role.rec')]);
    }

    public function totalCollaboratorCurrentMonth()
    {
        $start = Carbon::now()->startOfMonth();
        return $this->userRepository->total(['after_date' => $start, 'type' => config('constant.role.rec')]);
    }

    public function statisticalCollaboratorByMonth($fromDate = null, $toDate = null)
    {
        $data = $this->userRepository->statisticaByMonth(config('constant.role.rec'), $fromDate, $toDate);
        $data = $data->keyBy('month')->toArray();
        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }

    public function buildDatatableCollaborator()
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'name',
                ],
                'value' => 'Họ tên',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'email',
                ],
                'value' => 'Email',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'mobile',
                ],
                'value' => 'Số điện thoại',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'referral_define',
                ],
                'value' => 'Referral Definde',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'referral_code',
                ],
                'value' => 'Referral code',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'birthday_value',
                ],
                'value' => 'Ngày sinh',
            ],

            [
                'attributes' => [
                    'data-mdata' => 'is_active',
                    'data-fn' => 'renderIsPaymentStatus',
                ],
                'value' => 'Trạng thái',
            ],
        ];

        $renderAction = [
            'actionEditBonus',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('collaborator-datatable'))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('Danh sách CTV');
    }

    public function buildDatatableCollaboratorBonus($userId)
    {
        $renderValue = [
            [
                'attributes' => [
                    'data-mdata' => 'id',
                    'data-fn' => 'renderStt',
                ],
                'value' => 'No',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'id',
                ],
                'value' => 'ID',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'monthyear_value',
                ],
                'value' => 'Thời gian',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'money_value',
                ],
                'value' => 'Doanh thu',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'moneykpi_value',
                ],
                'value' => 'Thưởng KPI',
            ],
            [
                'attributes' => [
                    'data-mdata' => 'payment_status_value',
                    'data-fn' => 'renderPaymentStatusBonusValue',
                ],
                'value' => 'Trạng thái thanh toán',
            ],/*
            [
                'attributes' => [
                    'data-mdata' => 'payment_status',
                    'data-fn' => 'renderIsPaymentBonus',
                ],
                'value' => 'Active',
            ],*/
        ];
        $renderAction = [
            'renderIsPaymentBonus',
        ];

        $datatable = new Datatable();
        return $datatable
            ->setRoute(route('collaborator-history-bonus-datatable', ['user_id' => $userId]))
            ->setRenderValue($renderValue)
            ->setAction($renderAction)
            ->setTitle('Lịch sử doanh thu của CTV');
    }

    public function datatableCollaboratorBonus($userId, $params)
    {
        $params = Datatable::convertRequest($params);
        $params['request']['user_id'] = $userId;
        $data = $this->bonusService->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $response = [
            'data' => $data->items(),
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];

        return $response;
    }

    public function datatableCollaborator($params)
    {
        $params = Datatable::convertRequest($params);
        $params['request']['type'] = config('constant.role.rec');
        $data = $this->indexService($params['request'], $params['orders'], true);
        $total = $data->total();
        $res = [];
        if (count($data->items()) > 0) {
            foreach ($data->items() as $key => $value) {
                $res[$key] = $value;
                $bonusCheck = $this->bonusService->checkStatusBonus($value['id']);
                $res[$key]['check_bonus'] = $bonusCheck ? false : true;
            }
        }
        $response = [
            'data' => $res,
            'iTotalRecords' => $total,
            'iTotalDisplayRecords' => $total,
        ];

        return $response;
    }

    public function updateCollaborator(array $params, $id)
    {
        $isActive = isset($params['is_active']) ? config('constant.active') : config('constant.inActive');
        $dataUser = [
            'name' => $params['name'],
            'email' => $params['email'],
            'is_active' => $isActive,
            'birthday' => Carbon::createFromFormat('d/m/Y', $params['birthday'])->format('Y-m-d'),
        ];

        if (isset($params['avatar']) && is_file($params['avatar'])) {
            $dataUser['avatar'] = FileServiceS3::getInstance()->uploadToS3($params['avatar'], config('constant.sub_path_s3.collaborator'));
        }

        return $this->userRepository->update($id, [], $dataUser);
    }

    public function findAdminByEmail($email)
    {
        return $this->userRepository->findAdminByEmail($email);
    }

    public function teams($userId, $params)
    {
        return $this->userRepository->getTeams($userId, $params);
    }

    public function countTeams($userId, $params)
    {
        return $this->userRepository->countTeams($userId, $params);
    }

    public function detailUserTeam($id)
    {
        $months = array();
        for ($i = 1; $i <= 12; $i++) {
//            $months[] = date('m', mktime(0, 0, 0, $i, '01'));
            $months[] = $i;
        }

        $data = $this->bonusRepository->getAllWithUserId($id);
        $years = $this->getYearOfUser($id);

        $result = [];
        if (count($years)) {
            foreach ($years as $year) {
                foreach ($months as $month) {
                    foreach ($data as $item) {
                        if ($item['month'] == $month && $item['year'] == $year) {
                            $result[$year][$month]['money'] = $item['money'];
                            $result[$year][$month]['money_kpi'] = $item['money_kpi'];
                            break;
                        } else {
                            $result[$year][$month]['money'] = 0;
                            $result[$year][$month]['money_kpi'] = 0;
                        }
                    }
                }
            }
        }

        return $result;
    }

    public function getYearOfUser($userId)
    {
        return $this->bonusRepository->getYearWithUserId($userId);
    }

    public function findEmployerTypeUserId($id)
    {
        return $this->employeeTypeRepository->findWithUserId($id);
    }

    public function findById($id)
    {
        return $this->userRepository->find($id);
    }

    public function statisticalEmployerByMonth($fromDate = null, $toDate = null)
    {
        $data = $this->userRepository->statisticalEmployerByMonth(config('constant.role.employer'), $fromDate, $toDate);
        $data = $data->keyBy('month')->toArray();
        $statistical = [];
        for ($i = 1; $i <= 12; $i++) {
            $key = sprintf('%02d', $i);
            $statistical[] = !empty($data[$key]) ? (int)$data[$key]['total'] : '';
        }
        return $statistical;
    }
}
