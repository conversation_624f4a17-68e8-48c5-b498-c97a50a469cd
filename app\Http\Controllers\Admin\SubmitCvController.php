<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\SubmitCvDataTable;
use App\Http\Controllers\Controller;
use App\Services\Admin\SkillService;
use App\Services\Admin\SubmitCvPaymentDebitService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Frontend\SubmitCvOnboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubmitCvController extends Controller
{

    protected $submitCvService;
    protected $skillService;
    protected $submitCvOnboardService;
    protected $submitCvPaymentDebitService;

    public function __construct(SubmitCvService $submitCvService, SkillService $skillService, SubmitCvOnboardService $submitCvOnboardService,
    SubmitCvPaymentDebitService $submitCvPaymentDebitService)
    {
        $this->submitCvService = $submitCvService;
        $this->skillService = $skillService;
        $this->submitCvOnboardService = $submitCvOnboardService;
        $this->submitCvPaymentDebitService = $submitCvPaymentDebitService;
    }

    public function index(SubmitCvDataTable $dataTable)
    {
        $status = config('constant.status_recruitment.vi');

        return $dataTable->render('admin.pages.submitcv.list', compact('status'));
    }

    public function edit($id)
    {
        $data            = $this->submitCvService->findById($id);
        $currency        = config('constant.currency');
        $skill           = $this->skillService->getName();
        $status          = config('constant.status_recruitment.vi');
        $select_key      = select_key(optional($data->job)->bonus_type);
        $lang            = app()->getLocale();
        $yearExperiences = config('constant.sonamkinhnghiem');
        $rank            = config('job.rank.' . $lang);
        $career          = config('job.career.' . $lang);
        return view('admin.pages.submitcv.edit', compact('data', 'currency', 'skill', 'status', 'select_key', 'yearExperiences', 'rank', 'career'));
    }

    public function update($id)
    {
        try {
            DB::beginTransaction();
            if (\request()->ajax() && $this->submitCvService->updateService(request()->all(), $id)) {
                DB::commit();
                return response()->json('success', 200);
            } else {
                $validator = $this->validateData();
                if ($validator->fails()) {
                    return redirect()->back()->withErrors($validator)->withInput();
                }
                $data = request()->all();
                $data['career'] = implode(',', $data['career']);
                $this->submitCvService->updateService($data, $id);
                $this->submitCvService->updateSeMeta(request()->all(), $id);
            }
            DB::commit();
            Toast::success(__('message.edit_success'));
            return redirect()->route('submit-cv.edit', ['id' => $id]);
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::info('error log store job: ', [
                'content: ' => $exception->getMessage()
            ]);
            if (\request()->ajax()) {
                return response()->json($exception->getMessage(), $exception->getCode());
            }
            return redirect()->route('submit-cv.edit', ['id' => $id]);
        }

    }

    public function validateData()
    {
        $arr = [
            'candidate_name' => 'required',
            'candidate_email' => 'required|email',
            'assessment' => 'required',
            'candidate_job_title' => 'required',
            'candidate_mobile' => 'required|regex:/^[0-9()+.-]*$/|max:16|min:10',
            'status' => 'required',
            'skill' => 'required',
            'rank' => 'required',
            'candidate_est_timetowork' => 'required',
            'career' => 'required',
            'year_experience' => 'required',
            'candidate_salary_expect' => 'required|numeric',
        ];
        if (!\request()->get('cv_private_old')) {
            $arr['cv_private'] = 'required|mimes:pdf,docx';
        }

        if (!\request()->get('cv_public_old')) {
            $arr['cv_public'] = 'required|mimes:pdf,docx';
        }

        return \Validator::make(\request()->all(), $arr);
    }

    public function destroy($id)
    {

        try {
            $this->submitCvService->destroy($id);

            return response()->json(['success' => 'Xóa dữ liệu thành công']);
        } catch (\Exception $exception) {
            Log::error(__CLASS__ . __FUNCTION__, ['exception' => $exception]);
            return back()->with('success', false);
        }
    }

    public function preview(Request $request)
    {
        $params = $request->all();
        if ($params['tab'] == 'thongtinchitiet'){
            return $this->edit($params['id']);
        }

        if ($params['tab'] == 'thaoluan'){
            $data = $this->submitCvService->getDataDiscuss($request->all());
            $dataBook = $this->submitCvService->getDataBook($request->all());
            $onboard = $this->submitCvOnboardService->getBySubmitCvId($request->id);
            $view = view('admin.pages.submitcv.discuss', compact('data', 'onboard', 'dataBook'))->render();
            return $view;
        }

        if ($params['tab'] == 'trangthai'){
            $data = $this->submitCvService->getDataHistoryStatus($request->all());
            $submitCv = $this->submitCvService->findById($request->id);
            $audits = $submitCv->audits;
            return view('admin.pages.submitcv.historyStatus', compact('data', 'audits'))->render();
        }

        if ($params['tab'] == 'thanhtoan'){
            $data = $this->submitCvService->getDataHistoryPaymentStatus($request->all());
            return view('admin.pages.submitcv.historyPaymentStatus', compact('data'))->render();
        }

        if ($params['tab'] == 'khieunai'){
            $paymentDebitOnboard = $this->submitCvPaymentDebitService->getDataComplain($request->all());
            $paymentCv = $this->submitCvService->getDataComplain($request->all());
            if ($paymentCv){
                return view('admin.pages.submitcv.complain', compact('paymentCv'))->render();
            }elseif ($paymentDebitOnboard){
                return view('admin.pages.submitcv.complain', compact('paymentDebitOnboard'))->render();
            }else{
                return null;

            }
        }
    }

    public function updateComplain(Request $request)
    {
        $params = $request->all();
        $result = $this->submitCvService->updateComplain($params);
        if ($result) {
            $response = [
                'success' => true,
            ];
            if ($params['type'] == 4){
                Toast::success('Xác nhận khiếu nại thành công');
            }
            if ($params['type'] == 5){
                Toast::success('Từ chối khiếu nại thành công');
            }

        } else {
            $response = [
                'success' => false,
            ];
            if ($params['type'] == 4){
                Toast::error('Xác nhận khiếu nại thất bại');
            }
            if ($params['type'] == 5){
                Toast::error('Từ chối khiếu nại thất bại');
            }
        }

        return response()->json($response);    }
}
