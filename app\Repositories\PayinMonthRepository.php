<?php

namespace App\Repositories;

use App\Models\PayinMonth;
use Carbon\Carbon;

class PayinMonthRepository extends BaseRepository
{
    const MODEL = PayinMonth::class;

    public function getPayins($params = [])
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (!empty($params['month'])) {
            $query->where('month', $params['month']);
        }

        if (!empty($params['year'])) {
            $query->where('year', $params['year']);
        }

        if (!empty($params['user_id'])){
            $query->where('user_id', $params['user_id']);
        }

        $query->with(['warehouseCvSellingBuy.wareHouseCvSelling']);
        $query->orderBy('id', 'desc');

        return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
    }

    public function findByWarehouseCvSellingBuyId($warehouse_cv_selling_buy_id)
    {
        return $this->query()->where('warehouse_cv_selling_buy_id', $warehouse_cv_selling_buy_id)->first();
    }

    public function findBySubmitCvId($submit_cv_id)
    {
        return $this->query()->where('submit_cv_id', $submit_cv_id)->first();
    }

    public function countByWarehouseCvSellingBuyId($warehouse_cv_selling_buy_id)
    {
        return $this->query()->where('warehouse_cv_selling_buy_id', $warehouse_cv_selling_buy_id)->count();
    }
    public function countBySubmitCvId($submit_cv_id)
    {
        return $this->query()->where('submit_cv_id', $submit_cv_id)->count();
    }
    public function warehouseCvSellingBuyList($warehouse_cv_selling_buy_id)
    {
        return $this->query()->where('warehouse_cv_selling_buy_id', $warehouse_cv_selling_buy_id)->get();
    }

    public function submitCvSellingBuyList($submit_cv_id)
    {
        return $this->query()->where('submit_cv_id', $submit_cv_id)->get();
    }

    public function findByUser($userId, $type)
    {
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;

        $query = $this->query()->where('type', $type)->where('user_id', $userId)->where('month', $month)->where('year', $year);
        return $query->first();
    }

    public function findByUserSb($userId, $type, $submit_cv_id)
    {
        $now = Carbon::now();
        $month = $now->month;
        $year = $now->year;

        $query = $this->query()->where('type', $type)->where('user_id', $userId)->where('month', $month)->where('year', $year)->where('submit_cv_id', $submit_cv_id);
        return $query->first();
    }
}
