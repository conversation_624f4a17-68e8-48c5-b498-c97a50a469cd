<?php

namespace App\Services\Frontend;

use App\Notifications\ChangeStatusPassInterview;
use App\Notifications\EmployerSendDiscusToRec;
use App\Notifications\RecSendDiscusToEmployer;
use App\Repositories\WareHouseCvSellingBuyDiscussRepository;

class WareHouseCvSellingBuyDiscussService
{

    protected $wareHouseCvSellingBuyDiscussRepository;

    public function __construct(WareHouseCvSellingBuyDiscussRepository $wareHouseCvSellingBuyDiscussRepository)
    {
        $this->wareHouseCvSellingBuyDiscussRepository = $wareHouseCvSellingBuyDiscussRepository;
    }

    public function getByWarehouseCvBuyId($warehouseCvBuyId)
    {
        return $this->wareHouseCvSellingBuyDiscussRepository->getByWarehouseCvBuyId($warehouseCvBuyId);
    }

    public function employerSendDiscuss($wareHouseCvBuyId,$comment){
        $user = auth('client')->user();
        $data = [
            'ntd_id' => $user->id,
            'comment' => $comment,
            'warehouse_cv_selling_buy_id' => $wareHouseCvBuyId,
        ];
        $discuss = $this->wareHouseCvSellingBuyDiscussRepository->create($data);
        $discuss->load('employer');
        $sellingBuy = $discuss->wareHouseCvSellingBuy;
        $sellingBuy->rec->notify(new EmployerSendDiscusToRec($sellingBuy,$discuss));
        return $discuss;
    }

    public function recSendDiscuss($wareHouseCvBuyId,$comment){
        $user = auth('client')->user();
        $data = [
            'ctv_id' => $user->id,
            'comment' => $comment,
            'warehouse_cv_selling_buy_id' => $wareHouseCvBuyId,
        ];
        $discuss = $this->wareHouseCvSellingBuyDiscussRepository->create($data);
        $discuss->load('rec');
        $sellingBuy = $discuss->wareHouseCvSellingBuy;
        $sellingBuy->employer->notify(new RecSendDiscusToEmployer($sellingBuy,$discuss));
        return $discuss;
    }




}
