<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CandidateConfirmRecruitmentSubmitCv extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->submitCv->employer->name;  //name ntd
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->company->name;
        $type = $this->submitCv->bonus_type;
        $candidateJobTitle = '';
        if ($type == 'cv') {
            $candidateJobTitle = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)) {
            $candidateJobTitle = $this->submitCv->job->name;
        }
        $link = route('employer-submitcv') . '?discuss=' . $this->submitCv->id;
        $deadline = Carbon::now()->addDays(7)->format('d/m/Y');

        return (new MailMessage)
            ->view('email.ungvien_xacnhan_ungtuyen', [
                'name'              => $companyName,
                'candidateName'     => $candidateName,
                'companyName'       => $companyName,
                'candidateJobTitle' => $candidateJobTitle,
                'type'              => $type,
                'link'              => $link,
                'deadline'          => $deadline,
            ])
            ->subject('[Recland] Thông báo Ứng viên ' . $candidateName . ' đã đồng ý lời mời ứng tuyển của công ty ' . $companyName);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [];
    }
}
