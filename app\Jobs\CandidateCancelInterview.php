<?php

namespace App\Jobs;

use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployer;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRec;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class CandidateCancelInterview implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $wareHouseCvSellingBuyId;

    public function __construct($wareHouseCvSellingBuyId)
    {
        $this->wareHouseCvSellingBuyId = $wareHouseCvSellingBuyId;
    }

    public function handle()
    {
        $wareHouseCvSellingBuyRepository = app(WareHouseCvSellingBuyRepository::class);
        $wareHouseCvSellingBuyHistoryStatusRepository = app(WareHouseCvSellingBuyHistoryStatusRepository::class);
        $wareHouseCvSellingBuy = $wareHouseCvSellingBuyRepository->find($this->wareHouseCvSellingBuyId);

        if ($wareHouseCvSellingBuy->status_recruitment == 3) {
            //Recruiter Cancel Interview
            $wareHouseCvSellingBuy->update(['status_recruitment' => 6]);

            $wareHouseCvSellingBuyHistoryStatusRepository->logStatus($wareHouseCvSellingBuy);

            //send mail ntd
            $wareHouseCvSellingBuy->employer->notify(new ChangeStatusRecruiterCancelInterviewToEmployer($wareHouseCvSellingBuy));

            if ($wareHouseCvSellingBuy->wareHouseCvSelling->authority > 0){
                //send mail admin authority
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusRecruiterCancelInterviewToAdmin($wareHouseCvSellingBuy));
            }else{
                //send mail ctv
                $wareHouseCvSellingBuy->rec->notify(new ChangeStatusRecruiterCancelInterviewToRec($wareHouseCvSellingBuy));
            }

        }
    }




}
