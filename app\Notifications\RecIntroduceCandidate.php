<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class RecIntroduceCandidate extends Notification implements ShouldQueue
{
    use Queueable;

    protected $job;
    protected $warehouseCv;
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($job, $warehouseCv, $user)
    {
        $this->job = $job;
        $this->warehouseCv = $warehouseCv;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {

//        $settings = resolve(SettingService::class);
//
//        $lang = app()->getLocale();
//        $str = 'mail';
//
//        $arrLang = $settings->getAllByKey($str, $lang);

        $name = $this->user->name;
        $candidate = $this->warehouseCv->candidate_name;
        $jobName = $this->job->name;
//        $content = Common::transLang($arrLang['gioithieuungvienthanhcong'], ['name' => $name, 'candidate' => $candidate, 'jobName' => $jobName]);

        return (new MailMessage)
            ->view('email.recIntroduceCandidate', [
                'name'      => $name,
                'candidate' => $candidate,// . $this->warehouseCv->candidate_mobile . $this->warehouseCv->candidate_email,
                'jobName'   => $jobName,
//                    'content' => $content
            ])
            ->subject('[HRI RECLAND] [ THÔNG BÁO APPLY CV VÀO JOB THÀNH CÔNG]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);

        $str = 'notification';

        $candidate = $this->warehouseCv->candidate_name;
        $job = $this->job->name;
        $thumbnail = @$this->job->company->path_logo;

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $contentVi = Common::transLang($arrLangVi['ctv_gioithieuungvienthanhcong'], ['candidate' => $candidate, 'job' => $job]);
        $contentEn = Common::transLang($arrLangEn['ctv_gioithieuungvienthanhcong'], ['candidate' => $candidate, 'job' => $job]);

        return [
            'job_id'          => $this->job->id,
            'warehouse_cv_id' => $this->warehouseCv->id,
            'user_id'         => $this->user->id,
            'content_vi'      => $contentVi,
            'content_en'      => $contentEn,
            'thumbnail'       => $thumbnail,
        ];
    }
}
