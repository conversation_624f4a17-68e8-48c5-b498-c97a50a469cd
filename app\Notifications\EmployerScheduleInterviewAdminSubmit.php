<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;

class EmployerScheduleInterviewAdminSubmit extends Mailable
{
    use Queueable;

    protected $submitCv;
    protected $rec;
    protected $submitCvBook;

    public function __construct($submitCv,$rec,$submitCvBook)
    {
        $this->submitCv = $submitCv;
        $this->rec = $rec;
        $this->submitCvBook = $submitCvBook;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function content(): Content
    {
        $recName       = $this->rec->name;
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName   = $this->submitCv->employer->name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $timeInterview    = $this->submitCvBook->date_time_book_format;
        $phone            = $this->submitCvBook->phone;
        $address          = $this->submitCvBook->address;
        $type_of_sale     = $this->submitCv->bonus_type;
        $linkAdminDiscuss = route('submit-cv.edit', ['id' => $this->submitCv->id]) . '?tab=thaoluan';
        $linkAdminPreview = route('submit-cv.edit', ['id' => $this->submitCv->id]);

        return new Content(
            view: 'email.employerScheduleInterviewAdmin',
            with: [
                'recName'          => $recName,
                'candidateName'    => $candidateName,
                'companyName'      => $companyName,
                'position'         => $position,
                'type'             => $type,
                'timeInterview'    => $timeInterview,
                'phone'            => $phone,
                'address'          => $address,
                'linkAdminDiscuss' => $linkAdminDiscuss,
                'linkAdminPreview' => $linkAdminPreview,
                'type_of_sale'     => $type_of_sale,
            ],
        );
    }
    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->bonus_type;
        $position      = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $message->subject('[Ủy Quyền] Thông báo lịch phỏng vấn ứng viên '.$candidateName.' vị trí '.$position);
        return $this;
    }

}
