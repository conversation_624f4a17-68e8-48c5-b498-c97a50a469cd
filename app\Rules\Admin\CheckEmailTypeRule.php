<?php

namespace App\Rules\Admin;

use App\Repositories\UserRepository;
use Illuminate\Contracts\Validation\Rule;

class CheckEmailTypeRule implements Rule
{
    protected $id;
    protected $type;

    public function __construct($type,$id = null)
    {
        //

        $this->id = $id;
        $this->type = $type;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $userRepository = resolve(UserRepository::class);
        $user = $userRepository->findEmailType($value,$this->type);
        if ($this->id){
            if (!$user) {
                return true;
            } else if ($user->id == $this->id){
                return true;
            }
        }elseif (!$user) {
            return true;
        }
        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('message.email_exists');
    }
}
