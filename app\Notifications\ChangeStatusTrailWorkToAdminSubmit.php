<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusTrailWorkToAdminSubmit extends Mailable
{

    use Queueable;

    protected $submitCv;

    public function __construct($submitCv)
    {
        $this->submitCv = $submitCv;
    }

    public function content(): Content
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type          = $this->submitCv->bonus_type;
        $position      = $this->submitCv->job->name;
        $companyName   = $this->submitCv->employer->name;
        $url           = route('submit-cv.edit', ['id' => $this->submitCv->id]);
        return new Content(
            view: 'changeStatusTrailWorkToAdminSubmit',
            with: [
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'url'           => $url,
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $companyName = $this->submitCv->employer->name;
        $message->subject('[Ủy quyền] Ứng viên '.$candidateName.' bắt đầu ngày làm việc đầu tiên tại công ty '.$companyName);
        return $this;
    }


}

