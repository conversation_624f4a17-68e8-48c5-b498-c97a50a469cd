<?php

namespace App\Repositories;

use App\Models\PostMeta;

class PostMetaRepository extends BaseRepository
{
    const MODEL = PostMeta::class;

    public function findWithPostId($postId)
    {
        return $this->query()->where('post_id', $postId)->first();
    }

    public function updateWithPostId($postId, $data)
    {
        return $this->query()->where('post_id', $postId)->update($data);
    }

    public function updateIncrementTotalView($postId)
    {
        $post = $this->query()->where('post_id', $postId)->first();
        if (isset($post->view)) {
          $post->increment('view', 1);
        } else {
            $post->update(['view' => 1]);
        }
        return $post;
    }

}
