graph TD
    subgraph LuongUngTuyenCV ["Luồng Ứng tuyển CV - Submit CV"]
        subgraph GiaiDoan1 ["Giai đoạn 1: CV Data"]
            A[UV/CTV submit CV] --> B{Service xử lý logic DB Transaction}
            B --> C[Kiểm tra điều kiện và Tạo bản ghi ứng tuyển]
            C --> D[<PERSON>hi lịch sử]
            D --> E[Thông báo NTD, CTV, Admin]
            D -.-> D1[Job: RecSumPointSubmit<br/>Tự động trả hoa hồng CTV sau khi submit thành công]
            E --> F[Gửi email xác nhận cho UV]
            F --> F1{UV xác nhận qua email?}
            F1 -->|Đồng ý| F2[Status: Waiting Payment - 3]
            F1 -->|Từ chối| F3[Status: Candidate Cancel Apply - 2<br/>và Hoàn tiền NTD]
            F2 --> G[NTD nhấn Chấp nhận / Mở khóa CV]
            G --> H{Service xử lý logic DB Transaction}
            H --> I[Kiểm tra trạng thái]
            I --> J[Trừ Point NTD]
            J --> K[Trả hoa hồng cho CTV Synchronous]
            K --> L[Cập nhật trạng thái và Ghi lịch sử]
            L --> M[Commit Transaction và Phản hồi]
            L -.-> L1[Job: RecSumExpiredPointSubmit<br/>Xử lý khiếu nại tự động - 7 ngày]
        end

        subgraph GiaiDoan2 ["Giai đoạn 2: Interview"]
            INT1[NTD mời phỏng vấn] --> INT2{Service xử lý logic DB Transaction}
            INT2 --> INT3[Lấy dữ liệu và Kiểm tra]
            INT3 --> INT4[Cập nhật trạng thái: Waiting confirm calendar - 4<br/>và Ghi lịch sử]
            INT4 --> INT5[Commit và Phản hồi]
            INT4 -.-> INT6[Job: RejectBookExpireSubmit<br/>Tự động từ chối nếu CTV không xác nhận - 48 giờ]
            INT5 --> INT7{CTV xác nhận lịch phỏng vấn?}
            INT7 -->|Xác nhận| INT8[Status: Waiting Interview - 7]
            INT7 -->|Từ chối 3 lần| INT9[Status: Reject Interview<br/>và Hoàn tiền NTD]
            INT8 -.-> INT10[Job: SendemailBookSubmit<br/>Gửi email nhắc lịch cho NTD - 1 ngày sau lịch PV]
            INT8 -.-> INT11[Job: PassInterviewSubmit<br/>Tự động chuyển Pass Interview nếu NTD không cập nhật - 7 ngày sau lịch PV]
            INT8 --> INT12[NTD cập nhật kết quả phỏng vấn]
            INT12 --> INT13{Kết quả phỏng vấn}
            INT13 -->|Pass Interview - 8| INT14[Cập nhật trạng thái và Ghi lịch sử]
            INT13 -->|Fail Interview - 10| INT15[Cập nhật trạng thái và Ghi lịch sử]
            INT14 -.-> INT16[Job: PayInterviewSubmit<br/>Trả hoa hồng CTV - 24 giờ]
            INT15 -.-> INT17[Job: PayInterviewSubmit<br/>Trả hoa hồng CTV - 24 giờ]
        end

        subgraph GiaiDoan3 ["Giai đoạn 3: Onboard"]
            ONB1[NTD cập nhật trạng thái: Pass Interview - 8] --> ONB2[NTD cập nhật trạng thái: Offering - 11]
            ONB2 --> ONB3[NTD đặt lịch Onboard - 13]
            ONB3 -.-> ONB4[Job: ChangeToRejectOfferSubmit<br/>Tự động từ chối nếu UV không xác nhận - 2 ngày]
            ONB3 -.-> ONB5[Job: ChangeToTrailWorkSubmit<br/>7 ngày sau ngày Onboard]
            ONB5 --> ONB6{Kiểm tra số dư ví NTD<br/>và Xử lý ghi nợ nếu có}
            ONB6 -->|Đủ tiền| ONB7[Trừ tiền từ ví NTD<br/>90% giá trị Onboard]
            ONB6 -->|Không đủ| ONB8[Ghi nợ và Gửi email nhắc nợ]
            ONB7 --> ONB9[Cập nhật trạng thái SubmitCv: Trial work - 14]
            ONB8 --> ONB9
            ONB9 --> ONB10[Ghi lịch sử và Thông báo Admin, CTV]
            ONB10 -.-> ONB11[Gửi email nhắc hết hạn thử việc<br/>55-60 ngày]
            ONB10 -.-> ONB12[Job: PayOnboardSubmit<br/>15% hoa hồng CTV - 30 ngày sau ngày Onboard]
            ONB10 -.-> ONB13[Job: PayOnboardSubmit<br/>10% hoa hồng CTV - 45 ngày sau ngày Onboard]
            ONB10 -.-> ONB14[Job: PayOnboardSubmit<br/>75% hoa hồng CTV - 67 ngày sau ngày Onboard]
            ONB10 -.-> ONB15[Job: SuccessRecuitmentSubmit<br/>Tự động chuyển trạng thái Tuyển dụng thành công - 67 ngày sau ngày Onboard]
            ONB12 --> ONB16{Job: PayOnboardSubmit thực thi<br/>và Kiểm tra điều kiện}
            ONB13 --> ONB16
            ONB14 --> ONB16
            ONB16 --> ONB17[Xử lý thanh toán hoa hồng cho CTV]
            ONB17 --> ONB18[Quy trình hoàn tất và Cập nhật status_payment]
        end

        subgraph SadPaths ["Sad Paths - Submit CV"]
            K1[NTD khiếu nại status_complain = 1] -.-> K2[Job: RecSumExpiredPointSubmit<br/>Tự động xử lý khiếu nại và Hoàn tiền - 7 ngày]
            K2 --> K3[Hoàn tiền cho NTD và Cập nhật trạng thái khiếu nại]
            K3 --> K4[NTD và CTV nhận thông báo khiếu nại được xử lý]

            L1[UV từ chối lời mời qua email] --> L2[submitCv status = 2 Candidate Cancel Apply]
            L2 --> L3[Hoàn tiền cho NTD và Ghi log]
            L3 --> L4[CTV, NTD, Admin nhận thông báo]

            M1[CTV không xác nhận lịch PV - 48h] -.-> M2[Job: RejectBookExpireSubmit<br/>Cập nhật trạng thái và Hoàn tiền nếu đủ 3 lần từ chối]
            M2 -.-> M3[Job: OutOfDateBookInterviewSubmitCv<br/>NTD không đặt lại lịch - 7 ngày]

            N1[NTD cập nhật trạng thái:<br/>Fail phỏng vấn - 10 hoặc Từ chối Offer - 12] --> N2[Service cập nhật trạng thái]
            N2 -.-> N3[Job: DepositRefundRejectOfferSubmit<br/>Hoàn lại phí Interview - 48 giờ]

            O1[NTD cập nhật trạng thái: Thất bại thử việc - 17] --> O2[Service cập nhật trạng thái và Ghi log]
            O2 --> O3[Hoàn lại một phần phí Onboard cho NTD]

            P1[UV không xác nhận Onboard - 2 ngày] -.-> P2[Job: ChangeToRejectOfferSubmit<br/>Status: Reject Offer - 12]
            P2 -.-> P3[Job: DepositRefundRejectOfferSubmit<br/>Hoàn cọc - 48 giờ]

            Q1[Timeout các trạng thái khác] --> Q2[Các Job xử lý timeout tương ứng]
            Q2 --> Q3[Hoàn tiền và Thông báo theo logic nghiệp vụ]
        end
    end