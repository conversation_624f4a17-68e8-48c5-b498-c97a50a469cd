<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use App\Services\FileServiceS3;

class AboutUsAlbumPhoto extends BaseModel
{
    use CrudTrait, HasFactory;
//    protected $table = 'about_us_album_photos';
    protected $casts = [
        'options' => 'array',
    ];

    protected $appends = ['url_image'];

    protected $fillable = [
        'album_id',
        'photo_url',
        'type',
    ];

    public function getUrlImageAttribute(){
        return gen_url_file_s3($this->photo_url);
    }
    public function setPhotoUrlAttribute($value)
    {
        if (strpos($value, config('constant.sub_path_s3.album-about')) === false) {
            return $this->attributes['photo_url'] = FileServiceS3::getInstance()->uploadToS3($value, config('constant.sub_path_s3.album-about'));
        } else {
            return $this->attributes['photo_url'] = $value;
        }
        // $attribute_name = "image";
        // $disk = "public";
        // $destination_path = "folder_1/subfolder_1";
        // $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path, $fileName = null);
        // return $this->attributes[{$attribute_name}]; // uncomment if this is a translatable field
    }
}
