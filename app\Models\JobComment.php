<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class JobComment extends BaseModel
{
    use SoftDeletes, CrudTrait;

    protected $fillable = [
        'job_id',
        'user_id',
        'content',
        'parent_id'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::created(function ($comment) {
            if ($comment->parent_id) {
                $comment->parent()->update([
                    'last_reply_at' => now()
                ]); 
            }
        });
    }
    
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function job()
    {
        return $this->belongsTo(Job::class);
    }

    public function parent()
    {
        return $this->belongsTo(JobComment::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(JobComment::class, 'parent_id');
    }
} 