# Workflow: <PERSON><PERSON><PERSON><PERSON> chức năng thống kê danh sách Job trong Admin

**Ng<PERSON>y tạo:** 18/01/2025  
**<PERSON><PERSON> tả:** <PERSON><PERSON><PERSON> chức năng thống kê danh sách job trong admin với các cột: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> vự<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> lượng C<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> đi<PERSON> là<PERSON> việ<PERSON>, <PERSON><PERSON><PERSON> trạng.

## Các tác vụ đã thực hiện

### 1. Tạo config status_recruitment_approved
**File:** `config/constant.php`
- **Nội dung:** Thêm config `status_recruitment_approved` với các trạng thái được duyệt cho từng loại service:
  - `cv`: BuyCVdatasuccessfull (18)
  - `interview`: PassInterview (8), FailInterview (10), Waitingsetupinterview (3), Waitingconfirmcalendar (4), RejectInterviewschedule (5), WaitingInterview (7), SuccessRecruitment (16)
  - `onboard`: Tất cả trạng thái của interview + Offering (11), Waitingonboard (13), Trialwork (14)

### 2. Tạo JobStatisticsController
**File:** `app/Http/Controllers/Admin/JobStatisticsController.php`
- **Chức năng:**
  - `index()`: Hiển thị trang thống kê với bộ lọc
  - `filterData()`: Xử lý AJAX filter dữ liệu
  - `exportCsv()`: Xuất dữ liệu ra file CSV
- **Bộ lọc:** Từ ngày, đến ngày, công ty, loại dịch vụ, trạng thái, tìm kiếm

### 3. Tạo JobStatisticsService
**File:** `app/Services/Admin/JobStatisticsService.php`
- **Chức năng chính:**
  - `getJobStatistics()`: Lấy danh sách job với thống kê
  - `calculateJobStatistics()`: Tính toán metrics cho từng job
  - `getApprovedStatuses()`: Lấy trạng thái được duyệt theo service type
  - `exportToCsv()`: Xuất dữ liệu ra CSV
- **Metrics tính toán:**
  - Tổng số Apply: Đếm tất cả submit_cvs của job
  - Số CV được duyệt: Đếm submit_cvs có status trong danh sách approved
  - Thành tiền: Tổng bonus_ntd của CV được duyệt

### 4. Thêm route admin
**File:** `routes/admin.php`
- **Routes:**
  - `GET /admin/job-statistics`: Trang chính
  - `POST /admin/job-statistics/filter-data`: AJAX filter
  - `GET /admin/job-statistics/export-csv`: Xuất CSV
- **Import:** Thêm `JobStatisticsController` vào danh sách import

### 5. Tạo view trang thống kê
**File:** `resources/views/admin/pages/job-statistics/index.blade.php`
- **Nội dung:**
  - Form bộ lọc với các trường: từ ngày, đến ngày, công ty, dịch vụ, trạng thái, tìm kiếm
  - Container hiển thị bảng thống kê
  - Button xuất CSV
  - JavaScript xử lý AJAX filter và xuất CSV
  - CSS styling cho bảng thống kê

### 6. Tạo view filter thống kê
**File:** `resources/views/admin/pages/job-statistics/filter-statistics.blade.php`
- **Nội dung:**
  - Bảng hiển thị thống kê với 10 cột theo yêu cầu
  - Vị trí đang tuyển có link đến frontend
  - Hiển thị tổng cộng ở cuối bảng
  - Pagination cho dữ liệu
  - Empty state khi không có dữ liệu
  - CSS styling cho status badge và number formatting

## Cấu trúc dữ liệu

### Bảng thống kê gồm các cột:
1. **ID Job**: ID của job (#123)
2. **Công ty**: Tên công ty + quy mô nhân viên
3. **Ngành nghề**: Lấy từ config('job.career.vi') theo job.career
4. **Dịch vụ**: Loại service (CV Data, CV Interview, CV Onboard)
5. **Vị trí đang tuyển**: Tên job có link đến frontend + badge cao cấp
6. **Đơn giá**: job.bonus (VNĐ)
7. **Số lượng Apply**: Tổng submit_cvs của job
8. **Số lượng CV được duyệt**: Submit_cvs có status trong approved list
9. **Thành tiền**: Tổng bonus_ntd của CV được duyệt
10. **Địa điểm làm việc**: Từ job.address JSON
11. **Tình trạng**: Status job (Đang tuyển/Dừng tuyển/Hết hạn)

### Logic tính CV được duyệt:
- Dựa theo config `constants.status_recruitment_approved`
- Phân theo service_type: cv, interview, onboard
- Nếu filter "Tất cả dịch vụ" thì lấy tất cả trạng thái approved

## Ghi chú kỹ thuật

- Sử dụng pagination 20 items/page
- AJAX filter không reload trang
- Export CSV có UTF-8 BOM cho Excel
- Responsive design cho mobile
- Select2 cho dropdown công ty
- Number formatting cho tiền tệ
- Status badge với màu sắc phân biệt

## URL truy cập
- Trang thống kê: `/admin/job-statistics`
- Filter AJAX: `/admin/job-statistics/filter-data`
- Xuất CSV: `/admin/job-statistics/export-csv`

## Cập nhật bổ sung (18/01/2025)

### 7. Thêm cột ID Job và Ngành nghề
**Các file đã cập nhật:**
- `app/Services/Admin/JobStatisticsService.php`:
  - Thêm method `getCareerDisplay()` để lấy tên ngành nghề từ config('job.career.vi')
  - Cập nhật `calculateJobStatistics()` để thêm `career_display`
  - Cập nhật header và data CSV để bao gồm ID Job và Ngành nghề

- `resources/views/admin/pages/job-statistics/filter-statistics.blade.php`:
  - Thêm cột "ID Job" hiển thị #ID với style text-primary
  - Thêm cột "Ngành nghề" hiển thị badge với tên ngành nghề từ config
  - Điều chỉnh width các cột để phù hợp với layout mới
  - Cập nhật colspan trong tổng kết và empty state

### Cấu trúc bảng mới (11 cột):
1. ID Job (5%) - #123
2. Công ty (12%) - Tên + quy mô
3. Ngành nghề (10%) - Badge từ config job.career
4. Dịch vụ (8%) - CV Data/Interview/Onboard
5. Vị trí đang tuyển (18%) - Link + badge cao cấp
6. Đơn giá (8%) - VNĐ
7. Số lượng Apply (6%) - Badge
8. Số lượng CV được duyệt (8%) - Badge
9. Thành tiền (10%) - VNĐ
10. Địa điểm làm việc (10%) - Từ address JSON
11. Tình trạng (5%) - Status badge

### 8. Sửa logic tính toán thống kê submit_cvs
**Thay đổi quan trọng:**
- Trong hàm `calculateJobStatistics()`, thống kê về submit_cvs sẽ dựa trên `job.bonus_type` của từng job
- `$filters['service_type']` chỉ dùng để lọc các job theo bonus_type trong query chính
- Khi tính số lượng CV được duyệt và thành tiền, sẽ lấy submit_cvs có `bonus_type` giống với `job.bonus_type`
- Điều này đảm bảo thống kê chính xác theo từng loại dịch vụ của job

### 9. Thêm cột Link Frontend vào CSV export
**File cập nhật:** `app/Services/Admin/JobStatisticsService.php`
- Thêm cột "Link Frontend" vào header CSV
- Thêm `$job->frontend_url` vào data CSV
- CSV export hiện có 12 cột: ID Job, Công ty, Ngành nghề, Dịch vụ, Vị trí đang tuyển, **Link Frontend**, Đơn giá, Số lượng Apply, Số lượng CV được duyệt, Thành tiền, Địa điểm làm việc, Tình trạng
- Link frontend có format: `{APP_URL}/job/{job_slug}`

### 10. Sửa lỗi pagination với filter (405 Method not allowed)
**Vấn đề:** Khi có filter và bấm pagination, bị lỗi 405 vì pagination links gửi GET request đến route filter-data (chỉ hỗ trợ POST)

**Giải pháp đã áp dụng:**

**File:** `resources/views/admin/pages/job-statistics/index.blade.php`
- Cập nhật JavaScript để xử lý pagination với AJAX
- Thêm function `bindPaginationLinks()` để bind click event cho pagination links
- Khi click pagination, extract page number và gửi POST request đến filter-data
- Bind lại pagination links sau mỗi lần load dữ liệu mới

**File:** `app/Http/Controllers/Admin/JobStatisticsController.php`
- Thêm parameter `page` vào filters trong method `filterData()`

**File:** `app/Services/Admin/JobStatisticsService.php`
- Xử lý page parameter bằng cách set currentPageResolver
- Đảm bảo pagination hoạt động đúng với filter

**File:** `resources/views/admin/pages/job-statistics/filter-statistics.blade.php`
- Sử dụng `request()->all()` thay vì `request()->query()` cho pagination links

**Kết quả:** Pagination hoạt động mượt mà với AJAX, không reload trang, giữ được filter parameters

### 11. Sửa export CSV để xuất toàn bộ dữ liệu
**Vấn đề:** Export CSV chỉ xuất dữ liệu của trang hiện tại (30 records) thay vì toàn bộ dữ liệu phù hợp với filter

**Giải pháp:**
**File:** `app/Services/Admin/JobStatisticsService.php`
- Tạo method riêng `getAllJobsForExport()` để lấy toàn bộ dữ liệu không phân trang
- Sử dụng `->get()` thay vì `->paginate()`
- Áp dụng cùng logic filter như `getJobStatistics()` nhưng không có pagination
- Method `exportToCsv()` sử dụng `getAllJobsForExport()` thay vì `getJobStatistics()`

**Kết quả:**
- Export CSV xuất toàn bộ dữ liệu phù hợp với filter
- Không giới hạn bởi pagination (30 records/page)
- Giữ nguyên logic filter và tính toán thống kê

### 12. Thêm cột Ngày đăng vào bảng và CSV
**Các file cập nhật:**

**File:** `app/Services/Admin/JobStatisticsService.php`
- Thêm method `getCreatedDateDisplay()` để format ngày đăng: `d/m/Y H:i`
- Cập nhật `calculateJobStatistics()` để thêm `created_date_display`
- Cập nhật header CSV thêm cột "Ngày đăng" (vị trí thứ 7)
- Cập nhật data CSV thêm `$job->created_date_display`

**File:** `resources/views/admin/pages/job-statistics/filter-statistics.blade.php`
- Thêm cột "Ngày đăng" vào header bảng (width: 8%)
- Thêm hiển thị ngày đăng với class `text-center` và `text-muted`
- Điều chỉnh width các cột khác để phù hợp
- Cập nhật colspan trong tổng kết và empty state (từ 11 thành 12 cột)

**Cấu trúc bảng mới (12 cột):**
1. ID Job (5%)
2. Công ty (10%)
3. Ngành nghề (8%)
4. Dịch vụ (6%)
5. Vị trí đang tuyển (15%)
6. **Ngày đăng (8%)** ← *Mới thêm*
7. Đơn giá (8%)
8. Số lượng Apply (6%)
9. Số lượng CV được duyệt (8%)
10. Thành tiền (10%)
11. Địa điểm làm việc (10%)
12. Tình trạng (6%)

**CSV export hiện có 13 cột:** ID Job, Công ty, Ngành nghề, Dịch vụ, Vị trí đang tuyển, Link Frontend, **Ngày đăng**, Đơn giá, Số lượng Apply, Số lượng CV được duyệt, Thành tiền, Địa điểm làm việc, Tình trạng

### 13. Thêm validation cho export CSV
**Yêu cầu:** Không cho phép xuất CSV nếu không có filter ngày hoặc khoảng ngày > 3 tháng

**Các file cập nhật:**

**File:** `app/Http/Controllers/Admin/JobStatisticsController.php`
- Thêm validation trong method `exportCsv()`:
  - Kiểm tra bắt buộc phải có `from_date` và `to_date`
  - Kiểm tra khoảng thời gian không vượt quá 3 tháng
  - Kiểm tra từ ngày không lớn hơn đến ngày
  - Kiểm tra định dạng ngày hợp lệ
- Trả về JSON error response nếu validation fail

**File:** `resources/views/admin/pages/job-statistics/index.blade.php`
- Thêm validation frontend trước khi gửi request:
  - Kiểm tra bắt buộc phải có filter ngày
  - Tính toán khoảng thời gian và kiểm tra không vượt quá 3 tháng
  - Kiểm tra từ ngày không lớn hơn đến ngày
- Thêm alert container để hiển thị thông báo lỗi đẹp hơn
- Thêm function `showExportAlert()` để hiển thị thông báo
- Sử dụng AJAX để xử lý export và kiểm tra response type
- Auto hide alert sau 5 giây

**Validation rules:**
1. **Bắt buộc có filter ngày**: Phải chọn cả từ ngày và đến ngày
2. **Khoảng thời gian tối đa**: Không vượt quá 3 tháng
3. **Logic ngày**: Từ ngày không được lớn hơn đến ngày
4. **Định dạng ngày**: Phải đúng format Y-m-d

**User experience:**
- Hiển thị thông báo lỗi rõ ràng bằng alert box
- Validation cả frontend và backend
- Không reload trang khi có lỗi
- Auto hide thông báo sau 5 giây
