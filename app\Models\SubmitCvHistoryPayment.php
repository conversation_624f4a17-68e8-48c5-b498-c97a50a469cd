<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubmitCvHistoryPayment extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'submit_cvs_history_payment';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = [
        'status_payment',
    ];

    public function getStatusPaymentAttribute(){
        //type = 0 tru tien, percent
        //NTD
        /*1 => 'Đã cọc',
        2 => 'Hoàn cọc',
        3 => 'Hoàn tiền',
        4 => 'NTD Đã thanh toán',*/
        if ($this->type == 0 && $this->percent == 10){
            return 1;//Đã cọc 10%
        }
        if ($this->type == 0 && $this->percent == 90){
            return 4;//NTD Đã thanh toán
        }
        if ($this->type == 0 && $this->percent == 100){
            return 4;//NTD Đã thanh toán
        }

        if ($this->type == 1 && $this->percent == 10){
            return 2;//Hoàn cọc
        }
        if ($this->type == 1 && $this->percent == 90){
            return 3;//Hoàn tiền
        }
        if ($this->type == 1 && $this->percent == 100){
            return 3;//Hoàn tiền
        }
        //type = 3 hoan tien, percent
        return 3;
    }


    // public function wareHouseCvSelling()
    // {
    //     return $this->belongsTo(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_buy_id', 'id');
    // }

    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }
    public function submitCv()
    {
        return $this->belongsTo(SubmitCv::class, 'submit_cv_id', 'id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('d/m/Y, g:i A');
        }
        return null;
    }

    public function getPointTypeAttribute(){
        if ($this->type === 0){
            $point = '- '. number_format($this->point);
        }else{
            $point = '+ '. number_format($this->point);
        }
        return $point;
    }


}
