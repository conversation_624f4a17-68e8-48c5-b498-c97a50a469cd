<?php

namespace App\Console\Commands;

use App\Helpers\Common;
use App\Models\ItnaviSearchCv;
use App\Models\ItnaviUser;
use App\Models\ItnaviUserExternalSource;
use Illuminate\Console\Command;
use App\Models\ItnaviUserCv;
use App\Services\Frontend\WareHouseCvSellingService;
use App\Services\Frontend\WareHouseSubmitCvService;
use App\Repositories\WareHouseCvSellingRepository;
use App\Repositories\WareHouseCvRepository;

class ImportItnaviCvToSelling extends Command
{
    protected $signature = 'cv:import-itnavi';
    protected $description = 'Import CV từ Itnavi user cv sang bảng selling';

    protected $wareHouseSubmitCvService;
    protected $wareHouseCvRepository;
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingRepository;

    public function __construct(
        WareHouseSubmitCvService $wareHouseSubmitCvService,
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvRepository $wareHouseCvRepository,
        WareHouseCvSellingRepository $wareHouseCvSellingRepository
    ) {
        parent::__construct();
        $this->wareHouseSubmitCvService = $wareHouseSubmitCvService;
        $this->wareHouseCvRepository = $wareHouseCvRepository;
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingRepository = $wareHouseCvSellingRepository;
    }

    public function handle()
    {
        $this->info('Bắt đầu import...');
        $i = 0;
        $j = 0;
        // Lấy tất cả CV từ bảng itnavi_user_cv
        ItnaviUserCv::where('push_to_recland', 0)->whereNotNull('cv_public')->whereNotNull('cv_private')->chunk(100, function ($cvs) use (&$i, &$j) {
            foreach ($cvs as $cv) {
                $i++;
                // if ($i < 100) {
                //     continue;
                // }
                try {
                    # lay user_external_sources
                    $userExternalSources = ItnaviUserExternalSource::where('cv_path', $cv->cv_public)->first();
                    $user = null;
                    if (!$userExternalSources) {
                        $user = ItnaviUser::find($cv->user_id);
                        // $searchCvs = $user->searchCvs;
                        $searchCvs = ItnaviSearchCv::where('object_model_name', 'App\Models\User')->where('user_id', $user->id)->first();
                    } else {
                        $searchCvs = ItnaviSearchCv::where('object_model_name', 'App\\Models\\UserExternalSource')->where('user_id', $userExternalSources->id)->first();
                        // if ($searchCvs){
                        //     dd($searchCvs, $userExternalSources);
                        // }
                    }
                    if ($searchCvs) {
                        $j++;
                        $user = $userExternalSources ? $userExternalSources : $user;
                        $this->info("{$j} - {$user->name} - {$user->id} " . class_basename($user));
                    } else {
                        $this->info("Không tìm thấy search cv");

                        $cv->update(['push_to_recland' => -1]);
                        continue;
                    }
                    $arrSkillDescription = [];
                    if (isset($searchCvs->skill)) {
                        $skills = explode(',', $searchCvs->skill);
                        foreach ($skills as $k => $v) {
                            if ($v) {
                                $arrSkillDescription[$v] = '';
                            }
                        }
                    }
                    $mobile = Common::getTenNumberPhone($searchCvs->mobile);
                    $yoe = $searchCvs->yoe ?? 1;
                    if ($yoe < 1) {
                        $yoe = 1;
                    } elseif ($yoe > 7) {
                        $yoe = 7;
                    }
                    if (class_basename($user) == 'ItnaviUser') {
                        $params = $searchCvs->toArray();
                        // Tạo warehouse CV trước
                        $wareHouseData = [
                            'user_id'                    => 474,
                            'candidate_name'             => $searchCvs->name,
                            'candidate_email'            => strtolower($searchCvs->email),
                            'candidate_mobile'           => $mobile,
                            'assessment'                 => '',
                            'candidate_portfolio'        => data_get($params, 'candidate_portfolio', ''),
                            'candidate_job_title'        => $searchCvs->job_title,
                            'candidate_currency'         => 'VND',
                            'candidate_salary_expect'    => $searchCvs->salary_expect,
                            'candidate_salary_expect_to' => $searchCvs->salary_expect_to,
                            'year_experience'            => $yoe,
                            'candidate_address'          => data_get($params, 'candidate_address', ''),
                            'candidate_location'         => $searchCvs->location,
                            'main_skill'                 => json_encode($arrSkillDescription),
                            'career'                     => '30,31',         // IT phan mem , IT phan cung mang
                            'candidate_formwork'         => 1,
                            'candidate_est_timetowork'   => 2,
                            'source'                     => 'itnavi_import_tructiep',
                            'cv_public'                  => 'https://itnavi.s3.ap-southeast-1.amazonaws.com/' . $cv->cv_public,
                            'cv_private'                 => 'https://itnavi.s3.ap-southeast-1.amazonaws.com/' . $cv->cv_private,
                            'created_at'                 => $cv->created_at,
                            'updated_at'                 => now(),
                        ];
                    } elseif (class_basename($user) == 'ItnaviUserExternalSource') {
                        $params = $searchCvs->toArray();
                        // Tạo warehouse CV trước
                        $wareHouseData = [
                            'user_id'                    => 474,
                            'candidate_name'             => $searchCvs->name,
                            'candidate_email'            => strtolower($searchCvs->email),
                            'candidate_mobile'           => $mobile,
                            'assessment'                 => '',
                            'candidate_portfolio'        => data_get($params, 'candidate_portfolio', ''),
                            'candidate_job_title'        => $searchCvs->job_title,
                            'candidate_currency'         => 'VND',
                            'candidate_salary_expect'    => $searchCvs->salary_expect ?? 0,
                            'candidate_salary_expect_to' => $searchCvs->salary_expect_to ?? 0,
                            'year_experience'            => $yoe,
                            'candidate_address'          => data_get($params, 'candidate_address', ''),
                            'candidate_location'         => $searchCvs->location,
                            'main_skill'                 => json_encode($arrSkillDescription),
                            'career'                     => '30,31',         // IT phan mem , IT phan cung mang
                            'candidate_formwork'         => 1,
                            'candidate_est_timetowork'   => 2,
                            'source'                     => 'itnavi_import_tructiep',
                            'cv_public'                  => 'https://itnavi.s3.ap-southeast-1.amazonaws.com/' . $cv->cv_public,
                            'cv_private'                 => 'https://itnavi.s3.ap-southeast-1.amazonaws.com/' . $cv->cv_private,
                            'created_at'                 => $cv->created_at,
                            'updated_at'                 => $cv->updated_at,
                        ];
                    }

                    $wareHouseCv = $this->wareHouseCvRepository->create($wareHouseData);

                    // Tính price và level dựa vào năm kinh nghiệm
                    $price = 10000;
                    $level = 10;

                    if ($wareHouseData['year_experience'] > 4) {
                        $price = 30000;
                        $level = 12;
                    } elseif ($wareHouseData['year_experience'] > 2) {
                        $price = 20000;
                        $level = 11;
                    }

                    // Tạo selling record
                    $sellingData = [
                        'warehouse_cv_id'         => $wareHouseCv->id,
                        'level'                   => $level,
                        'type_of_sale'            => 'cv',
                        'price'                   => $price,
                        'point'                   => $price,
                        'candidate_salary_expect' => $wareHouseData['candidate_salary_expect'],
                        'skill'                   => '',
                        'authority'               => 1,
                        'status'                  => 0,
                        'candidate_mobile'        => $wareHouseData['candidate_mobile'],
                        'candidate_email'         => $wareHouseData['candidate_email'],
                        'user_id'                 => $wareHouseData['user_id'],
                        'created_at'              => $cv->created_at,
                        'updated_at'              => $cv->updated_at,
                    ];

                    $this->wareHouseCvSellingRepository->create($sellingData);
                    $cv->update(['push_to_recland' => 1]);
                    $this->info("Đã import thành công CV của {$cv->full_name}");
                    // dd($sellingData);
                } catch (\Exception $e) {
                    $this->error("Lỗi khi import CV {$cv->id}: " . $e->getMessage());
                    continue;
                }
            }
        });

        $this->info('Hoàn thành import!');
    }
}
