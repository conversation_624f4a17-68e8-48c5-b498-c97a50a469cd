<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('warehouse_cvs', function (Blueprint $table) {
            $table->tinyInteger('is_real')->default(1)->after('is_active')->comment('0: fake data, 1: real data');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('warehouse_cvs', function (Blueprint $table) {
            $table->dropColumn('is_real');
        });
    }
};
