<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TransactionRequest;
use App\Services\Admin\CompanyService;
use App\Services\Admin\JobService;
use App\Services\Admin\PaymentService;
use App\Services\Admin\SubmitCvService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $jobService;
    protected $companyService;
    protected $userService;
    protected $submitCvService;
    protected $paymentService;

    public function __construct(
        JobService $jobService,
        CompanyService $companyService,
        UserService $userService,
        SubmitCvService $submitCvService,
        PaymentService $paymentService
    ) {
        $this->jobService = $jobService;
        $this->companyService = $companyService;
        $this->userService = $userService;
        $this->submitCvService = $submitCvService;
        $this->paymentService = $paymentService;
    }

    public function index()
    {
        $datatable = $this->submitCvService->buildDatatablePayment();
        return view('admin.pages.payment.index', compact('datatable'));
    }

    public function datatable(Request $request)
    {
        $data = $this->submitCvService->datatablePayment($request->all(), true);
        return response($data);
    }

    public function getTransaction($submitCvId)
    {
        $data = $this->paymentService->indexService($submitCvId);
        $dataSubmitCv = $this->submitCvService->findById($submitCvId);

        if (count($data)) {

            $response = [
                'success'      => true,
                'data'         => $data,
                'dataSubmitCv' => $dataSubmitCv,
            ];
        } else {
            $response = [
                'success' => false,
                'data'    => [],
                'dataSubmitCv' => $dataSubmitCv,
            ];
        }

        return json_encode($response);
    }

    public function addTransaction(TransactionRequest $request)
    {
        $data = $this->paymentService->create($request->all());

        if ($data) {
            //update payment_actual
            $this->paymentService->updatePaymentActual($request->submit_cv_id);

            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return json_encode($response);
    }

    public function changeStatus($id, Request $request)
    {
        try {
            DB::beginTransaction();
            $statusPayment = $request->all()['status_payment'];
            $this->submitCvService->updateStatus($id, $statusPayment);
            DB::commit();
            Toast::success(__('message.edit_success'));
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::info('error log update change status: ', [
                'content: ' => $e->getMessage(),
            ]);

            return false;
        }
    }

}
