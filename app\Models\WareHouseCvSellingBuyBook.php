<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class WareHouseCvSellingBuyBook extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'warehouse_cv_selling_buy_books';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['date_hour','date_book_format','time_book_format', 'date_time_book_format'];

    public function wareHouseCvSellingBuy()
    {
        return $this->belongsTo(WareHouseCvSellingBuy::class, 'warehouse_cv_selling_buy_id', 'id');
    }

    public function rec(){
        return $this->hasOne(User::class,'id','ctv_id');
    }

    public function employer(){
        return $this->hasOne(User::class,'id','ntd_id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function getDateBookFormatAttribute(){
        if (!empty($this->date_book)){
            $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $this->date_book);
            return $carbonDate->format('d/m/Y');
        }
        return null;
    }

    public function getTimeBookFormatAttribute(){
        if (!empty($this->time_book)){
            $timeBook = explode(':', $this->time_book);
            $h = !empty($timeBook[0]) ? $timeBook[0] : '';
            $m = !empty($timeBook[1]) ? $timeBook[1] : '';
            return $h.':'.$m;
        }

        return null;
    }

    public function getBookTimeMinuteAttribute(){
        if (!empty($this->time_book)){
            $timeBook = explode(':', $this->time_book);
            $h = !empty($timeBook[0]) ? $timeBook[0] : '';
            $m = !empty($timeBook[1]) ? $timeBook[1] : '';
            return (int)$h*60 + (int)$m;
        }
        return 0;
    }

    public function getDateTimeBookFormatAttribute(){
        if (!empty($this->time_book) && !empty($this->date_book)){
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $this->date_book)->format('d/m/Y');

            return $date . ' ' . $this->time_book;
        }

        return null;
    }


}
