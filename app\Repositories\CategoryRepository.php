<?php

namespace App\Repositories;

use App\Models\Category;

use function Symfony\Component\Translation\t;

class CategoryRepository extends BaseRepository
{
    const MODEL = Category::class;

    public function getListCategory($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

//        if (!empty($orders)){
//            foreach ($orders as $key => $order){
//                $query->orderBy($key,$order);
//            }
//        }
        

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('name_vi', 'like', '%' . $params['search'] . '%')
                ->orWhere('name_en', 'like', '%' . $params['search'] . '%');
        }

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }
    /**
     * @return mixed
     */
    public function getListHome($limit = 3){
        $query = $this->query()
            ->where('home', 1)
            ->where('is_active', 1)
            ->orderBy('created_at', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get()->toArray();
    }

    public function findBySlug($slug){
        return $this->query()->where('slug_'.app()->getLocale(),$slug)->first();
    }

}
