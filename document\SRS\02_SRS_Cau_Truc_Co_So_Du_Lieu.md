# Tài liệu Đặc tả Cấu trúc Cơ sở Dữ liệu - RecLand

**Phi<PERSON><PERSON> bản:** 2.0  
**Ngày:** 2025-01-09  
**T<PERSON><PERSON> gi<PERSON>:** AI Assistant

---

## 1. Tổng quan cấu trúc Database

### 1.1. Database Engine
- **Primary Database:** MySQL 8.0
- **Character Set:** utf8mb4_unicode_ci
- **Engine:** InnoDB (primary), MyISAM (cho full-text search)
- **Connection:** Laravel Eloquent ORM

### 1.2. Naming Convention
- **Tables:** snake_case (ví dụ: `warehouse_cvs`, `submit_cvs`)
- **Columns:** snake_case
- **Foreign Keys:** `{table}_id` (ví dụ: `user_id`, `company_id`)
- **Pivot Tables:** `{table1}_{table2}` (ví dụ: `user_role`)

---

## 2. Bảng Core Entities

### 2.1. Bảng `users` - <PERSON><PERSON>ời dùng hệ thống

```sql
CREATE TABLE users (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    avatar VARCHAR(255) NULL,
    mobile VARCHAR(255) NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    type ENUM('admin','rec','employer') NOT NULL 
        COMMENT 'admin, rec đại diện cho CTV, employer cho nhà tuyển dụng',
    referral_code VARCHAR(255) NULL,
    referral_define VARCHAR(255) NULL,
    is_active TINYINT DEFAULT 1 COMMENT '0 in_active 1 active',
    company_id INT NULL,
    company_name VARCHAR(255) NULL,
    source VARCHAR(255) NULL,
    provider VARCHAR(255) NULL,
    provider_id VARCHAR(255) NULL,
    birthday DATE NULL,
    address VARCHAR(255) NULL,
    parent_id INT NULL COMMENT 'Người giới thiệu',
    work_position VARCHAR(255) NULL,
    flg TINYINT DEFAULT 0 COMMENT '0 default, 1 change by company',
    level TINYINT DEFAULT 0 COMMENT 'Cấp độ CTV: 0 = không đạt điều kiện, 1 = đạt điều kiện',
    last_login_at TIMESTAMP NULL,
    is_real TINYINT DEFAULT 1 COMMENT '0 fake data, 1 real data',
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    UNIQUE KEY unique_email_type (email, type)
);
```

**Mối quan hệ:**
- `hasOne(Company::class, 'id', 'company_id')` - Công ty
- `hasOne(UserInfo::class)` - Thông tin bổ sung
- `hasMany(Job::class, 'employer_id')` - Tin tuyển dụng (nếu là employer)
- `hasMany(SubmitCv::class)` - CV đã submit (nếu là rec)
- `hasOne(Wallet::class)` - Ví điện tử

### 2.2. Bảng `user_infos` - Thông tin bổ sung người dùng

```sql
CREATE TABLE user_infos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    bank_account VARCHAR(255) NULL,
    bank_account_number VARCHAR(255) NULL,
    bank_name VARCHAR(255) NULL,
    bank_branch VARCHAR(255) NULL,
    is_active TINYINT DEFAULT 1 COMMENT '0 inactive 1 active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL
);
```

### 2.3. Bảng `companies` - Công ty

```sql
CREATE TABLE companies (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    address JSON NOT NULL COMMENT 'Địa chỉ dạng JSON array',
    logo VARCHAR(255) NOT NULL,
    banner VARCHAR(255) NOT NULL,
    scale VARCHAR(255) NOT NULL COMMENT 'Quy mô công ty',
    website VARCHAR(255) NULL,
    about MEDIUMTEXT NULL,
    mst VARCHAR(255) NULL COMMENT 'Mã số thuế',
    priority_career VARCHAR(255) NULL,
    image JSON NULL COMMENT 'Hình ảnh công ty',
    video JSON NULL COMMENT 'Video giới thiệu',
    home TINYINT DEFAULT 0 COMMENT 'Hiển thị trang chủ',
    upload_business_registration VARCHAR(255) NULL COMMENT 'Giấy phép kinh doanh',
    is_active TINYINT DEFAULT 1 COMMENT '0 inactive 1 active',
    is_real TINYINT DEFAULT 1 COMMENT '0 fake data, 1 real data',
    admin_id INT NULL COMMENT 'Admin được assign quản lý',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FULLTEXT(name)
);
```

**Mối quan hệ:**
- `hasMany(Job::class)` - Tin tuyển dụng
- `hasMany(SubmitCv::class)` - CV ứng tuyển
- `belongsTo(User::class, 'admin_id')` - Admin quản lý

### 2.4. Bảng `job` - Tin tuyển dụng

```sql
CREATE TABLE job (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    employer_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    file_jd VARCHAR(255) NULL COMMENT 'File job description',
    jd LONGTEXT NULL COMMENT 'Job description content',
    address JSON NOT NULL COMMENT 'Địa chỉ làm việc dạng JSON',
    vacancies VARCHAR(255) DEFAULT '1' COMMENT 'Số lượng tuyển',
    career VARCHAR(255) NULL COMMENT 'Ngành nghề',
    skills TEXT NULL COMMENT 'Kỹ năng yêu cầu',
    rank TEXT NULL COMMENT 'Cấp bậc',
    type VARCHAR(255) DEFAULT 'all' COMMENT 'Loại công việc',
    bonus_type VARCHAR(255) NOT NULL COMMENT 'cv, interview, onboard',
    bonus INT DEFAULT 0 COMMENT 'Tiền thưởng',
    incentive INT DEFAULT 0 COMMENT 'Tiền thưởng bổ sung',
    bonus_currency VARCHAR(255) DEFAULT 'USD',
    bonus_self_apply INT DEFAULT 0 COMMENT 'Thưởng tự ứng tuyển',
    bonus_for_ctv INT DEFAULT 0 COMMENT 'Thưởng cho CTV',
    salary_min INT NULL,
    salary_max INT NULL,
    salary_currency VARCHAR(255) DEFAULT 'USD',
    expire_at DATE NOT NULL,
    remote TINYINT DEFAULT 0 COMMENT 'Làm việc từ xa',
    urgent TINYINT DEFAULT 0 COMMENT 'Tuyển gấp',
    status TINYINT DEFAULT 1 COMMENT '0: dừng tuyển, 1: đang tuyển, 2: hết hạn',
    is_active TINYINT DEFAULT 1 COMMENT '0 inactive 1 active',
    flg TINYINT DEFAULT 0 COMMENT '0 default, 1 change by user employer',
    note TEXT NULL,
    level TINYINT DEFAULT 0 COMMENT 'Level job: 0 - Thường, 1 - Cao cấp',
    job_type VARCHAR(255) NULL COMMENT 'Hình thức làm việc',
    skill_id VARCHAR(255) NULL COMMENT 'ID kỹ năng chính',
    is_real TINYINT DEFAULT 1 COMMENT '0 fake data, 1 real data',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FULLTEXT(name),
    FULLTEXT(skills)
);
```

**Mối quan hệ:**
- `belongsTo(Company::class)` - Công ty
- `belongsTo(User::class, 'employer_id')` - Nhà tuyển dụng
- `hasOne(JobMeta::class)` - Metadata
- `hasOne(JobSeo::class)` - SEO data
- `hasMany(SubmitCv::class)` - CV ứng tuyển

### 2.5. Bảng `job_meta` - Metadata tin tuyển dụng

```sql
CREATE TABLE job_meta (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    payment_fee INT NULL COMMENT 'Phí thanh toán',
    payment_actual INT NULL COMMENT 'Số tiền thực tế',
    status_payment TINYINT NULL COMMENT 'Trạng thái thanh toán',
    date_change_status_payment DATETIME NULL,
    interest_rate INT NULL COMMENT 'Lãi suất',
    interest_money INT NULL COMMENT 'Tiền lãi',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 2.6. Bảng `job_seo` - SEO data tin tuyển dụng

```sql
CREATE TABLE job_seo (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    job_id INT NOT NULL,
    title_vi VARCHAR(255) NULL,
    title_en VARCHAR(255) NULL,
    description_vi TEXT NULL,
    description_en TEXT NULL,
    keyword_vi TEXT NULL,
    keyword_en TEXT NULL,
    image VARCHAR(255) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 3. Bảng Warehouse CV System

### 3.1. Bảng `warehouse_cvs` - Kho CV

```sql
CREATE TABLE warehouse_cvs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT 'Ứng viên (nếu có)',
    candidate_name VARCHAR(255) NULL,
    candidate_mobile VARCHAR(255) NULL,
    candidate_email VARCHAR(255) NULL,
    candidate_job_title VARCHAR(255) NULL,
    candidate_salary_expect INT NULL,
    candidate_salary_expect_to INT NULL,
    candidate_portfolio VARCHAR(255) NULL,
    candidate_currency VARCHAR(255) DEFAULT 'USD',
    candidate_address VARCHAR(255) NULL,
    candidate_location VARCHAR(255) NULL,
    candidate_formwork VARCHAR(255) NULL COMMENT 'Hình thức làm việc',
    candidate_est_timetowork VARCHAR(255) NULL COMMENT 'Thời gian có thể làm việc',
    career VARCHAR(255) NULL COMMENT 'Ngành nghề',
    cv_public VARCHAR(255) NULL COMMENT 'CV công khai (có thông tin liên hệ)',
    cv_private VARCHAR(255) NULL COMMENT 'CV riêng tư (ẩn thông tin)',
    assessment TEXT NULL COMMENT 'Đánh giá ứng viên',
    main_skill TEXT NULL COMMENT 'Kỹ năng chính (JSON)',
    year_experience INT NULL COMMENT 'Số năm kinh nghiệm',
    rank VARCHAR(255) NULL COMMENT 'Cấp bậc',
    source VARCHAR(255) NULL COMMENT 'Nguồn CV',
    is_active TINYINT DEFAULT 1 COMMENT '0 inactive 1 active',
    is_real TINYINT DEFAULT 1 COMMENT '0 fake data, 1 real data',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

**Mối quan hệ:**
- `belongsTo(User::class)` - Ứng viên (nếu có)
- `hasMany(WareHouseCvSelling::class)` - Đăng bán CV
- `morphMany(MetaData::class, 'object')` - Metadata bổ sung

### 3.2. Bảng `warehouse_cv_sellings` - Đăng bán CV

```sql
CREATE TABLE warehouse_cv_sellings (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'CTV đăng bán',
    warehouse_cv_id INT NOT NULL,
    level TINYINT NOT NULL COMMENT 'Cấp độ CV',
    type_of_sale VARCHAR(255) NOT NULL COMMENT 'cv, interview, onboard',
    skill VARCHAR(255) NULL COMMENT 'Kỹ năng',
    candidate_salary_expect INT NULL,
    price INT NOT NULL COMMENT 'Giá bán',
    point INT NOT NULL COMMENT 'Điểm thưởng',
    exclude_company VARCHAR(255) NULL COMMENT 'Loại trừ công ty',
    authority TINYINT DEFAULT 0 COMMENT '0: Không ủy quyền, 1: ủy quyền, 2: xác nhận, 3: từ chối',
    status TINYINT DEFAULT 0 COMMENT '0: đăng bán, 1: hủy đăng bán, 2: Chờ duyệt',
    reason TEXT NULL COMMENT 'Lý do (nếu bị từ chối)',
    is_real TINYINT DEFAULT 1 COMMENT '0 fake data, 1 real data',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL
);
```

**Mối quan hệ:**
- `belongsTo(User::class)` - CTV
- `belongsTo(WareHouseCv::class)` - CV
- `hasMany(WareHouseCvSellingBuy::class)` - Giao dịch mua

### 3.3. Bảng `warehouse_cv_selling_buys` - Giao dịch mua CV

```sql
CREATE TABLE warehouse_cv_selling_buys (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'NTD mua',
    ctv_id INT NOT NULL COMMENT 'CTV bán',
    warehouse_cv_selling_id INT NOT NULL,
    job_id INT NULL COMMENT 'Tin tuyển dụng liên quan',
    status TINYINT DEFAULT 0,
    status_recruitment TINYINT DEFAULT 0 COMMENT 'Trạng thái tuyển dụng',
    status_payment TINYINT DEFAULT 0 COMMENT 'Trạng thái thanh toán',
    status_complain TINYINT DEFAULT 0 COMMENT 'Trạng thái khiếu nại',
    price INT NOT NULL COMMENT 'Giá mua',
    point INT NOT NULL COMMENT 'Điểm thưởng',
    date_book DATETIME NULL COMMENT 'Ngày đặt lịch',
    date_change_status DATETIME NULL,
    date_change_status_payment DATETIME NULL,
    count_complain INT DEFAULT 0,
    date_complain DATETIME NULL,
    txt_complain TEXT NULL COMMENT 'Nội dung khiếu nại',
    img_complain VARCHAR(255) NULL COMMENT 'Hình ảnh khiếu nại',
    view_token VARCHAR(255) NULL COMMENT 'Token xem CV',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

**Mối quan hệ:**
- `belongsTo(User::class, 'user_id')` - NTD
- `belongsTo(User::class, 'ctv_id')` - CTV
- `belongsTo(WareHouseCvSelling::class)` - CV đang bán
- `belongsTo(Job::class)` - Tin tuyển dụng
- `hasMany(WareHouseCvSellingHistoryBuy::class)` - Lịch sử thanh toán

---

## 4. Bảng Submit CV System

### 4.1. Bảng `submit_cvs` - CV ứng tuyển

```sql
CREATE TABLE submit_cvs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(255) NULL COMMENT 'Mã CV',
    user_id BIGINT NULL COMMENT 'CTV submit',
    job_id BIGINT NULL,
    company_id BIGINT NULL,
    candidate_salary_expect BIGINT NULL,
    candidate_currency VARCHAR(255) NULL,
    warehouse_cv_id BIGINT NULL,
    status TINYINT NULL COMMENT '0:pending-review, 1:accepted, 2:rejected, 3:pass-interview, 4:fail-interview, 5:onboarded, 6:cancel, 7:draft, 8:offering, 9:pending-confirm, 10:admin-review, 11:admin-rejected, 12:candidate-rejected',
    status_payment TINYINT NULL COMMENT 'Trạng thái thanh toán',
    status_complain TINYINT DEFAULT 0 COMMENT 'Trạng thái khiếu nại',
    bonus INT NULL COMMENT 'Tiền thưởng',
    authorize TINYINT DEFAULT 0 COMMENT 'Ủy quyền',
    authorize_status TINYINT DEFAULT 0,
    date_change_status DATETIME NULL,
    date_change_status_payment DATETIME NULL,
    date_confirm_candidate DATETIME NULL COMMENT 'Ngày ứng viên xác nhận',
    payment_fee INT NULL,
    payment_actual INT NULL,
    interest_rate INT NULL COMMENT 'Lãi suất',
    interest_money INT NULL COMMENT 'Tiền lãi',
    confirm_token VARCHAR(255) NULL COMMENT 'Token xác nhận tuyển dụng',
    txt_complain TEXT NULL COMMENT 'Nội dung khiếu nại',
    img_complain VARCHAR(255) NULL COMMENT 'Hình ảnh khiếu nại',
    count_complain INT DEFAULT 0,
    date_complain DATETIME NULL,
    note TEXT NULL,
    last_discuss_time DATETIME NULL COMMENT 'Thời gian thảo luận cuối',
    employer_feedback TEXT NULL COMMENT 'Phản hồi từ nhà tuyển dụng',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

**Mối quan hệ:**
- `belongsTo(User::class)` - CTV
- `belongsTo(Job::class)` - Tin tuyển dụng
- `belongsTo(Company::class)` - Công ty
- `belongsTo(WareHouseCv::class)` - CV
- `hasOne(SubmitCvMeta::class)` - Metadata
- `hasMany(SubmitCvHistoryStatus::class)` - Lịch sử trạng thái
- `hasMany(SubmitCvHistoryPayment::class)` - Lịch sử thanh toán

### 4.2. Bảng `submit_cv_meta` - Metadata CV ứng tuyển

```sql
CREATE TABLE submit_cv_meta (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    submit_cv_id INT NOT NULL,
    candidate_name VARCHAR(255) NULL,
    candidate_mobile VARCHAR(255) NULL,
    candidate_email VARCHAR(255) NULL,
    candidate_job_title VARCHAR(255) NULL,
    candidate_address VARCHAR(255) NULL,
    candidate_portfolio VARCHAR(255) NULL,
    cv_file VARCHAR(255) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 5. Bảng Payment & Wallet System

### 5.1. Bảng `wallets` - Ví điện tử

```sql
CREATE TABLE wallets (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    type ENUM('rec','employer') NOT NULL COMMENT 'rec: CTV, employer: NTD',
    point INT DEFAULT 0 COMMENT 'Điểm cho NTD',
    price INT DEFAULT 0 COMMENT 'Tiền cho CTV',
    amount DECIMAL(15,2) DEFAULT 0 COMMENT 'Số dư chính',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 5.2. Bảng `wallet_transactions` - Giao dịch ví

```sql
CREATE TABLE wallet_transactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    wallet_id BIGINT UNSIGNED NOT NULL,
    amount DECIMAL(15,2) NOT NULL COMMENT 'Số tiền thay đổi (dương: cộng, âm: trừ)',
    balance_after DECIMAL(15,2) NOT NULL COMMENT 'Số dư sau khi thay đổi',
    object_type VARCHAR(255) NULL COMMENT 'Polymorphic type',
    object_id BIGINT UNSIGNED NULL COMMENT 'Polymorphic id',
    note VARCHAR(255) NULL COMMENT 'Ghi chú về giao dịch',
    type VARCHAR(255) NULL COMMENT 'Loại giao dịch',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (wallet_id) REFERENCES wallets(id) ON DELETE CASCADE,
    INDEX idx_wallet_id (wallet_id),
    INDEX idx_created_at (created_at)
);
```

---

## 6. Bảng History & Audit

### 6.1. Bảng `warehouse_cv_selling_history_buys` - Lịch sử thanh toán MarketCV

```sql
CREATE TABLE warehouse_cv_selling_history_buys (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'NTD',
    warehouse_cv_selling_buy_id INT NOT NULL,
    type TINYINT DEFAULT 0 COMMENT '0: trừ tiền, 1: hoàn tiền',
    type_of_sale VARCHAR(255) NOT NULL COMMENT 'cv, interview, onboard',
    percent INT DEFAULT 0 COMMENT '10,90% cho onboard, 100% cho interview,cv',
    comment TEXT NULL,
    price INT NOT NULL,
    point INT NOT NULL,
    balance INT NOT NULL COMMENT 'Số dư sau giao dịch',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 6.2. Bảng `warehouse_cv_selling_buy_history_status` - Lịch sử trạng thái MarketCV

```sql
CREATE TABLE warehouse_cv_selling_buy_history_status (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'Người thay đổi',
    warehouse_cv_selling_buy_id INT NOT NULL,
    status_recruitment_old TINYINT NULL,
    status_recruitment_new TINYINT NULL,
    comment TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 6.3. Bảng `submit_cvs_history_status` - Lịch sử trạng thái Submit CV

```sql
CREATE TABLE submit_cvs_history_status (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'Người thay đổi',
    submit_cv_id INT NOT NULL,
    status_old TINYINT NULL,
    status_new TINYINT NULL,
    comment TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 6.4. Bảng `submit_cvs_history_payment` - Lịch sử thanh toán Submit CV

```sql
CREATE TABLE submit_cvs_history_payment (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'NTD',
    submit_cv_id INT NOT NULL,
    type TINYINT DEFAULT 0 COMMENT '0: trừ tiền, 1: hoàn tiền',
    type_of_sale VARCHAR(255) NOT NULL,
    percent INT DEFAULT 0,
    comment TEXT NULL,
    price INT NOT NULL,
    point INT NOT NULL,
    balance INT NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 6.5. Bảng `audits` - Audit log hệ thống

```sql
CREATE TABLE audits (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_type VARCHAR(255) NULL,
    user_id BIGINT UNSIGNED NULL,
    event VARCHAR(255) NOT NULL COMMENT 'created, updated, deleted',
    auditable_type VARCHAR(255) NOT NULL,
    auditable_id BIGINT UNSIGNED NOT NULL,
    old_values TEXT NULL COMMENT 'Giá trị cũ (JSON)',
    new_values TEXT NULL COMMENT 'Giá trị mới (JSON)',
    url TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent VARCHAR(1023) NULL,
    tags VARCHAR(255) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_auditable (auditable_type, auditable_id),
    INDEX idx_user (user_id, user_type)
);
```

---

## 7. Bảng Payment & Transaction

### 7.1. Bảng `zalopay_transactions` - Giao dịch ZaloPay

```sql
CREATE TABLE zalopay_transactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    app_trans_id VARCHAR(255) NOT NULL COMMENT 'Transaction ID từ app',
    zp_trans_id VARCHAR(255) NULL COMMENT 'Transaction ID từ ZaloPay',
    amount INT NOT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: pending, 1: success, 2: failed',
    raw_data TEXT NULL COMMENT 'Raw response data',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    UNIQUE KEY unique_app_trans_id (app_trans_id)
);
```

### 7.2. Bảng `deposits` - Nạp tiền

```sql
CREATE TABLE deposits (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: pending, 1: approved, 2: rejected',
    deposit_time DATETIME NULL,
    balance DECIMAL(15,2) DEFAULT 0 COMMENT 'Số dư sau khi nạp',
    transaction_source_id VARCHAR(255) NULL COMMENT 'ID giao dịch nguồn',
    note TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 7.3. Bảng `payment_debits` - Ghi nợ MarketCV

```sql
CREATE TABLE payment_debits (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'NTD',
    warehouse_cv_selling_buy_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: chưa thanh toán, 1: đã thanh toán',
    due_date DATE NULL,
    paid_at DATETIME NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 7.4. Bảng `submit_cvs_payment_debits` - Ghi nợ Submit CV

```sql
CREATE TABLE submit_cvs_payment_debits (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'NTD',
    submit_cv_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: chưa thanh toán, 1: đã thanh toán',
    due_date DATE NULL,
    paid_at DATETIME NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 8. Bảng Commission & Bonus

### 8.1. Bảng `payins` - Tổng thu nhập CTV theo tháng/năm

```sql
CREATE TABLE payins (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'CTV',
    month INT NOT NULL,
    year INT NOT NULL,
    total_amount DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    UNIQUE KEY unique_user_month_year (user_id, month, year)
);
```

### 8.2. Bảng `payin_months` - Chi tiết thu nhập CTV trong tháng

```sql
CREATE TABLE payin_months (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT 'CTV',
    payin_id INT NOT NULL,
    warehouse_cv_selling_buy_id INT NULL,
    submit_cv_id INT NULL,
    type VARCHAR(255) NOT NULL COMMENT 'cv, interview, onboard',
    amount DECIMAL(15,2) NOT NULL,
    percent INT DEFAULT 100 COMMENT 'Phần trăm hoa hồng',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 8.3. Bảng `bonus` - Cấu hình bonus

```sql
CREATE TABLE bonus (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    level_by_skill_main_id INT NOT NULL,
    type_of_sale VARCHAR(255) NOT NULL COMMENT 'cv, interview, onboard',
    price DECIMAL(15,2) NOT NULL,
    point INT NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 9. Bảng Supporting Data

### 9.1. Bảng `categories` - Danh mục ngành nghề

```sql
CREATE TABLE categories (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT NULL,
    image VARCHAR(255) NULL,
    home TINYINT DEFAULT 0 COMMENT 'Hiển thị trang chủ',
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL
);
```

### 9.2. Bảng `skills` - Kỹ năng

```sql
CREATE TABLE skills (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL
);
```

### 9.3. Bảng `skill_mains` - Kỹ năng chính

```sql
CREATE TABLE skill_mains (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    is_active TINYINT DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 9.4. Bảng `level_by_skill_mains` - Cấp độ theo kỹ năng

```sql
CREATE TABLE level_by_skill_mains (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    skill_main_id INT NOT NULL,
    level TINYINT NOT NULL COMMENT '1: Junior, 2: Middle, 3: Senior',
    name VARCHAR(255) NOT NULL,
    price_cv DECIMAL(15,2) DEFAULT 0,
    price_interview DECIMAL(15,2) DEFAULT 0,
    price_onboard DECIMAL(15,2) DEFAULT 0,
    price_submit_cv DECIMAL(15,2) DEFAULT 0,
    price_submit_interview DECIMAL(15,2) DEFAULT 0,
    price_submit_onboard DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 10. Bảng Notification & Communication

### 10.1. Bảng `notifications` - Thông báo hệ thống

```sql
CREATE TABLE notifications (
    id CHAR(36) PRIMARY KEY,
    type VARCHAR(255) NOT NULL,
    notifiable_type VARCHAR(255) NOT NULL,
    notifiable_id BIGINT UNSIGNED NOT NULL,
    data TEXT NOT NULL COMMENT 'Dữ liệu thông báo (JSON)',
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_notifiable (notifiable_type, notifiable_id)
);
```

### 10.2. Bảng `email_logs` - Log email

```sql
CREATE TABLE email_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    from_email VARCHAR(255) NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    cc_email VARCHAR(255) NULL,
    subject VARCHAR(255) NOT NULL,
    html_content LONGTEXT NOT NULL,
    hash VARCHAR(255) NOT NULL COMMENT 'Hash để identify email',
    status TINYINT DEFAULT 0 COMMENT '0: chưa gửi, 1: đã gửi',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_hash (hash),
    INDEX idx_created_at (created_at)
);
```

### 10.3. Bảng `discuss` - Thảo luận

```sql
CREATE TABLE discuss (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    submit_cv_id INT NOT NULL,
    user_id INT NOT NULL,
    sender_type ENUM('employer','rec','admin') NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 11. Bảng System & Configuration

### 11.1. Bảng `settings` - Cấu hình hệ thống

```sql
CREATE TABLE settings (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    key VARCHAR(255) NOT NULL,
    value LONGTEXT NULL,
    type VARCHAR(255) DEFAULT 'string',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    UNIQUE KEY unique_key (key)
);
```

### 11.2. Bảng `meta_data` - Metadata polymorphic

```sql
CREATE TABLE meta_data (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    object_type VARCHAR(255) NOT NULL,
    object_id BIGINT UNSIGNED NOT NULL,
    key VARCHAR(255) NOT NULL,
    value LONGTEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    INDEX idx_object (object_type, object_id),
    INDEX idx_key (key)
);
```

### 11.3. Bảng `job_logs` - Log background jobs

```sql
CREATE TABLE job_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    job_id VARCHAR(255) NULL,
    name VARCHAR(255) NOT NULL,
    queue VARCHAR(255) NULL,
    payload LONGTEXT NULL,
    status TINYINT DEFAULT 0 COMMENT '0: pending, 1: processing, 2: completed, 3: failed',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

---

## 12. Indexes và Constraints

### 12.1. Primary Indexes
- Tất cả bảng đều có `id` làm primary key với AUTO_INCREMENT
- Sử dụng BIGINT UNSIGNED cho khả năng mở rộng

### 12.2. Foreign Key Constraints
```sql
-- Users và Companies
ALTER TABLE users ADD CONSTRAINT fk_users_company
    FOREIGN KEY (company_id) REFERENCES companies(id);

-- Jobs
ALTER TABLE job ADD CONSTRAINT fk_job_company
    FOREIGN KEY (company_id) REFERENCES companies(id);
ALTER TABLE job ADD CONSTRAINT fk_job_employer
    FOREIGN KEY (employer_id) REFERENCES users(id);

-- Wallets
ALTER TABLE wallets ADD CONSTRAINT fk_wallets_user
    FOREIGN KEY (user_id) REFERENCES users(id);

-- Warehouse CV System
ALTER TABLE warehouse_cv_sellings ADD CONSTRAINT fk_cv_selling_user
    FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE warehouse_cv_sellings ADD CONSTRAINT fk_cv_selling_cv
    FOREIGN KEY (warehouse_cv_id) REFERENCES warehouse_cvs(id);

-- Submit CV System
ALTER TABLE submit_cvs ADD CONSTRAINT fk_submit_cv_user
    FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE submit_cvs ADD CONSTRAINT fk_submit_cv_job
    FOREIGN KEY (job_id) REFERENCES job(id);
```

### 12.3. Unique Constraints
```sql
-- Unique email per user type
ALTER TABLE users ADD CONSTRAINT unique_email_type
    UNIQUE (email, type);

-- Unique company slug
ALTER TABLE companies ADD CONSTRAINT unique_company_slug
    UNIQUE (slug);

-- Unique job slug
ALTER TABLE job ADD CONSTRAINT unique_job_slug
    UNIQUE (slug);
```

### 12.4. Full-text Search Indexes
```sql
-- Job search
ALTER TABLE job ADD FULLTEXT(name);
ALTER TABLE job ADD FULLTEXT(skills);

-- Company search
ALTER TABLE companies ADD FULLTEXT(name);
```

---

## 13. Data Types và Storage

### 13.1. JSON Fields
- `companies.address` - Địa chỉ công ty (array of objects)
- `companies.image` - Hình ảnh công ty
- `companies.video` - Video giới thiệu
- `job.address` - Địa chỉ làm việc
- `warehouse_cvs.main_skill` - Kỹ năng chính

### 13.2. ENUM Fields
- `users.type` - Loại người dùng (admin, rec, employer)
- `wallets.type` - Loại ví (rec, employer)
- `discuss.sender_type` - Người gửi (employer, rec, admin)

### 13.3. Soft Deletes
Các bảng sử dụng soft delete:
- `companies`
- `warehouse_cv_sellings`
- `user_infos`
- `categories`
- `skills`

### 13.4. Timestamps
Tất cả bảng đều có `created_at` và `updated_at` timestamps.

---

*Tài liệu cấu trúc cơ sở dữ liệu hoàn tất. Tổng cộng có 40+ bảng chính với đầy đủ mối quan hệ và constraints.*
