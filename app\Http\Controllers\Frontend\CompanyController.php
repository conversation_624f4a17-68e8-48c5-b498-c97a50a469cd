<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Common;
use App\Http\Controllers\Controller;
use App\Services\Admin\InformationContactService;
use App\Services\Frontend\CompanyService;
use App\Services\Frontend\SeoService;
use App\Services\Frontend\SettingService;
use Illuminate\Http\Request;

class CompanyController extends Controller
{
    protected $settingService;
    protected $informationContactService;
    protected $seoService;
    protected $companyService;

    public function __construct(
        SettingService $settingService,
        InformationContactService $informationContactService,
        SeoService $seoService,
        CompanyService $companyService,
    )
    {
        $this->settingService = $settingService;
        $this->informationContactService = $informationContactService;
        $this->seoService = $seoService;
        $this->companyService = $companyService;
        $this->routeName = \Route::currentRouteName();

    }

    public function listCompany(Request $request)
    {
        $lang = app()->getLocale();
        $str = 'company';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $career = config('job.career.' . $lang);
        unset($career[0]);
        $priority = config('constant.priority');
        $companyOutStanding = $this->companyService->getListByTypePriority(array_keys($priority)[0], false, $request->all());
        $cities = Common::getCities();
        $companyBrand = $this->companyService->getListByTypePriority(array_keys($priority)[1], false, $request->all());
        $company = $this->companyService->getListByTypePriority(0, true, $request->all(), config('constant.paginate_12'));
        return view('frontend.pages.company.list', compact(
            'arrLang', 'companyOutStanding', 'companyBrand', 'company', 'cities', 'career'
        ));
    }

    public function filterCompany(Request $request)
    {
        $lang = app()->getLocale();
        $str = 'company';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $career = config('job.career.' . $lang);
        unset($career[0]);
        $cities = Common::getCities();
        $company = $this->companyService->getListCompanyAnalysis($request->all());
        return view('frontend.pages.company.filter', compact(
            'arrLang', 'company', 'cities', 'career'
        ));
    }

    public function detailCompany($slug)
    {
        $lang = app()->getLocale();
        $this->companyService->getConfig($slug);
        $str = 'company';
        $arrLang = $this->settingService->getAllByKey($str, $lang);
        $company = $this->companyService->detailCompany($slug);
        return view('frontend.pages.company.detail', compact('arrLang', 'company'));
    }

}
