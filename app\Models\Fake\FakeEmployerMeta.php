<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeEmployerMeta extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'employer_metas';

    protected $fillable = [
        'employer_id',
        'tmp_company_name',
        'source',
        'tmp_mst',
    ];

    public function employer()
    {
        return $this->belongsTo(FakeEmployer::class, 'employer_id', 'id');
    }
}
