<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Models\Company;
use App\Models\User;
use App\Services\Frontend\SettingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ResetPassword extends Notification
{
    use Queueable;

    protected $token;
    protected $user;
    protected $url;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($user, $token, $url = null)
    {
        $this->token = $token;
        $this->user = $user;
        $this->url = $url;

//        dd($this->token, $this->user, $this->url);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if ($this->url) {
            $url = $this->url . '?token=' . $this->token;
        } else {
            $url = config('constant.url') . '/forgot-password/?token=' . $this->token;
        }

        return (new MailMessage)
            ->action('Reset Password', url($url))
            ->view('email.resetPassword', ['name' => $this->user->name, 'url' => $url,
                                           'title'=>'BẠN ĐÃ QUÊN MẬT KHẨU CỦA MÌNH?',
                                           'titleEn'=>'VERIFY YOUR EMAIL ADDRESS',

            ])
            ->subject('[HRI RECLAND] [ĐẶT LẠI MẬT KHẨU]');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     *
     * @return array
     */
    public function toArray($notifiable)
    {
        return [];
    }
}
