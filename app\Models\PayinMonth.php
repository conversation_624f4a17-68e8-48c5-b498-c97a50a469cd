<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class PayinMonth extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'payin_months';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = ['status_payment_value'];

    public function warehouseCvSellingBuy(){
        return $this->hasOne(WareHouseCvSellingBuy::class,'id','warehouse_cv_selling_buy_id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('d/m/Y, g:i A');
        }
        return null;
    }

    public function getStatusPaymentValueAttribute()
    {
        return !empty($this->status) ? config('constant.status_payments.vi')[$this->status] : 'Chưa thanh toán';
    }
}
