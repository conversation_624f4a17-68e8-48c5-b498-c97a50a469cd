<?php

namespace App\Models\Fake;

use Illuminate\Database\Eloquent\Model;

class FakeCompany extends Model
{
    protected $connection = 'fake_info';
    protected $table = 'companies';

    protected $fillable = [
        'status_id',
        'name',
        'short_name',
        'slug',
        'excerpt',
        'benefit',
        'overview',
        'size',
        'img_logo',
        'img_outstanding',
        'img_brand',
        'is_real',
        'mst',
        'admin_id',
    ];

    public function addresses()
    {
        return $this->hasMany(FakeCompanyAddress::class, 'company_id', 'id');
    }

    public function metas()
    {
        return $this->hasOne(FakeCompanyMeta::class, 'company_id', 'id');
    }

    public function employers()
    {
        return $this->hasMany(FakeEmployer::class, 'company_id', 'id');
    }

    public function jobs()
    {
        return $this->hasMany(FakeJob::class, 'company_id', 'id');
    }
}
