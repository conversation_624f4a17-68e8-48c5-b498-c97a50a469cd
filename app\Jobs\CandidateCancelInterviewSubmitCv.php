<?php

namespace App\Jobs;

use App\Models\SubmitCv;
use App\Models\SubmitCvHistoryStatus;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdmin;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToAdminSubmitCv;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRec;
use App\Notifications\ChangeStatusRecruiterCancelInterviewToRecSubmit;
use App\Repositories\SubmitCvHistoryStatusRepository;
use App\Repositories\SubmitCvRepository;
use App\Repositories\WareHouseCvSellingBuyHistoryStatusRepository;
use App\Repositories\WareHouseCvSellingBuyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class CandidateCancelInterviewSubmitCv implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $submitCvId;

    public function __construct($submitCvId)
    {
        $this->submitCvId = $submitCvId;
    }

    public function handle()
    {
        // return false; //tam thoi tat chuc nang nay di

        $submitCvRepository = app(SubmitCvRepository::class);
        $submitCvHistoryStatusRepository = app(SubmitCvHistoryStatusRepository::class);
        $submitCv = $submitCvRepository->find($this->submitCvId);

        if ($submitCv->status == config('constant.status_recruitment_revert.Waitingsetupinterview')) {
            //Recruiter Cancel Interview
            $submitCv->addAuditTag([get_class() . '::' . __FUNCTION__ . '(' . __LINE__ . ')', 'submit_cv_id' => $submitCv->id]);
            $submitCv->update(['status' => config('constant.status_recruitment_revert.RecruiterCancelInterview')]);

            $submitCvHistoryStatusRepository->logStatus($submitCv);

            //send mail ntd
            $submitCv->employer->notify(new ChangeStatusRecruiterCancelInterviewToEmployerSubmitCv($submitCv));

            if ($submitCv->authorize > 0){
                //send mail admin authority
                Mail::to(config('settings.global.email_admin'))->send(new ChangeStatusRecruiterCancelInterviewToAdminSubmitCv($submitCv));
            }else{
                //send mail ctv
                $submitCv->rec->notify(new ChangeStatusRecruiterCancelInterviewToRecSubmit($submitCv));
            }

        }
    }




}
