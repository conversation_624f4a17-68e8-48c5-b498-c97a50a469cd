<?php

namespace App\Services\Admin;

use Illuminate\Support\Facades\Auth;

class PermissionService
{

    private static $singletonObj = null;

    public static function getPermission()
    {
        if (self::$singletonObj !== null) {
            return self::$singletonObj;
        }
        $permissionCurrent = [];
        $user = Auth::guard('admin')->user();
        $user->load('roles:permission');
        $roles = $user->roles;
        foreach ($roles as $role){
            $permissionCurrent = array_merge($permissionCurrent,json_decode($role->permission,true));
        }
        $permissionCurrent = array_unique($permissionCurrent);
        self::$singletonObj = $permissionCurrent;
        return self::$singletonObj;
    }

    public static function checkPermission($route)
    {
        if (in_array($route,self::getPermission())){
            return true;
        }
        return false;
    }


}
