<?php

namespace App\Notifications;

use App\Helpers\Common;
use App\Services\Frontend\SettingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Services\Frontend\WareHouseSubmitCvService;

class CheckCvPending extends Notification implements ShouldQueue
{
    use Queueable;

    protected $submitCv;
    protected $user;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($submitCv, $user)
    {
        $this->submitCv = $submitCv;
        $this->user = $user;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return $notifiable->prefers_sms ? ['vonage'] : ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $name = $this->submitCv->submitCvMeta->candidate_name;

        $time = '00:00AM';

        $numDay = Carbon::parse($this->submitCv->date_change_status)->dayOfWeek;

        if ($this->submitCv->status == config('constant.submit_cvs_status_value.pending-review')) {
            //cv pending thi cong them 3 ngay

            //thu 3-4-5-6 lay du lieu 5 ngay truoc
            //chu nhat - thu 2 lay du lieu 3 ngay truoc
            //thu 7 lay du lieu 4 ngay truoc

            $addDate = 4;

            if ($numDay == 0 || $numDay == 1) {
                $addDate = 4;
            } else if ($numDay == 2 || $numDay == 3 || $numDay == 4 || $numDay == 5) {
                $addDate = 6;
            } else if ($numDay == 6) {
                $addDate = 5;
            }

            $date = Carbon::parse($this->submitCv->date_change_status)->addDays($addDate)->format('d/m/Y');

            return (new MailMessage)
                ->view('email.checkCvPending', [
                    'name' => $name,
                    'time' => $time,
                    'date' => $date,
                    'userName' => $this->user->name
                ])
                ->subject('[HRI RECLAND] [NHẮC NTD THAY ĐỔI TRẠNG THÁI CV CỦA ỨNG VIÊN MỚI ỨNG TUYỂN]');

        } else if ($this->submitCv->status == config('constant.submit_cvs_status_value.accepted')) {
            //cv accepted thi cong them 15 ngay

            //chu nhat -> thu 5 lay du lieu 21 ngay truoc
            //thu 6 lay du lieu 23 ngay truoc
            //thu 7 lay du lieu 22 ngay truoc
            $addDate = 16;

            if ($numDay == 0 || $numDay == 1 || $numDay == 2 || $numDay == 3 || $numDay == 4) {
                $addDate = 22;
            } else if ($numDay == 5) {
                $addDate = 24;
            } else if ($numDay == 6) {
                $addDate = 23;
            }

            $date = Carbon::parse($this->submitCv->date_change_status)->addDays($addDate)->format('d/m/Y');

            return (new MailMessage)
                ->view('email.checkCvAccept', [
                    'name' => $name,
                    'time' => $time,
                    'date' => $date,
                    'job'  => $this->submitCv->submitCvMeta->candidate_job_title,
                    'userName' => $this->user->name
                ])
                ->subject('[HRI RECLAND] [NHẮC NTD UPDATE STATUS ỨNG VIÊN SAU KHI CV CHUYỂN SANG TRẠNG THÁI ACCEPT]');
        }
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $settings = resolve(SettingService::class);
        $submitCvService = resolve(WareHouseSubmitCvService::class);

        $str = 'notification';

        $arrLangVi = $settings->getAllKeyWithQuery($str, config('constant.language.vi'));
        $arrLangEn = $settings->getAllKeyWithQuery($str, config('constant.language.en'));

        $name = $this->submitCv->submitCvMeta->candidate_name;

        $addDate = 0;

        $time = '00:00AM';

        $numDay = Carbon::parse($this->submitCv->date_change_status)->dayOfWeek;

        if ($this->submitCv->status == config('constant.submit_cvs_status_value.pending-review')) {
            //cv pending thi cong them 3 ngay

            //thu 3-4-5-6 lay du lieu 5 ngay truoc
            //chu nhat - thu 2 lay du lieu 3 ngay truoc
            //thu 7 lay du lieu 4 ngay truoc

//            $addDate = 4;
//
//            if ($numDay == 0 || $numDay == 1) {
//                $addDate = 4;
//            } else if ($numDay == 2 || $numDay == 3 || $numDay == 4 || $numDay == 5) {
//                $addDate = 6;
//            } else if ($numDay == 6) {
//                $addDate = 5;
//            }
            $expire_date_change_status = $submitCvService->getExpiredDateByStatus($this->submitCv->date_change_status, $this->submitCv->status);
            $date = Carbon::parse($expire_date_change_status)->format('d/m/Y');

            $contentVi = Common::transLang($arrLangVi['ntd_nhacnho_3ngay'], ['name' => $name]);
            $contentEn = Common::transLang($arrLangEn['ntd_nhacnho_3ngay'], ['name' => $name]);

            $descriptionVi = Common::transLang($arrLangVi['ntd_nhacnho_3ngay_description'], ['time' => $time, 'date' => $date]);
            $descriptionEn = Common::transLang($arrLangEn['ntd_nhacnho_3ngay_description'], ['time' => $time, 'date' => $date]);

        } else if ($this->submitCv->status == config('constant.submit_cvs_status_value.accepted')) {
            //cv accepted thi cong them 15 ngay
            $addDate = 16;

            //chu nhat -> thu 5 lay du lieu 21 ngay truoc
            //thu 6 lay du lieu 23 ngay truoc
            //thu 7 lay du lieu 22 ngay truoc

            if ($numDay == 0 || $numDay == 1 || $numDay == 2 || $numDay == 3 || $numDay == 4) {
                $addDate = 22;
            } else if ($numDay == 5) {
                $addDate = 24;
            } else if ($numDay == 6) {
                $addDate = 23;
            }

            $date = Carbon::parse($this->submitCv->date_change_status)->addDays($addDate)->format('d/m/Y');

            $contentVi = Common::transLang($arrLangVi['ntd_nhacnho_15ngay'], ['name' => $name, 'job' => $this->submitCv->submitCvMeta->candidate_job_title]);
            $contentEn = Common::transLang($arrLangEn['ntd_nhacnho_15ngay'], ['name' => $name, 'job' => $this->submitCv->submitCvMeta->candidate_job_title]);

            $descriptionVi = Common::transLang($arrLangVi['ntd_nhacnho_15ngay_description'], ['time' => $time, 'date' => $date]);
            $descriptionEn = Common::transLang($arrLangEn['ntd_nhacnho_15ngay_description'], ['time' => $time, 'date' => $date]);
        }

        return [
            'event'           => 'updateCv',
            'content_vi'      => $contentVi,
            'content_en'      => $contentEn,
            'description_vi'  => $descriptionVi,
            'description_en'  => $descriptionEn,
            'submitcvid'      => $this->submitCv->id,
            'routename'       => 'employer-submitcv'
        ];
    }
}
