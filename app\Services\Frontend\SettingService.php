<?php

namespace App\Services\Frontend;

use App\Repositories\SettingRepository;

class SettingService
{
    protected $settingRepository;

    public function __construct(SettingRepository $settingRepository)
    {
        $this->settingRepository = $settingRepository;
    }

    public function getByKey($key)
    {
        return $this->settingRepository->getFirst(['key' => $key]);
    }

    public function getAllByKey($str, $lang)
    {
        $key = 'settings.' . $lang . '.' . $str;
        $array = [];
        foreach (config($key) as $k => $item) {
            $kNew = str_replace($key . '.', '', $k);
            $kNew = str_replace('.textarea', '', $kNew);
            $array[$kNew] = $item;
        }

        return $array;
    }

    public function getListSettingGlobal()
    {
        $data = [];
        foreach (config('settings.global') as $k => $item) {
            $kTemp = 'setting.global.' . $k;
            $data[$kTemp] = $item;
        }

        return $data;
    }

    public function getAllKeyWithQuery($str, $lang)
    {
        $key = 'settings.' . $lang . '.' . $str;

        $data = $this->settingRepository->getAllByKey($key);

        $array = [];
        if (count($data)) {
            foreach ($data as $k => $item) {
                $kNew = str_replace($key . '.', '', $k);
                $kNew = str_replace('.textarea', '', $kNew);
                $array[$kNew] = $item;
            }
        } else {
            foreach (config($key) as $k => $item) {
                $kNew = str_replace($key . '.', '', $k);
                $kNew = str_replace('.textarea', '', $kNew);
                $array[$kNew] = $item;
            }
        }


        return $array;
    }
}
