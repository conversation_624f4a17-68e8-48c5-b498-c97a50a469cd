<!-- Bug Report <PERSON> -->
<div class="modal fade" id="bugReportModal" tabindex="-1" role="dialog" aria-labelledby="bugReportModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bugReportModalLabel">
                    {{-- <i class="fas fa-bug"></i> --}}Báo cáo lỗi
                </h5>
                {{-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button> --}}
            </div>
            <form id="bugReportForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        {{-- <i class="fas fa-info-circle"></i> --}}
                        H<PERSON><PERSON> mô tả chi tiết lỗi bạn gặp phải để chúng tôi có thể hỗ trợ bạn tốt nhất.
                    </div>

                    <!-- URL Field (Auto-filled) -->
                    <div class="form-group mt-2">
                        <label for="bug_url">URL trang lỗi:</label>
                        <input type="text" class="form-control  mt-2" id="bug_url" name="url" readonly>
                        <small class="form-text text-muted mt-2">URL này được tự động điền từ trang hiện tại</small>
                    </div>

                    <!-- Description Field -->
                    <div class="form-group mt-2">
                        <label for="bug_description">Mô tả lỗi <span class="text-danger">*</span>:</label>
                        <textarea class="form-control  mt-2" id="bug_description" name="description" rows="5"
                            placeholder="Vui lòng mô tả chi tiết lỗi bạn gặp phải, các bước để tái hiện lỗi..." required
                            maxlength="2000"></textarea>
                        <small class="form-text text-muted mt-2">
                            <span id="description_count">0</span>/2000 ký tự
                        </small>
                    </div>

                    <!-- Image Upload Field -->
                    <div class="form-group mt-2">
                        <label for="bug_image">Ảnh minh họa (tùy chọn):</label>
                        <div class="image-upload-area  mt-2" id="imageUploadArea">
                            <div class="upload-placeholder" id="uploadPlaceholder">
                                {{-- <i class="fas fa-cloud-upload-alt fa-3x text-muted"></i> --}}
                                <p class="mt-2 text-muted">
                                    Kéo thả ảnh vào đây hoặc <span class="text-primary">click để chọn file</span><br>
                                    <small>Hoặc dán ảnh từ clipboard (Ctrl+V)</small>
                                </p>
                                <input type="file" id="bug_image" name="image" accept="image/*" style="display: none;">
                            </div>
                            <div class="image-preview" id="imagePreview" style="display: none;">
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail">
                                <button type="button" class="btn btn-sm btn-danger remove-image" id="removeImage">
                                    {{-- <i class="fas fa-times"></i> --}}
                                    Xóa ảnh
                                </button>
                            </div>
                        </div>
                        <small class="form-text text-muted  mt-2">
                            Định dạng: JPG, PNG, GIF. Kích thước tối đa: 5MB
                        </small>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"
                        onclick="$('#bugReportModal').modal('hide')">
                        {{-- <i class="fas fa-times"></i> --}}
                        Hủy
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBugReport">
                        {{-- <i class="fas fa-paper-plane"></i> --}}
                        Gửi báo cáo
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .bugReportModal .modal-content {
        line-height: 22px;
    }

    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: border-color 0.3s ease;
        cursor: pointer;
        min-height: 150px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .image-upload-area:hover {
        border-color: #007bff;
    }

    .image-upload-area.dragover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .image-preview {
        position: relative;
        display: inline-block;
    }

    .image-preview img {
        max-width: 100%;
        max-height: 200px;
    }

    .remove-image {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    #bugReportModal .modal-dialog {
        max-width: 600px;
    }

    @media (max-width: 768px) {
        #bugReportModal .modal-dialog {
            margin: 10px;
            max-width: calc(100% - 20px);
        }

        .image-upload-area {
            min-height: 120px;
            padding: 15px;
        }
    }
</style>