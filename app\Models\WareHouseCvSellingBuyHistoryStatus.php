<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class WareHouseCvSellingBuyHistoryStatus extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'warehouse_cv_selling_buy_history_status';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $appends = [
        'created_at_value',
    ];

    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }

    public function getCreatedAtValueAttribute()
    {
        return $this->created_at ? $this->created_at->format('d/m/Y H:i') : null;
    }

}
