<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;

class SubmitCvOnboard extends BaseModel
{
    use HasFactory, Notifiable;

    protected $table = 'submit_cvs_onboards';
    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = ['date_hour','date_book_format','time_book_format'];
    protected static function booted()
    {
        static::created(function ($model) {
            $model->createDiscuss();
        });
    }

    public function getDateTimeBookFormatAttribute(){
        if (!empty($this->time_book) && !empty($this->date_book)){
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $this->date_book)->format('d/m/Y');
            return $date . ' ' . $this->time_book;
        }
        return null;
    }

    public function createDiscuss() {
        $comment = 'Đặt lịch onboard ứng viên:' . $this->name;
        $comment .= '<br> vào lúc: ' . $this->date_time_book_format;
        $comment .= '<br> tại: ' . $this->address;
        $submitCv = $this->submitCv;
        
        if (!empty($this->ntd_id)) {
            $receiver = $submitCv->rec;
            $senderType = 'employer';
        } else {
            $receiver = $submitCv->employer;
            $senderType = 'rec';
        }
        
        $discuss = new Discuss([
            'sender_id'   => !empty($this->ntd_id) ? $this->ntd_id : $this->ctv_id,
            'sender_type' => $senderType,
            'receiver_id' => $receiver ? $receiver->id : null,
            'message'     => $comment
        ]);

        $discuss->object()->associate($this->submitCv);
        $discuss->sub_object()->associate($this);
        $discuss->is_read     = 0;
        $discuss->created_at  = $this->created_at;
        $discuss->updated_at  = $this->updated_at;
        $discuss->save();

        return $discuss;
    }

    public function submitCv()
    {
        return $this->belongsTo(SubmitCv::class, 'submit_cvs_id', 'id');
    }

    public function rec(){
        return $this->hasOne(User::class,'id','ctv_id');
    }

    public function employer(){
        return $this->hasOne(User::class,'id','ntd_id');
    }

    public function getDateHourAttribute(){
        if (!empty($this->created_at)){
            return $this->created_at->format('g:i A, d/m/Y');
        }
        return null;
    }

    public function getDateBookFormatAttribute(){
        if (!empty($this->date_book)){
            $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $this->date_book);
            return $carbonDate->format('d/m/Y');
        }
        return null;
    }

    public function getTimeBookFormatAttribute(){
        if (!empty($this->time_book)){
            $timeBook = explode(':', $this->time_book);
            $h = !empty($timeBook[0]) ? $timeBook[0] : '';
            $m = !empty($timeBook[1]) ? $timeBook[1] : '';
            return $h.':'.$m;
        }

        return null;
    }

    public function getBookTimeMinuteAttribute(){
        if (!empty($this->time_book)){
            $timeBook = explode(':', $this->time_book);
            $h = !empty($timeBook[0]) ? $timeBook[0] : '';
            $m = !empty($timeBook[1]) ? $timeBook[1] : '';
            return (int)$h*60 + (int)$m;
        }
        return 0;
    }

}
