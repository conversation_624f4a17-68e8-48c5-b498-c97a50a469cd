<?php

namespace App\Services\Frontend;

use App\Repositories\CategoryRepository;

class CategoryService
{
    protected $categoryRepository;

    public function __construct(CategoryRepository $categoryRepository)
    {
        $this->categoryRepository = $categoryRepository;
    }

    public function getListHome($limit = null)
    {
        return $this->categoryRepository->getListHome($limit);
    }

    public function findBySlug($slug){
        return $this->categoryRepository->findBySlug($slug);
    }


}
