graph TD
    subgraph "Luồng Mua Bán CV (Marketplace)"
        subgraph "Giai đoạn 1: CV Data"
            A[NTD nhấn "Mua CV"] --> B{Controller tiếp nhận & Service xử lý};
            B --> C{Kiểm tra điều kiện & <PERSON><PERSON><PERSON> bản ghi mua hàng};
            C --> D[Trừ Point NTD & Ghi log giao dịch];
            D --> E[Ghi lịch sử mua hàng & trạng thái];
            E --> F[Commit Transaction & Thông báo];
            F -- Notification --> H[CTV nhận thông báo: BuyCvSuccess];
            F -- Dispatch Job (7 ngày) --> I[Job: RecSumPoint (Thanh toán hoa hồng CTV)];
            F --> G[HTTP Response: Thành công];
        end

        subgraph "Giai đoạn 2: Interview"
            J[NTD nhấn "Mời phỏng vấn"] --> K{Service xử lý logic (DB Transaction)};
            K --> L{<PERSON><PERSON>y dữ liệu & <PERSON><PERSON><PERSON> tra lịch phỏng vấn};
            L --> M[Cập nhật trạng thái đặt lịch & tuyển dụng];
            M --> N[Ghi lịch sử];
            N --> O[Commit Transaction & Phản hồi];
            O -- Notification --> P[NTD nhận thông báo: EmailConfirmInterView];
            O -- Dispatch Job (7 ngày sau phỏng vấn) --> Q[Job: PassInterview (Tự động chuyển trạng thái nếu NTD không cập nhật)];
            O --> R[NTD & UV/CTV chốt lịch (ngoài hệ thống)];
            R --> S[NTD cập nhật trạng thái: WaitingInterview (7)];
        end

        subgraph "Giai đoạn 3: Onboard"
            T[NTD cập nhật trạng thái: Pass phỏng vấn (8) hoặc Fail phỏng vấn (10)] --> U[NTD cập nhật trạng thái: Trial work (14)];
            U -- Dispatch Job --> V[Job: ChangeToTrailWork];
            V --> W{Kiểm tra số dư ví NTD & Xử lý ghi nợ (nếu có)};
            W --> X[Trừ tiền từ ví NTD (90% giá trị Onboard)];
            X --> Y[Cập nhật trạng thái WareHouseCvSellingBuy: Trial work (14)];
            Y --> Z[Ghi lịch sử & Thông báo (Admin, CTV)];
            Z -- Dispatch Job (67 ngày) --> AA[Job: SuccessRecuitment (Tự động chuyển trạng thái "Tuyển dụng thành công")];
            Z -- Dispatch Job (30 ngày) --> BB[Job: PayOnboard (15% hoa hồng CTV)];
            Z -- Dispatch Job (45 ngày) --> CC[Job: PayOnboard (10% hoa hồng CTV)];
            Z -- Dispatch Job (67 ngày) --> DD[Job: PayOnboard (75% hoa hồng CTV)];
            BB & CC & DD --> EE{Job: PayOnboard thực thi & Kiểm tra điều kiện};
            EE --> FF[Xử lý thanh toán hoa hồng cho CTV];
            FF --> GG[Quy trình hoàn tất & Cập nhật status_payment];
        end

        subgraph "Sad Paths (Marketplace)"
            H1[NTD khiếu nại (status_complain = 1)] -- Dispatch Job (7 ngày) --> H2[Job: RecSumExpiredPoint (Tự động xử lý khiếu nại & Hoàn tiền)];
            H2 --> H3[Hoàn tiền cho NTD & Cập nhật trạng thái khiếu nại];
            H3 -- Notification --> H4[NTD & CTV nhận thông báo khiếu nại được xử lý];

            I1[UV từ chối lời mời phỏng vấn qua email] --> I2[wareHouseCvSellingBuy->status_recruitment = 2 (Candidate Cancel Apply)];
            I2 --> I3[Hoàn tiền cho NTD & Ghi log];
            I3 -- Notification --> I4[CTV, NTD, Admin nhận thông báo];

            J1[NTD cập nhật trạng thái: Fail phỏng vấn (10) hoặc Từ chối Offer (12)] --> J2[Service cập nhật trạng thái];
            J2 -- Dispatch Job --> J3[Job: DepositRefundRejectOffer (Hoàn lại phí Interview)];

            K1[NTD cập nhật trạng thái: Thất bại thử việc (17)] --> K2[Service cập nhật trạng thái & Ghi log];
            K2 --> K3[Hoàn lại một phần phí Onboard cho NTD];

            L1[Lịch phỏng vấn hết hạn/bị từ chối (RejectBookExpire)] --> L2[Job: RejectBookExpire];
            L2 --> L3[Cập nhật trạng thái & Hoàn tiền (nếu đủ 3 lần từ chối)];
        end
    end