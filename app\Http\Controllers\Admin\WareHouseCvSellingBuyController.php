<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WareHouseCvSellingBuy;
use App\Services\Admin\CompanyService;
use App\Services\Admin\Toast\Facades\Toast;
use App\Services\Admin\WareHouseCvSellingBuyOnboardService;
use App\Services\Admin\WareHouseCvSellingBuyService;
use App\Services\Admin\WareHouseCvSellingService;
use App\Services\Admin\WareHouseCvSellingBuyBookService;
use Illuminate\Http\Request;

class WareHouseCvSellingBuyController extends Controller
{
    protected $wareHouseCvSellingService;
    protected $wareHouseCvSellingBuyService;
    protected $companyService;
    protected $wareHouseCvSellingBuyBookService;
    protected $wareHouseCvSellingBuyOnboardService;

    public function __construct(
        WareHouseCvSellingService $wareHouseCvSellingService,
        WareHouseCvSellingBuyService $wareHouseCvSellingBuyService,
        CompanyService $companyService,
        WareHouseCvSellingBuyBookService $wareHouseCvSellingBuyBookService,
        WareHouseCvSellingBuyOnboardService $wareHouseCvSellingBuyOnboardService
    )
    {
        $this->wareHouseCvSellingService = $wareHouseCvSellingService;
        $this->wareHouseCvSellingBuyService = $wareHouseCvSellingBuyService;
        $this->companyService = $companyService;
        $this->wareHouseCvSellingBuyBookService = $wareHouseCvSellingBuyBookService;
        $this->wareHouseCvSellingBuyOnboardService = $wareHouseCvSellingBuyOnboardService;
    }

    public function index()
    {
        $datatable = $this->wareHouseCvSellingBuyService->buildDatatable();
        $company = $this->companyService->getCompany();
        $totalComplain = $this->wareHouseCvSellingBuyService->getTotalComplain();
        $totalInterview = $this->wareHouseCvSellingBuyService->getTotalInterview();
        $totalOnboard = $this->wareHouseCvSellingBuyService->getTotalOnboard();
        return view('admin.pages.luot-ban.index', compact('datatable', 'company', 'totalComplain', 'totalInterview', 'totalOnboard'));
    }

    public function datatable(Request $request)
    {
        $data = $this->wareHouseCvSellingBuyService->datatable($request->all());
        return response($data);
    }

    public function show($id)
    {
        $data = $this->wareHouseCvSellingBuyService->detail($id);
        $lastBook = $this->wareHouseCvSellingBuyBookService->getLastBookBySellingBuyId($id);
        $lastOnboard = $this->wareHouseCvSellingBuyOnboardService->getCurrentByWarehouseCvBuyId($id);
        return view('admin.pages.luot-ban.detail', compact('data','lastBook','lastOnboard'));
    }

    public function preview(Request $request)
    {
        $params = $request->all();
        if ($params['tab'] == 'khieunai') {
            $data = $this->wareHouseCvSellingBuyService->getDataComplain($request->all());
            $view = view('admin.pages.luot-ban.complain', compact('data'));
            $view = $view->render();
            return $view;
        }

        if ($params['tab'] == 'thaoluan') {
            $data = $this->wareHouseCvSellingBuyService->getDataDiscuss($request->all());
            $dataBook = $this->wareHouseCvSellingBuyService->getDataBuyBook($request->all());
            $messageRejectInterview = $this->wareHouseCvSellingBuyService->getMessageRejectInterview($request->id,$dataBook);
            $onboard = $this->wareHouseCvSellingBuyOnboardService->getByWarehouseCvBuyId($request->id);
            $view = view('admin.pages.luot-ban.discuss', compact('data', 'dataBook','messageRejectInterview','onboard'));
            $view = $view->render();
            return $view;
        }

        if ($params['tab'] == 'danhgia') {
            $data = $this->wareHouseCvSellingBuyService->getDataEvaluate($request->all());
            $view = view('admin.pages.luot-ban.evaluates', compact('data'));
            $view = $view->render();
            return $view;
        }

        if ($params['tab'] == 'trangthai') {
            $data = $this->wareHouseCvSellingBuyService->getDataHistoryStatus($request->all());
            $sellingBuy = WareHouseCvSellingBuy::find($request->id);
            $audits = optional($sellingBuy)->audits;
            $view = view('admin.pages.luot-ban.historyStatus', compact('data', 'audits'));
            $view = $view->render();
            return $view;
        }

        if ($params['tab'] == 'thanhtoan') {
            $data = $this->wareHouseCvSellingBuyService->getDataHistoryPaymentStatus($request->all());
            $view = view('admin.pages.luot-ban.historyPaymentStatus', compact('data'));
            $view = $view->render();
            return $view;
        }
    }

    public function updateComplain(Request $request)
    {
        $params = $request->all();
        $result = $this->wareHouseCvSellingBuyService->updateComplain($params);
        if ($result) {
            $response = [
                'success' => true,
            ];
            if ($params['type'] == 4){
                Toast::success('Xác nhận khiếu nại thành công');
            }
            if ($params['type'] == 5){
                Toast::success('Từ chối khiếu nại thành công');
            }

        } else {
            $response = [
                'success' => false,
            ];
            if ($params['type'] == 4){
                Toast::error('Xác nhận khiếu nại thất bại');
            }
            if ($params['type'] == 5){
                Toast::error('Từ chối khiếu nại thất bại');
            }
        }

        return response()->json($response);
    }

    public function deleteEvaluate(Request $request)
    {
        $result = $this->wareHouseCvSellingBuyService->deleteEvaluate($request->id);
        if ($result) {
            $response = [
                'success' => true,
            ];
        } else {
            $response = [
                'success' => false,
            ];
        }

        return response()->json($response);
    }

    public function changeStatusRecruitment($id,Request $request){
        try {
            $this->wareHouseCvSellingBuyService->updateStatusRecruitmentById($id,$request->status_recruitment);
            Toast::success('Cập nhật trạng thái thành công');
        } catch (\Exception $exception){
            Toast::error(__('Cập nhật trạng thái thất bại'));
        }
        return redirect()->back();
    }

    public function changeStatusOnboard($id,Request $request){
        try {
            $this->wareHouseCvSellingBuyService->updateStatusOnboardById($id,$request->status_recruitment);
            Toast::success('Cập nhật trạng thái thành công');
        } catch (\Exception $exception){
            Toast::error(__('Cập nhật trạng thái thất bại'));
        }
        return redirect()->back();
    }

}
