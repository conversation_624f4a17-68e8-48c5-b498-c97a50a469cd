<?php

namespace App\Repositories;

use App\Models\Skill;

class SkillRepository extends BaseRepository
{
    const MODEL = Skill::class;

    public function getListSkill($params,$orders = array(),$paginate = false, $order_by = 'id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['start'])){
            $query->offset($params['start']);
        }

//        if (!empty($orders)){
//            foreach ($orders as $key => $order){
//                $query->orderBy($key,$order);
//            }
//        }

        if (isset($params['search'])) {
            $query->where('id', $params['search'])
                ->orWhere('name', 'like', '%' . $params['search'] . '%');
        }

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function getArrName($name = null)
    {
        $query = $this->query()->limit(50)->orderBy('id', 'desc');
        if ($name){
            if (is_array($name)){
                $query->whereIn('name',$name);
            }else{
                $query->where('name','like',"%$name%");
            }
        }
        return $query->pluck('name')->toArray();
    }

    public function getByName($name){
        return $this->query()->where('name',$name)->first();
    }

}
