<?php

namespace App\DataTables\Admin;

use App\Services\Admin\TopCollaboratorsService;
use App\Services\Frontend\TopRecService;
use App\Traits\DataTableTrait;

use Yajra\DataTables\Services\DataTable;


class TopRecDatatable extends DataTable
{
    use DataTableTrait;

    protected $topRecService;

    public function __construct(TopRecService $topRecService)
    {
        $this->topRecService = $topRecService;
    }
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->addColumn('action', function ($item) {

                return view('admin.pages.top-collaborators.action', compact('item'))->render();
            })
            ->editColumn('is_hot', function ($item) {
                return $item->is_hot == 1 ? 'Có' : 'Không';
            });
    }

//    /**
//     * Get query source of dataTable.
//     *
//     * @param \App\Models\Admin\TopCollaboratorsDatatable $model
//     * @return \Illuminate\Database\Eloquent\Builder
//     */
    public function query()
    {
        $param = request()->only(['rec_name', 'amount', 'increase_percent', 'is_hot']);
        $param['authorized'] = true;
        return $this->topRecService->indexService($param);
    }


    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
                    ->setTableId('topcollaboratorsdatatable-table')
                    ->columns($this->getColumns())
                    ->minifiedAjax()
                    ->dom('Bfrtip')
                    ->orderBy(1);

    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            'id' =>['title' => 'ID'],
            'rec_name' => ['title' => 'Tên Cộng tác viên'],
            'amount'=> ['title' => 'Doanh thu tháng'],
            'increase_percent' => ['title' => 'Phần trăm tăng so với tháng trước'],
            'is_hot' => ['title' => 'Biểu tượng hot'],
            'action' => ['title' => 'Hành động'],
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'Admin\TopCollaborators_' . date('YmdHis');
    }
}
