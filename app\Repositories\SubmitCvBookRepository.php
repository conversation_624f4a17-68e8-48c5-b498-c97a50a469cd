<?php

namespace App\Repositories;

use App\Models\SubmitCvBook;
use App\Models\WareHouseCvSellingBuyBook;
use Carbon\Carbon;

class SubmitCvBookRepository extends BaseRepository
{
    const MODEL = SubmitCvBook::class;


    public function getBySubmitCvId($submit_id){
        $query = $this->query()
            ->where('submit_cvs_id',$submit_id)
            ->with(['employer']);
        return $query->get();
    }

    public function findByCvSellingBuyId($submit_id){
        return $this->query()
            ->where('submit_cvs_id',$submit_id)
            ->first();
    }

    public function countBookReject($recId, $submit_id){
        return $this->query()
            ->where('ntd_id',$recId)
            ->where('submit_cvs_id',$submit_id)
            ->where('status',2)
            ->count();
    }

    public function getBookExpire(){
        $now = Carbon::now()->subDays(2);
        return $this->query()
            ->where('status',0)
            ->where('created_at','<=',$now)
            ->get();
    }

    public function getLastBookBySubmitId($sellingBuyId){
        return $this->query()
            ->where('submit_cvs_id',$sellingBuyId)
            ->where('status',0)->first();
    }
    public function getLastBookBySubmitIdAllStatus($sellingBuyId){
        return $this->query()
            ->where('submit_cvs_id',$sellingBuyId)->orderBy('id', 'desc')->first();
    }

    public function getWaitingInterviewBook($sellingBuyId){
        return $this->query()
            ->where('submit_cvs_id',$sellingBuyId)
            ->where('status',1)->first();
    }
}
