<?php

namespace App\Services\Admin;


use App\Repositories\AboutUsAlbumPhotoRepository;

class AboutUsAlbumPhotoService
{

    protected $aboutUsAlbumPhotoRepository;

    public function __construct(AboutUsAlbumPhotoRepository $aboutUsAlbumPhotoRepository)
    {
        $this->aboutUsAlbumPhotoRepository = $aboutUsAlbumPhotoRepository;
    }

    public function indexService($params)
    {
        return $this->aboutUsAlbumPhotoRepository->getQuery($params);
    }

    public function insert($params)
    {
        return $this->aboutUsAlbumPhotoRepository->insert($params);
    }

    public function create($attributes = [])
    {
//        $a = json_encode($attributes);
        return $this->aboutUsAlbumPhotoRepository->create($attributes);
        /*try {
            return $this->query()->create($attributes);
        } catch (\Illuminate\Database\QueryException $ex) {
            return $ex;
        }*/
    }

    public function update($id, $options = [], $attributes = [])
    {
        return $this->aboutUsAlbumPhotoRepository->update($id, $options, $attributes);

    }

    public function delete($id, $options)
    {
        return $this->aboutUsAlbumPhotoRepository->delete($id, $options);
    }
}
