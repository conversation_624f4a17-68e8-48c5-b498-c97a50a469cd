<?php

namespace App\Console\Commands;

use App\Services\Admin\SubmitCvService;
use Illuminate\Console\Command;

class CheckCvExpired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cv:expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Cv Expired';

    protected $submitCvService;

    public function __construct(SubmitCvService $submitCvService)
    {
        parent::__construct();
        $this->submitCvService = $submitCvService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('Batch CheckCvExpired Start');
        try {
            //get data pending
            $this->submitCvService->updateStatusCvExpired(config('constant.submit_cvs_status_value.pending-review'));

            //get data accept
            $this->submitCvService->updateStatusCvExpired(config('constant.submit_cvs_status_value.accepted'));

            \Log::info('Batch CheckCvExpired End');
        } catch (\Exception $e) {
            \Log::error('Batch CheckCvExpired Error: ' . $e);
        }
    }
}
