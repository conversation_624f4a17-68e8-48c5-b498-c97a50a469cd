<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\LoginController;

// --------------------------
// Custom Backpack Routes
// --------------------------
// This route file is loaded automatically by Backpack\Base.
// Routes you generate using Backpack\Generators will be placed here.

Route::group([
    'prefix'     => config('backpack.base.route_prefix', 'admin'),
    'middleware' => array_merge(
        (array) config('backpack.base.web_middleware', 'web'),
        (array) config('backpack.base.middleware_key', 'admin'),
        // (array) 'check-role'
    ),
    'namespace'  => 'App\Http\Controllers\Admin',
], function () { // custom admin routes
    Route::crud('album-about-us', 'AlbumAboutUsCrudController');
    Route::crud('new-collaborator', 'CollaboratorCrudController');
    Route::crud('submit-cv-discuss', 'SubmitCvDiscussCrudController');
    Route::crud('job-comment', 'JobCommentCrudController');
    Route::crud('wallet-transaction', 'WalletTransactionCrudController');

    // Thêm route cho Employer CRUD
    Route::crud('employers', 'EmployerCrudController');
    Route::crud('warehouse-cv', 'WareHouseCvCrudController');

    // Thêm route cho Job Management với Backpack
    Route::crud('jobs-management', 'JobCrudController');
    Route::crud('companies', 'CompanyCrudController');

    // Custom routes cho employer
    Route::post('employers/{id}/deposit', 'EmployerCrudController@deposit');
    Route::get('employers/{id}/wallet-history', 'EmployerCrudController@walletHistory');

    // Thêm route cho Bug Reports CRUD
    Route::crud('bug-reports', 'BugReportCrudController');

    // Custom routes cho bug reports
    Route::patch('bug-reports/{id}/status', 'BugReportCrudController@updateStatus');

    // Routes AJAX
    Route::get('ajax/companies', 'AjaxController@companies');
    Route::get('ajax/employers', 'AjaxController@employers');
}); // this should be the absolute last line of this file

Route::get('/login', [LoginController::class, 'getLogin'])->name('backpack.auth.login');
Route::get('/register', [LoginController::class, 'getLogin'])->name('backpack.auth.register');
