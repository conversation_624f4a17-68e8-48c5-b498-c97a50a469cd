<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckApiKey
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $apiKey = $request->header('X-API-Key');
        
        // Kiểm tra API Key có tồn tại không
        if (!$apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'API Key không được cung cấp'
            ], 401);
        }
        
        // Kiểm tra API Key có hợp lệ không
        $validApiKeys = config('api.valid_keys', []);
        if (!in_array($apiKey, $validApiKeys)) {
            return response()->json([
                'success' => false,
                'message' => 'API Key không hợp lệ'
            ], 401);
        }
        
        return $next($request);
    }
} 