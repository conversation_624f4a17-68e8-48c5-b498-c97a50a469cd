<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Testimonial extends BaseModel
{

    public $timestamps = true;
    protected $guarded = ['id'];

    protected $appends = [
        'avatar_url',
        'type_value',
    ];

    public function getAvatarUrlAttribute()
    {
        return gen_url_file_s3($this->avatar);
    }

    public function getTypeValueAttribute()
    {
        return config('constant.role_frontend.' . $this->type);
    }
}
