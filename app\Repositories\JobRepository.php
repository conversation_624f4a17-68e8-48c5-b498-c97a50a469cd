<?php

namespace App\Repositories;

use App\Helpers\Common;
use App\Models\Job;
use App\Services\Frontend\SettingService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class JobRepository extends BaseRepository
{
    const MODEL = Job::class;

    public function total($params = array())
    {
        $query = $this->query();
        $query->where('is_active', config('constant.active'));
        $query->where('status', config('constant.active'));
        return $query->count();
    }
    public function totalActive($params = array())
    {
        $query = $this->query();
        $query->where('is_active', config('constant.active'));
        $query->where('is_real', 1); // Chỉ lấy dữ liệu thật
        // $query->where('status', config('constant.active'));
        return $query->count();
    }

    public function getListJob($params, $orders = array(), $paginate = false, $order_by = 'job.id', $sort = 'desc')
    {
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        if (isset($params['size'])) {
            $limit = (int)$params['size'];
        } else {
            $limit = config('constant.limitPaginate');
        }

        if ($limit > 100) {
            $limit = 100;
        }

        $query = $this->query();

        if (isset($params['search'])) {

            $query->join('companies', 'job.company_id', '=', 'companies.id');
            $query->select('job.*', 'companies.name as company_name');
            $query->where('job.id', $params['search'])
                ->orWhere('job.name', 'like', '%' . $params['search'] . '%')
                ->orWhere('companies.name', 'like', '%' . $params['search'] . '%');
        }

        if (isset($params['is_active'])) {
            $query->where('is_active', isset($params['is_active']));
        }

        if (isset($params['start_date'])) {
            $query->where('publish_at', '>=', Carbon::parse($params['start_date'])->format('Y-m-d'));
        }
        if (isset($params['end_date'])) {
            $query->where('publish_at', '<', Carbon::parse($params['end_date'])->addDay()->format('Y-m-d'));
        }
        if (isset($params['job_new'])) {
            $query->where('publish_at', '>=', $params['job_new']);
        }

        $query->with('company', 'user');

        $query->orderBy($order_by, $sort);

        if ($paginate) {
            return $query->paginate($limit, $columns = ['*'], $pageName = 'page', $page);
        } else {
            return $query->get();
        }
    }

    public function findBySlug($slug)
    {
        $now = Carbon::now()->format('Y-m-d');
        return $this->query()
            ->where('is_active', config('constant.active'))
            //            ->where('expire_at', '>=', $now)
            //nếu có link job thì vẫn cho xem mặc dù quá hạn, chỉ lúc search job thì ko hiển thị job quá hạn
            ->where('slug', $slug)
            ->with('company')->firstOrFail();
    }

    public function findBySlugAllExpire($slug, $employerId = null)
    {
        $query = $this->query();

        if (!empty($employerId)) $query->where('employer_id', $employerId);

        return $query
            ->where('slug', $slug)
            ->with('company')->first();;
    }

    public function getRelateBySkill(array $skills = [], $take = 18)
    {
        $query = $this->query()->with('company')->where('is_active', config('constant.active'));
        foreach ($skills as $skill) {
            $query->orWhere('skills', 'LIKE', "%$skill%");
        }
        $query->orderBy('expire_at', 'desc');
        $query->take($take);

        return $query->get();
    }

    //Home Page CTV
    public function getJobCtv($limitJob, $skill = '')
    {
        $now = Carbon::now()->format('Y-m-d');
        $priority = config('job.priority');

        $query = $this->query()
            ->select('job.*', 'job_meta.priority')
            ->join('job_meta', 'job.id', '=', 'job_meta.job_id')
            ->where('job.is_active', config('constant.active'))
            ->where('job.status', config('constant.active'))
            ->where('job.expire_at', '>=', $now)
            ->orderByRaw('FIELD(job_meta.priority, ' . "'" . implode("', '", $priority) . "'" . ')')
            ->orderBy('job.publish_at', 'DESC')
            ->limit($limitJob)
            ->with('company', 'jobMeta');

        if (!empty($skill)) {
            $query->where('job.skills', 'like', '%' . $skill . '%');
        }

        return $query->get()->toArray();
    }

    //Home Page CTV
    public function getJobCtvCollection($limitJob, $skill = '')
    {
        $now = Carbon::now()->format('Y-m-d');
        $priority = config('job.priority');
        $query = $this->query()
            ->select('job.*', 'job_meta.priority')
            ->join('job_meta', 'job.id', '=', 'job_meta.job_id')
            ->where('job.is_active', config('constant.active'))
            ->where('job.status', config('constant.active'))
            ->where('job.expire_at', '>=', $now)
            ->orderByRaw('FIELD(job_meta.priority, ' . "'" . implode("', '", $priority) . "'" . ')')
            ->orderBy('job.publish_at', 'DESC')
            ->limit($limitJob)
            ->with('company', 'jobMeta');

        if (!empty($skill)) {
            $query->where('job.skills', 'like', '%' . $skill . '%');
        }

        return $query->get();
    }

    public function querySearchJob($query, $params)
    {
        $now = Carbon::now()->format('Y-m-d');
        $rate = config('settings.global.usd_to_vnd');
        $query->join('job_meta', 'job.id', '=', 'job_meta.job_id')
            ->where('job.is_active', config('constant.active'))
            ->where('job.status', config('constant.active'))
            ->where('job.expire_at', '>=', $now);
        if (isset($params['search'])) {

            //            $query->where(function ($q) use ($params) {
            //                $q->where('name', 'like', '%' . $params['search'] . '%')
            //                ->orWhere('skills', 'like', '%' . $params['search'] . '%')
            //                ->orWhereHas('company', function ($q) use ($params) {
            //                    $q->where('name', 'like', '%' . $params['search'] . '%');
            //                });
            //            });
            $searchKeys = Common::fullTextWildcards($params['search']);
            // dd($searchKeys);
            $query
                ->join('companies', 'job.company_id', '=', 'companies.id')
                ->where(function ($query) use ($searchKeys, $params) {
                    $query->whereRaw("MATCH (job.name,job.skills,companies.name) AGAINST (? IN BOOLEAN MODE)", $searchKeys)
                        ->orWhere('job.name', 'like', '%' . $params['search'] . '%')
                        ->orWhere('companies.name', 'like', '%' . $params['search'] . '%');
                });
        }

        if (isset($params['career'])) {
            if (!is_array($params['career'])) {
                $params['career'] = explode(',', $params['career']);
            }
            $query->where(function ($query) use ($params) {
                foreach ($params['career'] as $career) {
                    // $query->orWhere('career', 'like', '%' . $career . '%');
                    $query->orWhereRaw("CONCAT(',', career, ',') like '%," . $career . ",%'");
                }
            });
        }

        if (isset($params['address'])) {
            $query->where(function ($query) use ($params) {
                foreach ($params['address'] as $address) {
                    $query->orWhere('job.address', 'like', '%' . $address . '%');
                }
            });
        }

        if (isset($params['level']) && $params['level'] > 0) {
            $query->where('level', '<=', $params['level']);
        } else {
            $query->where('level', 0);
        }

        if (isset($params['salary'])) {
            $salary = explode(' - ', $params['salary']);
            $query->where(\DB::raw("IF (salary_currency = 'VND', `salary_min`/" . $rate . ", `salary_min`)"), '>=', $salary[0])
                ->where(\DB::raw("IF (salary_currency = 'VND', `salary_max`/" . $rate . ", `salary_max`)"), '<=', $salary[1]);
        }

        if (isset($params['bonus'])) {
            if ($params['bonus'] == '5000') {
                $query->where(\DB::raw("IF (bonus_currency = 'VND', `bonus`/" . $rate . ", `bonus`)"), '>=', $params['bonus']);
            } else {
                $bonus = explode('-', $params['bonus']);
                $query->where(\DB::raw("IF (bonus_currency = 'VND', `bonus`/" . $rate . ", `bonus`)"), '>=', $bonus[0])
                    ->where(\DB::raw("IF (bonus_currency = 'VND', `bonus`/" . $rate . ", `bonus`)"), '<=', $bonus[1]);
            }
            //            $query->where(\DB::raw("IF (bonus_currency = 'VND', `bonus`/". $rate .", `bonus`)"), $params['bonus']);
        }

        if (isset($params['rank'])) {
            $query->where('rank', 'like', '%' . $params['rank'] . '%');
        }

        if (isset($params['type'])) {
            $query->where('type', $params['type']);
        }

        if (isset($params['bonus_type'])) {
            $query->where('bonus_type', $params['bonus_type']);
        }

        //        if (isset($params['career'])) {
        //            $query->whereIn('career', $params['career']);
        //        }

        if (isset($params['company'])) {
            $query->whereIn('company_id', $params['company']);
        }

        if (isset($params['urgent'])) {
            $query->where('urgent', config('job.urgent.on'));
        }

        if (isset($params['remote'])) {
            $query->where('remote', config('job.remote.on'));
        }
        if (isset($params['order_salary_max'])) {
            $query->orderBy('salary_max', $params['order_salary_max']);
        }
        return $query;
    }

    //Home Page Job
    public function getJobSearch($params)
    {
        $priority = config('job.priority');

        $query = $this->query()
            ->select('job.*', 'job_meta.priority');
        $query = $this->querySearchJob($query, $params);
        // dd($query->toRawSql());
        if (isset($params['page'])) {
            $page = (int)$params['page'];
        } else {
            $page = 1;
        }
        $settingService = app(SettingService::class);
        $arrSettingGlobal = $settingService->getListSettingGlobal([
            'setting.global.limit_search_job',
        ]);
        $params['sort'] = 'salary_asc';
        $query = $query->with('company', 'jobMeta');

        if (isset($params['sort_by']) && $params['sort_by'] == 'salary_desc') {
            $query = $query->selectRaw("(CASE
                WHEN salary_currency = 'USD' THEN salary_max * " . config('constant.ty_gia_usd') . "
                ELSE salary_max
                END) AS luong")->orderByRaw('luong DESC');
        } elseif (isset($params['sort_by']) && $params['sort_by'] == 'salary_asc') {
            $query = $query->selectRaw("(CASE
                WHEN salary_currency = 'USD' THEN salary_max * " . config('constant.ty_gia_usd') . "
                ELSE salary_max
                END) AS luong")->orderByRaw('luong ASC');
        } else {
            $query = $query->orderByRaw('FIELD(job_meta.priority, ' . "'" . implode("', '", $priority) . "'" . ')')->orderBy('job.id', 'DESC');
        }
        // $sql = \Str::replaceArray('?', $query->getBindings(), $query->toSql());
        // dd($sql);
        $query = $query->paginate($arrSettingGlobal['setting.global.limit_search_job'], $page);

        return $query;
    }

    public function countJobSearch($params)
    {
        $query = $this->query();
        $query = $this->querySearchJob($query, $params);
        return $query->count();
    }


    public function getTotalJobWithCompany($companyId)
    {
        return $this->query()->where('company_id', $companyId)->count();
    }

    public function getJobWithKeyword($params)
    {
        $query = $this->query();

        if (isset($params['searchTerm'])) {
            $query->orWhere(function ($q) use ($params) {
                $q->where('id', $params['searchTerm'])
                    ->orWhere('name', 'like', '%' . $params['searchTerm'] . '%')
                    ->orWhereHas('company', function ($q) use ($params) {
                        $q->where('name', 'like', '%' . $params['searchTerm'] . '%');
                    });
            });
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (isset($params['is_active'])) {
            $query->where('is_active', $params['is_active']);
        }
        $query->with('company', 'user');

        return $query->get();
    }

    public function getJobEmployer($params, $userId)
    {
        $query = $this->query();

        if (!empty($userId)) $query->where('employer_id', $userId);

        if (isset($params['company_id'])) {
            $query->where('company_id', $params['company_id']);
        }

        if (isset($params['search'])) {
            $query->where('name', 'like', '%' . $params['search'] . '%');
        }

        $query->with('submitCv')->orderBy('id', 'desc');

        return $query->paginate(config('constant.limit_client'));
    }

    public function totalJob($params, $flg = 0)
    {
        $query = $this->query();

        if (isset($params['is_active'])) {
            $query->where('is_active', $params['is_active']);
        }

        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        if (!empty($params['employer_id'])) {
            $query->where('employer_id', $params['employer_id']);
        }

        if (isset($params['flg'])) {
            $query->where('flg', $params['flg']);
        }

        if ($flg == 0) {
            return $query->count();
        } else {
            return $query->get();
        }
    }

    public function findByUser($id, $user)
    {
        $query = $this->query()->where('id', $id);
        if ($user) {
            $query->where('employer_id', $user->id);
        }

        return $query->firstOrFail();
    }

    public function updateJobExpired()
    {
        $now = date("Y-m-d");

        $raw = "
                UPDATE job
                SET status = 2
                WHERE
                    id IN ( SELECT id FROM ( SELECT id FROM job WHERE expire_at < :now) AS u );
        ";

        $query = \DB::statement($raw, [
            'now' => $now,
        ]);

        return $query;
    }

    public function getListJobEmployer($companyId)
    {
        return $this->query()->where('company_id', $companyId)->where('status', 1)->pluck('name', 'id')->toArray();
    }

    public function getTopSkill($limit  = 8)
    {
        $now = Carbon::now()->format('Y-m-d');
        $query = $this->query()
            ->selectRaw('skills')
            ->whereNotNull('skills')
            ->where('skills', '!=', '')
            ->where('is_active', config('constant.active'))
            ->where('status', config('constant.active'))
            ->where('expire_at', '>=', $now)
            ->orderByRaw('COUNT(id) DESC')
            ->groupBy('skills')
            ->limit($limit)
            ->get()->pluck('skills')->toArray();

        // dd($query);
        return $query;
    }

    public function getLatestJobs($limit = 5)
    {
        $now = Carbon::now()->format('Y-m-d');
        return $this->query()
            ->select('job.*')
            ->where('job.is_active', config('constant.active'))
            ->where('job.status', config('constant.active'))
            ->where('job.expire_at', '>=', $now)
            ->with(['company:id,name,slug', 'jobMeta:job_id,priority'])
            ->orderBy('job.created_at', 'DESC')
            ->limit($limit)
            ->get();
    }

    public function numberJob($fromDate = null, $toDate = null)
    {
        $query = $this->query();
        if ($fromDate && $toDate) {
            $query->whereBetween('created_at', [$fromDate, $toDate]);
        }
        $totalRecruiting = $query->where('status', config('constant.active'))->where('is_active', config('constant.active'))->count();
        $totalStopRecruiting = $query->where('status', config('constant.inActive'))->where('is_active', config('constant.active'))->count();
        return [
            'totalRecruiting' => $totalRecruiting,
            'totalStopRecruiting' => $totalStopRecruiting
        ];
    }
    public function jobStatisticalByMonth($fromDate = null, $toDate = null)
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();
        if (!empty($fromDate) && !empty($toDate)) {
            $start = Carbon::parse($fromDate);
            $end = Carbon::parse($toDate);
        }
        $query = $this->query();
        return $query->where('is_active', config('constant.active'))
            ->where('is_real', 1) // Chỉ lấy dữ liệu thật
            // ->where('status', 1)
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('count(id) as total, DATE_FORMAT(created_at, "%m") as month')
            ->groupByRaw('DATE_FORMAT(created_at, "%m")')
            ->orderByRaw('DATE_FORMAT(created_at, "%m")')
            ->get();
    }
}
