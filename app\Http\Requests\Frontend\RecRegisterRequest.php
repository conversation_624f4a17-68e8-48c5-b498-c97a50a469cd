<?php

namespace App\Http\Requests\Frontend;

use App\Rules\Admin\CheckEmailRule;
use App\Rules\Admin\CheckPhoneRule;
use Illuminate\Foundation\Http\FormRequest;

class RecRegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'name'             => 'required',
            'email'            => ['required', 'email', new CheckEmailRule(config('constant.role.rec'))],
            'mobile'            => ['required', 'regex:/^[0-9()+.-]*$/', 'max:16','min:10', new CheckPhoneRule(config('constant.role.rec'))],
            'password'         => 'required|min:8|max:20',
            'confirm_password' => 'required|same:password',
        ];
    }

    public function messages()
    {
        return [
            'required'              => __('message.required'),
            'email.required'        => __('message.required'),
            'confirm_password.same' => __('message.same'),
            'min'                   => __('message.min'),
            'max'                   => __('message.max'),
            'email.email'           => __('message.email'),
            'mobile.regex'           => __('message.regex_phone'),
        ];
    }

}
