<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ChangeStatusCancelInterviewToAdminSubmit extends Mailable
{

    use Queueable;

    protected $submitCv;
    protected $bonus;

    public function __construct($submitCv,$bonus)
    {
        $this->submitCv = $submitCv;
        $this->bonus = $bonus;
    }


    public function content(): Content
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->bonus_type;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $companyName = $this->submitCv->employer->name;
        $bonus = $this->bonus;
        // $linkPreview = route('luot-ban.show',['luot_ban' => $this->submitCv->id]);
        $linkPreview           = route('submit-cv.edit', ['id' => $this->submitCv->id]);

        return new Content(
            view: 'email.changeStatusCancelInterviewToAdmin',
            with: [
                'candidateName' => $candidateName,
                'companyName'   => $companyName,
                'position'      => $position,
                'type'          => $type,
                'point'         => $bonus,
                'urlWallet'     => route('employer-wallet'),
                'urlMarket'     => route('market-cv'),
                'linkPreview'   => $linkPreview
            ],
        );
    }

    protected function buildSubject($message)
    {
        $candidateName = $this->submitCv->warehouseCv->candidate_name;
        $type = $this->submitCv->type_of_sale;
        $position = '';
        if ($type == 'cv'){
            $position = $this->submitCv->warehouseCv->candidate_job_title;
        }
        if ($type == 'interview' || $type == 'onboard' || !empty($this->submitCv->job)){
            $position = $this->submitCv->job->name;
        }
        $message->subject('[Ủy quyền] Thông báo hủy phỏng vấn ứng viên  '.$candidateName.' vị trí  '.$position);
        return $this;
    }




}
